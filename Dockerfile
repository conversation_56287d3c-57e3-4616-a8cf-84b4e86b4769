#Use Maven and Amazon JVM for this Java Build Stage
FROM maven:3.9.9-amazoncorretto-21-al2023 AS mvnbuild
COPY ./pom.xml ./pom.xml
#Download all dependencies
RUN mvn dependency:resolve dependency:resolve-plugins

#Copy over Source Code
COPY ./src ./src

#Create a version.html to view the docker build info
ARG DOCKER_REPO
ARG DOCKER_TAG
ARG SOURCE_BRANCH
ARG SOURCE_COMMIT
ARG COMMIT_MSG
RUN mkdir -p ./src/main/resources/static
RUN echo "DOCKER_REPO:$DOCKER_REPO<br>DOCKER_TAG:$DOCKER_TAG<br>SOURCE_BRANCH:$SOURCE_BRANCH<br>SOURCE_COMMIT:$SOURCE_COMMIT<br>COMMIT_MSG:$COMMIT_MSG" > ./src/main/resources/static/version.html
RUN echo "$DOCKER_TAG" > ./src/main/resources/static/tag.html

RUN sed -i "s|PLACEHOLDER_DOCKER_REPO|$DOCKER_REPO|" ./src/main/resources/application.yml
RUN sed -i "s|PLACEHOLDER_DOCKER_TAG|$DOCKER_TAG|" ./src/main/resources/application.yml
RUN sed -i "s|PLACEHOLDER_SOURCE_BRANCH|$SOURCE_BRANCH|" ./src/main/resources/application.yml
RUN sed -i "s|PLACEHOLDER_SOURCE_COMMIT|$SOURCE_COMMIT|" ./src/main/resources/application.yml
RUN sed -i "s|PLACEHOLDER_COMMIT_MSG|$COMMIT_MSG|" ./src/main/resources/application.yml

#Build and package
RUN mvn package

#Use Amazon JVM and Amazon Linux 2023
FROM amazoncorretto:21.0.5-al2023

#Install hostname for elk apm
RUN yum -y install hostname
#Check Java Home
RUN echo "JAVA_HOME: $JAVA_HOME"
#Increase ttl
RUN echo "networkaddress.cache.ttl=10" >> "$JAVA_HOME/conf/security/java.security"

#Copy the jar file from the build stage to the current stage
COPY --from=mvnbuild target/*.jar ./opt/noosh/services.jar
#Copy ELK APM agent jar
COPY --from=docker.elastic.co/observability/apm-agent-java:1.51.0 /usr/agent/elastic-apm-agent.jar /opt/elastic-apm-agent.jar

ENTRYPOINT ["java", "-jar", "/opt/noosh/services.jar"]

#Do nothing, just stay running
# SHELL ["tail", "-f", "/dev/null"]