#Use Maven and IBM JVM with OpenJDK and Eclipse OpenJ9 for this Java Build Stage
FROM maven:3-ibm-semeru-17-focal AS mvnbuild
COPY ./pom.xml ./pom.xml
#Download all dependencies
RUN mvn dependency:resolve dependency:resolve-plugins

#Copy over Source Code
COPY ./src ./src

#Create a version.html to view the docker build info
ARG DOCKER_REPO
ARG DOCKER_TAG
ARG SOURCE_BRANCH
ARG SOURCE_COMMIT
ARG COMMIT_MSG
RUN mkdir -p ./src/main/resources/static
RUN echo "DOCKER_REPO:$DOCKER_REPO<br>DOCKER_TAG:$DOCKER_TAG<br>SOURCE_BRANCH:$SOURCE_BRANCH<br>SOURCE_COMMIT:$SOURCE_COMMIT<br>COMMIT_MSG:$COMMIT_MSG" > ./src/main/resources/static/version.html
RUN echo "$DOCKER_TAG" > ./src/main/resources/static/tag.html

RUN sed -i "s|PLACEHOLDER_DOCKER_REPO|$DOCKER_REPO|" ./src/main/resources/application.yml
RUN sed -i "s|PLACEHOLDER_DOCKER_TAG|$DOCKER_TAG|" ./src/main/resources/application.yml
RUN sed -i "s|PLACEHOLDER_SOURCE_BRANCH|$SOURCE_BRANCH|" ./src/main/resources/application.yml
RUN sed -i "s|PLACEHOLDER_SOURCE_COMMIT|$SOURCE_COMMIT|" ./src/main/resources/application.yml
RUN sed -i "s|PLACEHOLDER_COMMIT_MSG|$COMMIT_MSG|" ./src/main/resources/application.yml

#Build and package
RUN mvn package

#Use IBM JVM with OpenJDK and Eclipse OpenJ9 and Ubuntu Focal Linux
FROM ibm-semeru-runtimes:open-17.0.7_7-jdk-focal
#Increase ttl
RUN echo "networkaddress.cache.ttl=10" >> /opt/java/openjdk/conf/security/java.security
COPY --from=mvnbuild target/*.jar ./opt/myservice/services.jar

#Adding the following statement in Dockerfile will copy the agent jar to /opt/elastic-apm-agent.jar.
COPY --from=docker.elastic.co/observability/apm-agent-java:1.45.0 /usr/agent/elastic-apm-agent.jar /opt/elastic-apm-agent.jar

ENTRYPOINT ["java", "-jar", "/opt/myservice/services.jar"]

#Do nothing, just stay running
#CMD tail -f /dev/null