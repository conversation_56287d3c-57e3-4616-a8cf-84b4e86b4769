<!DOCTYPE html>
<html>
<head>
    <title></title>
    <style type="text/css">
        @font-face {
            font-family: 'RobotoRegular';
            src: url("./Roboto/Roboto-Regular.ttf");
        }
        @font-face {
            font-family: 'RobotoMedium';
            src: url("./Roboto/Roboto-Medium.ttf");
        }
        @font-face {
            font-family: 'RobotoBold';
            src: url("./Roboto/Roboto-Bold.ttf");
        }

        body {
            padding: 0;
            margin: 0;
            font-family: 'RobotoRegular';
        }

        .glb-content {
            font-size: 16px;
            line-height: 16px;
            /*width: 612px;*/
            /*min-height: 852px;*/
            padding: 40px 40px 75px 40px;
            margin: 0 auto;
            box-sizing: border-box;
            position: relative;
        }

        .glb-bold {
            font-family: 'RobotoBold';
        }

        .glb-medium {
            font-family: 'RobotoMedium';
        }

        .glb-dark {
            color: #212121;
        }

        .glb-float-top-container > div {
            display: inline-block;
            vertical-align: top;
            margin-right: -3px;
        }

        .glb-float-middle-container {
            position: relative;
        }

        .glb-float-middle-container > div {
            display: inline-block;
            vertical-align: middle;
            margin-right: -3px;
        }

        .glb-absolute-top-right {
            position: absolute;
            top: 7px;
            right: 10px;
        }

        .float-left {
            float: left;
        }

        .float-right {
            float: right;
        }

        .clear-both {
            clear: both;
        }

        .grid-2 {
            width: 16.666%;
            max-width: 16.666%;
        }

        .grid-3 {
            width: 25%;
            max-width: 25%;
        }

        .grid-4 {
            width: 33.333%;
            max-width: 33.333%;
        }

        .grid-6 {
            width: 50%;
            max-width: 50%;
        }

        .grid-8 {
            width: 66.667%;
            max-width: 66.667%;
        }

        .grid-10 {
            width: 83.333%;
            max-width: 83.333%;
        }

        .grid-12 {
            width: 100%;
            max-width: 100%;
        }

        .glb-grid > div.grid-6 {
            margin-top: 5px;
        }

        .glb-main-title {
            color: #702BE8;
            font-size: 40px;
        }

        .glb-line-height-14 {
            line-height: 21px;
        }

        .glb-mt-10 {
            margin-top: 10px;
        }

        .glb-mb-10 {
            margin-bottom: 10px;
        }

        .glb-mt {
            margin-top: 40px;
        }

        .glb-mb {
            margin-bottom: 40px;
        }

        .glb-p-10 {
            padding: 10px;
        }

        .glb-py-1 {
            padding-top: 5px;
            padding-bottom: 5px;
        }

        .glb-small-text {
            color: #757575;
            font-size: 16px;
            line-height: 16px;
        }

        .glb-extra-small-text {
            color: #212121;
            font-size: 14px;
            line-height: 14px;
        }

        .glb-underline {
            text-decoration: underline;
        }

        .glb-logo {
            height: 48px;
        }

        .glb-width-100 {
            width: 150px;
        }

        .glb-width-155 {
            width: 155px;
        }

        .glb-width-412 {
            width: 412px;
        }

        .glb-max-width-330 {
            max-width: 330px;
        }

        .glb-max-width-400 {
            max-width: 400px;
        }

        .glb-max-width-450 {
            max-width: 450px;
        }

        .glb-grey-bg {
            padding: 7px 10px;
            background-color: #F1F1F1;
        }

        .glb-border-top {
            border-top: 1px solid #F1F1F1;
        }

        .glb-border-bottom {
            border-bottom: 1px solid #F1F1F1;
        }

        /*  page number  */
        @page {
            size: a4;
            <#if isLandscape>
                size: landscape;
            </#if>
            margin: 0px;
            @top-center {
                content: element(header);
            }
            @bottom-center {
                content: element(footer);
            }
        }
        div.header {
            margin-top: 1060px;
            margin-right: 40px;
            font-size: 16px;
            color: #757575;
            text-align: right;
            position: running(header);
        }

        div.footer {
            top: 40px;
            right: 10px;
            font-size: 16px;
            color: #333333;
            text-align: right;
            position: running(footer);
        }
        .pageNext{page-break-after: always;}
        #pagenumber:before {content: counter(page);}
        #pagecount:before {content: counter(pages);}

    </style>
</head>
<body>
<#if includePageNumber>
    <div class='header'>Page <span id="pagenumber" /> of <span id="pagecount" /></div>
</#if>

<div class="glb-content">
    <div class="glb-float-top-container">
        <div class="float-left">
            <div class="glb-main-title glb-mt glb-mb">Quotation</div>
            <div class="glb-bold">To</div>
            <div class="glb-line-height-14">${clientName!}</div>
            <div class="glb-line-height-14">${clientAddress.line1!} ${clientAddress.line2!} ${clientAddress.line3!}</div>
            <div class="glb-line-height-14">${clientAddress.countryName}, ${clientAddress.city!}, ${clientAddress.state!} ${clientAddress.postal!}</div>
        </div>
        <div class="float-right" style="min-width: 200px;">
            <img class="glb-logo glb-mb" <#if hasLogo?? && hasLogo> src="images/${logo.userFilename!}" </#if> />
            <div class="glb-small-text">${outsourcerName}</div>
            <div class="glb-small-text">${outsourcerAddress.line1!} ${outsourcerAddress.line2!} ${outsourcerAddress.line3!}</div>
            <div class="glb-small-text">${outsourcerAddress.countryName!}, ${outsourcerAddress.city!}, ${outsourcerAddress.state!} ${outsourcerAddress.postal!}</div>
        </div>
    </div>
    <div class="clear-both"></div>


    <div class="glb-float-middle-container glb-mt">
        <div class="glb-medium glb-width-100">Quotation ID</div>
        <div class="glb-dark">${proposalId!}</div>
    </div>
    <div class="glb-float-middle-container">
        <div class="glb-medium glb-width-100">Project Name</div>
        <div class="glb-dark">${projectName!}</div>
    </div>
    <div class="glb-float-middle-container">
        <div class="glb-medium glb-width-100">Prepared Date</div>
        <div class="glb-dark">${formatPdfDateTime(prepareDate)!}</div>
    </div>
    <div class="glb-float-middle-container">
        <div class="glb-medium glb-width-100">Expiration Date</div>
        <div class="glb-dark">${formatPdfDateTime(expirationDate)!}</div>
    </div>

    <div class="glb-small-text glb-dark glb-mt">
        ${quoteComments!}
    </div>

    <#list proposalItems! as item>
        <div class="glb-dark glb-mt">
            <div class="glb-float-middle-container glb-grey-bg">
                <div class="glb-medium glb-max-width-330">${item.itemIndex!}. ${item.specName!}</div>
                <div class="glb-absolute-top-right">Completion date: ${formatPdfDateTime(completionDate)!}</div>
            </div>
            <div class="glb-float-middle-container glb-p-10 glb-grid glb-border-bottom">
                <div class="grid-12 glb-medium glb-underline">Specification</div>
                <#list item.specSummaryFieldValues! as specSummaryFieldValue>
                    <div class="grid-6 glb-float-middle-container">
                        <div class="glb-medium glb-width-100">${specSummaryFieldValue.name!}</div>
                        <div class="glb-width-155">${specSummaryFieldValue.stringValue!}</div>
                    </div>
                </#list>
            </div>
            <div class="glb-float-top-container glb-p-10 glb-grid">
                <div class="glb-medium glb-width-100 glb-underline">Pricing<br/>Information</div>
                <div class="glb-width-412">
                    <div class="grid-12 glb-float-middle-container">
                        <div class="grid-2 glb-medium glb-border-bottom glb-py-1">Quantity</div>
                        <#list item.quotePrices! as quotePrice>
                            <div class="grid-2 glb-border-bottom glb-py-1">${quotePrice.quantity!}</div>
                        </#list>
                    </div>
                    <div class="grid-12 glb-float-middle-container">
                        <div class="grid-2 glb-medium glb-border-bottom glb-py-1">Item Price</div>
                        <#list item.quotePrices! as quotePrice>
                            <div class="grid-2 glb-border-bottom glb-py-1">${currencySymbol+quotePrice.price!?string('0.00000')}</div>
                        </#list>
                    </div>
                    <div class="grid-12 glb-float-middle-container">
                        <div class="grid-2 glb-medium glb-border-bottom glb-py-1">Unit Price</div>
                        <#list item.quotePrices! as quotePrice>
                            <div class="grid-2 glb-border-bottom glb-py-1">${currencySymbol+quotePrice.unitPrice!?string('0.00000')}</div>
                        </#list>
                    </div>
                </div>
            </div>
        </div>
        <div class="glb-small-text glb-dark glb-mt-10">
            ${item.quoteItemComments!}
        </div>
    </#list>


    <div class="glb-dark glb-mt-10">
        <div class="glb-float-middle-container glb-grid glb-border-top">
            <div class="grid-8"></div>
            <div class="grid-4 glb-float-middle-container">
                <div class="grid-12 glb-float-middle-container">
                    <div class="glb-bold grid-6 glb-border-bottom glb-py-1">Total</div>
                    <div class="grid-6 glb-border-bottom glb-py-1">${currencySymbol+totalPrice?string('0.00000')}</div>
                </div>
                <div class="grid-12 glb-float-middle-container">
                    <div class="grid-6 glb-border-bottom glb-py-1">${taxLabelString}</div>
                    <div class="grid-6 glb-border-bottom glb-py-1">${currencySymbol+tax?string('0.00000')}</div>
                </div>
                <div class="grid-12 glb-float-middle-container">
                    <div class="grid-6 glb-border-bottom glb-py-1">Shipping</div>
                    <div class="grid-6 glb-border-bottom glb-py-1">${currencySymbol+shipping?string('0.00000')}</div>
                </div>
                <div class="grid-12 glb-float-middle-container">
                    <div class="glb-bold grid-6 glb-border-bottom glb-py-1">Grand Total</div>
                    <div class="grid-6 glb-border-bottom glb-py-1">${currencySymbol+grandTotal?string('0.00000')}</div>
                </div>
            </div>
        </div>
    </div>

    <#if includeTermsConditions?? && includeTermsConditions>
        <div class="glb-small-text glb-bold">
            Terms and Conditions
        </div>
        <div class="glb-extra-small-text">
            ${termTexts!}
        </div>
    </#if>


</div>

</body>
</html>
