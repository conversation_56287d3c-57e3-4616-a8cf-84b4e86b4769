<!DOCTYPE html>
<html>
<head>
    <title></title>
    <style type="text/css">


        body {
            padding: 0;
            margin: 0;
            font-family: Open Sans, sans-serif;
        }

        .glb-content {
            position: relative;
            box-sizing: border-box;
            /*width: 600px;*/
            /*min-height: 100vh;*/
            min-height: 1010px;
            margin: 0 auto;
            padding: 20px 32px;
            position: relative;
            /*display: flex;*/
            /*flex-direction: column;*/
            /*justify-content: space-between;*/
        }

        .glb-page-num {
            position: absolute;
            right: 10px;
            top: 10px;
            font-size: 16px;
            color: #333333;
        }

        .glb-bg-grey {
            /*background-color: #FAFAFA;*/
        }

        .glb-header {
            /*display: flex;*/
            /*align-items: flex-start;*/
            /*justify-content: space-between;*/
            margin: -20px -32px;
            padding: 30px 32px;
        }

        .glb-logo {
            float: left;
            text-align: center;
        }

        .glb-logo img {
            max-width: 80px;
            max-height: 80px;
        }

        .glb-small-logo img {
            max-width: 45px;
            max-height: 45px;
        }

        .glb-logo p {
            margin: 0;
            font-size: 8px;
            color: #828691;
        }

        .glb-contact {
        }

        .glb-info {
            font-size: 8px;
            line-height: 13px;
            color: #828691;
        }

        .glb-info span {
            color: #0099FF;
            margin-right: 5px;
        }

        .glb-info-title {
            font-size: 8px;
            line-height: 11px;
            font-weight: 600;
            color: #1F2229;
            text-transform: uppercase;
        }

        .glb-margin-top {
            margin-top: 40px;
        }

        .glb-margin-bottom {
            margin-bottom: 10px;
        }

        .glb-align-right {
            text-align: right;
            float: right;
        }

        .glb-padding-right {
            padding-right: 20px ;
        }

        .glb-no-border {
            border-bottom: none !important;
        }

        .grid-container {
            /*display: flex;*/
            /*flex-wrap: wrap;*/
        }

        .grid-container > div {
            box-sizing: border-box;
            display: inline-block;
            margin-right: -3px;
            vertical-align: top;
        }

        .grid-3 {
            width: 25%;
            max-width: 25%;
            flex-basis: 25%;
            margin-right: -4px !important;
        }

        .grid-4 {
            width: 33.33%;
            max-width: 33.33%;
            flex-basis: 33.33%;
        }

        .grid-6 {
            width: 50%;
            max-width: 50%;
            flex-basis: 50%;
        }

        .bold {
            font-weight: 600;
        }

        .align-right {
            text-align: right;
        }

        .align-center {
            text-align: center;
        }

        .float-left {
            float: left;
        }

        .float-right {
            float: right;
        }

        .width-50 {
            width: 50%;
        }

        .table-2 td {
            width: 50%;
        }

        .table-4 td {
            width: 25%;
        }

        /*cover*/
        .cover-content {
            position: absolute;
            width: 100%;
            top: 50%;
            transform: translateY(-50%);
            left: 32px;
        }

        .cover-content .name {
            font-size: 64px;
            line-height: 87px;
            color: #333333;
        }

        .cover-content .date {
            font-size: 18px;
            line-height: 24px;
            color: #828282;
            margin-top: 5px;
        }

        .cover-footer {
            position: absolute;
            width: 100%;
            bottom: 20px;
            left: 32px;
        }

        .cover-footer .border {
            width: 152px;
            border-top: 5px solid #333333;
        }

        .cover-footer .company {
            font-size: 18px;
            color: #333333;
            margin-top: 10px;
        }

        .cover-footer .designation {
            font-size: 14px;
            color: #828282;
            margin-top: 5px;
        }

        /* proposal */
        .proposal-content {
            clear: both;
            box-sizing: border-box;
            height: 100%;
            padding: 20px 0;
        }

        .title {
            clear: both;
            font-size: 22px;
            line-height: 30px;
            font-weight: 600;
            color: #1F2229;
            margin-bottom: 15px;
        }

        .proposal-info-container > div {
            margin-bottom: 5px;
        }

        .proposal-footer {
            border-top: 1px solid #E7E8EC;
            padding-top: 20px;
            align-items: flex-start;
            justify-content: space-between;
        }

        .proposal-spec-title {
            padding: 10px 0;
            font-size: 8px;
            line-height: 11px;
            font-weight: 600;
            color: #9DA8BB;
            text-transform: uppercase;
        }

        .proposal-info-name {
            padding: 10px 0;
            /*border-bottom: 1px solid #E7E8EC;*/
            font-size: 8px;
            line-height: 11px;
            font-weight: 600;
            color: #1F2229;
        }

        .proposal-price-name {
            padding: 10px 0;
            border-bottom: 1px solid #E7E8EC;
            font-size: 8px;
            line-height: 11px;
            font-weight: 600;
            color: #9DA8BB;
        }

        .proposal-info-value {
            padding: 10px 0;
            border-bottom: 1px solid #E7E8EC;
            font-size: 8px;
            line-height: 11px;
            min-height: 11px;
            color: #1F2229;
        }

        .proposal-total-value {
            padding: 10px 0;
            border-bottom: 1px solid #E7E8EC;
            font-size: 12px;
            line-height: 16px;
            min-height: 16px;
            color: #0099FF;
        }

        .section-title {
            clear: both;
            font-size: 16px;
            line-height: 22px;
            font-weight: 600;
            color: #1F2229;
            margin-top: 30px;
            margin-bottom: 15px;
        }

        .section-content {
            font-size: 8px;
            line-height: 12px;
            color: #000000;
        }

        .glb-table {
            width: 100%;
            font-size: 8px;
            line-height: 11px;
            border-collapse: collapse;
        }

        .glb-table td {
            padding: 10.5px 0;
            color: #1F2229;
        }

        .border-bottom-table td {
            border-bottom: 1px solid #E7E8EC;
        }

        .no-border-table td {
            border-bottom: none;
        }

        .sub-table-container {
            background-color: #FBFBFB;
            margin: -10.5px 0;
        }

        .sub-table-container table td {
            padding: 10.5px;
        }

        /*  page number  */
        @page {
            size: a4;
            <#if isLandscape>
                size: landscape;
            </#if>
            margin: 0px;
            @top-center {
                content: element(header);
            }
            @bottom-center {
                content: element(footer);
            }
        }
        div.header {
            right: 20px;
            top: 20px;
            font-size: 16px;
            color: #333333;
            text-align: right;
            position: running(header);
        }
        div.footer {
            display: block; text-align: center;
            position: running(footer);
        }

        .pageNext{page-break-after: always;}
        #pagenumber:before {content: counter(page);}
        #pagecount:before {content: counter(pages);}
    </style>
</head>
<body>

<#if includePageNumber>
    <div class='header'>Page <span id="pagenumber" /> of <span id="pagecount" /></div>
</#if>

<#-- start cover page -->
<#if includeCoverPage?? && includeCoverPage>
    <div class="glb-content glb-bg-grey" id="cover">
        <div class="glb-header">
            <#if hasLogo?? && hasLogo>
                <#if logoPosition?? && logoPosition == 1>
                    <div class="glb-logo">
                        <img src="images/${logo.userFilename}" />
                        <p>YOUR LOGO</p>
                    </div>
                <#elseIf logoPosition?? && logoPosition == 2>
                    <div class="glb-logo">
                        <img src="images/${logo.userFilename}" />
                        <p>YOUR LOGO</p>
                    </div>
                <#else>
                    <div class="glb-logo">
                        <img src="images/${logo.userFilename}" />
                        <p>YOUR LOGO</p>
                    </div>
                </#if>
            </#if>
        </div>

        <div class="cover-content">
            <div class="name">${title}</div>
            <div class="date">${currentDate}</div>
        </div>
        <div class="cover-footer">
            <div class="border"></div>
            <div class="company">${outsourcerAddress.countryName}</div>
            <div class="designation">Designation</div>
        </div>
    </div>
    <#-- Force pagination  -->
    <div class="pageNext"></div>
</#if>
<#-- end cover page -->

<div class="glb-content">
    <div class="glb-header glb-bg-grey">
        <#--  start logo -->
        <#if hasLogo?? && hasLogo>
            <#if logoPosition?? && logoPosition == 1>
                <div class="glb-logo glb-small-logo">
                    <img src="images/${logo.userFilename}" />
                    <p>YOUR LOGO</p>
                </div>
            <#elseIf logoPosition?? && logoPosition == 2>
                <div class="glb-logo glb-small-logo">
                    <img src="images/${logo.userFilename}" />
                    <p>YOUR LOGO</p>
                </div>
            <#else>
                <div class="glb-logo glb-small-logo">
                    <img src="images/${logo.userFilename}" />
                    <p>YOUR LOGO</p>
                </div>
            </#if>
        </#if>
        <#--  end logo -->

        <div class="glb-contact glb-align-right">
            <div class="glb-info">
                ${outsourcerName}<br/>
                ${outsourcerAddress.line1!} ${outsourcerAddress.line2!} ${outsourcerAddress.line3!}<br/>
                ${outsourcerAddress.city!}, ${outsourcerAddress.state!} ${outsourcerAddress.postal!}<br/>
                ${outsourcerAddress.countryName}<br/>
                <br/>
            </div>
            <div class="glb-info"><span>@</span><EMAIL></div>
            <div class="glb-info"><span>m</span>${outsourcerPhone!}</div>
        </div>

        <div class="title">Proposal</div>
        <div class="grid-container">
            <div class="grid-4">
                <div class="glb-info-title glb-margin-bottom">RECIPIENT</div>
                <div class="glb-info">
                    ${clientName!}<br/>
                    ${clientAddress.line1!} ${clientAddress.line2!} ${clientAddress.line3!}<br/>
                    ${clientAddress.city!}, ${clientAddress.state!} ${clientAddress.postal!}<br/>
                    ${clientAddress.countryName}<br/>
                    <br/>
                </div>
                <div class="glb-info"><span>@</span><EMAIL></div>
                <div class="glb-info"><span>m</span>${clientPhone!}</div>
            </div>
            <div class="grid-4">
                <div class="glb-info-title glb-margin-bottom">PREPARER</div>
                <div class="glb-info">
                    ${outsourcerName}<br/>
                    ${outsourcerAddress.line1!} ${outsourcerAddress.line2!} ${outsourcerAddress.line3!}<br/>
                    ${outsourcerAddress.city!}, ${outsourcerAddress.state!} ${outsourcerAddress.postal!}<br/>
                    ${outsourcerAddress.countryName!}<br/>
                    <br/>
                </div>
                <div class="glb-info"><span>@</span><EMAIL></div>
                <div class="glb-info"><span>m</span>${outsourcerPhone!}</div>
            </div>
            <div class="grid-4">
                <div class="glb-info-title glb-margin-bottom">INFO</div>
                <div class="grid-container proposal-info-container">
                    <div class="grid-6 glb-info-title">Project No.:</div>
                    <div class="grid-6 glb-info glb-align-right">${proposalId!}</div>
                    <div class="grid-6 glb-info-title">PREPARED DATE:</div>
                    <div class="grid-6 glb-info glb-align-right">${prepareDate!}</div>
                    <div class="grid-6 glb-info-title">Expiration date:</div>
                    <div class="grid-6 glb-info glb-align-right">${expirationDate!}</div>
                </div>
            </div>
        </div>
    </div>

    <#-- start cover letter -->
    <#if includeCoverLetter?? && includeCoverLetter>
        <div class="section-title">Salutation</div>
        <div class="section-content">
            ${saluteText!}
        </div>
        <div class="section-title">Introduction</div>
        <div class="section-content">
            ${introText!}
        </div>
        <div class="section-title">Conclusion</div>
        <div class="section-content">
            ${conclusionText!}
        </div>
        <div class="section-title">Closing</div>
        <div class="section-content">
            ${closingText!}
        </div>
        <#-- Force pagination  -->
        <p style="margin: 0pt">
            <div style="page-break-before: always; clear: both"/>
        </p>
    </#if>
    <#-- end cover letter -->

    <!-- start Pricing Summary -->
    <#if showAggregation>
        <div class="section-title">Pricing Summary</div>
        <#list categories! as category>
<#--            <#assign qtyList=catQtyMap["${category.categoryId}"]>-->
<#--            <#assign bkMap=catBkMap["${category.categoryId}"]>-->

            <table class="glb-table border-bottom-table table-2">
                <tr><td class="bold">${category.name!}</td>
                    <#list category.qtyList! as quantity>
                        <td>${quantity!}</td>
                    </#list>
                    <td>&nbsp;</td>
                </tr>

                <tr>
                    <td colspan="2">
                        <div class="sub-table-container">
                            <table class="glb-table no-border-table table-4">
                                <#list breakoutTypes! as breakoutType>
                                    <#if category.breakoutMap??>
                                        <tr>
                                            <td>${breakoutType.name}</td>

                                            <#list category.priceMap[breakoutType.name]! as price>
                                                <!-- price per-->
                                                <td class="align-right">
                                                    ${currencySymbol+price!?string('0.00000')}
                                                </td>
                                            </#list>
                                            <td class="align-right">
                                                <#if breakoutType.pricePer != "">
                                                    <#if breakoutType.pricePer == "1">
                                                        per Unit
                                                    <#else>
                                                        per ${breakoutType.pricePer!}
                                                    </#if>
                                                </#if>
                                            </td>
                                        </tr>
                                    </#if>
                                </#list>
                            </table>
                        </div>
                    </td>
                </tr>

                <tr><td class="bold">Price</td>
                    <#list category.itemPrices! as itemPrice>
                        <td>
                            <!-- price -->
                            ${currencySymbol+itemPrice!?string('0.00000')}
                        </td>
                    </#list>
                </tr>
                <tr><td class="bold">Tax</td>
                    <#list category.taxs! as tax>
                        <td>
                            <!-- price -->
                            ${currencySymbol+tax!?string('0.00000')}
                        </td>
                    </#list>
                </tr>
                <tr><td class="bold">Shipping</td>
                    <#list category.shippings! as shipping>
                        <td>
                            <!-- price -->
                            ${currencySymbol+shipping!?string('0.00000')}
                        </td>
                    </#list>
                </tr>
                <tr><td class="bold"><B>Price</B></td>
                    <#list category.totalPrices! as totalPrice>
                        <td>
                            <!-- price -->
                            ${currencySymbol+totalPrice!?string('0.00000')}
                        </td>
                    </#list>
                </tr>
            </table>
        </#list>
    </#if>
    <!-- end Pricing Summary -->

    <div class="proposal-content">
        <#list proposalItems! as item>
            <#-- start Specification Name -->
            <div class="section-title">Specification Name: ${item.specName!}</div>
            <table class="glb-table border-bottom-table table-2">
                <tr><td class="bold">Descriptions</td><td class="align-right">${item.comments!}</td></tr>
                <tr><td class="bold">Product Category</td><td class="align-right">${item.categoryName!}</td></tr>
            </table>

            <#--price infomation-->
            <div class="section-title">Price Information</div>
            <!-- start layout vertical -->
            <#if layout == "vertical">
                <#list item.quotePrices! as quotePrice>
                    <table class="glb-table border-bottom-table table-2">
                    <#if isSeparateSpecPricing>
                        <tr><td class="bold">Spec</td><td class="align-right">${item.specName!}</td></tr>
                    </#if>
                    <tr><td class="bold">Quantity</td><td class="align-right">${quotePrice.quantity!}</td></tr>
                    <tr><td class="bold">Supplier</td><td class="align-right">${quotePrice.supplierName!}</td></tr>
                    <tr><td class="bold">Markup Percent</td><td class="align-right">${quotePrice.markupPercent!?string('0.00')}%</td></tr>
                    <tr><td class="bold">Markup Fixed</td><td class="align-right">${quotePrice.markupFixed!}</td></tr>
                    <tr>
                        <td colspan="2">
                            <div class="sub-table-container">
                                <table class="glb-table no-border-table table-4">
                                    <tr>
                                        <#if includePriceBreakouts && quotePrice.breakouts?size != 0>
                                            <td>Price Breakouts</td>
                                        </#if>
                                        <#if quotePrice.hasSubQty>
                                            <td>Subquantities</td>
                                        </#if>
                                        <#if isMarkupVisible>
                                            <td>Cost</td>
                                        </#if>
                                        <#if (includePriceBreakouts && quotePrice.breakouts?size != 0) || isMarkupVisible>
                                            <td>Price</td>
                                        </#if>
                                    </tr>
                                    <#if includePriceBreakouts && quotePrice.breakouts?size != 0>
                                        <#list quotePrice.breakouts! as breakout>
                                        <tr>
                                            <td>${breakout.breakoutType.name}</td>

                                            <#if quotePrice.hasSubQty>
                                                <td>${breakout.value!}</td>
                                            </#if>

                                            <!-- cost -->
                                            <#if isMarkupVisible>
                                                <td>${currencySymbol+breakout.preMarkup!}</td>
                                            </#if>

                                            <!-- price per-->
                                            <td>
                                                ${currencySymbol+breakout.price!}
                                                <#if breakout.breakoutType.pricePer != "">
                                                    <#if breakout.breakoutType.pricePer == "1">
                                                        per Unit
                                                    <#else>
                                                        per ${breakout.breakoutType.pricePer!}
                                                    </#if>
                                                </#if>
                                            </td>
                                        </tr>
                                        </#list>
                                    </#if>
                                </table>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="bold">Item Price</td>

                        <td class="bold align-right">
                            <!-- cost -->
                            <#if isMarkupVisible>
                                <div class="width-50 float-left">${currencySymbol+quotePrice.preMarkup!}</div>
                            </#if>
                            <!-- price -->
                            <#if quotePrice.isChoosePrice>
                                <div class="width-50 float-left"><B>${currencySymbol+quotePrice.price!?string('0.00000')}</B></div>
                            <#else>
                                ${currencySymbol+quotePrice.price!?string('0.00000')}
                            </#if>
                        </td>
                    </tr>
                    <tr><td></td></tr>
                </table>
                </#list>
            <!-- end layout vertical -->
            <#else>
                <!-- start layout horizontal -->
                <table class="glb-table border-bottom-table table-2">
                    <#if isSeparateSpecPricing>
                        <tr><td class="bold">Spec</td><td class="align-right">${item.specName!}</td></tr>
                    </#if>
                    <tr><td class="bold">Quantity</td>
                        <#list item.quotePrices! as quotePrice>
                            <td>${quotePrice.quantity!}</td>
                        </#list>
                    </tr>
                    <tr><td class="bold">Supplier</td>
                        <#list item.quotePrices! as quotePrice>
                            <td>${quotePrice.supplierName!}</td>
                        </#list>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <div class="sub-table-container">
                                <table class="glb-table no-border-table table-4">
                                    <#if includePriceBreakouts>
                                        <tr>
                                            <td>Price Breakouts</td>
                                        </tr>
                                    </#if>

                                    <#if includePriceBreakouts && item.quotePrices!?size != 0>
                                        <#list 0..(item.quotePrices[0].breakouts!?size-1) as i>
                                            <tr>
                                                <td>${item.quotePrices[0].breakouts[i].breakoutType.name}</td>

                                                <#list item.quotePrices! as quotePrice>
                                                    <!-- price per-->
                                                    <td class="align-center">
                                                        ${currencySymbol+quotePrice.breakouts[i].price!?string('0.00000')}
                                                        <#if quotePrice.breakouts[i].breakoutType.pricePer != "">
                                                            <#if quotePrice.breakouts[i].breakoutType.pricePer == "1">
                                                                per Unit
                                                            <#else>
                                                                per ${quotePrice.breakouts[i].breakoutType.pricePer!}
                                                            </#if>
                                                        </#if>
                                                    </td>
                                                </#list>
                                            </tr>
                                        </#list>
                                    </#if>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr><td class="bold">Item Price</td>
                        <#list item.quotePrices! as quotePrice>
                            <td>
                                <!-- price -->
                                <#if quotePrice.isChoosePrice>
                                    <div class="width-50 float-left"><B>${currencySymbol+quotePrice.price!?string('0.00000')}</B></div>
                                <#else>
                                    ${currencySymbol+quotePrice.price!?string('0.00000')}
                                </#if>
                            </td>
                        </#list>
                    </tr>
                </table>
            </#if>

        </#list>

        <div class="grid-container">

            <div class="grid-6">&nbsp;</div>
            <div class="grid-6 grid-container">
                <div class="grid-6 proposal-price-name">Total</div>
                <div class="grid-6 proposal-info-value glb-align-right">${currencySymbol+totalPrice?string('0.00000')}</div>
            </div>
            <div class="grid-6">&nbsp;</div>
            <div class="grid-6 grid-container">
                <div class="grid-6 proposal-price-name">${taxLabelString}</div>
                <div class="grid-6 proposal-info-value glb-align-right">${currencySymbol+tax?string('0.00000')}</div>
            </div>
            <div class="grid-6">&nbsp;</div>
            <div class="grid-6 grid-container">
                <div class="grid-6 proposal-price-name">Shipping</div>
                <div class="grid-6 proposal-info-value glb-align-right">${currencySymbol+shipping?string('0.00000')}</div>
            </div>
            <div class="grid-6">&nbsp;</div>
            <div class="grid-6 grid-container">
                <div class="grid-6 proposal-price-name">Grand Total</div>
                <div class="grid-6 proposal-total-value glb-align-right">${currencySymbol+grandTotal?string('0.00000')}</div>
            </div>
            <div style="clear: both"></div>
        </div>

        <#-- start Terms and Conditions -->
        <#if includeTermsConditions?? && includeTermsConditions>
            <div class="section-title">Terms and Conditions</div>
            <div class="section-content">
                ${termTexts!}
            </div>
        </#if>
        <#-- end Terms and Conditions -->

        <#-- start Proposal Notes -->
        <#if includeProposalNote?? && includeProposalNote>
            <div class="section-title">Proposal Note</div>
            <div class="section-content">
                ${proposalNote!}
            </div>
        </#if>
        <#-- end Proposal Notes -->
    </div>

    <#-- start Signature Page -->
    <#if includeSignaturePage?? && includeSignaturePage>
        <div class="glb-margin-top grid-container">
            <div class="grid-3 proposal-info-name glb-no-border">Presented By:</div>
            <div class="grid-3 glb-padding-right">
                <div class="proposal-info-value">Apparel</div>
            </div>
            <div class="grid-3 proposal-info-name glb-no-border">Accepted By:</div>
            <div class="grid-3 glb-padding-right">
                <div class="proposal-info-value"></div>
            </div>

            <div class="grid-3 proposal-info-name glb-no-border"></div>
            <div class="grid-3 glb-padding-right">
                <div class="proposal-info-value">${preparedName!}</div>
            </div>

            <#if preparedTitle??>
                <div class="grid-3 proposal-info-name glb-no-border"></div>
                <div class="grid-3 glb-padding-right">
                    <div class="proposal-info-value">${preparedTitle!}</div>
                </div>
            </#if>

            <#if outsourcerPhone??>
                <div class="grid-3 proposal-info-name glb-no-border"></div>
                <div class="grid-3 glb-padding-right">
                    <div class="proposal-info-value">${outsourcerPhone!}</div>
                </div>
            </#if>

            <div class="grid-3 proposal-info-name glb-no-border">Title:</div>
            <div class="grid-3 glb-padding-right">
                <div class="proposal-info-value"></div>
            </div>

            <div class="grid-3 proposal-info-name glb-no-border">Date:</div>
            <div class="grid-3 glb-padding-right">
                <div class="proposal-info-value"></div>
            </div>
        </div>
    </#if>
    <#-- end Signature Page -->

    <div class="proposal-footer grid-container">
        <div class="grid-4">
            <div class="glb-info">YOUR COMPANY</div>
            <div class="glb-info">1331 Hart Ridge Road, 48436 Gaines, MI</div>
        </div>
        <div class="grid-4">
            <div class="glb-info"><span>@</span><EMAIL></div>
            <div class="glb-info"><span>m</span>+*********** 3115</div>
        </div>
        <div class="grid-4">
            <div class="glb-info glb-align-right">
                The company is registered in the <br/>
                business register under no. 87650000
            </div>
        </div>
    </div>
</div>

</body>
</html>
