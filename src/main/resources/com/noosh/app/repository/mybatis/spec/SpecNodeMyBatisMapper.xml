<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.spec.SpecNodeMyBatisMapper">

    <resultMap id="specNodeResultMap" type="com.noosh.app.commons.dto.spec.SpecNodeDTO">
        <result property="id" column="SP_SPEC_NODE_ID"/>
        <result property="specId" column="SP_SPEC_ID"/>
        <result property="parentNodeId" column="PARENT_SP_SPEC_NODE_ID"/>
        <result property="rootNodeId" column="ROOT_SP_SPEC_NODE_ID"/>
        <result property="treeId" column="SP_SPEC_TREE_ID"/>
    </resultMap>

    <sql id="specNodeColumns">
        ${alias}.SP_SPEC_NODE_ID ${prefix}SP_SPEC_NODE_ID,
        ${alias}.SP_SPEC_ID ${prefix}SP_SPEC_ID,
        ${alias}.PARENT_SP_SPEC_NODE_ID ${prefix}PARENT_SP_SPEC_NODE_ID,
        ${alias}.ROOT_SP_SPEC_NODE_ID ${prefix}ROOT_SP_SPEC_NODE_ID,
        ${alias}.SP_SPEC_TREE_ID ${prefix}SP_SPEC_TREE_ID
    </sql>

</mapper>
