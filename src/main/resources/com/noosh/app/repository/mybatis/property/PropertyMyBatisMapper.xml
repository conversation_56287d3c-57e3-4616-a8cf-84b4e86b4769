<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper">

    <resultMap id="propertyTypeResultMap" type="com.noosh.app.commons.dto.property.PropertyTypeDTO">
        <result property="propertyTypeId" column = "PR_PROPERTY_TYPE_ID" />
        <result property="typeName" column = "TYPE_NAME" />
        <result property="description" column = "DESCRIPTION" />
        <result property="parentPropertyTypeId" column = "PARENT_PR_PROPERTY_TYPE_ID" />
    </resultMap>

    <resultMap id="propertyAttributeDtoResultMap" type="com.noosh.app.commons.dto.property.PropertyAttributeDTO">
        <result property="prPropertyAttributeId" column="pr_property_attribute_id" jdbcType="DECIMAL" />
        <result property="paramName" column="param_name" jdbcType="VARCHAR" />
        <result property="numberValue" column="number_value" jdbcType="DECIMAL" />
        <result property="stringValue" column="string_value" jdbcType="VARCHAR" />
        <result property="dateValue" column="date_value"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
    </resultMap>

    <select id="findTree" resultMap="propertyTypeResultMap">
        SELECT
            PT.PR_PROPERTY_TYPE_ID
            , PT.TYPE_NAME
            , PT.DESCRIPTION
            , PT.PARENT_PR_PROPERTY_TYPE_ID
        FROM PR_PROPERTY_TYPE PT
        START WITH PT.PR_PROPERTY_TYPE_ID = #{propertyTypeId}
        CONNECT BY PRIOR PT.PR_PROPERTY_TYPE_ID = PT.PARENT_PR_PROPERTY_TYPE_ID
    </select>

    <select id="findPropertyAttributes" resultMap="propertyAttributeDtoResultMap">
        SELECT
            pa.pr_property_id,
            pa.pr_property_attribute_id,
            pm.param_name,
            pm.pr_data_type_id,
            pa.number_value,
            pa.string_value,
            pa.date_value
        FROM
            pr_property_param pm,
            pr_property_attribute pa

        WHERE
            pm.PR_PROPERTY_PARAM_ID = pa.PR_PROPERTY_PARAM_ID
            AND pa.pr_property_id = #{propertyId}

    </select>

</mapper>