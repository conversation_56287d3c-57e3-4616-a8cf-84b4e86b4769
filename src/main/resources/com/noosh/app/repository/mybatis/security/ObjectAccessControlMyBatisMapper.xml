<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.security.ObjectAccessControlMyBatisMapper">


    <resultMap id="resultMap" type="com.noosh.app.commons.entity.security.ObjectAccessControl">
        <result property="id" column = "re_object_access_control_id" />
        <result property="accessIsAllowed" column = "ACCESS_IS_ALLOWED" />
    </resultMap>


    <select id="findObjectAccessControl" resultMap="resultMap">
        select
        re_object_access_control_id,
        access_is_allowed
        from re_object_access_control
        where
        re_access_context_id = #{permissionId}
        and (
        tm_team_id is null
        <if test="teamId != -1">
            OR tm_team_id = #{teamId}
        </if>
        )
        <if test="objectId != -1">
            and object_id = #{objectId}
        </if>
        <if test="objectId == -1">
            and object_id is null
        </if>
        and  (
        user_id = #{userId}
        <if test="groupId != -1">
            OR ac_workgroup_id = #{groupId}
        </if>
        <if test="roleId != -1">
            OR re_role_Id = #{roleId}
        </if>
        OR team_access = 1
        OR global_access = 1
        )
    </select>

</mapper>