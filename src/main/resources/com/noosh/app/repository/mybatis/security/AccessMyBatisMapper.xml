<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.security.AccessMyBatisMapper">

    <select id="hasAccessToProject" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END AS RESULT_VALUE FROM (
            select 1 as RESULT_VALUE
            from tm_team_member, tm_team_object
            where tm_team_object.object_id = #{projectId} and
                 tm_team_object.oc_object_class_id = 1000000 and
                 tm_team_member.tm_team_id = tm_team_object.tm_team_id and
                 tm_team_member.user_id = #{userId} and
                 tm_team_member.current_record = 1
            UNION
            select 1
            from pm_project, tm_team_member, tm_team_object, re_access_rule_rel, ac_account_user
            where tm_team_object.object_id = #{workgroupId} and
                 tm_team_object.oc_object_class_id = 1000100 and
                 tm_team_member.tm_team_id = tm_team_object.tm_team_id and
                 tm_team_member.user_id = #{userId} and
                 re_access_rule_rel.re_role_id = tm_team_member.re_role_id and
                 re_access_rule_rel.re_access_rule_id = 1001201 and
                 ac_account_user.user_id = #{userId} and
                 ac_account_user.oc_object_state_id = 2000056 and
                 pm_project.pm_project_id = #{projectId} and
                 pm_project.owner_ac_workgroup_id = #{workgroupId}
        ) X
    </select>

    <select id="hasAccessToWorkgroup" resultType="java.lang.Boolean">
        SELECT CASE WHEN COUNT(1) > 0
            THEN 1
               ELSE 0 END AS RESULT_VALUE
        FROM (
            SELECT 1 AS RESULT_VALUE
            FROM tm_team_member, tm_team_object, ac_account_user
            WHERE tm_team_object.object_id = #{workgroupId} AND
                  tm_team_object.oc_object_class_id = 1000100 AND
                  tm_team_member.tm_team_id = tm_team_object.tm_team_id AND
                  tm_team_member.user_id = #{userId} AND
                  ac_account_user.user_id = #{userId} AND
                  ac_account_user.oc_object_state_id = 2000056
        ) X
    </select>
    
</mapper>