<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.spec.SpecTypeMyBatisMapper">
    <resultMap id="specTypeMap" type="com.noosh.app.commons.dto.spec.SpecTypeDTO">
        <result property="id" column="SP_SPEC_TYPE_ID"/>
        <result property="labelStrId" column="LABEL_STR_ID"/>
        <result property="labelStr" column="LABEL_STR"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="templateBaseName" column="TEMPLATE_BASE_NAME"/>
    </resultMap>

    <select id="findSpecTypes" resultMap="specTypeMap">
        select
            T.SP_SPEC_TYPE_ID
            , T.LABEL_STR_ID
            , T.LABEL_STR
            , T.DESCRIPTION
            , T.TEMPLATE_BASE_NAME
        from SP_SPEC_TYPE T
            , SP_SPEC_REGISTRATION R
            , CO_SERVICE SER
        where
        T.CO_SERVICE_ID = SER.CO_SERVICE_ID
        and ( T.IS_DEPRECATED = 0 OR T.IS_DEPRECATED is null )
        and R.SP_SPEC_TYPE_ID = T.SP_SPEC_TYPE_ID
        and R.AC_WORKGROUP_ID = #{workgroupId}
        order by SER.CO_SERVICE_ID
    </select>

</mapper>