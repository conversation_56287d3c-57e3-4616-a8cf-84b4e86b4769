<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.quote.QuoteChartMyBatisMapper">

    <resultMap id="quoteChartResultMap" type="com.noosh.app.commons.vo.chart.QuoteChartItemVO">
        <result property="avgType" column="avg_type"/>
        <result property="quoteSentDays" column="each_quote_sent_days"/>
        <result property="quoteSentToSellOrderDays" column="each_quote_accept_days"/>
    </resultMap>

    <resultMap id="quoteConversionChartResultMap" type="com.noosh.app.commons.vo.chart.QuoteConversionChartItemVO">
        <result property="clientName" column="client_name"/>
        <result property="quoteSubmittedCount" column="created_quote_count"/>
        <result property="sellOrderAcceptedCount" column="accepted_order_quote_count"/>
    </resultMap>

    <select id="findTimeToQuote" resultMap="quoteChartResultMap">
 select case when fq.days is null then 0 else fq.days end AS each_quote_sent_days,
case when fq1.days is null then 0 else fq1.days end AS each_quote_accept_days,
ap.first_name || ' ' || ap.last_name AS avg_type
        from (SELECT count(ov.CREATE_USER_ID) totalNum, ov.CREATE_USER_ID
        from pc_quote ov WHERE ov.SUPPLIER_AC_WORKGROUP_ID = #{workgroupId}  and ov.submit_date &gt;= SYSDATE - #{filter}
        	GROUP BY ov.CREATE_USER_ID ORDER BY totalNum fetch  first 10 rows ONLY) bwx
        left join
        (
          select sum(delta)/count(CREATE_USER_ID) days, CREATE_USER_ID  from (
            select x.submit_date - x.project_create_date delta, x.CREATE_USER_ID
            from (
              select ov.pc_quote_id, ov.submit_date, ov.CREATE_USER_ID, pm.project_create_date
              from pc_quote ov
              inner join sy_containable c on c.object_id = ov.pc_quote_id and c.object_class_id = 1000013 and c.PARENT_OBJECT_CLASS_ID = 1000000
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.submit_date &gt;= SYSDATE - #{filter}
              and ov.submit_date is not null
            ) x
          ) created_quote
          GROUP BY CREATE_USER_ID
        ) fq
        on fq.CREATE_USER_ID = bwx.CREATE_USER_ID
        left join
        (
        SELECT CREATE_USER_ID, sum(delta)/count(CREATE_USER_ID) days FROM (
        select fqx.CREATE_USER_ID, oov.accept_date - fqx.quote_submit_date delta
          from (
            select x.pc_quote_id, x.submit_date quote_submit_date, x.CREATE_USER_ID
            from (
              select ov.pc_quote_id, ov.submit_date, ov.CREATE_USER_ID
              from pc_quote ov WHERE ov.SUPPLIER_AC_WORKGROUP_ID = #{workgroupId}
              and ov.submit_date &gt;= SYSDATE - #{filter}
              and ov.submit_date is not null
            ) x
          ) fqx inner join or_order_version oov on fqx.pc_quote_id = oov.pc_quote_id and oov.accept_date is not NULL) w
          GROUP BY CREATE_USER_ID
        ) fq1 on fq1.CREATE_USER_ID = bwx.CREATE_USER_ID
        INNER JOIN AC_ACCOUNT_USER aau ON aau.USER_ID = bwx.CREATE_USER_ID
        INNER JOIN AC_PERSON ap ON ap.AC_PERSON_ID = aau.AC_PERSON_ID
    </select>

    <select id="findTimeToSellOrder" resultMap="quoteChartResultMap">
        select bwx.name avg_type, case when fq.delta is null then 0 else fq.delta end days
        from (
        select cw.client_ac_workgroup_id, w.name
        from bu_client_workgroup cw, ac_workgroup w
        where cw.owner_ac_workgroup_id = #{workgroupId}
        and cw.client_ac_workgroup_id = w.ac_workgroup_id
        
        union 
        
        select w.ac_workgroup_id client_ac_workgroup_id, w.name
        from  ac_workgroup w
        where w.ac_workgroup_id = #{workgroupId}
        ) bwx
        left join
        (
          select fqx.buyer_ac_workgroup_id, oov.accept_date - fqx.quote_submit_date delta
          from (
            select x.pc_quote_id, x.submit_date quote_submit_date, x.buyer_ac_workgroup_id,
            row_number() over(partition by x.buyer_ac_workgroup_id order by x.submit_date asc) rn
            from (
              select ov.pc_quote_id, ov.submit_date, ov.buyer_ac_workgroup_id
              from pc_quote ov 
              inner join sy_containable c on c.object_id = ov.pc_quote_id and c.object_class_id = 1000013 and c.PARENT_OBJECT_CLASS_ID = 1000000 
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.submit_date is not null
            ) x 
          ) fqx inner join or_order_version oov on fqx.pc_quote_id = oov.pc_quote_id and oov.accept_date is not null
          where rn = 1
        ) fq
        on fq.buyer_ac_workgroup_id = bwx.client_ac_workgroup_id
    </select>

    <select id="findQuoteConversion" resultMap="quoteConversionChartResultMap">
        select 
        case when created_quote_count is null then 0 else created_quote_count end created_quote_count,
        case when accepted_order_quote_count is null then 0 else accepted_order_quote_count end accepted_order_quote_count, 
        bwx.name client_name
        from (
        select cw.client_ac_workgroup_id, w.name
        from bu_client_workgroup cw, ac_workgroup w
        where cw.owner_ac_workgroup_id = #{workgroupId}
        and cw.client_ac_workgroup_id = w.ac_workgroup_id
        and cw.IS_INACTIVE = 0
        
        union 
        
        select w.ac_workgroup_id client_ac_workgroup_id, w.name
        from  ac_workgroup w
        where w.ac_workgroup_id = #{workgroupId}
        ) bwx
        left join
        (
          select 
          count(ov.pc_quote_id) created_quote_count, ov.buyer_ac_workgroup_id
          from pc_quote ov 
          inner join sy_containable c on c.object_id = ov.pc_quote_id and c.object_class_id = 1000013 and c.PARENT_OBJECT_CLASS_ID = 1000000 
          inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
          where pm.owner_ac_workgroup_id = #{workgroupId}
          and ov.submit_date &gt;= SYSDATE - #{filter}
          group by ov.buyer_ac_workgroup_id
        
        ) created_quote
        on created_quote.buyer_ac_workgroup_id = bwx.client_ac_workgroup_id
        left join
        (
          select 
          count(ov.pc_quote_id) accepted_order_quote_count, ov.buyer_ac_workgroup_id
          from pc_quote ov 
          inner join sy_containable c on c.object_id = ov.pc_quote_id and c.object_class_id = 1000013 and c.PARENT_OBJECT_CLASS_ID = 1000000 
          inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
          inner join or_order_version oov on ov.pc_quote_id = oov.pc_quote_id and oov.accept_date is not null
          where pm.owner_ac_workgroup_id = #{workgroupId}
          and ov.submit_date &gt;= SYSDATE - #{filter}
          group by ov.buyer_ac_workgroup_id
        ) accepted_order 
        on created_quote.buyer_ac_workgroup_id = accepted_order.buyer_ac_workgroup_id 
    </select>

</mapper>