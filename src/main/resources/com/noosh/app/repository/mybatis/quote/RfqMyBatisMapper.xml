<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.quote.RfqMyBatisMapper">

    <resultMap id="rfqListResultMap" type="com.noosh.app.commons.dto.quote.RfqWidgetDTO">
        <result property="rfqId" column="PC_RFQ_ID"/>
        <result property="rfqTitle" column="TITLE"/>
        <result property="stateId" column="OC_OBJECT_STATE_ID"/>
        <result property="submittedDate" column="CREATE_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="dueDate" column="QUOTE_DUE_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="projectId" column="PARENT_OBJECT_ID"/>
        <result property="projectName" column="projectName"/>
        <result property="clientWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <association property="quoteCount" column="PC_RFQ_ID"
                     select="getRfqQuoteCount"
                     javaType="java.lang.Integer"/>
        <association property="clientWorkgroupName" column="BUYER_AC_WORKGROUP_ID"
                     select="getWorkgroupName"
                     javaType="java.lang.String"/>
        <association property="supplierWorkgroupName" column="SUPPLIER_AC_WORKGROUP_ID"
                     select="getWorkgroupName"
                     javaType="java.lang.String"/>

    </resultMap>

    <select id="getRfqQuoteCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM pc_quote
        WHERE pc_rfq_id = #{rfqId}
    </select>

    <select id="getWorkgroupName" resultType="java.lang.String">
        SELECT name
        FROM ac_workgroup
        WHERE ac_workgroup_id = #{workgroupId}
    </select>

    <select id="getAllRfqByProjectId" resultMap="rfqListResultMap">
        SELECT
            pq.TITLE,
            pq.BUYER_AC_WORKGROUP_ID,
            pq.CREATE_DATE,
            pq.QUOTE_DUE_DATE,
            pq.PC_RFQ_ID      AS pqRFQId,
            prs.OC_OBJECT_STATE_ID,
            prs.SUPPLIER_AC_WORKGROUP_ID,
            prs.PC_RFQ_SUPPLIER_ID,
            prs.PC_RFQ_ID,
            sc.PARENT_OBJECT_ID,
            sc.X_PARENT_TITLE AS projectName
        FROM pc_rfq pq, pc_rfq_supplier prs, sy_containable sc
        WHERE pq.PC_RFQ_ID = prs.PC_RFQ_ID
              AND prs.PC_RFQ_SUPPLIER_ID = sc.OBJECT_ID
              AND sc.OBJECT_CLASS_ID = 1000012
              AND sc.PARENT_OBJECT_CLASS_ID = 1000000
              AND sc.PARENT_OBJECT_ID = #{projectId}
          <if test="rfqStatusIdFilter != null and rfqStatusIdFilter.size() > 0">
              AND pq.OC_OBJECT_STATE_ID NOT IN
            <foreach item="item" index="index" collection="rfqStatusIdFilter" open="(" separator="," close=")">
                #{item}
            </foreach>
          </if>
        ORDER BY pq.create_date ASC

    </select>
</mapper>