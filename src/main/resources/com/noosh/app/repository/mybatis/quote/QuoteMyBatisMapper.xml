<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.quote.QuoteMyBatisMapper">

    <resultMap id="quoteListResultMap" type="com.noosh.app.commons.dto.quote.QuoteWidgetDTO">
        <result property="id" column="PC_QUOTE_ID"/>
        <result property="title" column="TITLE"/>
        <result property="stateId" column="OC_OBJECT_STATE_ID"/>
        <result property="clientWorkgroupName" column="CLIENT_WORKGROUP_NAME"/>
        <result property="supplierWorkgroupName" column="SUPPLIER_WORKGROUP_NAME"/>
        <result property="submitDate" column="SUBMIT_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="completionDate" column="COMPLETION_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="projectId" column="PM_PROJECT_ID"/>
        <result property="projectName" column="PROJECT_NAME"/>
        <result property="clientWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="rate" column="RATE"/>
        <result property="exCurrencyId" column="EX_CURRENCYID"/>
    </resultMap>

    <select id="getAllQuotesByProjectIdAndRrfqId" resultMap="quoteListResultMap">
        SELECT
        Q.*,
        CONT.X_PARENT_TITLE PROJECT_NAME,
        CONT.PARENT_OBJECT_ID PM_PROJECT_ID,
        WB.NAME CLIENT_WORKGROUP_NAME,
        WS.NAME SUPPLIER_WORKGROUP_NAME
        FROM PC_QUOTE Q , SY_CONTAINABLE CONT, AC_WORKGROUP WB, AC_WORKGROUP WS
        where Q.PC_QUOTE_ID = CONT.OBJECT_ID
        AND Q.OC_OBJECT_CLASS_ID = 1000013
        AND CONT.OBJECT_CLASS_ID = 1000013
        AND CONT.PARENT_OBJECT_CLASS_ID = 1000000
        AND CONT.PARENT_OBJECT_ID = #{projectId}
        AND Q.BUYER_AC_WORKGROUP_ID = WB.AC_WORKGROUP_ID
        AND Q.SUPPLIER_AC_WORKGROUP_ID = WS.AC_WORKGROUP_ID
        <if test="rfqId != null">
            AND Q.PC_RFQ_ID = #{rfqId}
        </if>
        <if test="rfqId == null">
            AND Q.PC_RFQ_ID IS NULL
        </if>
        <if test="quoteStatusIdFilter != null and quoteStatusIdFilter.size() > 0">
            AND Q.OC_OBJECT_STATE_ID NOT IN
            <foreach item="item" index="index" collection="quoteStatusIdFilter" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY Q.CREATE_DATE ASC

    </select>
</mapper>