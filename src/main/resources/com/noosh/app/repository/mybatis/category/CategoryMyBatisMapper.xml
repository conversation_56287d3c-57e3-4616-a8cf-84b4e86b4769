<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.category.CategoryMyBatisMapper">

    <resultMap id="categoryResultMap" type="com.noosh.app.commons.dto.proposal.CategoryDTO">
        <result property="categoryId" column="CO_CATEGORY_ID" jdbcType="NUMERIC" />
        <result property="name" column="NAME" jdbcType="VARCHAR" />
        <result property="description" column="NAME" jdbcType="VARCHAR" />
        <result property="objectId" column="OBJECT_ID" jdbcType="NUMERIC" />
    </resultMap>

    <select id="findCategoryByObjects" resultMap="categoryResultMap">
        SELECT C.CO_CATEGORY_ID,
            C.NAME,
            C.DESCRIPTION,
            CO.OBJECT_ID
        FROM CO_CATEGORY C,
            CO_CATEGORY_OBJECT CO,
            CO_CATEGORY_CLASS CC
        WHERE CO.OC_OBJECT_CLASS_ID = 1000018
        AND CO.OBJECT_ID IN
        <foreach collection="proposalItemIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND CO.CO_CATEGORY_ID = C.CO_CATEGORY_ID
        AND (C.USER_ID =#{userId} OR C.AC_WORKGROUP_ID = #{workgroupId})
        AND C.CO_CATEGORY_ID = CC.CO_CATEGORY_ID
        AND CO.OC_OBJECT_CLASS_ID = CC.OC_OBJECT_CLASS_ID
        ORDER BY CC.ORDINAL_NUMBER
    </select>

</mapper>