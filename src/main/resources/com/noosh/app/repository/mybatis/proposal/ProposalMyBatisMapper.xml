<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.proposal.ProposalMyBatisMapper">

    <resultMap id="logoResultMap" type="com.noosh.app.commons.dto.proposal.WorkgroupLogoDTO">
        <result property="logoId" column = "AC_WG_LOGO_ID" />
        <result property="name" column = "NAME" />
        <result property="width" column = "WIDTH" />
        <result property="height" column = "HEIGHT" />
        <result property="resourcePath" column = "RESOURCE_PATH" />
        <result property="createdDate" column = "CREATE_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="lastUpdatedDate" column = "MOD_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="createdFirstName" column = "CREATED_FIRST_NAME" />
        <result property="createdMiddleName" column = "CREATED_MIDDLE_NAME" />
        <result property="createdLastName" column = "CREATED_LAST_NAME" />
        <result property="updatedFirstName" column = "UPDATED_FIRST_NAME" />
        <result property="updatedMiddleName" column = "UPDATED_MIDDLE_NAME" />
        <result property="updatedLastName" column = "UPDATED_LAST_NAME" />
    </resultMap>

    <resultMap id="categorieResultMap" type="com.noosh.app.commons.dto.proposal.CategoryDTO">
        <result property="categoryId" column = "CO_CATEGORY_ID" />
        <result property="name" column = "NAME" />
        <result property="description" column = "DESCRIPTION" />
        <result property="categoryClassId" column = "CO_CATEGORY_CLASS_ID" />
    </resultMap>

    <resultMap id="templateResultMap" type="com.noosh.app.commons.dto.proposal.ProposalTemplateDTO">
        <result property="templateId" column = "PC_PROPOSAL_TEMPLATE_ID" />
        <result property="name" column = "NAME" />
        <result property="createdDate" column = "CREATE_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="lastUpdatedDate" column = "MOD_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="isDefault" column = "IS_DEFAULT" />
        <result property="createdFirstName" column = "CREATED_FIRST_NAME" />
        <result property="createdMiddleName" column = "CREATED_MIDDLE_NAME" />
        <result property="createdLastName" column = "CREATED_LAST_NAME" />
        <result property="updatedFirstName" column = "UPDATED_FIRST_NAME" />
        <result property="updatedMiddleName" column = "UPDATED_MIDDLE_NAME" />
        <result property="updatedLastName" column = "UPDATED_LAST_NAME" />
    </resultMap>

    <resultMap id="specSummaryResultMap" type="com.noosh.app.commons.dto.proposal.SpecSummaryDTO">
        <result property="specSummaryId" column = "PC_PROPOSAL_SPEC_SUMMARY_ID" />
        <result property="name" column = "NAME" />
        <result property="iconUrl" column = "ICON_URL" />
        <result property="specTypeId" column = "SP_SPEC_TYPE_ID" />
        <result property="specTypeLabelStrId" column = "LABEL_STR_ID" />
        <result property="specTypeLabelStr" column = "LABEL_STR" />
        <result property="isDefault" column = "IS_DEFAULT" />
    </resultMap>

    <resultMap id="proposalResultMap" type="com.noosh.app.commons.dto.proposal.ProposalDTO">
        <id property="proposalId" column = "PC_PROPOSAL_ID" />
        <result property="quoteId" column = "PC_QUOTE_ID" />
        <result property="title" column = "TITLE" />
        <result property="reference" column = "REFERENCE" />
        <result property="ownerReference" column = "OWNER_REFERENCE" />
        <result property="prepareDate" column = "PREPARE_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="respondByDate" column = "RESPOND_BY_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="expirationDate" column = "EXPIRATION_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="completionDate" column = "COMPLETION_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="saluteText" column = "SALUTE_TEXT" />
        <result property="introText" column = "INTRO_TEXT" />
        <result property="conclusionText" column = "CONCLUSION_TEXT" />
        <result property="closingText" column = "CLOSING_TEXT" />
        <result property="isMarkupVisible" column = "IS_MARKUP_VISIBLE" />
        <result property="outsourcerName" column = "OUTSOURCER_NAME" />
        <result property="outsourcerAddressId" column = "OUTSOURCER_AC_ADDRESS_ID" />
        <result property="outsourcerPhone" column = "OUTSOURCER_PHONE" />
        <result property="outsourcerFax" column = "OUTSOURCER_FAX" />
        <result property="outsourcerUrl" column = "OUTSOURCER_URL" />
        <result property="clientName" column = "CLIENT_NAME" />
        <result property="clientAddressId" column = "CLIENT_AC_ADDRESS_ID" />
        <result property="clientPhone" column = "CLIENT_PHONE" />
        <result property="clientFax" column = "CLIENT_FAX" />
        <result property="clientUrl" column = "CLIENT_URL" />
        <result property="termsId" column = "AC_TERMS_ID" />
        <result property="objectStateId" column = "OC_OBJECT_STATE_ID" />
        <result property="stateChangeComment" column = "STATE_CHANGE_COMMENT" />
        <result property="propertyId" column = "CUSTOM_PR_PROPERTY_ID" />
        <result property="serviceId" column = "CO_SERVICE_ID" />
        <result property="preparedUserId" column = "PREPARED_USER_ID" />
        <result property="isAggregationEnable" column = "IS_AGGREGATION_ENABLE" />
        <result property="preparedName" column = "PREPARED_NAME" />
        <result property="preparedTitle" column = "PREPARED_TITLE" />
        <result property="logoId" column = "AC_WG_LOGO_ID" />
        <result property="useSpecSummary" column = "USE_SPEC_SUMMARY" />
        <result property="isSpecSummaryCompact" column = "IS_SPEC_SUMMARY_COMPACT" />
        <result property="includeCoverPage" column = "INCLUDE_COVER_PAGE" />
        <result property="includeCoverLetter" column = "INCLUDE_COVER_LETTER" />
        <result property="includeTermsConditions" column = "INCLUDE_TERMS_CONDITIONS" />
        <result property="includeSignaturePage" column = "INCLUDE_SIGNATURE_PAGE" />
        <result property="includePageNumber" column = "INCLUDE_PAGE_NUMBER" />
        <result property="layout" column = "LAYOUT" />
        <result property="includePriceBreakouts" column = "INCLUDE_PRICE_BREAKOUTS" />
        <result property="isLandscape" column = "IS_LANDSCAPE" />
        <result property="useSpecSummaryBorder" column = "USE_SPEC_SUMMARY_BORDER" />
        <result property="specSummaryNumColumns" column = "SPEC_SUMMARY_NUM_COLUMNS" />
        <result property="includeProposalNote" column = "INCLUDE_PROPOSAL_NOTE" />
        <result property="proposalNote" column = "PROPOSAL_NOTE" />
        <result property="isSeparateSpecPricing" column = "IS_SEPARATE_SPEC_PRICING" />
        <association property="logo" javaType="com.noosh.app.commons.dto.proposal.WorkgroupLogoDTO" >
            <id property="logoId" column = "AC_WG_LOGO_ID" />
            <result property="name" column = "NAME" />
            <result property="userFilename" column = "USER_FILENAME" />
            <result property="resourcePath" column = "RESOURCE_PATH" />
            <result property="width" column = "WIDTH" />
            <result property="height" column = "HEIGHT" />
            <result property="isActive" column = "IS_ACTIVE" />
        </association>
        <association property="clientAddress" javaType="com.noosh.app.commons.dto.address.AddressDTO" >
            <id property="addressId" column = "CLIENT_AC_ADDRESS_ID" />
            <result property="description" column = "CLIENT_DESCRIPTION" />
            <result property="line1" column = "CLIENT_LINE_1" />
            <result property="line2" column = "CLIENT_LINE_2" />
            <result property="line3" column = "CLIENT_LINE_3" />
            <result property="city" column = "CLIENT_CITY" />
            <result property="state" column = "CLIENT_STATE" />
            <result property="postal" column = "CLIENT_POSTAL" />
            <result property="countryId" column = "CLIENT_AC_COUNTRY_ID" />
            <result property="countryNameStrId" column = "CLIENT_COUNTRY_NAME_STR_ID" />
        </association>
        <association property="outsourcerAddress" javaType="com.noosh.app.commons.dto.address.AddressDTO" >
            <id property="addressId" column = "OUTSOURCER_AC_ADDRESS_ID" />
            <result property="description" column = "OUTSOURCER_DESCRIPTION" />
            <result property="line1" column = "OUTSOURCER_LINE_1" />
            <result property="line2" column = "OUTSOURCER_LINE_2" />
            <result property="line3" column = "OUTSOURCER_LINE_3" />
            <result property="city" column = "OUTSOURCER_CITY" />
            <result property="state" column = "OUTSOURCER_STATE" />
            <result property="postal" column = "OUTSOURCER_POSTAL" />
            <result property="countryId" column = "OUTSOURCER_AC_COUNTRY_ID" />
            <result property="countryNameStrId" column = "OUTSOURCER_COUNTRY_NAME_STR_ID" />
        </association>
    </resultMap>

    <resultMap id="proposalItemResultMap" type="com.noosh.app.commons.dto.proposal.ProposalItemDTO">
        <result property="proposalItemId" column = "PC_PROPOSAL_ITEM_ID" />
        <result property="quoteItemId" column = "PC_QUOTE_ITEM_ID" />
        <result property="itemIndex" column = "ITEM_INDEX" />
        <result property="comments" column = "COMMENTS" />
        <result property="customPropertyId" column = "CUSTOM_PR_PROPERTY_ID" />
        <result property="proposalSpecSummaryId" column = "PC_PROPOSAL_SPEC_SUMMARY_ID" />
        <result property="isShowAllSpecInfo" column = "IS_A_S_INFO" />
        <result property="quoteId" column = "PC_QUOTE_ID" />
        <result property="specId" column = "SP_SPEC_ID" />
        <result property="jobId" column = "PC_JOB_ID" />
        <result property="specName" column = "SPEC_NAME" />
        <result property="versionNumber" column = "VERSION_NUMBER" />
        <result property="refNumber" column = "REF_NUMBER" />
        <result property="sku" column = "SKU" />
        <result property="quantity" column = "QUANTITY" />
    </resultMap>

    <select id="findCategories" resultMap="categorieResultMap">
        SELECT C.CO_CATEGORY_ID
            , C.NAME
            , C.DESCRIPTION
            , CC.CO_CATEGORY_CLASS_ID
        FROM CO_CATEGORY C,
             CO_CATEGORY_CLASS CC
        WHERE ((C.USER_ID = #{userId} AND C.AC_WORKGROUP_ID IS NULL)
            OR (C.AC_WORKGROUP_ID = #{workgroupId}))
            AND C.CO_CATEGORY_ID = CC.CO_CATEGORY_ID
            AND CC.OC_OBJECT_CLASS_ID  = 1000018
            AND C.IS_DISABLED = 0
            ORDER BY CC.ORDINAL_NUMBER
    </select>

    <select id="findLogos" resultMap="logoResultMap">
        SELECT L.AC_WG_LOGO_ID
            , L.NAME
            , L.WIDTH
            , L.HEIGHT
            , L.CREATE_DATE
            , L.MOD_DATE
            , L.RESOURCE_PATH
            , CP.FIRST_NAME CREATED_FIRST_NAME
            , CP.MIDDLE_NAME CREATED_MIDDLE_NAME
            , CP.LAST_NAME CREATED_LAST_NAME
            , MP.FIRST_NAME UPDATED_FIRST_NAME
            , MP.MIDDLE_NAME UPDATED_MIDDLE_NAME
            , MP.LAST_NAME UPDATED_LAST_NAME
        FROM AC_WG_LOGO L,
            AC_ACCOUNT_USER CU,
            AC_PERSON CP,
            AC_ACCOUNT_USER MU,
            AC_PERSON MP
        WHERE L.AC_WORKGROUP_ID = #{workgroupId}
        AND L.IS_ACTIVE = 1
        AND L.CREATE_USER_ID = CU.USER_ID
        AND CU.AC_PERSON_ID = CP.AC_PERSON_ID
        AND L.MOD_USER_ID = MU.USER_ID (+)
        AND MU.AC_PERSON_ID = MP.AC_PERSON_ID (+)
        ORDER BY L.NAME
    </select>

    <select id="findTemplates" resultMap="templateResultMap">
        SELECT T.PC_PROPOSAL_TEMPLATE_ID
            , T.NAME
            , T.CREATE_DATE
            , T.MOD_DATE
            , T.IS_DEFAULT
            , L.RESOURCE_PATH
            , CP.FIRST_NAME CREATED_FIRST_NAME
            , CP.MIDDLE_NAME CREATED_MIDDLE_NAME
            , CP.LAST_NAME CREATED_LAST_NAME
            , MP.FIRST_NAME UPDATED_FIRST_NAME
            , MP.MIDDLE_NAME UPDATED_MIDDLE_NAME
            , MP.LAST_NAME UPDATED_LAST_NAME
        FROM PC_PROPOSAL_TEMPLATE T,
            AC_WG_LOGO L,
            AC_ACCOUNT_USER CU,
            AC_PERSON CP,
            AC_ACCOUNT_USER MU,
            AC_PERSON MP
        WHERE T.AC_WORKGROUP_ID = #{workgroupId}
        AND T.AC_WG_LOGO_ID = L.AC_WG_LOGO_ID (+)
        AND T.CREATE_USER_ID = CU.USER_ID
        AND CU.AC_PERSON_ID = CP.AC_PERSON_ID
        AND T.MOD_USER_ID = MU.USER_ID (+)
        AND MU.AC_PERSON_ID = MP.AC_PERSON_ID (+)
        ORDER BY T.NAME
    </select>

    <select id="findSpecSummary" resultMap="specSummaryResultMap">
        SELECT
            P.PC_PROPOSAL_SPEC_SUMMARY_ID
            , P.NAME
            , P.IS_DEFAULT
            , ST.SP_SPEC_TYPE_ID
            , ST.LABEL_STR_ID
            , ST.LABEL_STR
            , ST.ICON_URL
        FROM PC_PROPOSAL_SPEC_SUMMARY P,
            SP_SPEC_TYPE ST,
            PR_PROPERTY_TYPE PT
        WHERE P.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        AND P.SP_SPEC_TYPE_ID = ST.SP_SPEC_TYPE_ID
        AND P.PR_PROPERTY_TYPE_ID = PT.PR_PROPERTY_TYPE_ID
        AND (P.IS_DISABLED = 0 OR P.IS_DISABLED is null )
    </select>

    <select id="findDefaultSpecSummary" resultMap="specSummaryResultMap">
        SELECT
            S.PC_PROPOSAL_SPEC_SUMMARY_ID
            , S.SP_SPEC_TYPE_ID
            , S.NAME
        FROM PC_PROPOSAL_SPEC_SUMMARY S,
             PR_PROPERTY_TYPE T
        WHERE S.SP_SPEC_TYPE_ID  = #{specTypeId}
            AND S.OWNER_AC_WORKGROUP_ID = #{workgroupId}
            AND S.PR_PROPERTY_TYPE_ID = T.PR_PROPERTY_TYPE_ID
            AND S.IS_DEFAULT = 1
            AND (S.IS_DISABLED = 0 OR S.IS_DISABLED is null)
    </select>

    <select id="findProposal" resultMap="proposalResultMap">
        SELECT
            P.PC_PROPOSAL_ID
            , P.PC_QUOTE_ID
            , P.TITLE
            , P.REFERENCE
            , P.OWNER_REFERENCE
            , P.PREPARE_DATE
            , P.RESPOND_BY_DATE
            , P.EXPIRATION_DATE
            , P.COMPLETION_DATE
            , P.SALUTE_TEXT
            , P.INTRO_TEXT
            , P.CONCLUSION_TEXT
            , P.CLOSING_TEXT
            , P.IS_MARKUP_VISIBLE
            , P.TAX
            , P.TAX_AC_CURRENCY_ID
            , P.SHIPPING
            , P.SHIPPING_AC_CURRENCY_ID
            , P.OUTSOURCER_NAME
            , P.OUTSOURCER_AC_ADDRESS_ID
            , P.OUTSOURCER_PHONE
            , P.OUTSOURCER_FAX
            , P.OUTSOURCER_URL
            , P.CLIENT_NAME
            , P.CLIENT_AC_ADDRESS_ID
            , P.CLIENT_PHONE
            , P.CLIENT_FAX
            , P.CLIENT_URL
            , P.AC_TERMS_ID
            , P.OC_OBJECT_STATE_ID
            , P.STATE_CHANGE_COMMENT
            , P.CUSTOM_PR_PROPERTY_ID
            , P.CO_SERVICE_ID
            , P.PREPARED_USER_ID
            , P.IS_AGGREGATION_ENABLE
            , P.PREPARED_NAME
            , P.PREPARED_TITLE
            , P.AC_WG_LOGO_ID
            , P.USE_SPEC_SUMMARY
            , P.IS_SPEC_SUMMARY_COMPACT
            , P.INCLUDE_COVER_PAGE
            , P.INCLUDE_COVER_LETTER
            , P.INCLUDE_TERMS_CONDITIONS
            , P.INCLUDE_SIGNATURE_PAGE
            , P.INCLUDE_PAGE_NUMBER
            , P.LAYOUT
            , P.INCLUDE_PRICE_BREAKOUTS
            , P.IS_LANDSCAPE
            , P.USE_SPEC_SUMMARY_BORDER
            , P.SPEC_SUMMARY_NUM_COLUMNS
            , P.INCLUDE_PROPOSAL_NOTE
            , P.PROPOSAL_NOTE
            , P.IS_SEPARATE_SPEC_PRICING

            , L.NAME
            , L.USER_FILENAME
            , L.RESOURCE_PATH
            , L.WIDTH
            , L.HEIGHT
            , L.IS_ACTIVE

            , CA.DESCRIPTION CLIENT_DESCRIPTION
            , CA.LINE_1 CLIENT_LINE_1
            , CA.LINE_2 CLIENT_LINE_2
            , CA.LINE_3 CLIENT_LINE_3
            , CA.CITY CLIENT_CITY
            , CA.STATE CLIENT_STATE
            , CA.POSTAL CLIENT_POSTAL
            , CA.AC_COUNTRY_ID CLIENT_AC_COUNTRY_ID
            , CC.NAME_STR_ID CLIENT_COUNTRY_NAME_STR_ID

            , OA.DESCRIPTION OUTSOURCER_DESCRIPTION
            , OA.LINE_1 OUTSOURCER_LINE_1
            , OA.LINE_2 OUTSOURCER_LINE_2
            , OA.LINE_3 OUTSOURCER_LINE_3
            , OA.CITY OUTSOURCER_CITY
            , OA.STATE OUTSOURCER_STATE
            , OA.POSTAL OUTSOURCER_POSTAL
            , OA.AC_COUNTRY_ID OUTSOURCER_AC_COUNTRY_ID
            , OC.NAME_STR_ID OUTSOURCER_COUNTRY_NAME_STR_ID
        FROM PC_PROPOSAL P,
            AC_WG_LOGO L,
            AC_ADDRESS CA,
            AC_ADDRESS OA,
            AC_COUNTRY CC,
            AC_COUNTRY OC
        WHERE EXISTS (
            SELECT 'x'
            FROM SY_CONTAINABLE CONT
            WHERE CONT.OBJECT_CLASS_ID = 1000017
            AND CONT.OBJECT_ID = P.PC_PROPOSAL_ID
            AND CONT.PARENT_OBJECT_ID = #{projectId}
            AND CONT.PARENT_OBJECT_CLASS_ID = 1000000
        )
        AND P.PC_PROPOSAL_ID = #{proposalId}
        AND P.AC_WG_LOGO_ID = L.AC_WG_LOGO_ID(+)
        AND P.CLIENT_AC_ADDRESS_ID = CA.AC_ADDRESS_ID
        AND CA.AC_COUNTRY_ID = CC.AC_COUNTRY_ID
        AND P.OUTSOURCER_AC_ADDRESS_ID = OA.AC_ADDRESS_ID
        AND OA.AC_COUNTRY_ID = OC.AC_COUNTRY_ID
    </select>

    <select id="findProposalItems" resultMap="proposalItemResultMap">
        SELECT
            PPI.PC_PROPOSAL_ITEM_ID
            , PPI.PC_QUOTE_ITEM_ID
            , PPI.ITEM_INDEX
            , PPI.COMMENTS
            , PPI.CUSTOM_PR_PROPERTY_ID
            , PPI.PC_PROPOSAL_SPEC_SUMMARY_ID
            , PPI.IS_A_S_INFO
            , PQI.PC_QUOTE_ID
            , PQI.SP_SPEC_ID
            , PQI.PC_JOB_ID
            , SPEC.SPEC_NAME
            , SPEC.VERSION_NUMBER
            , RFE.REF_NUMBER
            , RFE.SKU
            , PQP.QUANTITY
        FROM PC_PROPOSAL_ITEM PPI,
            PC_QUOTE_ITEM PQI,
            PC_QUOTE_PIRCE PQP,
            SP_SPEC SPEC,
            SP_SPEC_REFERENCE RFE
        WHERE
        PPI.PC_QUOTE_ITEM_ID = PQI.PC_QUOTE_ITEM_ID
        AND PQI.CHOSEN_PC_QUOTE_PRICE_ID = PQP.PC_QUOTE_PRICE_ID
        AND PQI.SP_SPEC_ID = SPEC.SP_SPEC_ID
        AND SPEC.SP_SPEC_REFERENCE_ID = REF.SP_SPEC_REFERENCE_ID
        AND PPI.PC_PROPOSAL_ID = #{proposalId}
    </select>
    
    <select id="findProposalItemSpecSummaryFields"
            resultType="com.noosh.app.commons.dto.proposal.SpecSummaryFieldValueDTO">
        SELECT ppp.PARAM_NAME paramName, ppa.STRING_VALUE stringValue
        FROM SP_SPEC ss, PR_PROPERTY_ATTRIBUTE ppa, PR_PROPERTY_PARAM ppp, PC_PROPOSAL_SPEC_FIELD ppsf, PC_PROPOSAL_ITEM ppi, PC_QUOTE_ITEM pqi
        WHERE ss.PR_PROPERTY_ID = ppa.PR_PROPERTY_ID
          AND ppa.PR_PROPERTY_PARAM_ID = ppp.PR_PROPERTY_PARAM_ID
          AND ppsf.PR_PROPERTY_PARAM_ID = ppp.PR_PROPERTY_PARAM_ID
          AND ss.SP_SPEC_ID = PQI.SP_SPEC_ID
          AND ppsf.PC_PROPOSAL_SPEC_SUM_ITEM_ID = ppi.PC_PROPOSAL_SPEC_SUMMARY_ID
          AND pqi.PC_QUOTE_ITEM_ID = ppi.PC_QUOTE_ITEM_ID
          AND ppi.PC_PROPOSAL_ITEM_ID = #{proposalItemId}
    </select>

    <select id="findShipTos" resultType="com.noosh.app.commons.dto.spec.ShipmentWidgetDTO">
        SELECT
            sr.QUANTITY,
            sr.FIRST_NAME firstName, sr.LAST_NAME lastName,
            sr.COMPANY_NAME companyName,
            aa.LINE_1 line1, aa.LINE_2 line2, aa.LINE_3 line3,
            aa.CITY, aa.STATE, aa.POSTAL
        FROM SH_SHIPMENT ss, SH_REQUEST sr, AC_ADDRESS aa
        WHERE
            sr.AC_ADDRESS_ID = aa.AC_ADDRESS_ID
          AND ss.SH_SHIPMENT_ID = sr.SH_SHIPMENT_ID
          AND ss.PC_JOB_ID = #{jobId}
    </select>

    <select id="findPropertyIdByEstimateItemPriceId" resultType="java.lang.Long">
        SELECT eei.CUSTOM_PR_PROPERTY_ID
        FROM EM_ESTIMATE_ITEM eei, EM_ESTIMATE_ITEM_PRICE eeip
        WHERE
            eei.EM_ESTIMATE_ITEM_ID = eeip.EM_ESTIMATE_ITEM_ID
          AND EM_ESTIMATE_ITEM_PRICE_ID= #{estimateItemPriceId}
    </select>

    <select id="findPropertyIdByOrderItemId" resultType="java.lang.Long">
        SELECT ooi.CUSTOM_PR_PROPERTY_ID
        FROM OR_ORDER_ITEM ooi
        WHERE ooi.OR_ORDER_ITEM_ID = #{orderItemId}
    </select>
</mapper>