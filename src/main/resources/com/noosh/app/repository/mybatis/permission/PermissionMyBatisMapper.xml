<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.permission.PermissionMyBatisMapper">


    <resultMap id="permissionInfoResultMap" type="com.noosh.app.commons.dto.permission.PermissionWidgetDTO">
        <result property="permissionId" column = "PERMISSION_ID" />
        <result property="parentObjectClassId" column = "PARENT_OBJECT_CLASS_ID" />
        <result property="accessIsAllowed" column = "ACCESS_IS_ALLOWED" />
        <result property="accessIsPartial" column = "ACCESS_IS_PARTIAL" />
    </resultMap>


    <select id="findPermissionInfo" resultMap="permissionInfoResultMap">
        select
          a.main_oc_object_class_id as parent_object_class_id,
          a.re_access_context_id as PERMISSION_ID,
          b.access_is_allowed,
          b.access_is_partial,
          a.permission as PERMISSION_NAME
        from
          (
            select distinct
              oc_object_class_rel.main_oc_object_class_id,
              re_access_context.re_access_context_id,
              re_access_context.constant_token as permission
            from re_access_context, oc_object_class_rel
            where re_access_context.oc_object_class_id = oc_object_class_rel.dependent_oc_object_class_id
              and oc_object_class_rel.main_oc_object_class_id in
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
          ) a,
          (
            select
              re_access_rule.re_access_context_id,
              re_access_rule.access_is_allowed,
              re_access_rule.access_is_partial
            from re_access_rule, re_access_rule_rel
            where re_role_id = #{roleId}
              and re_access_rule.re_access_rule_id  = re_access_rule_rel.re_access_rule_id
          ) b
        where
        a.re_access_context_id  = b.re_access_context_id (+)
    </select>

</mapper>