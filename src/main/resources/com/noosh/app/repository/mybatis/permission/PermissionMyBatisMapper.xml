<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.permission.PermissionMyBatisMapper">


    <resultMap id="permissionInfoResultMap" type="com.noosh.app.commons.dto.permission.PermissionWidgetDTO">
        <result property="permissionId" column = "PERMISSION_ID" />
        <result property="parentObjectClassId" column = "PARENT_OBJECT_CLASS_ID" />
        <result property="accessIsAllowed" column = "ACCESS_IS_ALLOWED" />
        <result property="accessIsPartial" column = "ACCESS_IS_PARTIAL" />
    </resultMap>

    <resultMap id="simplePermissionInfoResultMap" type="com.noosh.app.commons.dto.permission.PermissionWidgetDTO">
        <result property="permissionId" column = "PERMISSION_ID" />
        <result property="parentObjectClassId" column = "PARENT_OBJECT_CLASS_ID" />
        <result property="accessIsAllowed" column = "ACCESS_IS_ALLOWED" />
        <result property="accessIsPartial" column = "ACCESS_IS_PARTIAL" />
        <result property="workgroupId" column = "AC_WORKGROUP_ID" />
        <result property="teamId" column = "TM_TEAM_ID" />
        <result property="roleId" column = "RE_ROLE_ID" />
        <result property="objectId" column = "object_id" />
    </resultMap>



    <select id="findPermissionInfo" resultMap="permissionInfoResultMap">
        select
          a.main_oc_object_class_id as parent_object_class_id,
          a.re_access_context_id as PERMISSION_ID,
          b.access_is_allowed,
          b.access_is_partial,
          a.permission as PERMISSION_NAME
        from
          (
            select distinct
              oc_object_class_rel.main_oc_object_class_id,
              re_access_context.re_access_context_id,
              re_access_context.constant_token as permission
            from re_access_context, oc_object_class_rel
            where re_access_context.oc_object_class_id = oc_object_class_rel.dependent_oc_object_class_id
              and oc_object_class_rel.main_oc_object_class_id in
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
          ) a,
          (
            select
              re_access_rule.re_access_context_id,
              re_access_rule.access_is_allowed,
              re_access_rule.access_is_partial
            from re_access_rule, re_access_rule_rel
            where re_role_id = #{roleId}
              and re_access_rule.re_access_rule_id  = re_access_rule_rel.re_access_rule_id
          ) b
        where
        a.re_access_context_id  = b.re_access_context_id (+)
    </select>

    <select id="findForWorkgroup" resultMap="simplePermissionInfoResultMap">

        SELECT
            a.main_oc_object_class_id AS parent_object_class_id,
            a.re_access_context_id    AS PERMISSION_ID,
            b.access_is_allowed,
            b.access_is_partial,
            a.ac_workgroup_id,
            a.tm_team_id,
            a.re_role_id,
            a.object_id
        FROM
            (
                SELECT DISTINCT
                    oc_object_class_rel.main_oc_object_class_id,
                    re_access_context.re_access_context_id,
                    TM_TEAM.ac_workgroup_id,
                    TM_TEAM_MEMBER.tm_team_id,
                    TM_TEAM_MEMBER.re_role_id,
                    TM_TEAM_OBJECT.object_id
                FROM re_access_context, oc_object_class_rel, re_access_rule, re_access_rule_rel,
                    TM_TEAM_MEMBER, TM_TEAM_OBJECT, TM_TEAM
                WHERE re_access_context.oc_object_class_id = oc_object_class_rel.dependent_oc_object_class_id
                  AND re_access_rule.re_access_rule_id = re_access_rule_rel.re_access_rule_id
                  AND re_access_rule_rel.re_role_id = TM_TEAM_MEMBER.re_role_id
                  AND TM_TEAM_MEMBER.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                  AND TM_TEAM.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                  AND TM_TEAM_OBJECT.object_id = #{workgroupId}
                  AND TM_TEAM_OBJECT.oc_object_class_id = 1000100
                  AND TM_TEAM_MEMBER.current_record = 1
                  AND TM_TEAM_MEMBER.user_id = #{userId}
                  AND oc_object_class_rel.main_oc_object_class_id IN (1000098, 1000103, 2500000)
                  AND re_access_context.re_access_context_id = #{permissionId}
            ) a
            LEFT JOIN
            (
                SELECT
                    re_access_rule.re_access_context_id,
                    re_access_rule.access_is_allowed,
                    re_access_rule.access_is_partial
                FROM re_access_rule, re_access_rule_rel, TM_TEAM_MEMBER, TM_TEAM_OBJECT
                WHERE
                    re_access_rule.re_access_rule_id = re_access_rule_rel.re_access_rule_id
                    AND re_access_rule_rel.re_role_id = TM_TEAM_MEMBER.re_role_id
                    AND TM_TEAM_MEMBER.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                    AND TM_TEAM_OBJECT.object_id = #{workgroupId}
                    AND TM_TEAM_OBJECT.oc_object_class_id = 1000100
                    AND TM_TEAM_MEMBER.current_record = 1
                    AND TM_TEAM_MEMBER.user_id = #{userId}
                    AND re_access_context_id = #{permissionId}
            ) b
                ON a.re_access_context_id = b.re_access_context_id

    </select>

    <select id="findForProject" resultMap="simplePermissionInfoResultMap">

        SELECT
            a.main_oc_object_class_id AS parent_object_class_id,
            a.re_access_context_id    AS PERMISSION_ID,
            b.access_is_allowed,
            b.access_is_partial,
            a.ac_workgroup_id,
            a.tm_team_id,
            a.re_role_id,
            a.object_id
        FROM
            (
                SELECT DISTINCT
                    oc_object_class_rel.main_oc_object_class_id,
                    re_access_context.re_access_context_id,
                    TM_TEAM.ac_workgroup_id,
                    TM_TEAM_MEMBER.tm_team_id,
                    TM_TEAM_MEMBER.re_role_id,
                    TM_TEAM_OBJECT.object_id
                FROM re_access_context, oc_object_class_rel, re_access_rule, re_access_rule_rel,
                    TM_TEAM_MEMBER, TM_TEAM_OBJECT, TM_TEAM
                WHERE re_access_context.oc_object_class_id = oc_object_class_rel.dependent_oc_object_class_id
                  AND re_access_rule.re_access_rule_id = re_access_rule_rel.re_access_rule_id
                  AND re_access_rule_rel.re_role_id = TM_TEAM_MEMBER.re_role_id
                  AND TM_TEAM_MEMBER.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                  AND TM_TEAM.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                  AND TM_TEAM_OBJECT.object_id = #{projectId}
                  AND TM_TEAM_OBJECT.oc_object_class_id = 1000000
                  AND TM_TEAM_MEMBER.current_record = 1
                  AND TM_TEAM_MEMBER.user_id = #{userId}
                  AND oc_object_class_rel.main_oc_object_class_id IN (1000098, 1000103, 2500000)
                  AND re_access_context.re_access_context_id = #{permissionId}
            ) a
            LEFT JOIN
            (
                SELECT
                    re_access_rule.re_access_context_id,
                    re_access_rule.access_is_allowed,
                    re_access_rule.access_is_partial
                FROM re_access_rule, re_access_rule_rel, TM_TEAM_MEMBER, TM_TEAM_OBJECT
                WHERE
                    re_access_rule.re_access_rule_id = re_access_rule_rel.re_access_rule_id
                    AND re_access_rule_rel.re_role_id = TM_TEAM_MEMBER.re_role_id
                    AND TM_TEAM_MEMBER.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                    AND TM_TEAM_OBJECT.object_id = #{projectId}
                    AND TM_TEAM_OBJECT.oc_object_class_id = 1000000
                    AND TM_TEAM_MEMBER.current_record = 1
                    AND TM_TEAM_MEMBER.user_id = #{userId}
                    AND re_access_context_id = #{permissionId}
            ) b
                ON a.re_access_context_id = b.re_access_context_id

    </select>

    <select id="findForProjects" resultMap="simplePermissionInfoResultMap">

        SELECT
            a.main_oc_object_class_id AS parent_object_class_id,
            a.re_access_context_id    AS PERMISSION_ID,
            b.access_is_allowed,
            b.access_is_partial,
            a.ac_workgroup_id,
            a.tm_team_id,
            a.re_role_id,
            a.object_id
        FROM
            (
                SELECT DISTINCT
                    oc_object_class_rel.main_oc_object_class_id,
                    re_access_context.re_access_context_id,
                    TM_TEAM.ac_workgroup_id,
                    TM_TEAM_MEMBER.tm_team_id,
                    TM_TEAM_MEMBER.re_role_id,
                    TM_TEAM_OBJECT.object_id
                FROM re_access_context, oc_object_class_rel, re_access_rule, re_access_rule_rel,
                    TM_TEAM_MEMBER, TM_TEAM_OBJECT, TM_TEAM
                WHERE re_access_context.oc_object_class_id = oc_object_class_rel.dependent_oc_object_class_id
                  AND re_access_rule.re_access_rule_id = re_access_rule_rel.re_access_rule_id
                  AND re_access_rule_rel.re_role_id = TM_TEAM_MEMBER.re_role_id
                  AND TM_TEAM_MEMBER.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                  AND TM_TEAM.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                  AND TM_TEAM_OBJECT.object_id IN
        <foreach collection="projectIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
                  AND TM_TEAM_OBJECT.oc_object_class_id = 1000000
                  AND TM_TEAM_MEMBER.current_record = 1
                  AND TM_TEAM_MEMBER.user_id = #{userId}
                  AND oc_object_class_rel.main_oc_object_class_id IN (1000098, 1000103, 2500000)
                  AND re_access_context.re_access_context_id IN
        <foreach collection="permissionIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
            ) a
            LEFT JOIN
            (
                SELECT
                    re_access_rule.re_access_context_id,
                    re_access_rule.access_is_allowed,
                    re_access_rule.access_is_partial,
                    TM_TEAM_OBJECT.object_id
                FROM re_access_rule, re_access_rule_rel, TM_TEAM_MEMBER, TM_TEAM_OBJECT
                WHERE
                    re_access_rule.re_access_rule_id = re_access_rule_rel.re_access_rule_id
                    AND re_access_rule_rel.re_role_id = TM_TEAM_MEMBER.re_role_id
                    AND TM_TEAM_MEMBER.tm_team_id = TM_TEAM_OBJECT.tm_team_id
                    AND TM_TEAM_OBJECT.object_id IN
        <foreach collection="projectIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
                    AND TM_TEAM_OBJECT.oc_object_class_id = 1000000
                    AND TM_TEAM_MEMBER.current_record = 1
                    AND TM_TEAM_MEMBER.user_id = #{userId}
                    AND re_access_context_id IN
        <foreach collection="permissionIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
            ) b
                ON a.re_access_context_id = b.re_access_context_id
                  AND  a.object_id = b.object_id

    </select>


</mapper>