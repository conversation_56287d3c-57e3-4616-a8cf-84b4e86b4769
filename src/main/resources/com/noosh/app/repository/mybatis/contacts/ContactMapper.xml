<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.noosh.app.repository.mybatis.contacts.ContactMyBatisMapper">
    
    <!-- Result Map for Contact with Address -->
    <resultMap id="contactResultMap" type="com.noosh.app.commons.dto.contacts.ContactDTO">
        <id property="id" column="CM_CONTACT_ID"/>
        <result property="firstName" column="FIRST_NAME"/>
        <result property="lastName" column="LAST_NAME"/>
        <result property="middleName" column="MIDDLE_NAME"/>
        <result property="title" column="TITLE"/>
        <result property="phoneNumber1" column="PHONE_NUMBER_1"/>
        <result property="phoneNumber2" column="PHONE_NUMBER_2"/>
        <result property="mobileNumber" column="MOBILE_NUMBER"/>
        <result property="pagerNumber" column="PAGER_NUMBER"/>
        <result property="faxNumber" column="FAX_NUMBER"/>
        <result property="email" column="EMAIL"/>
        <result property="website" column="WEBSITE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="addressId" column="AC_ADDRESS_ID"/>
        <result property="contactNotes" column="CONTACT_NOTES"/>
        <result property="ownerTypeId" column="CM_CONTACT_OWNER_TYPE_ID"/>
        <result property="ownerObjectId" column="OWNER_OBJECT_ID"/>
        <result property="isLiveUser" column="IS_LIVE_USER"/>
        <result property="contactUserId" column="CONTACT_USER_ID"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
        <result property="isSystemGenerated" column="IS_SYSTEM_GENERATED"/>
        <result property="contactTypeId" column="CM_CONTACT_TYPE_ID"/>
        <result property="isLocked" column="IS_LOCKED"/>
        <result property="isObsolete" column="IS_OBSOLETE"/>
        <result property="workgroupName" column="WORKGROUP_NAME"/>
        <association property="address" columnPrefix="ADDR_" resultMap="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressResultMap"/>
    </resultMap>


    <sql id="contactColumns">
        ${alias}.CM_CONTACT_ID ${prefix}CM_CONTACT_ID,

    </sql>



    <select id="findContactWithAddress" resultMap="contactResultMap">

    </select>
    
</mapper>