<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.noosh.app.repository.mybatis.contacts.ContactMyBatisMapper">
    
    <!-- Result Map for Contact with Address -->
    <resultMap id="contactResultMap" type="com.noosh.app.commons.dto.contacts.ContactDTO">
        <id property="id" column="CM_CONTACT_ID"/>
        <result property="firstName" column="FIRST_NAME"/>
        <result property="lastName" column="LAST_NAME"/>
        <result property="middleName" column="MIDDLE_NAME"/>
        <result property="title" column="TITLE"/>
        <result property="phoneNumber1" column="PHONE_NUMBER_1"/>
        <result property="phoneNumber2" column="PHONE_NUMBER_2"/>
        <result property="mobileNumber" column="MOBILE_NUMBER"/>
        <result property="pagerNumber" column="PAGER_NUMBER"/>
        <result property="faxNumber" column="FAX_NUMBER"/>
        <result property="email" column="EMAIL"/>
        <result property="website" column="WEBSITE"/>
        <result property="companyName" column="COMPANY_NAME"/>
        <result property="addressId" column="AC_ADDRESS_ID"/>
        <result property="contactNotes" column="CONTACT_NOTES"/>
        <result property="ownerTypeId" column="CM_CONTACT_OWNER_TYPE_ID"/>
        <result property="ownerObjectId" column="OWNER_OBJECT_ID"/>
        <result property="isLiveUser" column="IS_LIVE_USER"/>
        <result property="contactUserId" column="CONTACT_USER_ID"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
        <result property="isSystemGenerated" column="IS_SYSTEM_GENERATED"/>
        <result property="contactTypeId" column="CM_CONTACT_TYPE_ID"/>
        <result property="isLocked" column="IS_LOCKED"/>
        <result property="isObsolete" column="IS_OBSOLETE"/>
        <result property="workgroupName" column="WORKGROUP_NAME"/>
        <association property="address" columnPrefix="ADDR_" resultMap="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressResultMap"/>
    </resultMap>


    <sql id="contactColumns">
        ${alias}.CM_CONTACT_ID ${prefix}CM_CONTACT_ID,
        ${alias}.FIRST_NAME ${prefix}FIRST_NAME,
        ${alias}.LAST_NAME ${prefix}LAST_NAME,
        ${alias}.MIDDLE_NAME ${prefix}MIDDLE_NAME,
        ${alias}.TITLE ${prefix}TITLE,
        ${alias}.PHONE_NUMBER_1 ${prefix}PHONE_NUMBER_1,
        ${alias}.PHONE_NUMBER_2 ${prefix}PHONE_NUMBER_2,
        ${alias}.MOBILE_NUMBER ${prefix}MOBILE_NUMBER,
        ${alias}.PAGER_NUMBER ${prefix}PAGER_NUMBER,
        ${alias}.FAX_NUMBER ${prefix}FAX_NUMBER,
        ${alias}.EMAIL ${prefix}EMAIL,
        ${alias}.WEBSITE ${prefix}WEBSITE,
        ${alias}.COMPANY_NAME ${prefix}COMPANY_NAME,
        ${alias}.AC_ADDRESS_ID ${prefix}AC_ADDRESS_ID,
        ${alias}.CONTACT_NOTES ${prefix}CONTACT_NOTES,
        ${alias}.CM_CONTACT_OWNER_TYPE_ID ${prefix}CM_CONTACT_OWNER_TYPE_ID,
        ${alias}.OWNER_OBJECT_ID ${prefix}OWNER_OBJECT_ID,
        ${alias}.IS_LIVE_USER ${prefix}IS_LIVE_USER,
        ${alias}.CONTACT_USER_ID ${prefix}CONTACT_USER_ID,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID,
        ${alias}.IS_SYSTEM_GENERATED ${prefix}IS_SYSTEM_GENERATED,
        ${alias}.CM_CONTACT_TYPE_ID ${prefix}CM_CONTACT_TYPE_ID,
        ${alias}.IS_LOCKED ${prefix}IS_LOCKED,
        ${alias}.IS_OBSOLETE ${prefix}IS_OBSOLETE,
        ${alias}.IS_SUPPLIER_INVITE ${prefix}IS_SUPPLIER_INVITE,
        ${alias}.CONTACT_IMPORTED_TYPE_ID ${prefix}CONTACT_IMPORTED_TYPE_ID,
        ${alias}.CONTACT_IMPROTED_ID ${prefix}CONTACT_IMPROTED_ID,
        ${alias}.CONTACT_USER_TYPE_ID ${prefix}CONTACT_USER_TYPE_ID,
        ${alias}.CONTACT_IMPROTED_PROFILE_IMG ${prefix}CONTACT_IMPROTED_PROFILE_IMG,
        ${alias}.AC_SOURCE_TYPE_ID ${prefix}AC_SOURCE_TYPE_ID
    </sql>

    <select id="findBillingContacts" resultMap="contactResultMap">

    </select>


    <select id="findContactWithAddress" resultMap="contactResultMap">
        SELECT
            <include refid="contactColumns">
                <property name="alias" value="C"/>
                <property name="prefix" value=""/>
            </include>,
            C.WORKGROUP_NAME as WORKGROUP_NAME,
            <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
                <property name="alias" value="A"/>
                <property name="prefix" value="ADDR_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
                <property name="alias" value="T"/>
                <property name="prefix" value="ADDR_T_"/>
            </include>
        FROM CM_CONTACT_USER_V C, AC_ADDRESS A, AC_COUNTRY T
        WHERE C.AC_ADDRESS_ID = A.AC_ADDRESS_ID
        AND A.AC_COUNTRY_ID = T.AC_COUNTRY_ID
        AND C.CM_CONTACT_ID = #{contactId}
    </select>
    
</mapper>