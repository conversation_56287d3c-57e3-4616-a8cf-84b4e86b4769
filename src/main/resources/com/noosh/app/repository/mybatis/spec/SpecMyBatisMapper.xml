<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.spec.SpecMyBatisMapper">

    <resultMap id="jobResultMap" type="com.noosh.app.commons.dto.spec.JobDTO">
        <result property = "jobId" column = "PC_JOB_ID"/>
        <result property = "jobUserStateId" column = "jobUserStateId"/>
    </resultMap>

    <resultMap id="jobStatusMap" type="com.noosh.app.commons.dto.spec.JobStatusDTO">
        <result property = "pcJobStatusId" column = "JS_PC_JOB_STATUS_ID"/>
        <result property = "pcJobId" column = "JS_PC_JOB_ID"/>
        <result property = "pmProjectId" column = "JS_PM_PROJECT_ID"/>
        <result property = "buClientWorkgroupId" column = "JS_BU_CLIENT_WORKGROUP_ID"/>
        <result property = "buyerAcWorkgroupId" column = "JS_BUYER_AC_WORKGROUP_ID"/>
        <result property = "ownerAcWorkgroupId" column = "JS_OWNER_AC_WORKGROUP_ID"/>
        <result property = "ocObjectStateId" column = "JS_OC_OBJECT_STATE_ID"/>
        <result property = "rfeCount" column = "JS_RFE_COUNT"/>
        <result property = "estimateCount" column = "JS_ESTIMATE_COUNT"/>
        <result property = "rfqCount" column = "JS_RFQ_COUNT"/>
        <result property = "quoteCount" column = "JS_QUOTE_COUNT"/>
        <result property = "prCount" column = "JS_PR_COUNT"/>
        <result property = "orderPendingCount" column = "JS_ORDER_PENDING_COUNT"/>
        <result property = "orderAcceptedCount" column = "JS_ORDER_ACCEPTED_COUNT"/>
        <result property = "userStateId" column = "USER_STATE_ID"/>
        <result property = "totalOrderCount" column = "TOTAL_ORDER_COUNTER"/>
        <result property = "orderCompletedCount" column = "JS_ORDER_COMPLETED_COUNT"/>
    </resultMap>

    <select id="getAllJobStatusByIdAndWorkgroupId" resultMap="jobStatusMap">
    SELECT
    JS.PC_JOB_STATUS_ID JS_PC_JOB_STATUS_ID,
    JS.PC_JOB_ID JS_PC_JOB_ID,
    JS.PM_PROJECT_ID JS_PM_PROJECT_ID,
    JS.BU_CLIENT_WORKGROUP_ID JS_BU_CLIENT_WORKGROUP_ID,
    JS.BUYER_AC_WORKGROUP_ID JS_BUYER_AC_WORKGROUP_ID,
    JS.OWNER_AC_WORKGROUP_ID JS_OWNER_AC_WORKGROUP_ID,
    JS.OC_OBJECT_STATE_ID JS_OC_OBJECT_STATE_ID,
    JS.RFE_COUNT JS_RFE_COUNT,
    JS.ESTIMATE_COUNT JS_ESTIMATE_COUNT,
    JS.RFQ_COUNT JS_RFQ_COUNT,
    JS.QUOTE_COUNT JS_QUOTE_COUNT,
    JS.PR_COUNT JS_PR_COUNT,
    JS.ORDER_PENDING_COUNT JS_ORDER_PENDING_COUNT,
    JS.ORDER_ACCEPTED_COUNT JS_ORDER_ACCEPTED_COUNT,
    JS.ORDER_COMPLETED_COUNT JS_ORDER_COMPLETED_COUNT,
    DECODE(OC_OBJECT_STATE_ID,2000024,
    (SELECT DISTINCT OC_OBJECT_STATE_ID
    FROM SH_SHIPMENT SH
    WHERE SH.PC_JOB_ID = JS.PC_JOB_ID), null) as USER_STATE_ID,
    (SELECT SUM(JS2.ORDER_PENDING_COUNT + JS2.ORDER_ACCEPTED_COUNT + JS2.ORDER_COMPLETED_COUNT)
    FROM PC_JOB_STATUS JS2
    WHERE JS2.PC_JOB_ID = JS.PC_JOB_ID
    AND JS2.BUYER_AC_WORKGROUP_ID = JS.BUYER_AC_WORKGROUP_ID
    AND JS2.BU_CLIENT_WORKGROUP_ID is null) AS TOTAL_ORDER_COUNTER
    FROM PC_JOB_STATUS JS
    WHERE JS.PC_JOB_ID =#{jobId}
    AND JS.OWNER_AC_WORKGROUP_ID=#{workgroupId}
    </select>

</mapper>