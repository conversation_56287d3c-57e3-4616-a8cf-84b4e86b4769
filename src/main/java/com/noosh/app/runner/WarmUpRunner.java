package com.noosh.app.runner;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @since 11/3/2020
 */

/**
 * ApplicationRunner run() will get execute, just after ApplicationContext is created and before spring boot application startup
 * Use for warming up various APIs
 */
@Component
@Profile("!test")
public class WarmUpRunner implements ApplicationRunner {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String LOCALHOST = "http://localhost";
    public static final String WARMUP_ACTUATOR_HEALTH = "/actuator/health";

    @Autowired
    private Environment environment;

    @Transactional(readOnly = true)
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("WarmUpRunner: Initializing");
        Long now = System.currentTimeMillis();
        warmUpServices();
        warmUpAPIs();
        Long after = System.currentTimeMillis();
        logger.info("WarmUpRunner: initialization completed in {} ms", (after-now));
        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
    }

    //Warm up various jdbc queries
    //Load various classes into the JVM
    private void warmUpServices() {
        //For developer to add
    }

    //Will initialize the DispatcherServlet 'dispatcherServlet'
    private void warmUpAPIs() {
        try {
            RestTemplate restTemplate = new RestTemplate();
            String port = environment.getProperty("server.port");

            //Actuator Health
            ResponseEntity<String> actuatorHealthCheckResponse = restTemplate.getForEntity(LOCALHOST + ":" + port + WARMUP_ACTUATOR_HEALTH, String.class);
            logger.info("Actuator Health Check : {}", actuatorHealthCheckResponse.getStatusCode().value());

            logger.info("End of warmUpAPIs method");
        }
        catch (Exception exception) {
            if (exception.getMessage() != null) {
                logger.error(exception.getMessage());
            }
        }
    }
}