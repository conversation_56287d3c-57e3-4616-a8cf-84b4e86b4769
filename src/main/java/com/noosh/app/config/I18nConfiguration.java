package com.noosh.app.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import java.net.Authenticator;
import java.net.PasswordAuthentication;


@Configuration
public class I18nConfiguration {
    @Value("${SPRING.CLOUD.CONFIG.URI}")
    private String configUri;

    @Value("${SPRING.CLOUD.CONFIG.USERNAME}")
    private String configUserName;

    @Value("${SPRING.CLOUD.CONFIG.PASSWORD}")
    private String configUserPassword;

    @Value("${SPRING.CLOUD.CONFIG.LABEL}")
    private String configLabel;

    @Bean
    @RefreshScope
    public ReloadableResourceBundleMessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource =
                new ReloadableResourceBundleMessageSource();
        Authenticator.setDefault(new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(configUserName,
                        configUserPassword.toCharArray());
            }
        });
        messageSource.setBasename(buildMessageLocation());
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }

    private String buildMessageLocation() {
        return configUri + "/i18nresource/locale"
                + "/" + configLabel + "/messages";
    }

}
