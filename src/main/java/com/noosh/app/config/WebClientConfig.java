package com.noosh.app.config;

import com.noosh.app.properties.WebClientProperty;
import io.netty.channel.ChannelOption;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

import javax.net.ssl.SSLException;

@Configuration
public class WebClientConfig {

    @Autowired
    private WebClientProperty webClientProperty;

    @RefreshScope
    @Bean("externalWebClientBuilder")
    /**
     * These 2 methods have the same body, just different annotations
     */
    WebClient.Builder externalWebClientBuilder() throws SSLException {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, webClientProperty.getConnectTimeoutInSeconds() * 1000)
                .doOnConnected(connection ->
                        connection
                                .addHandlerLast(new ReadTimeoutHandler(webClientProperty.getReadTimeoutInSeconds()))
                                .addHandlerLast(new WriteTimeoutHandler(webClientProperty.getWriteTimeoutInSeconds())))
                .followRedirect(webClientProperty.getFollowRedirect())
                .wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);

        if (webClientProperty.getUseInsecureTrustmanager()) {
            SslContext context = SslContextBuilder.forClient().trustManager(InsecureTrustManagerFactory.INSTANCE).build();
            httpClient = httpClient.secure(sslContextSpec -> sslContextSpec.sslContext(context));
        }

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient));
    }

    @RefreshScope
    @LoadBalanced
    @Bean("internalWebClientBuilder")
    /**
     * These 2 methods have the same body, just different annotations
     */
    WebClient.Builder internalWebClientBuilder() throws SSLException {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, webClientProperty.getConnectTimeoutInSeconds() * 1000)
                .doOnConnected(connection ->
                        connection
                                .addHandlerLast(new ReadTimeoutHandler(webClientProperty.getReadTimeoutInSeconds()))
                                .addHandlerLast(new WriteTimeoutHandler(webClientProperty.getWriteTimeoutInSeconds())))
                .followRedirect(webClientProperty.getFollowRedirect())
                .wiretap("reactor.netty.http.client.HttpClient", LogLevel.DEBUG, AdvancedByteBufFormat.TEXTUAL);

        if (webClientProperty.getUseInsecureTrustmanager()) {
            SslContext context = SslContextBuilder.forClient().trustManager(InsecureTrustManagerFactory.INSTANCE).build();
            httpClient = httpClient.secure(sslContextSpec -> sslContextSpec.sslContext(context));
        }

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient));
    }
}