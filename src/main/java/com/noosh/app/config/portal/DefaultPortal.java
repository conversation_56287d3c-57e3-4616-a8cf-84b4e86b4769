package com.noosh.app.config.portal;


import com.noosh.app.config.portal.service.Portal;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by neals on 12/19/2016.
 */
@Service
public class DefaultPortal implements Portal {

    @Override
    public Map<String, String> getConfig() {
        Map<String, String> portal = new HashMap<>();
        portal.put("service-name", "Noosh");
        portal.put("notification-sender-name", "Noosh Notification");
        return portal;
    }
}
