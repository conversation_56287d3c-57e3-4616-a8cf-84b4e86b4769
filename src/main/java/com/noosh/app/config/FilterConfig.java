package com.noosh.app.config;

import com.noosh.app.feign.ProjectOpenFeignClient;
import com.noosh.app.filter.ProjectPermissionFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilterConfig {

    private static final String[] FILTER_URL_PATTERNS = {
            "/order/*",
            "/aggregate/*",
            "/change/*",
            "/invoice/*",
            "/rating/*"
    };

    @Autowired
    private ProjectOpenFeignClient projectOpenFeignClient;

    @Bean
    public FilterRegistrationBean<ProjectPermissionFilter> projectPermissionFilter() {
        FilterRegistrationBean<ProjectPermissionFilter> bean = new FilterRegistrationBean<>();
        bean.setFilter(new ProjectPermissionFilter(projectOpenFeignClient));
        bean.addUrlPatterns(FILTER_URL_PATTERNS);
        return bean;
    }

}
