package com.noosh.app.config;

import com.noosh.app.exception.JSONAccessDeniedHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

@EnableWebSecurity
public class WebSecurityConfig {

    private WebSecurityConfig () {

    }

    /**
     * This entry point is to secure the Swagger UI
     */
    @Configuration
    public static class HttpBasicWebSecurityConfigurerAdapter {

        //Require password
        private static final String[] SWAGGER_BLACKLIST = {
                "/v3/api-docs",
                "/v3/api-docs/**",
                "/swagger-resources/**",
                "/swagger-ui/**",
                "/swagger/**",
                "/swagger-ui.html",
                "/webjars/**"
        };

        private static final String SWAGGER_ROLE = "SWAGGER";

        @Order(1)
        @Bean
        public SecurityFilterChain httpBasicFilterChain(HttpSecurity http) throws Exception {
            http
                    .securityMatchers(requestMatcherConfigurer ->
                            requestMatcherConfigurer
                                    .requestMatchers(SWAGGER_BLACKLIST))
                    .sessionManagement(sessionManagementCustomizer ->
                            sessionManagementCustomizer
                                    .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                    )
                    .authorizeHttpRequests(authorizeHttpRequestsCustomizer ->
                            authorizeHttpRequestsCustomizer
                                    .anyRequest().hasRole(SWAGGER_ROLE)
                    )
                    .httpBasic();
            return http.build();
        }

        @Bean
        public InMemoryUserDetailsManager swaggerUser(Environment environment) {
            UserDetails user = User.withUsername(environment.getProperty("swaggerUI.username"))
                    .password(environment.getProperty("swaggerUI.password"))
                    .passwordEncoder(passwordEncoder()::encode)
                    .roles(SWAGGER_ROLE)
                    .build();
            return new InMemoryUserDetailsManager(user);
        }

        @Bean
        public PasswordEncoder passwordEncoder() {
            return new BCryptPasswordEncoder();
        }
    }

    /**
     * To secure everything else
     */
    @Configuration
    public static class Oauth2WebSecurityConfigurationAdapter {

        //Allow paths
        private static final String[] WHITELIST = {
                "/actuator/**",
                "/error",
                "/version.html",
                "/tag.html"
        };

        private static final String[] ALLOWED_ROLES = new String[]{"NE_USER", "NP_USER", "NM_USER"};

        @Order(2)
        @Bean
        public SecurityFilterChain oauth2FilterChain(HttpSecurity http) throws Exception {
            http
                    .securityMatchers(requestMatcherConfigurer ->
                            requestMatcherConfigurer
                                    .anyRequest())
                    .sessionManagement(sessionManagementCustomizer ->
                            sessionManagementCustomizer
                                    .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                    )
                    .authorizeHttpRequests(authorizeHttpRequestsCustomizer ->
                            authorizeHttpRequestsCustomizer
                                    .requestMatchers(WHITELIST).permitAll()
                                    .requestMatchers("/**").hasAnyRole(ALLOWED_ROLES)
                                    .anyRequest().authenticated()
                    )
                    .exceptionHandling(exceptionHandlingCustomizer ->
                            exceptionHandlingCustomizer
                                    .accessDeniedHandler(jsonAccessDeniedHandler()))
                    .oauth2ResourceServer()
                    .jwt()
                    .jwtAuthenticationConverter(jwtAuthenticationConverter());
            return http.build();
        }

        @Bean
        public AuthorityJwtAuthenticationConverter jwtAuthenticationConverter() {
            return new AuthorityJwtAuthenticationConverter();
        }

        @Bean
        public JSONAccessDeniedHandler jsonAccessDeniedHandler() {
            return new JSONAccessDeniedHandler();
        }
    }
}