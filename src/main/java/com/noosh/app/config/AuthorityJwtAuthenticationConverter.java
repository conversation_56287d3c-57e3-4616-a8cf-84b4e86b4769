package com.noosh.app.config;

import org.springframework.core.convert.converter.Converter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtClaimNames;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * This class is based on the default JwtAuthenticationConverter
 * The default class only adds the scope attribute into the authorities, but I want to include other authorities
 * <AUTHOR>
 * @since 1/23/2020
 */
public class AuthorityJwtAuthenticationConverter implements Converter<Jwt, AbstractAuthenticationToken> {

    private Converter<Jwt, Collection<GrantedAuthority>> jwtGrantedAuthoritiesConverter = new JwtGrantedAuthoritiesConverter();

    private String principalClaimName = JwtClaimNames.SUB;

    private static final String JWT_AUTHORITY_PREFIX = "";

    private static final Collection<String> WELL_KNOWN_JWT_ATTRIBUTE_NAMES = Arrays.asList("authorities");

    @Override
    public AbstractAuthenticationToken convert(Jwt jwt) {
        Collection<GrantedAuthority> scope = this.jwtGrantedAuthoritiesConverter.convert(jwt);

        Collection<GrantedAuthority> grantedAuthorities = this.extractJwtAuthorities(jwt);
        grantedAuthorities.addAll(scope);

        String principalClaimValue = jwt.getClaimAsString(this.principalClaimName);
        return new JwtAuthenticationToken(jwt, grantedAuthorities, principalClaimValue);
    }

    public void setJwtGrantedAuthoritiesConverter(
            Converter<Jwt, Collection<GrantedAuthority>> jwtGrantedAuthoritiesConverter) {
        Assert.notNull(jwtGrantedAuthoritiesConverter, "jwtGrantedAuthoritiesConverter cannot be null");
        this.jwtGrantedAuthoritiesConverter = jwtGrantedAuthoritiesConverter;
    }

    /**
     * Sets the principal claim name. Defaults to {@link JwtClaimNames#SUB}.
     * @param principalClaimName The principal claim name
     * @since 5.4
     */
    public void setPrincipalClaimName(String principalClaimName) {
        Assert.hasText(principalClaimName, "principalClaimName cannot be empty");
        this.principalClaimName = principalClaimName;
    }

    private Collection<GrantedAuthority> extractJwtAuthorities(Jwt jwt) {
        return this.getJwtAuthorities(jwt)
                .stream()
                .map(authority -> JWT_AUTHORITY_PREFIX + authority)
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    private Collection<String> getJwtAuthorities(Jwt jwt) {
        Collection<String> jwtAuthorities = new ArrayList<>();
        for ( String attributeName : WELL_KNOWN_JWT_ATTRIBUTE_NAMES) {
            Object scopes = jwt.getClaims().get(attributeName);
            if (scopes instanceof String) {
                if (StringUtils.hasText((String) scopes)) {
                    jwtAuthorities.addAll(Arrays.asList(((String) scopes).split(" ")));
                }
            } else if (scopes instanceof Collection) {
                jwtAuthorities.addAll((Collection<String>) scopes);
            }
        }

        return jwtAuthorities;
    }
}