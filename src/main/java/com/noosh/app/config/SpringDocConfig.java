package com.noosh.app.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.util.ArrayList;

@Configuration
public class SpringDocConfig {

    public static final String HTTP = "http://";
    public static final String LOCALHOST = "http://localhost";

    @Bean
    public OpenAPI openAPI(Environment environment) {
        String name = environment.getProperty("spring.application.name", "Service");

        ArrayList<Server> servers = new ArrayList<>();
        String cloudgatewayServerUrl = environment.getProperty("cloudgateway.root-url") + "/" + name;
        Server cloudgatewayServer = new Server();
        cloudgatewayServer.setUrl(cloudgatewayServerUrl);
        servers.add(cloudgatewayServer);

        for (String activeProfile : environment.getActiveProfiles()) {
            if (activeProfile.equals("dev") || activeProfile.equals("dev-mvn")) {
                String port = environment.getProperty("server.port");
                String httpServiceUrl = HTTP + name + ":" + port;
                Server httpServiceServer = new Server();
                httpServiceServer.setUrl(httpServiceUrl);
                servers.add(httpServiceServer);

                String httpLocalhostUrl = LOCALHOST + ":" + port;
                Server httpLocalServer = new Server();
                httpLocalServer.setUrl(httpLocalhostUrl);
                servers.add(httpLocalServer);

                String cloudgatewayHost = environment.getProperty("server.domain");
                String cloudgatewayHostUrl = HTTP + cloudgatewayHost + ":" + port;
                Server cloudgatewayHostServer = new Server();
                cloudgatewayHostServer.setUrl(cloudgatewayHostUrl);
                servers.add(cloudgatewayHostServer);

                break;
            }
        }

        String swaggerExternalDocUrl = environment.getProperty("swaggerUI.external-doc.confluence-url");
        String swaggerExternalDocDescription = name.substring(0,1).toUpperCase() + name.substring(1)+ " Confluence Documentation";

        OpenAPI openAPI = new OpenAPI()
                .info(new Info()
                        .title(name.toUpperCase() + " REST API")
                        .description("REST API for Noosh")
                        .version("1.0.0"))
                .components(new Components()
                        .addSecuritySchemes("JWT", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("Bearer")
                                .bearerFormat("JWT")))
                .servers(servers);

        if (swaggerExternalDocUrl != null) {
            openAPI = openAPI.externalDocs(new ExternalDocumentation()
                    .url(swaggerExternalDocUrl)
                    .description(swaggerExternalDocDescription));
        }

        return openAPI;
    }
}