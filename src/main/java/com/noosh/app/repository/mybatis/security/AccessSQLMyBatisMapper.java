package com.noosh.app.repository.mybatis.security;

import com.noosh.app.commons.dto.security.AccessSQLDTO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * User: <PERSON>
 * Date: 8/14/2018
 */
public interface AccessSQLMyBatisMapper {

    @Results({
            @Result(property = "accessSQLID", column = "ACCESS_SQL_ID"),
            @Result(property = "accessSQL", column = "ACCESS_SQL")
    })
    @Select("select re_access_sql_id ACCESS_SQL_ID, access_sql_string ACCESS_SQL from re_access_sql")
    List<AccessSQLDTO> findAllAccessSQL();

    @Results({
            @Result(property = "accessSQLID", column = "ACCESS_SQL_ID"),
            @Result(property = "accessSQL", column = "ACCESS_SQL")
    })
    @Select("select re_access_sql_id ACCESS_SQL_ID, access_sql_string ACCESS_SQL from re_access_sql where re_access_sql_id = #{accessId}")
    @ResultType(AccessSQLDTO.class)
    AccessSQLDTO findAccessSQL(@Param("accessId") Long accessId);

}
