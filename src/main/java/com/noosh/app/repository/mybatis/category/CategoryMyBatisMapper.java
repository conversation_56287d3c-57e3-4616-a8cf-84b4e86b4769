package com.noosh.app.repository.mybatis.category;

import com.noosh.app.commons.dto.proposal.CategoryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 5/28/2019
 */
public interface CategoryMyBatisMapper {

    List<CategoryDTO> findCategoryByObjects(@Param("userId") Long userId,
                                      @Param("workgroupId") Long workgroupId,
                                      @Param("proposalItemIds") List<Long> proposalItemIds);

}
