package com.noosh.app.repository.mybatis.contacts;

import com.noosh.app.commons.dto.contacts.ContactDTO;
import com.noosh.app.commons.entity.contacts.Contact;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MyBatis Mapper for Contact database operations
 * Handles invoice billing contacts queries
 */
@Mapper
public interface ContactMyBatisMapper {

    /**
     * Find billing contacts for a workgroup with pagination support
     * @return List of billing contacts with full details
     */
    List<ContactDTO> findBillingContacts(@Param("workgroupId") Long workgroupId);
    
    /**
     * Find contact with address by ID
     * Based on ContactBeanHome.findContactWithAddress
     * 
     * @param contactId The contact ID
     * @return Contact with address information
     */
    ContactDTO findContactWithAddress(@Param("contactId") Long contactId);
}