package com.noosh.app.repository.mybatis.address;

import com.noosh.app.commons.dto.address.AddressDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * User: leilaz
 * Date: 12/17/23
 */
public interface AddressMyBatisMapper {
    List<AddressDTO> findAddressByIds(@Param("addressIds") List<Long> addressIds);

    List<AddressDTO> findWorkgroupMainAddress(@Param("workgroupId") Long workgroupId, @Param("isIncludingWarehouse") boolean isIncludingWarehouse);
}
