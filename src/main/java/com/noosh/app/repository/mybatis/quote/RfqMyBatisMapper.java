package com.noosh.app.repository.mybatis.quote;

import com.noosh.app.commons.dto.quote.RfqWidgetDTO;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface RfqMyBatisMapper {

    int getRfqQuoteCount(@Param("rfqId") Long rfqId);

    String getWorkgroupName(@Param("workgroupId") Long workgroupId);

    List<RfqWidgetDTO> getAllRfqByProjectId(@Param("projectId") Long projectId, @Param("rfqStatusIdFilter") List<Long> rfqStatusIdFilter);
}
