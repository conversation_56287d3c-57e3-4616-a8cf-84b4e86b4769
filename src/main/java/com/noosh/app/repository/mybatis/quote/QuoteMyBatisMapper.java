package com.noosh.app.repository.mybatis.quote;

import com.noosh.app.commons.dto.quote.QuoteWidgetDTO;
import org.apache.ibatis.annotations.*;

import java.util.List;


public interface QuoteMyBatisMapper {

    List<QuoteWidgetDTO> getAllQuotesByProjectIdAndRrfqId(@Param("projectId") Long projectId,
                                                          @Param("rfqId") Long rfqId,
                                                          @Param("quoteStatusIdFilter") List<Long> quoteStatusIdFilter);


}
