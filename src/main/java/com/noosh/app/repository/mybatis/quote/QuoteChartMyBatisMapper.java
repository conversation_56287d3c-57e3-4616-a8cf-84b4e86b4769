package com.noosh.app.repository.mybatis.quote;

import com.noosh.app.commons.vo.chart.QuoteChartItemVO;
import com.noosh.app.commons.vo.chart.QuoteConversionChartItemVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QuoteChartMyBatisMapper {

    List<QuoteChartItemVO> findTimeToQuote(@Param("workgroupId") Long workgroupId, @Param("filter") Integer filter);
    List<QuoteChartItemVO> findTimeToSellOrder(@Param("workgroupId") Long workgroupId);

    List<QuoteConversionChartItemVO> findQuoteConversion(@Param("workgroupId") Long workgroupId, @Param("filter") Integer filter);
}
