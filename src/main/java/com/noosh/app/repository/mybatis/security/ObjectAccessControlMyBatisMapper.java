package com.noosh.app.repository.mybatis.security;

import com.noosh.app.commons.entity.security.ObjectAccessControl;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface ObjectAccessControlMyBatisMapper {

    List<ObjectAccessControl> findObjectAccessControl(@Param("teamId") Long teamId,
                                                      @Param("userId") Long userId,
                                                      @Param("groupId") Long groupId,
                                                      @Param("roleId") Long roleId,
                                                      @Param("permissionId") Long permissionId,
                                                      @Param("objectId") Long objectId);

}
