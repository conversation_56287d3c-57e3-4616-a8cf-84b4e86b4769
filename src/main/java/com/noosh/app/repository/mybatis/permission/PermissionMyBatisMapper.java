package com.noosh.app.repository.mybatis.permission;

import com.noosh.app.commons.dto.permission.PermissionWidgetDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PermissionMyBatisMapper {

    List<PermissionWidgetDTO> findPermissionInfo(@Param("roleId") Long roleId,
                                                 @Param("list") final Long[] parentObjectClassList);

    PermissionWidgetDTO findForWorkgroup(@Param("permissionId") Long permissionId,
                                         @Param("workgroupId") Long workgroupId,
                                         @Param("userId") Long userId);

    PermissionWidgetDTO findForProject(@Param("permissionId") Long permissionId,
                                       @Param("userId") Long userId,
                                       @Param("projectId") Long projectId);

    List<PermissionWidgetDTO> findForProjects(@Param("permissionIds") List<Long> permissionIds,
                                              @Param("userId") Long userId,
                                              @Param("projectIds") List<Long> projectIds);
}
