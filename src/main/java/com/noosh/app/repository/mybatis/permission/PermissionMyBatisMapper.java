package com.noosh.app.repository.mybatis.permission;

import com.noosh.app.commons.dto.permission.PermissionWidgetDTO;
import org.apache.ibatis.annotations.*;

import java.util.List;

public interface PermissionMyBatisMapper {

        List<PermissionWidgetDTO> findPermissionInfo(@Param("roleId") Long roleId,
                                                     @Param("list") final Long[] parentObjectClassList);

}
