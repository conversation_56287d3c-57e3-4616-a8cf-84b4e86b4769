package com.noosh.app.repository.mybatis.proposal;

import com.noosh.app.commons.dto.proposal.*;
import com.noosh.app.commons.dto.spec.ShipmentWidgetDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 10/31/2021
 */
public interface ProposalMyBatisMapper {

    List<CategoryDTO> findCategories(@Param("userId") Long userId, @Param("workgroupId") Long workgroupId);
    List<WorkgroupLogoDTO> findLogos(@Param("workgroupId") Long workgroupId);
    List<ProposalTemplateDTO> findTemplates(@Param("workgroupId") Long workgroupId);
    List<SpecSummaryDTO> findSpecSummary(@Param("workgroupId") Long workgroupId);
    SpecSummaryDTO findDefaultSpecSummary(@Param("specTypeId") Long specTypeId, @Param("workgroupId") Long workgroupId);
    ProposalDTO findProposal(@Param("projectId") Long projectId, @Param("proposalId") Long proposalId);
    List<ProposalItemDTO> findProposalItems(@Param("proposalId") Long proposalId);
    List<SpecSummaryFieldValueDTO> findProposalItemSpecSummaryFields(@Param("proposalItemId") Long proposalItemId);
    List<ShipmentWidgetDTO> findShipTos(@Param("jobId") Long jobId);
    Long findPropertyIdByEstimateItemPriceId(@Param("estimateItemPriceId") Long estimateItemPriceId);
    Long findPropertyIdByOrderItemId(@Param("orderItemId") Long orderItemId);
}
