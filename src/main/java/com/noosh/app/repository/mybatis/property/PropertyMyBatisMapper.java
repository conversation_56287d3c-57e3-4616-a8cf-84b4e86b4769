package com.noosh.app.repository.mybatis.property;

import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.property.PropertyTypeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 11/28/2021
 */
public interface PropertyMyBatisMapper {

    List<PropertyTypeDTO> findTree(@Param("propertyTypeId") Long propertyTypeId);
    List<PropertyAttributeDTO> findPropertyAttributes(@Param("propertyId") Long propertyId);
}
