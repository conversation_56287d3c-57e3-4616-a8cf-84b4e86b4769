package com.noosh.app.repository.jpa.quote;

import com.noosh.app.commons.entity.quote.QuotePrice;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface QuotePriceRepository extends JpaRepository<QuotePrice, Long> {
    QuotePrice findByParentQuotePriceId(Long parentQuotePriceId);
    List<QuotePrice> findAllByParentQuotePriceId(Long parentQuotePriceId);
}