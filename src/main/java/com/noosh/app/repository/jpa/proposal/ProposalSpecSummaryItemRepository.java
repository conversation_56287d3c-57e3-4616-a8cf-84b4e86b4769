package com.noosh.app.repository.jpa.proposal;

import com.noosh.app.commons.entity.proposal.ProposalSpecSummaryItem;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ProposalSpecSummaryItemRepository extends JpaRepository<ProposalSpecSummaryItem, Long> {
    List<ProposalSpecSummaryItem> findByProposalSpecSummaryId(Long proposalSpecSummaryId);
}
