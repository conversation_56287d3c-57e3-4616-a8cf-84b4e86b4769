package com.noosh.app.repository.jpa.security;

import com.noosh.app.commons.entity.security.ObjectCounter;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 6/6/19
 */

public interface ObjectCounterRepository extends JpaRepository<ObjectCounter, Long> {
    ObjectCounter findByWorkgroupIdAndCounterTypeId(Long workgroupId, Long counterTypeId);

    List<ObjectCounter> findByWorkgroupIdInAndCounterTypeId(List<Long> workgroupId, Long counterTypeId);
}
