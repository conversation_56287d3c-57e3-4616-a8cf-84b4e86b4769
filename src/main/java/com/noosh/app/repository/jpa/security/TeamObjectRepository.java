package com.noosh.app.repository.jpa.security;

import com.noosh.app.commons.entity.security.TeamObject;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;


public interface TeamObjectRepository extends JpaRepository<TeamObject, Long> {

    TeamObject findByObjectIdAndObjectClassId(Long objectId, Long objectClassId);

    @Query("select tto from TeamObject tto, TeamMember tm, SyContainable C where tm.isCurrent = true and tm.teamId = tto.teamId " +
            " and tto.objectClassId = 1000000 and C.parentObjectId = tto.objectId " +
            " and C.objectClassId = 1000108 and C.itemOcObjectStateId=2500040 " +
            " and C.parentObjectClassId = 1000000 and tm.userId = ?1 and C.objectId = ?2")
    TeamObject findTeamObjectByTmIdAndTaskId(Long teammemberId, Long taskId);
}
