package com.noosh.app.repository.jpa.category;

import com.noosh.app.commons.entity.category.CategoryClass;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 4/29/20
 */
public interface CategoryClassRepository extends JpaRepository<CategoryClass, Long> {
    List<CategoryClass> findAllByCategoryId(Long categoryId);
}
