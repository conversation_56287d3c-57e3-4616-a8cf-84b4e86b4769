package com.noosh.app.repository.jpa.security;

import com.noosh.app.commons.entity.security.TeamMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: neals
 * @Date: 12/29/2015
 */

public interface TeamMemberRepository extends JpaRepository<TeamMember, Long> {
    TeamMember findByTeamIdAndUserId(Long teamId, Long userId);
    TeamMember findByTeamIdAndUserIdAndIsCurrent(Long teamId, Long userId, boolean isCurrent);
    List<TeamMember> findByTeamId(Long teamId);
    List<TeamMember> findByTeamIdAndRoleId(Long teamId, Long roleId);
    List<TeamMember> findByTeamIdIn(List<Long> teamIds);
    List<TeamMember> findByIdIn(List<Long> teamMemberIds);
    @Query("select case when (count(tm) > 0)  then true else false end from TeamObject tto, TeamMember tm where tm.isCurrent = true and tm.teamId = tto.teamId " +
            "and tto.objectClassId = 1000000 and tto.objectId = ?1 and tm.userId = ?2")
    Boolean findCurrentTeamMember(Long projectId, Long teamMemberId);

    @Query("select tm from TeamObject tto, TeamMember tm where tm.isCurrent = true and tm.teamId = tto.teamId " +
            "and tto.objectClassId = 1000000 and tto.objectId = ?1 and tm.userId = ?2")
    TeamMember findCurrentTeamMemberByProjectId(Long projectId, Long userId);
}
