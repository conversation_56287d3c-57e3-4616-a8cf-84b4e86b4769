package com.noosh.app.repository.jpa.account;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.entity.account.AccountUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;


public interface AccountUserRepository extends JpaRepository<AccountUser, Long> {

    List<AccountUser> findAccountUserByPersonId(Long personId);

    @Query(" SELECT au FROM AccountUser au, Workgroup w " +
            " WHERE w.id = au.workgroupId " +
            "   AND au.personId = ?1 " +
            "   AND w.statusId = " + ObjectStateID.WORKGROUP_ACTIVATED +
            "   AND au.objectStateId = ?2 " +
            " ORDER BY w.name asc ")
    List<AccountUser> findAccountUserByPersonIdAndStatusId(Long personId, Long statusId);

    @Query("select au from AccountUser au, TeamMember tm where au.id = tm.userId and tm.teamId = ?1")
    List<AccountUser> findAccountUserByTeamId(Long teamId);

    AccountUser findByWorkgroupIdAndPersonId(Long workgroupId, Long personId);

    List<AccountUser> findByPersonId(Long personId);

    List<AccountUser> findAllByIdInOrderByIdAsc(List<Long> ids);

    Optional<AccountUser> findByIdAndWorkgroupId(Long id, Long workgroupId);

}
