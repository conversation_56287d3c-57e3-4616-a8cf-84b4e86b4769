package com.noosh.app.repository.jpa.property;

import com.noosh.app.commons.entity.property.PropertyParam;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * User: leilaz
 * Date: 12/30/15
 */

public interface PropertyParamRepository extends JpaRepository<PropertyParam, Long> {
    PropertyParam findByParamNameAndPrPropertyTypeId(String name, Long prPropertyTypeId);
    PropertyParam findByParamNameAndPrDataTypeId(String name, Long prDataTypeId);
}
