package com.noosh.app.repository.jpa.property;

import com.noosh.app.commons.entity.property.PropertyAttribute;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * User: leilaz
 * Date: 12/30/15
 * Time: 10:24 PM
 */

public interface PropertyAttributeRepository extends JpaRepository<PropertyAttribute, Long> {
    PropertyAttribute findByPrPropertyIdAndPrPropertyParamId(Long propertyId, Long paramId);
}
