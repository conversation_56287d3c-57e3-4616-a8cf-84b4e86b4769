package com.noosh.app.repository.jpa.task;

import com.noosh.app.commons.entity.task.TaskType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 7/11/16
 */

public interface TaskTypeRepository extends JpaRepository<TaskType, Long> {
    @Query("   select tt from TaskType tt where ((ownerAcWorkgroupId = ?1 and isUserDefined = 1 and isActive = 1) " +
            "or (ownerAcWorkgroupId is null and (isUserDefined is null or isUserDefined = 0)))")
    List<TaskType> findTaskTypeByWorkgroupId(Long workgroupId);

    @Query("   select tt from TaskType tt where ((ownerAcWorkgroupId = ?1 and isUserDefined = 1 and isActive = 1) " +
            " or (ownerAcWorkgroupId is null and isUserDefined = 1))")
    List<TaskType> findCustomTaskTypeByWorkgroupId(Long workgroupId);

    @Query(" select TT from TaskType TT " +
            " where TT.isMilestone = 1 and TT.ownerAcWorkgroupId = ?1 and TT.isActive = 1 " +
            " order by upper(TT.nameStr)")
    List<TaskType> findMilestonesByWorkgroupId(long workgroupId);
}
