package com.noosh.app.repository.jpa.property;

import com.noosh.app.commons.entity.property.Property;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 12/30/15
 * Time: 6:50 PM
 */

public interface PropertyRepository extends JpaRepository<Property, Long> {
    @Query("select P from Property P where objectId = ?1 and objectClassId = ?2 and prPropertyTypeId = ?3 order by createDate desc")
    public List<Property> findByObjectIdAndObjectClassIdAndPrPropertyTypeId(Long objectId, Long objectClassId, Long prPropertyTypeId);

    public List<Property> findByParentPropertyIdAndPropertyName(Long parentPropertyId, String name);
}
