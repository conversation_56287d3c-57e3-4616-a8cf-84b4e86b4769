package com.noosh.app.repository.tracking;

import com.noosh.app.commons.entity.tracking.Tracking;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 9/17/19
 */

public interface TrackingRepository extends JpaRepository<Tracking, Long> {
    public List<Tracking> findByObjectIdAndObjectClassIdAndTrTrackingTypeId(Long objectId, Long objectClassId, Long trackingTypeId);

    @Query("select T from Tracking T Where T.trTrackingId in (select SC.objectId from SyContainable SC where SC.parentObjectId = ?1 and SC.parentObjectClassId = ?2 and SC.objectClassId = ?3) " +
            "and T.trTrackingTypeId = ?4")
    public List<Tracking> findByContainable(Long parentObjectId, Long parentObjectClassId, Long objectClassId, Long trackingTypeId);
}
