package com.noosh.app.repository.uofm;

import com.noosh.app.commons.entity.uofm.Uofm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 9/30/17
 */

public interface UofmRepository extends JpaRepository<Uofm, Long> {
    @Query("select u from Uofm u, UofmSpecType ut where u.id = ut.uofmId and ut.isDefault = 1 and ut.specTypeId = ?1")
    public Uofm findDefaultBySpecTypeId(Long specTypeId);

    @Query("select u from Uofm u, UofmSpecType ut where u.id = ut.uofmId and ut.specTypeId = ?1 order by u.conversionFactor asc")
    public List<Uofm> findBySpecTypeId(Long specTypeId);
}
