package com.noosh.app.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix="web-client")
public class WebClientProperty {

    private Integer connectTimeoutInSeconds;
    private Integer readTimeoutInSeconds;
    private Integer writeTimeoutInSeconds;
    private Boolean followRedirect;
    private Boolean useInsecureTrustmanager;
    private Integer retryMaxAttempts;
    private Integer retryMinBackoffInSeconds;
    private Integer retryMaxBackoffInSeconds;

    public Integer getConnectTimeoutInSeconds() {
        return connectTimeoutInSeconds;
    }

    public void setConnectTimeoutInSeconds(Integer connectTimeoutInSeconds) {
        this.connectTimeoutInSeconds = connectTimeoutInSeconds;
    }

    public Integer getReadTimeoutInSeconds() {
        return readTimeoutInSeconds;
    }

    public void setReadTimeoutInSeconds(Integer readTimeoutInSeconds) {
        this.readTimeoutInSeconds = readTimeoutInSeconds;
    }

    public Integer getWriteTimeoutInSeconds() {
        return writeTimeoutInSeconds;
    }

    public void setWriteTimeoutInSeconds(Integer writeTimeoutInSeconds) {
        this.writeTimeoutInSeconds = writeTimeoutInSeconds;
    }

    public Boolean getFollowRedirect() {
        return followRedirect;
    }

    public void setFollowRedirect(Boolean followRedirect) {
        this.followRedirect = followRedirect;
    }

    public Boolean getUseInsecureTrustmanager() {
        return useInsecureTrustmanager;
    }

    public void setUseInsecureTrustmanager(Boolean useInsecureTrustmanager) {
        this.useInsecureTrustmanager = useInsecureTrustmanager;
    }

    public Integer getRetryMaxAttempts() {
        return retryMaxAttempts;
    }

    public void setRetryMaxAttempts(Integer retryMaxAttempts) {
        this.retryMaxAttempts = retryMaxAttempts;
    }

    public Integer getRetryMinBackoffInSeconds() {
        return retryMinBackoffInSeconds;
    }

    public void setRetryMinBackoffInSeconds(Integer retryMinBackoffInSeconds) {
        this.retryMinBackoffInSeconds = retryMinBackoffInSeconds;
    }

    public Integer getRetryMaxBackoffInSeconds() {
        return retryMaxBackoffInSeconds;
    }

    public void setRetryMaxBackoffInSeconds(Integer retryMaxBackoffInSeconds) {
        this.retryMaxBackoffInSeconds = retryMaxBackoffInSeconds;
    }
}
