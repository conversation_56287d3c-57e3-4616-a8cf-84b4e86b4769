package com.noosh.app.properties;

import java.util.HashMap;

public class Validation {
    String requestpattern;
    HashMap<String, String> fieldmapping;

    public String getRequestpattern() {
        return requestpattern;
    }

    public void setRequestpattern(String requestpattern) {
        this.requestpattern = requestpattern;
    }

    public HashMap<String, String> getFieldmapping() {
        return fieldmapping;
    }

    public void setFieldmapping(HashMap<String, String> fieldmapping) {
        this.fieldmapping = fieldmapping;
    }
}