package com.noosh.app;

import com.itextpdf.licensing.base.LicenseKey;
import com.noosh.app.commons.entity.security.Locale;
import com.noosh.app.repository.jpa.security.LocaleRepository;
import com.noosh.app.service.util.I18NUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.MessageSource;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.EnableAsync;

import jakarta.annotation.PostConstruct;

import java.io.File;
import java.io.IOException;
import java.util.List;

@EnableFeignClients
@EnableDiscoveryClient
@SpringBootApplication
@EnableAsync
public class QuoteResourceApplication {
    private final Logger logger = LoggerFactory.getLogger(QuoteResourceApplication.class);

    @Autowired
    private Environment env;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private LocaleRepository localeRepository;
    @Autowired
    private I18NUtils i18nUtils;

    public static void main(String[] args) {
        SpringApplication.run(QuoteResourceApplication.class, args);
    }

    @PostConstruct
    public void initApplication() throws IOException {
        String itextPDFLicense = env.getProperty("ITEXT.LICENSE");
        if (itextPDFLicense != null && !itextPDFLicense.isEmpty()) {
            try {
                File file = new File(itextPDFLicense);
                LicenseKey.loadLicenseFile(file);
            } catch (Exception e) {
                e.printStackTrace();
                System.out.print(e);
                System.out.print("[Error]=======>Can not load license of itextpd!!!!!!");
            }

        }

        initI18nDefaultData();
        asyncInitI18nRemainingData();
    }

    private void initI18nDefaultData() {
        if (env.getProperty("server.i18nLoad").equalsIgnoreCase("true")) {
            logger.info("Message Source: Initializing message source for " + I18NUtils.DEFAULT_LOCALE.toString());
            Long now = System.currentTimeMillis();
            messageSource.getMessage("error.title", new String[]{}, I18NUtils.DEFAULT_LOCALE);
            Long after = System.currentTimeMillis();
            logger.info("Message Source: initialization completed for " + I18NUtils.DEFAULT_LOCALE.toString() + " in " + (after - now) + " ms");
        }
    }

    private void asyncInitI18nRemainingData() {
        if (env.getProperty("server.i18nLoad").equalsIgnoreCase("true")) {
            List<Locale> locales = localeRepository.findAll(Sort.by(Sort.Direction.ASC, "id"));
            logger.info("Message Source: Async Initializing message source for " + (locales.size() - 1) + " locales");
            locales.stream().forEach(l -> {
                if (!l.getLocaleCode().toUpperCase().equals(I18NUtils.DEFAULT_LOCALE.toString().toUpperCase())) {
                    i18nUtils.asyncInit18nDataByLocale(l);
                }
            });
        }
    }
}