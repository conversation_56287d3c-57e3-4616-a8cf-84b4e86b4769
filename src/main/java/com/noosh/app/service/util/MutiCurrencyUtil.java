package com.noosh.app.service.util;

import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.quote.QuoteWidgetDTO;
import com.noosh.app.commons.entity.currency.NCurrency;
import com.noosh.app.repository.jpa.currency.CurrencyRepository;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 5/11/2021
 */
@Component
public class MutiCurrencyUtil {

    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private CurrencyRepository currencyRepository;

    public boolean isMutiCurrency(QuoteWidgetDTO quote) {
        boolean isMutiCurrency = false;
        if (quote.getExCurrencyId() != null && quote.getExCurrencyId() != -1 &&
                quote.getRate() != null &&  quote.getRate().compareTo(BigDecimal.ZERO) != 0) {
            NCurrency currency = currencyRepository.findById(quote.getExCurrencyId()).orElse(null);
            if (currency != null) {
                //TODO handle offline client
                isMutiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, quote.getBuyerWorkgroupId());
            }
        }
        return isMutiCurrency;
    }

    public boolean isShowBothCurrency(Long workgroupId, boolean isClientProject) {
        boolean hideTransCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY, workgroupId);
        return !isClientProject || !hideTransCurrency;
    }

}
