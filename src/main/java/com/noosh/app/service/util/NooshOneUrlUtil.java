package com.noosh.app.service.util;


import com.noosh.app.commons.constant.EnterpriseLinkDestination;
import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.config.PropertiesUtil;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;

/**
 * User: leilaz
 * Date: 12/14/15
 */
public class NooshOneUrlUtil {

    public static final String SSL_PROTOCOL = "https";
    public static final String HTTP_PROTOCOL = "http";

    /**
     * compose link to Enterprise link
     *
     * @param url - manage link in EnterpriseLinkDestination.java
     * @return absolute url
     */
    public static String composeLinkToEnterprise(final String url) {
        return composeLinkToEnterprise(url, null);
    }

    public static String composeLinkToEnterprise(final String url, HashMap<String, String> params) {
        if (params == null) {
            params = new HashMap<>();
        }

        boolean isHttps = true;
		String host = PropertiesUtil.getEnterpriseDomain();
		RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
    	if(attributes !=null && attributes instanceof ServletRequestAttributes) {
    		HttpServletRequest request = ((ServletRequestAttributes) attributes).getRequest();
    		if (request != null) {
    			isHttps = true;
                String neEntry = request.getParameter("domain");
                if (neEntry != null) {
                    host = neEntry;
                }

            }
    	}

        return new NooshUrl(isHttps ? SSL_PROTOCOL : HTTP_PROTOCOL, host, getNEPort(isHttps), url, params).toString();
    }

    private static int getNEPort(boolean isHttps) {
        String instance = PropertiesUtil.getInstance();
        if (instance != null && instance.equalsIgnoreCase("dist")) {
            return isHttps ? 8443 : 7777;
        } else if (instance != null) {
            return -1;
        }
        return -1;
    }

    public static String composeBatchActionButtonLinkToEnterprise(int actionId) {
        HashMap params = new HashMap();
//        params.put("ac", "" + ActionUtil.getActionId("GOTO_BATCH_ACTION", EnterpriseLinkDestination.GOTO_BATCH_ACTION));
        params.put("batchActionId", "" + actionId);

        String url = EnterpriseLinkDestination.GOTO_BATCH_ACTION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeExcelExportLinkToEnterprise() {
        HashMap params = new HashMap();
//        params.put("ac", "" + ActionUtil.getActionId("ACTION_RUN_PROJECT_EXCEL", EnterpriseLinkDestination.ACTION_RUN_PROJECT_EXCEL));

        String url = EnterpriseLinkDestination.ACTION_RUN_PROJECT_EXCEL;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeExcelForMilestoneExportLinkToEnterprise() {
        HashMap params = new HashMap();
//        params.put("ac", "" + ActionUtil.getActionId("ACTION_RUN_PRODUCTION_MATRIX_EXCEL", EnterpriseLinkDestination.ACTION_RUN_PRODUCTION_MATRIX_EXCEL));

        String url = EnterpriseLinkDestination.ACTION_RUN_PRODUCTION_MATRIX_EXCEL;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeGotoProjectHomeLinkToEnterprise(final long objectId) {
        HashMap params = new HashMap();
        params.put("objectId", "" + objectId);
        params.put("objectClassId", String.valueOf(ObjectClassID.OBJECT_CLASS_PROJECT));
//        params.put("ac", "" + ActionUtil.getActionId("GOTO_PROJECT_HOME", EnterpriseLinkDestination.GOTO_PROJECT_HOME));

        String url = EnterpriseLinkDestination.GOTO_PROJECT_HOME;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCategoryMoveUpLinkToEnterprise(Long categoryId) {
        HashMap params = new HashMap();
        params.put("categoryId", String.valueOf(categoryId));
        params.put("acName", "ACTION_CATEGORY_MOVE_UP");

        String url = EnterpriseLinkDestination.PROPOSAL_LIST;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCategoryMoveDownLinkToEnterprise(Long categoryId) {
        HashMap params = new HashMap();
        params.put("categoryId", String.valueOf(categoryId));
        params.put("acName", "ACTION_CATEGORY_MOVE_DOWN");

        String url = EnterpriseLinkDestination.PROPOSAL_LIST;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditCategoryLinkToEnterprise(Long categoryId) {
        HashMap params = new HashMap();
        params.put("categoryId", String.valueOf(categoryId));
        params.put("acName", "ACTION_UPDATE_CATEGORY");

        String url = EnterpriseLinkDestination.EDIT_CATEGORY;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDeleteCategoryLinkToEnterprise(Long categoryId) {
        HashMap params = new HashMap();
        params.put("categoryId", String.valueOf(categoryId));
        params.put("acName", "ACTION_DELETE_CATEGORY");

        String url = EnterpriseLinkDestination.PROPOSAL_LIST;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDetailsLogoLinkToEnterprise(Long logoId) {
        HashMap params = new HashMap();
        params.put("logo_id", String.valueOf(logoId));
        String url = EnterpriseLinkDestination.VIEW_LOGO;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCreateLogoLinkToEnterprise() {
        HashMap params = new HashMap();
        params.put("acName", "ACTION_CREATE_LOGO");

        String url = EnterpriseLinkDestination.EDIT_LOGO;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditLogoLinkToEnterprise(Long logoId) {
        HashMap params = new HashMap();
        params.put("logo_id", String.valueOf(logoId));
        params.put("acName", "ACTION_UPDATE_LOGO");

        String url = EnterpriseLinkDestination.EDIT_LOGO;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDeleteLogoLinkToEnterprise(Long logoId) {
        HashMap params = new HashMap();
        params.put("logo_id", String.valueOf(logoId));
        params.put("acName", "ACTION_DELETE_LOGO");

        String url = EnterpriseLinkDestination.PROPOSAL_LIST;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDetailsTemplateLinkToEnterprise(Long templateId) {
        HashMap params = new HashMap();
        params.put("template_id", String.valueOf(templateId));
        String url = EnterpriseLinkDestination.VIEW_TEMPLATE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCreateTemplateLinkToEnterprise() {
        HashMap params = new HashMap();
        params.put("acName", "ACTION_CREATE_TEMPLATE");

        String url = EnterpriseLinkDestination.EDIT_TEMPLATE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditTemplateLinkToEnterprise(Long templateId) {
        HashMap params = new HashMap();
        params.put("template_id", String.valueOf(templateId));
        params.put("acName", "ACTION_UPDATE_TEMPLATE");

        String url = EnterpriseLinkDestination.EDIT_TEMPLATE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDeleteTemplateLinkToEnterprise(Long templateId) {
        HashMap params = new HashMap();
        params.put("template_id", String.valueOf(templateId));
        params.put("acName", "ACTION_DELETE_TEMPLATE");

        String url = EnterpriseLinkDestination.PROPOSAL_LIST;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditSpecSummaryLinkToEnterprise(Long specSummaryId, Long specTypeId) {
        HashMap params = new HashMap();
        params.put("proposalSpecSummaryId", String.valueOf(specSummaryId));
        params.put("specTypeId", String.valueOf(specTypeId));
        params.put("acName", "ACTION_UPDATE_TEMPLATE");

        String url = EnterpriseLinkDestination.EDIT_SPEC_SUMMARY;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDeleteSpecSummaryLinkToEnterprise(Long specSummaryId, Long specTypeId) {
        HashMap params = new HashMap();
        params.put("proposalSpecSummaryId", String.valueOf(specSummaryId));
        params.put("specTypeId", String.valueOf(specTypeId));
        params.put("acName", "ACTION_DELETE_TEMPLATE");

        String url = EnterpriseLinkDestination.DELETE_SPEC_SUMMARY;
        return composeLinkToEnterprise(url, params);
    }

}
