package com.noosh.app.service.util;

import java.rmi.UnexpectedException;
import java.util.Iterator;
import java.util.Map;
import java.util.StringTokenizer;

/**
 * Represents a utility class for URL parsing and encoding.
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public final class URLUtil {

    private URLUtil() {
    }

    public static final String encode(String str) {
        return Encoder.encode(str);
    }

    public static final StringBuffer encode(String str, StringBuffer buffer) {
        return Encoder.encode(str, buffer);
    }

    public static final String encodeURIComponent(String str) {
        return encodeURIComponent(str, new StringBuffer()).toString();
    }

    //TODO: Can be improved to make a single pass through the string.
    /**
     * Encodes a string to the "x-www-form-urlencoded" form.  Then applies
     * fixups for compatibility with javascript:encodeURIComponent(String).
     * The resulting encoded string can be decoded by
     * javascript:decodeURIComponent(String) without modification.
     *
     * @param str The string to be encoded.
     * @param buffer The buffer to write to.
     * @return buffer
     * @throws NullPointerException If str is null.
     */
    public static final StringBuffer encodeURIComponent(String str, StringBuffer buffer) {
        String encoded = Encoder.encode(str);
        encoded = Util.replace(encoded, "+",   "%20");
        encoded = Util.replace(encoded, "%21", "!");
        encoded = Util.replace(encoded, "%27", "'");
        encoded = Util.replace(encoded, "%28", "(");
        encoded = Util.replace(encoded, "%29", ")");
        encoded = Util.replace(encoded, "%7E", "~");
        return buffer.append(encoded);
    }

    /**
     * Optimized encoder for HTML forms encoded with "x-www-form-urlencoded".
     * Mandates UTF-8 encoding.
     */
    private static final class Encoder {
        /** Maps ASCII char values to their corresponding char or hex escape string. */
        private static final String[] ASCII_HEX_ESCAPES = {
                "%00", "%01", "%02", "%03", "%04", "%05", "%06", "%07",
                "%08", "%09", "%0A", "%0B", "%0C", "%0D", "%0E", "%0F",
                "%10", "%11", "%12", "%13", "%14", "%15", "%16", "%17",
                "%18", "%19", "%1A", "%1B", "%1C", "%1D", "%1E", "%1F",
                // ' ' => '+'
                "+", "%21", "%22", "%23", "%24", "%25", "%26", "%27",
                // '*', '-', '.' => unreserved
                "%28", "%29", "*", "%2B", "%2C", "-", ".", "%2F",
                "0", "1", "2", "3", "4", "5", "6", "7",
                "8", "9", "%3A", "%3B", "%3C", "%3D", "%3E", "%3F",
                "%40", "A", "B", "C", "D", "E", "F", "G",
                "H", "I", "J", "K", "L", "M", "N", "O",
                "P", "Q", "R", "S", "T", "U", "V", "W",
                // '_' => unreserved
                "X", "Y", "Z", "%5B", "%5C", "%5D", "%5E", "_",
                "%60", "a", "b", "c", "d", "e", "f", "g",
                "h", "i", "j", "k", "l", "m", "n", "o",
                "p", "q", "r", "s", "t", "u", "v", "w",
                "x", "y", "z", "%7B", "%7C", "%7D", "%7E", "%7F"
        };

        /** Maps multi-byte UTF chars to hex escapes. */
        private static final String[] UTF_HEX_ESCAPES = {
                "%00", "%01", "%02", "%03", "%04", "%05", "%06", "%07",
                "%08", "%09", "%0A", "%0B", "%0C", "%0D", "%0E", "%0F",
                "%10", "%11", "%12", "%13", "%14", "%15", "%16", "%17",
                "%18", "%19", "%1A", "%1B", "%1C", "%1D", "%1E", "%1F",
                "%20", "%21", "%22", "%23", "%24", "%25", "%26", "%27",
                "%28", "%29", "%2A", "%2B", "%2C", "%2D", "%2E", "%2F",
                "%30", "%31", "%32", "%33", "%34", "%35", "%36", "%37",
                "%38", "%39", "%3A", "%3B", "%3C", "%3D", "%3E", "%3F",
                "%40", "%41", "%42", "%43", "%44", "%45", "%46", "%47",
                "%48", "%49", "%4A", "%4B", "%4C", "%4D", "%4E", "%4F",
                "%50", "%51", "%52", "%53", "%54", "%55", "%56", "%57",
                "%58", "%59", "%5A", "%5B", "%5C", "%5D", "%5E", "%5F",
                "%60", "%61", "%62", "%63", "%64", "%65", "%66", "%67",
                "%68", "%69", "%6A", "%6B", "%6C", "%6D", "%6E", "%6F",
                "%70", "%71", "%72", "%73", "%74", "%75", "%76", "%77",
                "%78", "%79", "%7A", "%7B", "%7C", "%7D", "%7E", "%7F",
                "%80", "%81", "%82", "%83", "%84", "%85", "%86", "%87",
                "%88", "%89", "%8A", "%8B", "%8C", "%8D", "%8E", "%8F",
                "%90", "%91", "%92", "%93", "%94", "%95", "%96", "%97",
                "%98", "%99", "%9A", "%9B", "%9C", "%9D", "%9E", "%9F",
                "%A0", "%A1", "%A2", "%A3", "%A4", "%A5", "%A6", "%A7",
                "%A8", "%A9", "%AA", "%AB", "%AC", "%AD", "%AE", "%AF",
                "%B0", "%B1", "%B2", "%B3", "%B4", "%B5", "%B6", "%B7",
                "%B8", "%B9", "%BA", "%BB", "%BC", "%BD", "%BE", "%BF",
                "%C0", "%C1", "%C2", "%C3", "%C4", "%C5", "%C6", "%C7",
                "%C8", "%C9", "%CA", "%CB", "%CC", "%CD", "%CE", "%CF",
                "%D0", "%D1", "%D2", "%D3", "%D4", "%D5", "%D6", "%D7",
                "%D8", "%D9", "%DA", "%DB", "%DC", "%DD", "%DE", "%DF",
                "%E0", "%E1", "%E2", "%E3", "%E4", "%E5", "%E6", "%E7",
                "%E8", "%E9", "%EA", "%EB", "%EC", "%ED", "%EE", "%EF",
                "%F0", "%F1", "%F2", "%F3", "%F4", "%F5", "%F6", "%F7",
                "%F8", "%F9", "%FA", "%FB", "%FC", "%FD", "%FE", "%FF"
        };

        /** Fast access char array version of the ASCII hex escapes. */
        private static final char[][] ASCII_HEX_ESCAPE_CACHE;
        /** Fast access char array version of the UTF hex escapes. */
        private static final char[][] UTF_HEX_ESCAPE_CACHE;

        static {
            ASCII_HEX_ESCAPE_CACHE = new char[ASCII_HEX_ESCAPES.length][];
            for (int c = 0; (c < ASCII_HEX_ESCAPES.length); c++) {
                ASCII_HEX_ESCAPE_CACHE[c] = ASCII_HEX_ESCAPES[c].toCharArray();
            }

            UTF_HEX_ESCAPE_CACHE = new char[UTF_HEX_ESCAPES.length][];
            for (int c = 0; (c < UTF_HEX_ESCAPES.length); c++) {
                UTF_HEX_ESCAPE_CACHE[c] = UTF_HEX_ESCAPES[c].toCharArray();
            }
        }

        private Encoder() {
        }

        public static final String encode(String str) {
            return encode(str, new StringBuffer()).toString();
        }

        /**
         * Encodes a string to the "x-www-form-urlencoded" form.
         * This method will produce the same output as:
         * java.net.URLEncoder.encode(str, "UTF-8")
         *
         * @param  str The string to be encoded.
         * @param  buffer The output buffer.
         * @return buffer
         * @throws NullPointerException If str or buffer are null.
         */
        public static final StringBuffer encode(String str, StringBuffer buffer) {
            char[] chars = str.toCharArray();
            int size = chars.length;
            buffer.ensureCapacity(buffer.length() + Math.round(size * 1.4f));
            for (int i = 0; i < size; i++) {
                encodeChar(chars[i], buffer);
            }
            return buffer;
        }

        /**
         * Encode a char to the "x-www-form-urlencoded" form, enhanced with the
         * UTF-8-in-URL proposal.
         *
         * <ul>
         * <li><p>The ASCII characters 'a' through 'z', 'A' through 'Z',
         *        and '0' through '9' remain the same.
         *
         * <li><p>The unreserved characters '-', '_', '.', '*' remain the same.
         *
         * <li><p>The space character ' ' is converted into a plus sign '+'.
         *
         * <li><p>All other ASCII characters are converted into the
         *        3-character string "%xy", where xy is
         *        the two-digit hexadecimal representation of the character
         *        code
         *
         * <li><p>All non-ASCII characters are encoded in two steps: first
         *        to a sequence of 2 or 3 bytes, using the UTF-8 algorithm;
         *        secondly each of these bytes is encoded as "%xx".
         * </ul>
         * @param ch The char to encode.
         * @param buffer A buffer to append the encoded char/string to.
         */
        private static final void encodeChar(char ch, StringBuffer buffer) {
            if (ch < 128) { // ASCII [0-127] (1 byte)
                char[] cache = ASCII_HEX_ESCAPE_CACHE[ch];
                if (cache.length == 1) { // NOTE: Avoid System.arrayCopy for single chars.
                    buffer.append(cache[0]);
                } else {
                    buffer.append(cache);
                }
            } else if (ch < 2048) { // ISO [128-255] and Unicode [256-2047] (2 bytes)
                buffer.append(UTF_HEX_ESCAPE_CACHE[0xc0 | (ch >> 6)]);
                buffer.append(UTF_HEX_ESCAPE_CACHE[0x80 | (ch & 0x3F)]);
            } else { // Unicode [2048-65535] (3 bytes)
                buffer.append(UTF_HEX_ESCAPE_CACHE[0xe0 | (ch >> 12)]);
                buffer.append(UTF_HEX_ESCAPE_CACHE[0x80 | ((ch >> 6) & 0x3F)]);
                buffer.append(UTF_HEX_ESCAPE_CACHE[0x80 | (ch & 0x3F)]);
            }
        }
    }


    public static final String decode(String str) throws Exception {
        return Decoder.decode(str);
    }

    public static final String decode(String str, String encoding) throws Exception {
        return Decoder.decode(str, encoding);
    }

    /**
     * Alias for javascript:decodeURIComponent(String) does not perform any
     * additional decoding fixups.  Calls {@link #decode(String)} internally.
     *
     * @param str
     * @return The decoded string.
     */
    public static final String decodeURIComponent(String str) throws Exception {
        return Decoder.decode(str);
    }

    /**
     * Wraps the java.net.URLDecoder until a faster implementation can be put
     * in place.
     */
    private static final class Decoder {
        private Decoder() {
        }

        /**
         * Mandates the string be UTF-8.
         * Calls {@link #decode(String, String)} internally.
         */
        public static final String decode(String str) throws Exception {
            return decode(str, "UTF-8");
        }

        /**
         * Decodes a string that was previously encoded into the
         * "x-www-form-urlencoded" form.
         *
         * @param  str The string to decode.
         * @param  encoding The character encoding of the input string.
         * @return The decoded string.
         */
        public static final String decode(String str, String encoding) throws Exception {
            try {
                return java.net.URLDecoder.decode(str, encoding);
            } catch(Exception e) {
                throw new Exception(e);
            }
        }
    }

    public static final String mapToQueryString(Map map) throws Exception {
        return (map.isEmpty()) ? "" : mapToQueryString(map, new StringBuffer()).toString();
    }

    /**
     * Converts a map of parameters into a query string.
     *
     * @param  map The parameter map.
     * @param  query
     * @return The query string, if map is empty returns an empty string.
     * @throws NullPointerException If map is null.
     */
    public static final StringBuffer mapToQueryString(Map map, StringBuffer query) throws Exception {
        // Pre allocate
        int size = map.size();
        query.ensureCapacity(query.length() + (size * 10));

        // Encode params
        for (Iterator entries = map.entrySet().iterator(); entries.hasNext(); ) {
            Map.Entry entry = (Map.Entry) entries.next();
            String key = (String) entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                query.append(key);
                query.append('=');
                URLUtil.encode((String) value, query);
                query.append('&');
            } else if (value instanceof Object[]) {
                Object[] array = (Object[]) value;
                for (int i = 0, arrayLength = array.length; i < arrayLength; i++) {
                    query.append(key);
                    query.append('=');
                    URLUtil.encode(array[i].toString(), query);
                    query.append('&');
                }
            } else {
                throw new UnexpectedException("Invalid object type: (" + value.getClass().getName() + ") with param: " + key);
            }
        }

        // Remove trailing '&'.
        if (size > 0) {
            query.setLength(query.length() - 1);
        }

        return query;
    }

    /**
     * Mandates the query segment be UTF-8 encoded.
     * Calls {@link #queryStringToMap(String, String, java.util.Map)} internally.
     */
    public static final Map<String, String> queryStringToMap(String queryString, Map parameters) throws Exception {
        return URLUtil.queryStringToMap(queryString, "UTF-8", parameters);
    }

    //TODO: Should handle leading '?'.
    /**
     * Populates a map with the parameters parsed from the query string.
     *
     * @param  queryString The query segment of a URL to parse parameters from.
     * @param  encoding The character encoding used on the URL query segment.
     * It will be used to choose a matching decoder.
     * @param  parameters The destination map to save parameters to.
     * @return parameters
     * @throws NullPointerException If encoding or parameters is null.
     */
    public static final Map queryStringToMap(String queryString, String encoding, Map parameters) throws Exception {
        // Check if there are parameters to process.
        if (queryString == null || queryString.length() == 0) {
            return parameters;
        }

        StringTokenizer st = new StringTokenizer(queryString, "&");
        while (st.hasMoreTokens()) {
            String pair = st.nextToken();
            int pairSeparatorIndex = pair.indexOf('=');
            if (pairSeparatorIndex == -1) {
                continue;
            }

            String key   = URLUtil.decode(pair.substring(0, pairSeparatorIndex), encoding);
            String value = URLUtil.decode(pair.substring(pairSeparatorIndex + 1, pair.length()), encoding).trim();

            if (parameters.containsKey(key)) {
                Object object = parameters.get(key);

                if (object instanceof String) {
                    parameters.put(key, new String[] { (String) object, value });
                } else if (object instanceof String[]) {
                    String oldValues[] = (String[]) object;
                    final int oldValuesLength = oldValues.length;
                    String values[] = new String[oldValuesLength + 1];
                    for (int i = 0; i < oldValuesLength; i++) {
                        values[i] = oldValues[i];
                    }
                    values[oldValuesLength] = value;
                    parameters.put(key, values);
                } else {
                    throw new UnexpectedException("Invalid object type: (" + object.getClass().getName() + ") with param: " + key);
                }
            } else {
                parameters.put(key, value);
            }
        }

        return parameters;
    }

}
