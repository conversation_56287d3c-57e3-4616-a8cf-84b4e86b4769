package com.noosh.app.service.util;

import jakarta.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public class MobileUtil {

    /**
     * Sync logic with front-end
     *
     * var jUserAgent = navigator.userAgent;
     * var jIsIphoneOs = /iphone os/i.test(jUserAgent),
     * jIsIpad = /ipad/i.test(jUserAgent),
     * jIsIpod = /ipod/i.test(jUserAgent),
     * jIsAndroid = /android/i.test(jUserAgent);
     * $rootScope.isMobileDevice = jIsIphoneOs || jIsIpad || jIsIpod || jIsAndroid;
     *
     * NOTE: this is not a best approach, we can find a rich util on internet or use spring mobile instead
     *
     * @param request
     * @return 1 == true, 0 == false, this is cooperated with Enterprise side
     */
    public static String isFromMobileDevice(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent").toLowerCase();

        if(userAgent.contains("iphone os") || userAgent.contains("ipad")
                || userAgent.contains("ipod") || userAgent.contains("android")) {
            return "1";
        }

        return "0";
    }

}
