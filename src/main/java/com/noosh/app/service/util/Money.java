package com.noosh.app.service.util;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * Money is a container for a monetary value composed of a numeric amount and
 * currency part.
 *
 * For performance reasons the table of allowed currencies is cached in memory.
 * Thus must be kept in sync with the AC_CURRENCY table.
 *
 * <AUTHOR>
 * @date 12/28/2021
 */
public class Money {
    public static final long CURRENCY_NOT_DEFINED = -1;

    private static Map symbols; // { currencyId => symbol, ... }
    private static String[] currencyIds;

    static {
        Money.symbols = new HashMap(30);
        // unicode symbols
        Money.symbols.put("USD", "\u0024");
        Money.symbols.put("EUR", "\u20AC");
        Money.symbols.put("GBP", "\u00A3");
        Money.symbols.put("CAD", "\u0024CAD");
        Money.symbols.put("AUD", "\u0024AUD");
        Money.symbols.put("NZD", "\u0024NZD");
        Money.symbols.put("ZAR", "\u0052");
        Money.symbols.put("INR", "\u0052\u0073");
        Money.symbols.put("CNY", "\u00A5");
        Money.symbols.put("HKD", "HK\u0024");
        Money.symbols.put("AED", "Dh");
        Money.symbols.put("THB", "\u0E3F");
        Money.symbols.put("IDR", "\u0052\u0070");
        Money.symbols.put("PHP", "\u20B1");
        Money.symbols.put("VND", "\u20AB");
        Money.symbols.put("JPY", "\u00A5");
        Money.symbols.put("SAR", "SAR");
        Money.symbols.put("PKR", "\u20A8");
        Money.symbols.put("NGN", "\u20A6");
        Money.symbols.put("KES", "KSh");
        Money.symbols.put("TWD", "NT\u0024");
        Money.symbols.put("CHF", "\u0043\u0048\u0046");
        Money.symbols.put("MXN", "\u0024MXN");
        Money.symbols.put("EGP", "E\u00A3");
        Money.symbols.put("TZS", "TSh");
        Money.symbols.put("SGD", "\u0024SGD");
        Money.symbols.put("MYR", "RM");

        Money.symbols.put("TRY", "\u20BA");
        Money.symbols.put("BGN", "\u043bB");
        Money.symbols.put("HRK", "kn");
        Money.symbols.put("CZK", "K\u010d");
        Money.symbols.put("DKK", "DKK");
        Money.symbols.put("HUF", "Ft");
        Money.symbols.put("NOK", "NOK");
        Money.symbols.put("PLN", "z\u0142");
        Money.symbols.put("RON", "lei");
        Money.symbols.put("RUB", "py\u0431");
        Money.symbols.put("RSD", "\u0414\u0438\u043d");
        Money.symbols.put("SEK", "SEK");
        Money.symbols.put("UAH", "\u20b4");
        Money.symbols.put("MAD", "DH");
        Money.symbols.put("DZD", "AD");
        Money.symbols.put("XAF", "CFAF");
        Money.symbols.put("ETB", "ETB");
        Money.symbols.put("TND", "TD");
        Money.symbols.put("UGX", "UGX");
        Money.symbols.put("ILS", "\u20aa");
        Money.symbols.put("JOD", "JD");
        Money.symbols.put("LBP", "\u00a3");
        Money.symbols.put("KRW", "\u20a9");
        Money.symbols.put("ZMW", "ZK");
        Money.symbols.put("MZN", "MZN");
        Money.symbols.put("BHD", "BHD");
        Money.symbols.put("KWD", "KWD");
        Money.symbols.put("OMR", "OMR");
        Money.symbols.put("QAR", "QAR");
        Money.symbols.put("BRL", "\u0052\u0024");

        // iso symbols
        Money.symbols.put("USD-ISO", "$");
        Money.symbols.put("EUR-ISO", String.valueOf((char) 0x80));
        Money.symbols.put("GBP-ISO", String.valueOf((char) 0xA3));
        Money.symbols.put("CAD-ISO", "$CAD");
        Money.symbols.put("AUD-ISO", "$AUD");
        Money.symbols.put("NZD-ISO", "$NZD");
        Money.symbols.put("ZAR-ISO", "R");
        Money.symbols.put("INR-ISO", "Rs");
        Money.symbols.put("CNY-ISO", String.valueOf((char) 0xA5));
        Money.symbols.put("HKD-ISO", "HK$");
        Money.symbols.put("AED-ISO", "Dh");
        Money.symbols.put("THB-ISO", "THB");
        Money.symbols.put("IDR-ISO", "Rp");
        Money.symbols.put("PHP-ISO", "P");
        Money.symbols.put("VND-ISO", String.valueOf((char) 0x20AB));
        Money.symbols.put("JPY-ISO", String.valueOf((char) 0xA5));
        Money.symbols.put("SAR-ISO", "SAR");
        Money.symbols.put("PKR-ISO", String.valueOf((char) 0x20A8));
        Money.symbols.put("NGN-ISO", String.valueOf((char) 0x20A6));
        Money.symbols.put("KES-ISO", "KSh");
        Money.symbols.put("TWD-ISO", "NT$");
        Money.symbols.put("CHF-ISO", "CHF");
        Money.symbols.put("MXN-ISO", "$MXN");
        Money.symbols.put("EGP-ISO", "E£");
        Money.symbols.put("TZS-ISO", "TSh");
        Money.symbols.put("SGD-ISO", "$SGD");
        Money.symbols.put("MYR-ISO", "RM");

        Money.symbols.put("TRY-ISO", String.valueOf((char) 0x20BA));
        Money.symbols.put("BGN-ISO", String.valueOf((char) 0x43B) + "B");
        Money.symbols.put("HRK-ISO", "kn");
        Money.symbols.put("CZK-ISO", "K" + String.valueOf((char) 0x10D));
        Money.symbols.put("DKK-ISO", "DKK");
        Money.symbols.put("HUF-ISO", "Ft");
        Money.symbols.put("NOK-ISO", "NOK");
        Money.symbols.put("PLN-ISO", "z" + String.valueOf((char) 0x142));
        Money.symbols.put("RON-ISO", "lei");
        Money.symbols.put("RUB-ISO", "py" + String.valueOf((char) 0x431));
        Money.symbols.put("RSD-ISO", String.valueOf((char) 0x414) + String.valueOf((char) 0x438) + String.valueOf((char) 0x43d));
        Money.symbols.put("SEK-ISO", "SEK");
        Money.symbols.put("UAH-ISO", String.valueOf((char) 0x20B4));
        Money.symbols.put("MAD-ISO", "DH");
        Money.symbols.put("DZD-ISO", "AD");
        Money.symbols.put("XAF-ISO", "CFAF");
        Money.symbols.put("ETB-ISO", "ETB");
        Money.symbols.put("TND-ISO", "TD");
        Money.symbols.put("UGX-ISO", "UGX");
        Money.symbols.put("ILS-ISO", String.valueOf((char) 0x20AA));
        Money.symbols.put("JOD-ISO", "JD");
        Money.symbols.put("LBP-ISO", String.valueOf((char) 0xA3));
        Money.symbols.put("KRW-ISO", String.valueOf((char) 0x20A9));
        Money.symbols.put("ZMW-ISO", "ZMW");
        Money.symbols.put("MZN-ISO", "MZN");
        Money.symbols.put("BHD-ISO", "BHD");
        Money.symbols.put("KWD-ISO", "KWD");
        Money.symbols.put("OMR-ISO", "OMR");
        Money.symbols.put("QAR-ISO", "QAR");
        Money.symbols.put("BRL-ISO", "R$");

        // Must match the order in the AC_CURRENCY table.
        Money.currencyIds = new String[]{"", "USD", "EUR", "GBP", "CAD", "AUD", "NZD", "ZAR", "INR", "CNY", "HKD", "AED", "THB", "IDR", "PHP", "VND", "JPY", "SAR", "PKR", "NGN", "KES", "TWD", "CHF", "MXN", "EGP", "TZS", "SGD", "MYR"
                , "TRY", "BGN", "HRK", "CZK", "DKK", "HUF", "NOK", "PLN", "RON", "RUB", "RSD", "SEK", "UAH", "MAD", "DZD", "XAF", "ETB", "TND", "UGX", "ILS", "JOD", "LBP", "KRW","ZMW", "MZN", "BHD", "KWD", "OMR", "QAR", "BRL"};
    }

    public static String getCurrencySymbol(String currency) {
        String symbol = (String) Money.symbols.get(currency);
        return (symbol == null) ? currency : symbol;
    }

    public static String getCurrencySymbolIso(String currency) {
        String symbol = (String) Money.symbols.get(currency + "-ISO");
        return (symbol == null) ? currency : symbol;
    }

    public static String getCurrency(long cid) {
        return (cid == Money.CURRENCY_NOT_DEFINED) ? null : Money.currencyIds[(int) cid];
    }
}
