package com.noosh.app.service.util;


import com.noosh.app.config.PropertiesUtil;

/**
 * User: leilaz
 * Date: 9/21/16
 */
public class URLEncryptor {

    public static final String ENCRYPTION_TOKEN = "$";
    public static final String ENCODED_ENCRYPTION_TOKEN = URLUtil.encode(ENCRYPTION_TOKEN);

    private static boolean enabled = PropertiesUtil.getHrefEncryption();

    public static void enable() {
        URLEncryptor.enabled = true;
    }

    public static boolean isEnabled() {
        return URLEncryptor.enabled;
    }

    public static String encrypt(String query) throws Exception {
        // check that there is data to encrypt - also prevent encrypting more than once
        if (!URLEncryptor.enabled || query == null || query.length() == 0 || parseEncryptionToken(query) != null) {
            return query;
        }
        String encrypted = ENCODED_ENCRYPTION_TOKEN + Crypto.encrypt(query);
        return encrypted;
    }

    public static String decrypt(String query) throws Exception {
        // check that there is data to decrypt
        if (!URLEncryptor.enabled || query == null || query.length() == 0) {
            return query;
        }
        // parse off the encryption token used - if there isn't one then we don't know how to decrypt it
        String encryptionToken = parseEncryptionToken(query);
        if (encryptionToken == null) {
            return query;
        }
        String unencrypted = "";
        // if it's a hybrid encrypted/decrypted url then extract the unencrypted segment
        int i = query.indexOf('&');
        if (i > -1) {
            unencrypted = query.substring(i);
            query = query.substring(0, i);
        }
        // trim off the encryption token
        query = query.substring(encryptionToken.length());
        // assemble the fully decrypted query string
        String decrypted = Crypto.decrypt(query).trim() + unencrypted;
        return decrypted;
    }

    public static String parseEncryptionToken(String query) {
        // check if the query string starts with: "%24"
        int tokenIndex = query.indexOf(ENCODED_ENCRYPTION_TOKEN);
        if (tokenIndex == 0) {
            return ENCODED_ENCRYPTION_TOKEN;
        }
        // check if the query string starts with: "$" (for backward compatibilty)
        tokenIndex = query.indexOf(ENCRYPTION_TOKEN);
        if (tokenIndex == 0) {
            return ENCRYPTION_TOKEN;
        }
        // not found
        return null;
    }


//    public static final class TestCase extends junit.framework.TestCase {
//        private String[] da;
//        private String[] ea;
//        private int iterations;
//
//        public TestCase(String name) {
//            super(name);
//            this.iterations = 1;
//        }
//
//        public TestCase configure(String[] args) {
//            if (args != null && args.length > 0) {
//                this.iterations = Integer.parseInt(args[0]);
//            }
//            return this;
//        }
//
//        protected void setUp() throws Exception {
//            com.noosh.nfc.system.Config.init();
//            com.noosh.nfc.security.Crypto.init();
//            URLEncryptor.enabled = true;
//
//            da = new String[] {
//                /*null,*/ "",
//                    "a", "a=1",
//                    "objectId=1234567&objectClassId=1000000"
//            };
//            ea = new String[] {
//                /*null,*/ "",
//                    "$", "%24", //NOTE: These mean don't encrypt an already encrypted string.
//                    "a", "$b", "%24c", "$&", "%24&", "$c=1", "%24c=1",
//                    "$supmOlpn8/k=",
//                    "$xPfM1MVqrFY=",
//                    "$7Ky5BfQ3C6xRs08VZRmXNO6zNJu/BzTPKMYFLCGx68uL1onJjJO1xA==",
//                    "%247Ky5BfQ3C6xRs08VZRmXNO6zNJu/BzTPKMYFLCGx68uL1onJjJO1xA==",
//                    "$c=1&d=2sdfsf",
//                    "$2G+pyDAFTCok3O52piHA+CL23MCI2ri4WGL5XVG2GUKWEDwHPa9mhTO2uaBqWc3/yhXl9HGlNsU/pmQONwVvvw==&test=1",
//                    "%242G+pyDAFTCok3O52piHA+CL23MCI2ri4WGL5XVG2GUKWEDwHPa9mhTO2uaBqWc3/yhXl9HGlNsU/pmQONwVvvw==&test=1"
//            };
//        }
//
//        public void test() {
//            for (int i = 0; i < this.iterations; i++) {
//                // encrypt
//                for (int d = 0; d < da.length; d++) {
//                    System.out.print("encrypting: |" + da[d] + "| = ");
//                    String encrypted = URLEncryptor.encrypt(da[d]);
//                    System.out.println(encrypted);
//                    String decrypted = URLEncryptor.decrypt(encrypted);
//                    if (!decrypted.equals(da[d])) {
//                        throw new AssertionError("Decrypted string: " + decrypted + " differs from original: " + da[d]);
//                    }
//                }
//
//                // decrypt
//                for (int e = 0; e < ea.length; e++) {
//                    System.out.print("decrypting: |" + ea[e] + "| = ");
//                    String decrypted = URLEncryptor.decrypt(ea[e]);
//                    System.out.println(decrypted);
//                    /*String encrypted = */URLEncryptor.encrypt(decrypted);
//                    /*if (!encrypted.equals(ea[e])) {
//                        throw new AssertionError("Encrypted string: " + encrypted + " differs from original: " + ea[e]);
//                    }*/
//                }
//            }
//        }
//    }
//
//    public static void main(String[] args) throws Throwable {
//        new TestCase("test").configure(args).runBare();
//    }
}
