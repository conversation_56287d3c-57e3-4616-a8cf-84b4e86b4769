package com.noosh.app.service.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @date 12/1/2021
 */
public class FileUtil {

    public static File inputStreamToFile(MultipartFile multiFile) {
        File file = new File(multiFile.getOriginalFilename());
        try(
            InputStream ins = multiFile.getInputStream();
            OutputStream os = new FileOutputStream(file);
        ) {
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return file;
    }

}
