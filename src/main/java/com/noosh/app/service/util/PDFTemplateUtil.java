package com.noosh.app.service.util;

import com.noosh.app.commons.dto.proposal.ProposalDTO;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.Version;
import org.springframework.util.ResourceUtils;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;
import java.net.URL;
import java.util.Locale;


/**
 * <AUTHOR>
 * @date 12/26/2021
 */
public class PDFTemplateUtil {

    public static ByteArrayOutputStream createPDF(ProposalDTO proposal, String templateFileName) {

        Configuration cfg = new Configuration(new Version("2.3.31"));
        cfg.setClassForTemplateLoading(PDFTemplateUtil.class, "/templates");
        cfg.setEncoding(Locale.ENGLISH, "UTF-8");
        ITextRenderer renderer = new ITextRenderer();

        try(OutputStream out = new ByteArrayOutputStream()) {
            // get template
            Template template = cfg.getTemplate(templateFileName, "UTF-8");
            StringWriter writer = new StringWriter();
            template.process(proposal, writer);
            writer.flush();

            String html = writer.toString();
            renderer.setDocumentFromString(html);

            final URL imagesFolder = PDFTemplateUtil.class.getClassLoader().getResource("images");
            if (imagesFolder != null) {
                String url = imagesFolder.toURI().toString();
                renderer.getSharedContext().setBaseURL(url);
            }
            renderer.layout();

            renderer.createPDF(out, false);
            renderer.finishPDF();
            out.flush();

            // remove the images under resources folder
            if (proposal.getLogo() != null) {
                File file = new File(ResourceUtils.getURL("classpath:").getPath() + "/images/" + proposal.getLogo().getUserFilename());
                if (file.isFile()) {
                    file.delete();
                }
            }

            return (ByteArrayOutputStream)out;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
