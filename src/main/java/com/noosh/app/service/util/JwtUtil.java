package com.noosh.app.service.util;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

/**
 * Get userId, workgroupId etc from JWT token.
 * Created by neals on 3/22/2020.
 */
public class JwtUtil {

    public static Long getUserId() {
        return (Long) get("userId");
    }

    public static Long getWorkgroupId() {
        return (Long) get("workgroupId");
    }

    private static Object get(String paramName) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof JwtAuthenticationToken) {
            Jwt jwt = (Jwt) authentication.getPrincipal();
            return jwt.getClaims().get(paramName);
        }

        return null;
    }


}
