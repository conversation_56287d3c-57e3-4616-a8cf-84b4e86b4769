package com.noosh.app.service.util;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Array;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


//AES/TODO: Rename all methods that join an array or list into a String to: join

public class Util {

    public Util() {
        return;
    }
    
	public static boolean equalLists(List<String> one, List<String> two) {
		if (one == null && two == null) {
			return true;
		}

		if ((one == null && two != null) || one != null && two == null || one.size() != two.size()) {
			return false;
		}

		// to avoid messing the order of the lists we will use a copy
		// as noted in comments by A. R. S.
		one = new ArrayList<String>(one);
		two = new ArrayList<String>(two);

		Collections.sort(one);
		Collections.sort(two);
		return one.equals(two);
	}

    /*
     * Array utilities:
     */

    //TODO: Use this method to remove all overloaded array joining methods.
    /**
     * @throws IllegalArgumentException If array is not an array.
     */
    private static String join(Object array, String separator, String quote, boolean trailingSeparator) {
        /*if (array instanceof Collection) {
            return Util.join((Collection) array, separator, quote, trailingSeparator);
        }*/
        int size = Array.getLength(array);
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < size; i++) {
            buffer.append(quote);
            buffer.append(Array.get(array, i));
            buffer.append(quote);
            buffer.append(separator);
        }
        return (size > 0 && !trailingSeparator)
                ? buffer.substring(0, buffer.length() - String.valueOf(separator).length())
                : buffer.toString();
    }

    //E.sirota 11/17/03
    //Needed for GUID searches.
    //Gave explicit function name to avoid potentially breaking other code.
    public static String arrayOfStringsToString(String[] array, String separator) {
        return Util.arrayOfStringsToString(array, separator, true /*putSeparatorAtEnd*/);
    }

    public static String arrayOfStringsToString(String[] array, String separator, boolean putSeparatorAtEnd) {
        StringBuffer buffer = new StringBuffer();
        for (int i = 0, size = array.length, last = size - 1; i < size; i++) {
            buffer.append('\'');
            buffer.append(array[i]);
            buffer.append('\'');
            if (i < last || putSeparatorAtEnd) {
                buffer.append(separator);
            }
        }
        return buffer.toString();
    }

    public static String arrayToString(Object[] array, String separator) {
        return Util.arrayToString(array, separator, true /*putSeparatorAtEnd*/);
    }

    public static String arrayToString(Object[] array, String separator, boolean putSeparatorAtEnd) {
        StringBuffer buffer = new StringBuffer();
        for (int i = 0, size = array.length, last = size - 1; i < size; i++) {
            buffer.append(String.valueOf(array[i]));
            if (i < last || putSeparatorAtEnd) {
                buffer.append(separator);
            }
        }
        return buffer.toString();
    }

    public static String arrayToString(long[] array, String separator) {
        return Util.arrayToString(array, separator, true /*putSeparatorAtEnd*/);
    }

    public static String arrayToString(long[] array, String separator, boolean putSeparatorAtEnd) {
        StringBuffer buffer = new StringBuffer();
        for (int i = 0, size = array.length, last = size - 1; i < size; i++) {
            buffer.append(String.valueOf(array[i]));
            if (i < last || putSeparatorAtEnd) {
                buffer.append(separator);
            }
        }
        return buffer.toString();
    }

    public static Long[] toLongArray(long[] longs) {
        if (longs == null) {
            return null;
        }
        int size = longs.length;
        Long[] longObjects = new Long[size];
        for (int i = 0; i < size; i++) {
            longObjects[i] = new Long(longs[i]);
        }
        return longObjects;
    }

    public static long[] toLongArray(Long[] longObjects) {
        if (longObjects == null) {
            return null;
        }
        int size = longObjects.length;
        long[] longs = new long[size];
        for (int i = 0; i < size; i++) {
            longs[i] = longObjects[i].longValue();
        }
        return longs;
    }

    public static Long[] toLongArray(String[] strings) {
        int size = strings.length;
        Long[] array = new Long[size];
        for (int i = 0; i < size; i++) {
            array[i] = new Long(strings[i]);
        }
        return array;
    }

    public static long[] stringArrayToLongArray(String[] strings) {
        int size = strings.length;
        long[] array = new long[size];
        for (int i = 0; i < size; i++) {
            array[i] = Long.parseLong(strings[i]);
        }
        return array;
    }

    /*
     * Object utilities:
     */

    /** @deprecated */
    public static String stringOf(Object obj) {
        return (obj == null) ? "" : obj.toString();
    }

    /** @deprecated */
    public static boolean isNullOrEmpty(Object obj) {
        return (obj == null || obj.toString().length() == 0);
    }

    /** @deprecated */
    public static String getClassLabel(Object obj) {
        if (obj == null) {
            return "<null>";
        }
        String className = obj.getClass().getName();
        int comnoosh = "com.noosh.".length();
        return className.substring(className.lastIndexOf('.') + 1)
                + "(" + className.substring(comnoosh, className.indexOf('.', comnoosh)) + ")";
    }

    public static String getShortClassName(Object obj) {
        if (obj == null) {
            return "<null>";
        }
        return Util.shortClassNameOf(obj.getClass().getName());
    }



    /*
     * String utilities:
     */

    public static boolean isNullOrEmpty(String str) {
        return (str == null || str.length() == 0);
    }

    public static String shortClassNameOf(String className) {
        int pos = className.lastIndexOf('.');
        return (pos > -1) ? className.substring(pos + 1) : className;
    }

    /** @deprecated */
    public static boolean stringIsIn(String item, String list, String separator) {
        String str = separator + list + separator;
        int pos = str.indexOf(separator + item + separator);
        return (pos > -1);
    }

    public static String[] pairOf(String str, String delimiter) {
        return Util.pairOfFrom(str, delimiter, true /*left*/);
    }

    public static String[] lastPairOf(String str, String delimiter) {
        return Util.pairOfFrom(str, delimiter, false /*right*/);
    }

    /**
     * Divides a string into 2 separate pieces.
     *
     * @param  str The input string to parse.
     * @param  delimiter The string to split on, this is specified as a literal non-regex and non-set based string.
     * @param  searchFrom Whether to start searching from the left (true) or right (false) side of the string.
     * @return If the string was split on the delimiter returns a 2-element array.
     * If either the input string or delimiter are null or the delimiter was not
     * found an empty array is returned.
     */
    private static String[] pairOfFrom(String str, String delimiter, boolean searchFrom) {
        if (str == null || delimiter == null) {
            return new String[0];
        }
        int pos = (searchFrom) ? str.indexOf(delimiter) : str.lastIndexOf(delimiter);
        if (pos == -1) {
            return new String[0];
        }
        String[] pair = new String[] {
                str.substring(0, pos),
                str.substring(pos + delimiter.length())
        };
        return pair;
    }

    /**
     * Overloaded version of split that splits on whitespace.
     *
     * @param  str The input string to parse.
     * @return An array of string tokens not including any characters from the
     * input string.  If no delimiters were found a single element array consisting of the input string.
     * @throws NullPointerException If str is null.
     * @see #split(String, String)
     */
    public static String[] split(String str) {
        return Util.split(str, " \t\n\r\f");
    }

    /**
     * Splits a string into n number of tokens.  Uses StringTokenizer internally
     * to perform the tokenization.
     *
     * @param  str The input string to parse.
     * @param  delimiterSet The delimiter set to split on, each char within the
     * set is a separate delimiter similar to the regex set [] operator.
     * @return An array of string tokens not including any characters from the
     * input string.  If no delimiters were found a single element array consisting of the input string.
     * @throws NullPointerException If either str or delimiterSet are null.
     * @see java.util.StringTokenizer
     */
    public static String[] split(String str, String delimiterSet) {
        StringTokenizer tokenizer = new StringTokenizer(str, delimiterSet);
        List tokens = new ArrayList();
        while (tokenizer.hasMoreTokens()) {
            tokens.add(tokenizer.nextToken());
        }
        return (String[]) tokens.toArray(new String[tokens.size()]);
    }

    /** @deprecated Use: Util.split(String str, String delimiterSet) */
    public static String[] stringToArray(String str, String delimiterSet) {
        return Util.split(str, delimiterSet);
    }

    public static long[] stringToLongArray(String str, String delimiterSet) {
        String[] array = Util.split(str, delimiterSet);
        int size = array.length;
        long longArray[] = new long[size];
        for (int i = 0; i < size; i++) {
            longArray[i] = Long.parseLong(array[i]);
        }
        return longArray;
    }

    public static Long[] stringToLongObjectArray(String str, String delimiterSet) {
        String[] array = Util.split(str, delimiterSet);
        int size = array.length;
        Long longArray[] = new Long[size];
        for (int i = 0; i < size; i++) {
            longArray[i] = new Long(array[i]); //Long.parseLong(array[i]);
        }
        return longArray;
    }

    public static double[] stringToDoubleArray(String str, String delimiterSet) {
        String[] array = Util.split(str, delimiterSet);
        int size = array.length;
        double doubleArray[] = new double[size];
        for (int i = 0; i < size; i++) {
            doubleArray[i] = Double.parseDouble(array[i]);
        }
        return doubleArray;
    }

    public static Double[] stringToDoubleObjectArray(String str, String delimiterSet) {
        String[] array = Util.split(str, delimiterSet);
        int size = array.length;
        Double doubleArray[] = new Double[size];
        for (int i = 0; i < size; i++) {
            doubleArray[i] = new Double(array[i]);
        }
        return doubleArray;
    }

    /**
     * Overloaded version of cut that will always return an array of fields
     * large enough to fully accommodate the string.
     *
     * @param  str The string to create fields from.
     * @param  fieldLength The length of each field created.
     * @return A string array of fields or an empty array if the input string was null.
     * @see #cut(String, int, int, boolean)
     */
    public static String[] cut(String str, int fieldLength) {
        return Util.cut(str, fieldLength, 0, false /*markTruncated*/);
    }

    /**
     * Creates an array of string fields out of a single string.  Each field
     * created will be <= fieldLength in size.  Stops creating fields if the
     * maximum number of fields is hit before exhausting the string.
     * If maxFieldCount is <= 0 or greater than the actual field count it will be set to field count.
     *
     * @param  str The string to create fields from.
     * @param  fieldLength The length of each field created.
     * @param  maxFieldCount The maximum number of fields that can be created.
     * @param  markTruncated A flag to determine if the last field is marked as
     * truncated (has "..." appended) if maxFieldCount was reached before exhausting the string.
     * @return A string array of fields or an empty array if the input string was null.
     * i.e.: cut("12345", 2, 1, false) => [ "12" ]
     *       cut("12345", 2, 2, false) => [ "12", "34" ]
     *       cut("12345", 2, 3, false) => [ "12", "34", "5" ]
     */
    public static String[] cut(String str, int fieldLength, int maxFieldCount, boolean markTruncated) {
        if (str == null) {
            return new String[0];
        }

        str = str.trim();
        int strLength = str.length();
        // Return immediately if the string fits into a single field.
        if (fieldLength <= 0 || strLength <= fieldLength) {
            return new String[] { str };
        }

        // If maxFieldCount is invalid then correct it.
        int fieldCount = Math.round((float) Math.ceil((float) strLength / (float) fieldLength));
        if (maxFieldCount <= 0 || maxFieldCount > fieldCount) {
            maxFieldCount = fieldCount;
        }

        String[] fields = new String[maxFieldCount];
        for (int f = 0; f < maxFieldCount; f++) {
            String field = null;
            // Create a field of the specified size.
            if (strLength > fieldLength) {
                if (markTruncated && (f + 1) >= maxFieldCount) {
                    field = truncate(str, fieldLength, true, true);
                } else {
                    field = str.substring(0, fieldLength);
                }
                str = str.substring(fieldLength);
            } else {
                // The string is exhausted so create a field containing the remainder of it.
                field = str;
                str = str.substring(strLength);
            }
            fields[f] = field;
            strLength = str.length();
        }

        return fields;
    }

    /**
     * Trims all the elements of the input array.
     *
     * @param strings
     * @return strings
     * @throws NullPointerException If strings is null.
     */
    public static String[] trim(String[] strings) {
        for (int i = 0, size = strings.length; i < size; i++) {
            strings[i] = strings[i].trim();
        }
        return strings;
    }

    /**
     * Performs a literal search and replace of all instances of the search
     * string with the replacement string within the input string.
     *
     * @param  str The source string to be processed.
     * @param  search The literal string to search for within str.
     * @param  replacement The string to replace search with, set to null or "" for removal.
     * @return The resulting string, or the input str if no instances of the search string were found.
     * <AUTHOR> (Amit)
     * @throws NullPointerException If either str or search are null.
     */
    public static String replace(String str, String search, String replacement) {
        int next = str.indexOf(search);
        if (next == -1) {
            return str;
        }
        if (replacement == null) {
            replacement = "";
        }
        StringBuffer buffer = new StringBuffer(str.length());
        int searchLength = search.length();
        int index = 0;
        while (next > -1) {
            buffer.append(str.substring(index, next));
            buffer.append(replacement);
            index = next + searchLength;
            next = str.indexOf(search, index);
        }
        buffer.append(str.substring(index));
        return buffer.toString();
    }

    /**
     * Performs a literal removal of all instances of the search string.
     *
     * @param  str The source string to be processed.
     * @param  search The literal string to search for within str.
     * @return The resulting string, or the input str if no instances of the search string were found.
     * @throws NullPointerException If either str or search are null.
     */
    public static String remove(String str, String search) {
        return Util.replace(str, search, null /*removal*/);
    }

    /**
     * Removes all characters in the delimiter set from the source string.
     *
     * @param  str The input string to parse.
     * @param  delimiterSet The delimiter set to remove, each char within the
     * set is a separate delimiter similar to the regex set [] operator.
     * @return The string with all chars in the delimiter set removed.
     * @throws NullPointerException If either str or delimiterSet are null.
     * @see java.util.StringTokenizer
     */
    public static String removeSet(String str, String delimiterSet) {
        StringTokenizer tokenizer = new StringTokenizer(str, delimiterSet);
        StringBuffer buffer = new StringBuffer(str.length());
        while (tokenizer.hasMoreTokens()) {
            buffer.append(tokenizer.nextToken());
        }
        return buffer.toString();
    }

    public static String replaceFirst(String str, String search, String replacement) {
        return Util.replaceFirstFrom(str, search, replacement, true /*left*/);
    }

    public static String replaceLast(String str, String search, String replacement) {
        return Util.replaceFirstFrom(str, search, replacement, false /*right*/);
    }

    /**
     * Replaces the first occurrence of a literal string with a different string.
     *
     * @param  str The source string to be processed.
     * @param  search The string to search for within str.
     * @param  replacement The string to replace search with, set to null or "" for deletion.
     * @param  searchFrom Whether to start searching from the left (true) or right (false) side of the string.
     * @return The modified string, or the input string if the search string was not found.
     * @throws NullPointerException If the source or search string are null.
     */
    private static String replaceFirstFrom(String str, String search, String replacement, boolean searchFrom) {
        int index = (searchFrom) ? str.indexOf(search) : str.lastIndexOf(search);
        if (index == -1) {
            return str;
        }
        if (replacement == null) {
            replacement = "";
        }
        StringBuffer buffer = new StringBuffer(str.length());
        buffer.append(str.substring(0, index));
        buffer.append(replacement);
        buffer.append(str.substring(index + search.length()));
        return buffer.toString();
    }

    public static String truncate(String str, int maxLength) {
        return Util.truncate(str, maxLength, true /*maxIncludesDots*/, true /*useDots*/);
    }

    public static String truncate(String str, int maxLength, boolean maxIncludeDots) {
        return Util.truncate(str, maxLength, maxIncludeDots, true /*useDots*/);
    }

    /**
     * Truncates a string to a maximum length.
     *
     * @param  str The string to truncate.
     * @param  maxLength The maximum length of the string.
     * @param  maxIncludesDots Whether the maxLength includes the truncation marker "...".
     * @param  useDots Whether to use the truncation marker "...".
     * @return The truncated string or if maxLength is >= the length of the string returns str as is.
     * Returns null if str is null.
     */
    public static String truncate(String str, int maxLength, boolean maxIncludesDots, boolean useDots) {
        if (str == null) {
            return null;
        }
        str = str.trim();
        int strLength = str.length();
        if (!useDots && strLength > maxLength) {
            str = str.substring(0, maxLength);
        } else if (useDots && strLength > maxLength && strLength >= 3 && maxLength >= 3) {
            if (maxIncludesDots) {
                maxLength -= 3;
            }
            str = str.substring(0, maxLength) + "...";
        }
        return str;
    }

    /**
     * Breaks a string at a point where a length of characters greater
     * than segmentLength are not soft broken by a space, if any.
     * If str does not contain any spaces, then call truncate normally.
     * If str does not contain any non-soft-broken segments greater
     * than segmentLength, then the logic simply returns str.
     *
     * @param  str String to be processed
     * @param  segmentLength the max length of non-soft-broken characters
     * @return processed String
     *
     * <AUTHOR> Jerahian
     * TODO: LB/REVIEW: At some point in the future, replace this method
     *       with one called hyphenate that will hyphenate str (which is
     *       considered a soft break in HTML) and achieve the same end
     *       result without actually truncating the string.
     */
    public static String breakString(String str, int segmentLength) {
        if (str == null) {
            return null;
        }
        int i = str.indexOf(' ');
        if (i == -1) {
            return Util.truncate(str, segmentLength);
        }

        int strLength = str.length();
        int prevIndex = 0;
        while (i != -1) {
            if (i - prevIndex >= segmentLength) {
                int truncIndex = prevIndex + segmentLength - 3;
                return str.substring(0, (truncIndex > 0) ? truncIndex : 1) + "...";
            }
            prevIndex = i;
            if (i + 1 < strLength) {
                i = str.indexOf(' ', i + 1);
            } else {
                i = -1;
            }
        }
        return str;
    }

    /**
     * Transforms the string into title case by first converting it to lower
     * case then converting the first character to upper case.
     * Works exactly like toTitleCase(str, Character.MIN_VALUE, Character.MIN_VALUE);
     *
     * @param  str The string to process.
     * @return Title case string, if str is null or empty it is returned as is.
     * i.e.: toTitleCase("CONSTANT_TOKEN") => "Constant_token"
     */
    public static String toTitleCase(String str) {
        return Util.toTitleCase(str, Character.MIN_VALUE, Character.MIN_VALUE);
    }

    /**
     * Transforms the string into title case by first converting it to lower
     * case, then converting to upper case the first character of each word
     * boundary.  The first character of the string will always be
     * converted regardless of word boundaries.
     *
     * @param  str The string to process.
     * @param  boundary The word boundary.
     * @param  replacement The replacement for the word boundary.
     * @return Title case string, if str is null or empty it is returned as is.
     * i.e.: toTitleCase("CONSTANT_TOKEN", '_', ' ') => "Constant Token"
     */
    public static String toTitleCase(String str, char boundary, char replacement) {
        if (str == null || str.length() == 0) {
            return str;
        }
        char[] chars = str.toLowerCase().toCharArray();
        for (int i = 0, size = chars.length; i < size; i++) {
            if (chars[i] == boundary) {
                chars[i] = replacement;
            } else if (i == 0 || chars[i - 1] == replacement) {
                chars[i] = Character.toTitleCase(chars[i]);
            }
        }
        return new String(chars);
    }

    /**
     * Converts all win32 CRLF "\r\n" to unix LF "\n".
     *
     * @param  str The string to process.
     * @return The string with unix style line feeds.
     * @throws NullPointerException If str is null.
     */
    public static String toUnixStyle(String str) {
        char[] source = str.toCharArray();
        int size = source.length;
        char[] result = new char[size];
        int j = 0;
        for (int i = 0; i < size; i++) {
            if (source[i] == '\r') {
                continue;
            }
            result[j++] = source[i];
        }
        return new String(result, 0, j);
    }

    public static String toCsv(String[] fields) {
        StringBuffer buffer = new StringBuffer();
        for (int i = 0, size = fields.length; i < size; i++) {
            buffer.append(Util.replace(fields[i], ",", "\\,"));
            if (i < size - 1) {
                buffer.append(',');
            }
        }
        return buffer.toString();
    }

    public static String[] fromCsv(String str) {
        str = Util.replace(str, "\\,", "<comma>");
        // Must use String.split because it handles empty fields properly.
        String[] fields = str.split(",");
        for (int i = 0, size = fields.length; i < size; i++) {
            fields[i] = Util.replace(fields[i], "<comma>", ",").trim();
        }
        return fields;
    }

    /**
     * Processes a string template by replacing var tokens with values from the
     * context (map).  Assumes the shell variable syntax of ${var}
     *
     * @param template
     * @param context
     * @return String
     * @throws NullPointerException if template is null.
     */
    public static String processTemplate(String template, Map context) {
        return processTemplate(template, context, "${", "}");
    }

    /**
     * Processes a string template by replacing var tokens with values from the
     * context (map).
     *
     * @param template
     * @param context
     * @param varBegin
     * @param varEnd
     * @return String
     * @throws NullPointerException if template is null.
     */
    public static String processTemplate(String template, Map context, String varBegin, String varEnd) {
        StringBuffer buffer = new StringBuffer(template.length());
        if (context == null) {
            context = Collections.EMPTY_MAP;
        }
        int i;
        int i0 = 0;
        while ((i = template.indexOf(varBegin, i0)) > -1) {
            int j = template.indexOf(varEnd, i);
            String param = template.substring(i + varBegin.length(), j);
            buffer.append(template.substring(i0, i));
            buffer.append(context.get(param));
            i0 = j + varEnd.length();
        }
        buffer.append(template.substring(i0));
        return buffer.toString();
    }


    /*
     * Throwable utilities:
     */

    public static String getStackTrace(Throwable t) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw, true /*autoflush*/);
        t.printStackTrace(pw);
        return sw.toString();
    }



    /*
     * Collection utilities:
     */

    public static String join(Collection items, String separator) {
        StringBuffer buffer = new StringBuffer(17 + (items.size() * 8));
        for (Iterator i = items.iterator(); i.hasNext(); ) {
            buffer.append(i.next());
            if (i.hasNext()) {
                buffer.append(separator);
            }
        }
        return buffer.toString();
    }



    /*
     * List utilities
     */

    /**
     * Converts an object to a list.
     * Supports primitive[] arrays, Object[] arrays, and Collections.
     * Forwards to Arrays.asList for primitive[] and Object[] arrays.
     *
     * @param  source The source object to convert.
     * @return Source converted to a list, if source is already a list it is
     * returned as is.
     * @throws NullPointerException If source is null.
     * @throws IllegalArgumentException If source cannot be converted to a list.
     */
    public static List asList(Object source) {
        if (source == null) {
            throw new NullPointerException("Cannot convert null to a List.");
        }

        List list;
        if (source instanceof List) {
            list = (List) source;
        } else if (source instanceof Collection) {
            list = new ArrayList((Collection) source);
        } else if (source instanceof Object[]) {
            list = Arrays.asList((Object[]) source);
        } else if (source.getClass().isArray()) {
            // Convert primitive[] arrays.
            int length = Array.getLength(source);
            if (length == 0) {
                // Cannot determine component type so use empty list.
                list = Collections.EMPTY_LIST;
            } else {
                // Promote to Object[] array with equivalent wrapper objects.
                Class componentType = Array.get(source, 0).getClass();
                Object[] dest = (Object[]) Array.newInstance(componentType, length);
                for (int i = 0; i < length; i++) {
                    dest[i] = Array.get(source, i);
                }
                list = Arrays.asList(dest);
            }
        } else {
            throw new IllegalArgumentException("Unsupported list conversion of: " + source.getClass().getName());
        }

        return list;
    }

    public static List sublist(List source, int beginIndex, int endIndex) {
        if (endIndex > source.size()) {
            endIndex = source.size();
        }
        int noOfEntries = (endIndex > beginIndex) ? endIndex - beginIndex : 0;
        List newList = new ArrayList(noOfEntries + 1);
        if (beginIndex >= 0 && beginIndex < endIndex ) {
            for (int cInd = beginIndex; cInd < endIndex; cInd++) {
                newList.add(source.get(cInd));
            }
        }
        return newList;
    }



    /*
     * Set utilities
     */

    /**
     * Creates a union of two sets. The union is defined as all elements
     * in set A plus all elements in set B.  S = (A + B)
     *
     * @param  alpha
     * @param  beta
     * @return set
     */
    public static Set unionSet(Collection alpha, Collection beta) {
        Set union = new HashSet(alpha.size() + beta.size());
        union.addAll(alpha);
        union.addAll(beta);
        return union;
    }

    /**
     * Creates a set containing the intersection of two sets.  The intersection
     * is defines as all elements in A that exist in B and all elements in B
     * that exist in A.
     *
     * @param  alpha
     * @param  beta
     * @return set
     */
    public static Set intersectionSet(Collection alpha, Collection beta) {
        Set intersection = new HashSet(alpha);
        intersection.retainAll(beta);
        return intersection;
    }

    /**
     * AKA: asymmetric difference
     * Depending on the order of the parameters you will get either A - B or B - A.
     *
     * @param  alpha
     * @param  beta
     * @return set
     */
    public static Set differenceSet(Collection alpha, Collection beta) {
        Set difference = new HashSet(alpha);
        difference.removeAll(beta);
        return difference;
    }

    /**
     * Creates a set of differences from both sets.  The set returned will
     * contain all non-intersecting elements.
     *
     * @param  alpha
     * @param  beta
     * @return set
     */
    public static Set symmetricDifferenceSet(Collection alpha, Collection beta) {
        Set symmetricDifference = new HashSet();
        symmetricDifference.addAll(Util.differenceSet(alpha, beta));
        symmetricDifference.addAll(Util.differenceSet(beta, alpha));
        return symmetricDifference;
    }

    //AES/REVIEW: Remove this method the equals implementation on AbstractSet is superior.
    /**
     * Determines if two sets are equal.
     *
     * @param  alpha
     * @param  beta
     * @return true if the sets are equal, false otherwise.
     */
    public static boolean equalsSet(Set alpha, Set beta) {
        return (alpha.containsAll(beta) && beta.containsAll(alpha));
    }



    /*
     * Map utilities:
     */

    public static Map getAttributeMap(String str) {
        return Util.getAttributeMap(str, " ", "=");
    }

    /**
     * Parses out name/value attribute pairs from the specified string.
     *
     * @param  str The string to parse in the form of: "a=b, c=d, x=y"
     * @param  pairSeparator The string used to separate attribute pairs.
     * @param  valueSeparator The string used to separate attribute names from attribute values.
     * @return The attribute map in the form of: { a=b, c=d, x=y }
     * @throws NullPointerException If the source string or separator strings are null.
     */
    public static Map getAttributeMap(String str, String pairSeparator, String valueSeparator) {
        StringTokenizer tokenizer = new StringTokenizer(str, pairSeparator);
        Map attributes = new HashMap();
        while (tokenizer.hasMoreTokens()) {
            String[] pair = Util.split(tokenizer.nextToken(), valueSeparator);
            if (pair.length == 2) {
                attributes.put(pair[0], pair[1]);
            }
        }
        return attributes;
    }

    public static Map getAttributeMap(String str, String pairSeparator, String valueSeparator, boolean includeBlankValue) {
        StringTokenizer tokenizer = new StringTokenizer(str, pairSeparator);
        Map attributes = new HashMap();
        while (tokenizer.hasMoreTokens()) {
            String[] pair = Util.split(tokenizer.nextToken(), valueSeparator);
            if (includeBlankValue) {
                if (pair.length == 1) {
                    attributes.put(pair[0], "");
                }
                else if (pair.length == 2) {
                    attributes.put(pair[0], pair[1]);
                }
            }
            else {
                if (pair.length == 2) {
                    attributes.put(pair[0], pair[1]);
                }
            }
        }
        return attributes;
    }

    public static Map prefixMap(Map source, String prefix) {
        return Util.affixMap(source, prefix, true /*prefix*/);
    }

    public static Map suffixMap(Map source, String suffix) {
        return Util.affixMap(source, suffix, false /*suffix*/);
    }

    /**
     * Adds an affix string to each key within a map.
     *
     * @param  source The source map to process.
     * @param  affix The string to add to each key.
     * @param  affixedFrom Whether to add the string a prefix (true) or suffix (false).
     * @return A map with the same values as the source map with each key
     * starting with the specified affix.
     * @throws NullPointerException If the source map is null.
     */
    private static Map affixMap(Map source, String affix, boolean affixedFrom) {
        Map dest = new HashMap();
        for (Iterator keys = source.keySet().iterator(); keys.hasNext(); ) {
            Object key = keys.next();
            Object value = source.get(key);
            if (affixedFrom) {
                dest.put(affix + key.toString(), value);
            } else {
                dest.put(key.toString() + affix, value);
            }
        }
        return dest;
    }

    //TODO: Unify filterMap variants.

    public static Map filterMap(Map source, String pattern) {
        return Util.filterMap(source, pattern, true /*left*/, false /*dontstripkeys*/);
    }

    /**
     * Finds a subset of the map containing keys that match the search string.
     *
     * @param  source The source map to process.
     * @param  search The search string to match against each key.
     * @param  whence Whether to search from the left (true) or right (false) side of the key string. (true=startsWith,false=endsWith)
     * @param  stripKeys Whether the search string will be stripped off the keys in the resulting map.
     * @return The filtered map, or if no keys matched the search string returns an empty map.
     * @throws NullPointerException If the source map or search string are null.
     */
    public static Map filterMap(Map source, String search, boolean whence, boolean stripKeys) {
        Map dest = new HashMap();
        for (Iterator keys = source.keySet().iterator(); keys.hasNext(); ) {
            Object key = keys.next();
            // Handle null keys by converting null => "null"
            String keyStr = String.valueOf(key);
            if (whence && keyStr.startsWith(search)) {
                Object value = source.get(key);
                if (stripKeys) {
                    // Convert key to a string if we are stripping off search string.
                    key = keyStr.substring(search.length());
                }
                dest.put(key, value);
            } else if (!whence && keyStr.endsWith(search)) {
                Object value = source.get(key);
                if (stripKeys) {
                    // Convert key to a string if we are stripping off search string.
                    key = keyStr.substring(0, keyStr.length() - search.length());
                }
                dest.put(key, value);
            }
        }
        return dest;
    }

    public static Map filterMapIndexOf(Map attrs, String pattern) {
        Map dest = new HashMap();
        for (Iterator keys = attrs.keySet().iterator(); keys.hasNext(); ) {
            String key = (String) keys.next();
            if (key.indexOf(pattern) > -1) {
                dest.put(key, attrs.get(key));
            }
        }
        return dest;
    }

    public static Map filterMapRegex(Map source, String regex) {
        return Util.filterMapRegex(source, Pattern.compile(regex), false /*stripKeys*/);
    }

    public static Map filterMapRegex(Map source, String regex, boolean stripKeys) {
        return Util.filterMapRegex(source, Pattern.compile(regex), stripKeys);
    }

    public static Map filterMapRegex(Map source, Pattern pattern, boolean stripKeys) {
        Map dest = new HashMap();
        for (Iterator keys = source.keySet().iterator(); keys.hasNext(); ) {
            Object key = keys.next();
            // Handle null keys by converting null => "null"
            String keyStr = String.valueOf(key);
            Matcher matcher = pattern.matcher(keyStr);
            if (matcher.matches()) {
                Object value = source.get(key);
                if (stripKeys) {
                    // Convert key to a string if we are stripping off search string.
                    key = matcher.replaceFirst("");
                }
                dest.put(key, value);
            }
        }
        return dest;
    }

    public static int randomWithRange(int min, int max) {
        int range = Math.abs(max - min) + 1;
        return (int) (Math.random() * range) + (min <= max ? min : max);
    }

    public static String getUserTimezone() {
        TimeZone localTimeZone = TimeZone.getDefault();
        int rawOffset = localTimeZone.getRawOffset();
        String symbol = "+";
        if (rawOffset < 0) {
            symbol = "-";
        }
        rawOffset = Math.abs(rawOffset);
        int offsetHore = rawOffset / 3600000;
        int offsetMinute = rawOffset % 3600000 / 60000;
        String hour = String.format("%1$02d", offsetHore);
        String minute = String.format("%1$02d", offsetMinute);
        String timeZone = symbol + hour + ":" + minute;
        return timeZone;
    }

}

