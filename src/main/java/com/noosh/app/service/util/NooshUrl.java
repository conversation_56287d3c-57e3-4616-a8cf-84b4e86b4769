package com.noosh.app.service.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 * @since 1/31/14
 */
public class NooshUrl implements Serializable {

    private static final long serialVersionUID = 2646166673163233316L;
    private URL root;
    private Map<String, String> params = new HashMap<String, String>();
    private boolean random;
    private boolean encrypt;
    private boolean lowerCaseIt;
    private final Logger log = LoggerFactory.getLogger(NooshUrl.class);

    public NooshUrl(URL root) {
        this.root = root;
    }

    public NooshUrl(String protocol, String host, int port, String file) {
        this(protocol, host, port, file, new HashMap<String, String>());
    }

    public NooshUrl(String protocol, String host, int port, String file, Map<String, String> params) {
        try {
            this.root = new URL(protocol, host, port, file);
        } catch (MalformedURLException e) {
            //throw new Exception("MalformedURLException");//Handle it later
            log.error("MalformedURLException");
        }
        this.params = params;
        this.random = false;
        this.encrypt = false;
        this.lowerCaseIt = false; // TODO: mark this as false, please add comment here if you know why we need this, it cause params not match in NE
    }

    public NooshUrl(URL root, Map params, boolean random, boolean encrypt, boolean lowerCaseIt) {
        this.root = root;
        this.params = params;
        this.random = random;
        this.encrypt = encrypt;
        this.lowerCaseIt = lowerCaseIt;
    }

    public URL getRoot() {
        return root;
    }

    public void setRoot(URL root) {
        this.root = root;
    }

    public Map getParams() {
        return params;
    }

    public void setParams(Map params) {
        this.params = params;
    }

    public void addParam(String name, String value) {
        this.params.put(name, value);
    }

    public void removeParam(String name, String value) {
        this.params.remove(value);
    }

    public boolean isRandom() {
        return random;
    }

    public void setRandom(boolean random) {
        this.random = random;
    }

    public boolean isEncrypt() {
        return encrypt;
    }

    public void setEncrypt(boolean encrypt) {
        this.encrypt = encrypt;
    }

    public boolean isLowerCaseIt() {
        return lowerCaseIt;
    }

    public void setLowerCaseIt(boolean lowerCaseIt) {
        this.lowerCaseIt = lowerCaseIt;
    }

    @Override
    public String toString() {
        String queryString = null;
        StringBuffer buffer = new StringBuffer();
        if (this.isRandom()) {
            params.put("rnd", Long.toString(new Random().nextLong()));
        }
        if (!params.isEmpty()) {
            for (Iterator entries = params.entrySet().iterator(); entries.hasNext(); ) {
                Map.Entry entry = (Map.Entry) entries.next();
                String key = (String) entry.getKey();
                String value = (String) entry.getValue();
                if (value == null || (value != null && (value.isEmpty() || value.equalsIgnoreCase("null")))) {
                    continue;
                }
                if (this.lowerCaseIt) {
                    /*if (NooshSecurityUtils.getCurrentUserLocale() != null) {
                        value = value.toLowerCase(NooshSecurityUtils.getCurrentUserLocale());
                    }
                    else {
                        value = value.toLowerCase();
                    }*/
                    value = value.toLowerCase(); //Handle it later
                }
                try {
                    key = URLEncoder.encode(key, "UTF-8");
                    value = URLEncoder.encode(value, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    //throw new NooshException("UnsupportedEncodingException!");
                    log.error("UnsupportedEncodingException");//handle it later
                }
                buffer.append(key).append("=").append(value).append("&");
            }
            buffer.setLength(buffer.length() - 1);
            queryString = buffer.toString();
        }
        try {
            URL rootUrl = new URL(this.root.getProtocol(), this.root.getHost(), this.root.getPort(), "");
            URL url;
            if (queryString != null) {
                queryString = URLEncryptor.encrypt(queryString);
                url = new URL(rootUrl, this.root.getFile()+"?"+queryString);
            }
            else {
                url = new URL(rootUrl, this.root.getFile());
            }
            return url.toString();
        }
        catch (MalformedURLException e) {
            //throw new NooshException("MalformedURLException");
            log.error("MalformedURLException");//Handle it later
            return null;
        }
        catch (Exception e) {
            //throw new NooshException("MalformedURLException");
            log.error("MalformedURLException");//Handle it later
            return null;
        }
    }
}

