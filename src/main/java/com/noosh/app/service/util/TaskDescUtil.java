package com.noosh.app.service.util;

import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * User: leilaz
 * Date: 10/29/15
 * Time: 8:17 PM
 */
@Component
public class TaskDescUtil {
    @Inject
    private MessageSource messageSource;

    public Locale getCurrentRequestLocale() {
        if(RequestContextHolder.getRequestAttributes() !=null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            Locale locale = request.getLocale();
            if (locale != null) {
                return locale;
            }
        }
        return Locale.ENGLISH;
    }

    public String getTaskDescription(String i18nData, String i18nMsg) throws Exception{
        return getTaskDescription(i18nData, i18nMsg, null);
    }

    public String getTaskDescription(String i18nData, String i18nMsg, Locale locale) throws Exception{
        Map<String, String> map = new HashMap<>();
        try {
            map = URLUtil.queryStringToMap(i18nData, map);
        } catch (Exception e) {
            throw new Exception("Error retrieving i18nData from tracking object");
        }

        String[] argsArray = new String[3];
        Set<String> keySet = map.keySet();
        for (String key : keySet) {
            if (key.equalsIgnoreCase("rfeReference") || key.equalsIgnoreCase("name") || key.equalsIgnoreCase("objectClassName")
                    || key.equalsIgnoreCase("invoice.ownerReference") || key.equalsIgnoreCase("order.title")
                    || key.equalsIgnoreCase("quote.title")|| key.equalsIgnoreCase("rfq.title")) {
                argsArray[0] = map.get(key);
            } else if (key.equalsIgnoreCase("title") || key.equalsIgnoreCase("openBidNote")) {
                argsArray[1] = map.get(key);
            } else {
                argsArray[2] = " ";
            }
        }
        return messageSource.getMessage(i18nMsg, argsArray, locale == null ? getCurrentRequestLocale() : locale);
    }

    public String getI18NMessage(String i18nMsg) {
        return messageSource.getMessage(i18nMsg, new String[]{}, getCurrentRequestLocale());
    }

    public String getI18NMessage(String i18nMsg, Locale locale) {
        return messageSource.getMessage(i18nMsg, new String[]{}, locale);
    }

    public Long getProjectId(String i18nData) throws Exception {
        Map<String, String> map = new HashMap<>();
        try {
            map = URLUtil.queryStringToMap(i18nData, map);
        } catch (Exception e) {
            throw new Exception("Error retrieving i18nData from tracking object");
        }
        return Long.parseLong(map.get("projectId"));
    }

    public String getCompany(String i18nData) throws Exception {
        Map<String, String> map = new HashMap<>();
        try {
            map = URLUtil.queryStringToMap(i18nData, map);
        } catch (Exception e) {
            throw new Exception("Error retrieving i18nData from tracking object");
        }
        if (map.containsKey("company")) {
            return map.get("company");
        } else {
            return "";
        }
    }
	
	public String getProjectName(String i18nData) throws Exception {
        Map<String, String> map = new HashMap<>();
        try {
            map = URLUtil.queryStringToMap(i18nData, map);
        } catch (Exception e) {
            throw new Exception("Error retrieving i18nData from tracking object");
        }
        if (map.containsKey("projectName")) {
            return map.get("projectName");
        } else {
            return "";
        }
    }

    public String getObjectTitle(String i18nData) throws Exception {
        Map<String, String> map = new HashMap<>();
        try {
            map = URLUtil.queryStringToMap(i18nData, map);
        } catch (Exception e) {
            throw new Exception("Error retrieving i18nData from tracking object");
        }
        if (map.containsKey("title")) {
            return map.get("title");
        } else if (map.containsKey("order.title")) {
            return map.get("order.title");
        } else {
            return "";
        }
    }
}
