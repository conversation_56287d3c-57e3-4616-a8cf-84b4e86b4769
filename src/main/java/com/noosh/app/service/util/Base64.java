package com.noosh.app.service.util;

import java.io.UnsupportedEncodingException;

/**
 * User: leilaz
 * Date: 9/21/16
 */
public class Base64 {
    private Base64() {}


    public static final byte[] encode(final byte[] src) {
        return DefaultEncoder.instance.encode(src);
    }

    public static final byte[] encode(final String str) {
        return DefaultEncoder.instance.encode(str);
    }

    public static final byte[] encode(final String str, final String charset) {
        return DefaultEncoder.instance.encode(str, charset);
    }

    public static final String encodeString(final String str) {
        return DefaultEncoder.instance.encodeString(str);
    }

    public static final String encodeString(final String str, final String charset) {
        return DefaultEncoder.instance.encodeString(str, charset);
    }

    private static abstract class Encoder {
        protected Encoder() {}

        public abstract byte[] encode(byte[] bytes);

        public byte[] encode(final String str) {
            byte[] encoded = encode(str.getBytes());
            return encoded;
        }

        public byte[] encode(final String str, final String charset) {
            byte[] decoded;
            try {
                decoded = str.getBytes(charset);
            } catch (UnsupportedEncodingException unsence) {
                throw new RuntimeException(unsence);
            }
            byte[] encoded = encode(decoded);
            return encoded;
        }

        public String encodeString(final String str) {
            String encodedStr = new String(encode(str.getBytes()));
            return encodedStr;
        }

        public String encodeString(final String str, final String charset) {
            byte[] decoded;
            try {
                decoded = str.getBytes(charset);
            } catch (UnsupportedEncodingException unsence) {
                throw new RuntimeException(unsence);
            }
            byte[] encoded = encode(decoded);

            String encodedStr;
            try {
                encodedStr = new String(encoded, charset);
            } catch (UnsupportedEncodingException unsence) {
                throw new RuntimeException(unsence);
            }
            return encodedStr;
        }
    }

    private static final class DefaultEncoder extends Encoder {
        private static final DefaultEncoder instance = new DefaultEncoder();

        private static final char PAD = '=';

        private static final char[] encodeMap = {
                'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J',
                'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
                'U', 'V', 'W', 'X', 'Y', 'Z',
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j',
                'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',
                'u', 'v', 'w', 'x', 'y', 'z',
                '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                '+', '/'
        };

        private DefaultEncoder() {}

        /**
         * Encodes the given byte[] using the Base64 encoding specified in RFC-2045.
         *
         * @param  src The data to encode.
         * @return The Base64 encoded byte array..
         */
        public byte[] encode(final byte[] src) {
            // check that there is data to process
            if (src == null || src.length == 0) {
                return src;
            }

            // data length cache
            final int sourceLength = src.length;
            // create a buffer to hold the results
            final byte[] dest = new byte[((sourceLength + 2) / 3) * 4];

            // 3-byte to 4-byte conversion and
            // 0-63 to ASCII printable conversion
            final char[] ec = encodeMap; // cache of encode array
            byte b0, b1, b2; // temps for byte conversion
            int j3b = 0; // 3-byte counter
            int i4b = 0; // 4-byte counter
            for (final int sourceMax = sourceLength - 2; j3b < sourceMax; ) {
                b0 = src[j3b++];
                b1 = src[j3b++];
                b2 = src[j3b++];
                dest[i4b++] = (byte) ec[(b0 >>> 2) & 077];
                dest[i4b++] = (byte) ec[(b1 >>> 4) & 017 | (b0 << 4) & 077];
                dest[i4b++] = (byte) ec[(b2 >>> 6) & 003 | (b1 << 2) & 077];
                dest[i4b++] = (byte) ec[b2 & 077];
            }
            if (j3b < sourceLength) {
                dest[i4b++] = (byte) ec[(src[j3b] >>> 2) & 077];
                if (j3b < sourceLength - 1) {
                    dest[i4b++] = (byte) ec[(src[j3b + 1] >>> 4) & 017 | (src[j3b] << 4) & 077];
                    dest[i4b++] = (byte) ec[(src[j3b + 1] << 2)  & 077];
                } else {
                    dest[i4b++] = (byte) ec[(src[j3b] << 4) & 077];
                }
            }

            // pad with "=" characters
            for (int destLength = dest.length; i4b < destLength; i4b++) {
                dest[i4b] = (byte) PAD;
            }

            return dest;
        }
    }

//    private static final class CommonsEncoder extends Encoder {
//        public byte[] encode(final byte[] src) {
//            return org.apache.commons.codec.binary.Base64.encodeBase64(src);
//        }
//    }
//
//    private static final class PimEncoder extends Encoder {
//        public byte[] encode(final byte[] src) {
//            return net.wimpi.pim.util.Base64.encode(src);
//        }
//    }


    public static final byte[] decode(final byte[] src) {
        return DefaultDecoder.instance.decode(src);
    }

    public static final byte[] decode(final String str) {
        return DefaultDecoder.instance.decode(str);
    }

    public static final byte[] decode(final String str, final String charset) {
        return DefaultDecoder.instance.decode(str, charset);
    }

    public static final String decodeString(final String str) {
        return DefaultDecoder.instance.decodeString(str);
    }

    public static final String decodeString(final String str, final String charset) {
        return DefaultDecoder.instance.decodeString(str, charset);
    }

    private static abstract class Decoder {
        protected Decoder() {}

        public abstract byte[] decode(byte[] bytes);

        public byte[] decode(final String str) {
            byte[] decoded = decode(str.getBytes());
            return decoded;
        }

        public byte[] decode(final String str, final String charset) {
            byte[] encoded;
            try {
                encoded = str.getBytes(charset);
            } catch (UnsupportedEncodingException unsence) {
                throw new RuntimeException(unsence);
            }
            byte[] decoded = decode(encoded);
            return decoded;
        }

        public String decodeString(final String str) {
            String decodedStr = new String(decode(str.getBytes()));
            return decodedStr;
        }

        public String decodeString(final String str, final String charset) {
            byte[] encoded;
            try {
                encoded = str.getBytes(charset);
            } catch (UnsupportedEncodingException unsence) {
                throw new RuntimeException(unsence);
            }
            byte[] decoded = decode(encoded);

            String decodedStr;
            try {
                decodedStr = new String(decoded, charset);
            } catch (UnsupportedEncodingException unsence) {
                throw new RuntimeException(unsence);
            }
            return decodedStr;
        }
    }

    private static final class DefaultDecoder extends Decoder {
        private static final DefaultDecoder instance = new DefaultDecoder();

        private static final byte[] decodeMap;
        static {
            decodeMap = new byte[128];
            for (int i = 0; i < DefaultEncoder.encodeMap.length; i++) {
                decodeMap[DefaultEncoder.encodeMap[i]] = (byte) i;
            }
        }

        private DefaultDecoder() {}

        /**
         * Decodes the given byte[] using the Base64 encoding specified in RFC-2045.
         *
         * @param  src The Base64 encoded data to decode.
         * @return The decoded byte array.
         */
        public byte[] decode(final byte[] src) {
            // check that there is data to process
            if (src == null || src.length == 0) {
                return src;
            }

            // source length cache
            final int sourceLength = src.length;
            // calculate length without the padding on the end
            int unpaddedSourceLength = sourceLength;
            while (src[unpaddedSourceLength - 1] == DefaultEncoder.PAD) {
                unpaddedSourceLength--;
            }

            // create a buffer to hold the results
            final byte dest[] = new byte[unpaddedSourceLength - (sourceLength / 4)];
            // dest length cache
            final int destLength = dest.length;

            // ASCII printable to 0-63 conversion and
            // 4-byte to 3-byte conversion
            final byte[] dc = decodeMap; // cache of decode array
            byte b0, b1, b2, b3; // temps for byte conversion
            int i4b = 0; // 4-byte counter
            int j3b = 0; // 3-byte counter
            for (final int destMax = destLength - 2; j3b < destMax; ) {
                b0 = dc[src[i4b++]];
                b1 = dc[src[i4b++]];
                b2 = dc[src[i4b++]];
                b3 = dc[src[i4b++]];
                dest[j3b++] = (byte) (((b0 << 2) & 255) | ((b1 >>> 4) & 003));
                dest[j3b++] = (byte) (((b1 << 4) & 255) | ((b2 >>> 2) & 017));
                dest[j3b++] = (byte) (((b2 << 6) & 255) | (b3 & 077));
            }
            if (j3b < destLength) {
                dest[j3b] = (byte) (((dc[src[i4b]] << 2) & 255) | ((dc[src[i4b + 1]] >>> 4) & 003));
            }
            j3b++;
            if (j3b < destLength) {
                dest[j3b] = (byte) (((dc[src[i4b + 1]] << 4) & 255) | ((dc[src[i4b + 2]] >>> 2) & 017));
            }

            return dest;
        }
    }

//    private static final class CommonsDecoder extends Decoder {
//        public byte[] decode(final byte[] src) {
//            return org.apache.commons.codec.binary.Base64.decodeBase64(src);
//        }
//    }
//
//    private static final class PimDecoder extends Decoder {
//        public byte[] decode(final byte[] src) {
//            return net.wimpi.pim.util.Base64.decode(src);
//        }
//    }


//    public static class Test extends junit.framework.TestCase {
//        private String[] args;
//
//        public Test(String name) {
//            super(name);
//        }
//
//        public Test configure(String[] args) {
//            if (args == null || args.length == 0) {
//                args = new String[] {
//                        "blah",
//                        "http://scd/noosh/project/home?objectId=123&objectClassId=5000000&rnd=1234567890",
//                        "http://scd/noosh/contacts/viewContact?%24Lc5hneL3ZpRkEA0SSV//iv4U17P79w6WRGRTDqNgp4/3Xs7sbj4FFw=="
//                };
//            }
//            this.args = args;
//            return this;
//        }
//
//        public void test() {
//            Encoder[] encoders = new Encoder[] {
//                    DefaultEncoder.instance,
//                    new CommonsEncoder(),
//                    new PimEncoder(),
//            };
//            Decoder[] decoders = new Decoder[] {
//                    DefaultDecoder.instance,
//                    new CommonsDecoder(),
//                    new PimDecoder(),
//            };
//
//            // unit test
//            for (int c = 0; c < encoders.length; c++) {
//                for (int a = 0; a < this.args.length; a++) {
//                    byte[] str = this.args[a].getBytes();
//                    byte[] enc = encoders[c].encode(str);
//                    byte[] dec = decoders[c].decode(enc);
//
//                    System.out.println(encoders[c] + " " + decoders[c] + "|" + new String(str) + "| >> |" + new String(enc) + "| << |" + new String(dec) + "|");
//
//                    if (!new String(str).equals(new String(dec))) {
//                        throw new AssertionError();
//                    }
//                }
//            }
//
//            // performance test
//            int iterations = 100000;
//            for (int c = 0; c < encoders.length; c++) {
//                long t0 = System.currentTimeMillis();
//                for (int i = 0; i < iterations; i++) {
//                    for (int a = 0; a < args.length; a++) {
//                        decoders[c].decode(encoders[c].encode(args[a].getBytes()));
//                    }
//                }
//                long t1 = System.currentTimeMillis();
//                System.out.println(encoders[c] + "| elapsed: " + (t1 - t0) + "ms");
//            }
//        }
//    }

//    public static void test(String[] args) throws Throwable {
//        new Test("test").configure(args).runBare();
//    }
//
//    public static void main(String[] args) throws Throwable {
//        //test(args);
//        if (args.length == 0
//                || (!"encode".equalsIgnoreCase(args[0]) && !"decode".equalsIgnoreCase(args[0]))) {
//            System.err.println("Usage: java " + Base64.class.getName() + " <encode|decode> <string1> ...");
//            return;
//        }
//
//        String cmd = args[0];
//        for (int a = 1; a < args.length; a++) {
//            if ("encode".equalsIgnoreCase(cmd)) {
//                System.out.println(new String(encode(args[a].getBytes())));
//            } else if ("decode".equalsIgnoreCase(cmd)) {
//                System.out.println(new String(decode(args[a].getBytes())));
//            }
//        }
//    }
}
