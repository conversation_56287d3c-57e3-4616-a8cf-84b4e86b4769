package com.noosh.app.service.util;

import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * User: leilaz
 * Date: 5/31/16
 */
@Component
public class I18NUtils {
    public final static Locale DEFAULT_LOCALE = Locale.US;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ObjectStateRepository objectStateRepository;
    private final Logger log = LoggerFactory.getLogger(I18NUtils.class);

    public String getObjectStateMessage(Long objectStateId) {
        return getObjectStateMessage(objectStateId, CookieUtil.getCurrentCookieLocale());
    }

    public String getObjectStateMessage(Long objectStateId, Locale locale) {
        if (objectStateId != null) {
            try {
                return getObjectStateMessage(objectStateId, new String[]{}, locale);
            } catch (NoSuchMessageException e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }

    public String getObjectStateMessage(Long objectStateId, String[] args) {
        return getObjectStateMessage(objectStateId, args,  CookieUtil.getCurrentCookieLocale());
    }

    public String getObjectStateMessage(Long objectStateId, String[] args, Locale locale) {
        ObjectState objectState = objectStateRepository.findById(objectStateId).orElse(null);
        if (objectState != null) {
            Long key = objectState.getDescriptionStrId();
            try {
                return messageSource.getMessage(key.toString(), args, locale == null ? DEFAULT_LOCALE : locale);
            } catch (NoSuchMessageException e) {
                try {
                    return messageSource.getMessage(key.toString(), args, DEFAULT_LOCALE);
                } catch (NoSuchMessageException ex) {
                    return null;
                }
            }

        } else {
            return null;
        }
    }


    public String getMessage(Long key) {
        return getMessage(key, CookieUtil.getCurrentCookieLocale());
    }

    public String getMessage(Long key,Locale locale) {
        try {
            return messageSource.getMessage(key.toString(), new String[] {}, locale == null ? DEFAULT_LOCALE : locale);
        } catch (NoSuchMessageException e) {
            try {
                return messageSource.getMessage(key.toString(), new String[] {}, DEFAULT_LOCALE);
            } catch (NoSuchMessageException ex) {
                return null;
            }
        }
    }

    public String getMessage(String key) {
        return getMessage(key, CookieUtil.getCurrentCookieLocale());
    }

    public String getMessage(String key, Locale locale) {
        try {
            return messageSource.getMessage(key, new String[] {}, locale == null ? DEFAULT_LOCALE : locale);
        } catch (NoSuchMessageException e) {
            try {
                return messageSource.getMessage(key, new String[] {}, DEFAULT_LOCALE);
            } catch (NoSuchMessageException ex) {
                return null;
            }
        }
    }

    @Async
    public void asyncInit18nDataByLocale(com.noosh.app.commons.entity.security.Locale locale) {
        String localCode = locale.getLocaleCode();
        Long now = System.currentTimeMillis();
        messageSource.getMessage("error.title", new String[] {}, java.util.Locale.forLanguageTag(localCode.replaceAll("_", "-")));
        Long after = System.currentTimeMillis();
        log.info("Message Source: async initialization completed for " + localCode + " in " + (after-now) + " ms");
    }
}