package com.noosh.app.service.preference;

import java.util.HashMap;
import java.util.Map;

/**
 * User: leilaz
 * Date: 7/6/16
 */
public class ParamNameUtil {

    public static final String PROCUREMENT_RFE_ESTIMATING_PAGESIZE = "1";

    private static Map<String, String> map = new HashMap();

    static {
        map.put(PROCUREMENT_RFE_ESTIMATING_PAGESIZE, "PROCUREMENT_RFE_ESTIMATING_PAGESIZE");
    }

    public static String getPreference(String key) {
        String result = map.get(key);
        return result;
    }
}
