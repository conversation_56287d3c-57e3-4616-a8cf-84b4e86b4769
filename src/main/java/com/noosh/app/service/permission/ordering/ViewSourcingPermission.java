package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/26/17
 */
@Service
public class ViewSourcingPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private PreferenceService preferenceService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.VIEW_SOURCING, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof OrderVersionDTO) {
            OrderVersionDTO order = (OrderVersionDTO) object;
            ProjectDTO parentOwner = order.getParent();
            // If sourcing strategies is not enabled, return false
            if (!preferenceService.check(PreferenceID.WORKGROUP_OPTION_SOURCING_STRATEGIES,
                    order.getBuyerWorkgroupId())) {
                return false;
            }

            // Only buyers can see sourcing strategies
            if (order.isUserBuyer()) {
                return true;
            }

            return false;
        }
        return true;
    }
}
