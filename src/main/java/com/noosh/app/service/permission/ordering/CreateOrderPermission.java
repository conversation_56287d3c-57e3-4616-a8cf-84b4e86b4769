package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.preference.Preference;
import com.noosh.app.repository.jpa.preference.PreferenceRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * @Author: neals
 * @Date: 08/05/2016
 */
@Service
public class CreateOrderPermission extends Permission {

    @Inject
    private PreferenceRepository preferenceRepository;
    @Inject
    private PermissionService permissionService;


    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.CREATE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof OrderVersionDTO) {
            OrderVersionDTO order = (OrderVersionDTO) object;
            Preference prefs = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP, order.getParent().getOwnerWorkgroupId());
            if (!prefs.check("WORKGROUP_OPTION_PROCUREMENT")) {
                return false;
            }

            // check if external ordering is okay (ariba...)
            if (isExternalOrdering(order.getParent().getOwnerWorkgroupId())) {
                return false;
            }
        } else if (object instanceof ProjectDTO) {
            ProjectDTO projectDTO = (ProjectDTO) object;
            Preference prefs = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP, projectDTO.getOwnerWorkgroupId());
            if (!prefs.check("WORKGROUP_OPTION_PROCUREMENT")) {
                return false;
            }

            // check if external ordering is okay (ariba...)
            if (isExternalOrdering(projectDTO.getOwnerWorkgroupId())) {
                return false;
            }
        }
        return true;
    }

    public boolean isExternalOrdering(long workgroupId) {
        Preference prefs = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP, workgroupId);
        return prefs.check("WORKGROUP_OPTION_ARIBA_PO");
    }
}
