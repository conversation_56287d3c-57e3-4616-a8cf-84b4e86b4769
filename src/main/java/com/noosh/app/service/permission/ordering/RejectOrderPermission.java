package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.collaboration.Psf;
import com.noosh.app.repository.collaboration.PsfRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * @Author: neals
 * @Date: 08/05/2016
 */
@Service
public class RejectOrderPermission extends Permission {

    @Inject
    private PermissionService permissionService;
    @Inject
    private CreateOrderPermission createOrderPermission;
    @Inject
    private PsfRepository psfRepository;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        if (createOrderPermission.isExternalOrdering(order.getParent().getPortalWorkgroupId()))
            return false;
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.REJECT_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        Psf psf = psfRepository.findById(order.getParent().getPsfId()).orElse(null);
        boolean isSupplierDirect = psf != null && psf.getIsSupplierDirect() != null ? psf.getIsSupplierDirect() : false;
        if (order.isUserBuyer()) {
            return false;
        } else if (!order.isPending() && !order.isPendingSubmission()) {
            return false;
        } else if (order.getIsGenerated() && !isSupplierDirect) {
            return false;
        }

        return true;
    }
}
