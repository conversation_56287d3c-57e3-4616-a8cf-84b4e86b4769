package com.noosh.app.service.permission.quote;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class SendQuotePermission extends Permission {
    @Autowired
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, long workgroupId, long userId, long projectId) {
        ProjectDTO project = (ProjectDTO) object;
        if (!checkState(object, workgroupId, userId, projectId)) {
            return false;
        }
        return permissionService.checkAll(PermissionID.SEND_QUOTE, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, long workgroupId, long userId, long projectId) {
        ProjectDTO project = (ProjectDTO) object;
        if (project.isBrokerProject() || project.isOutsourcerProject()) {
            return true;
        } else {
            return false;
        }
    }
}
