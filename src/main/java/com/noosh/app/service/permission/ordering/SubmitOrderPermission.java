package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.preference.Preference;
import com.noosh.app.repository.jpa.preference.PreferenceRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 10/18/17
 */
@Service
public class SubmitOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private PreferenceRepository preferenceRepository;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.CREATE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO)object;

        Preference prefs = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP, order.getBuyerWorkgroupId());

        boolean isRoutingEnabled = prefs.check(PreferenceID.WORKGROUP_OPTION_APPROVALS_ROUTING);
        boolean isManagerApproval = prefs.check(PreferenceID.PS_REQUIRE_MANAGER_APPROVAL);

        if (!order.isUserBuyer()) {
            return false;

            // the order can only be requiring submission if it had previously
            // been in a requiresApproval state and is now approved.
        }

        else if (!(order.isPendingSubmission() &&
                ((!order.getRequiresSequentialRouting() && order.getIsApproved()) || (order.getRequiresSequentialRouting() && order.getIsAllRecipientsApproved())
                        || !isRoutingEnabled)))
        {
            return false;
        }
        else if (!(order.isPendingSubmission() &&
                ((!isManagerApproval && order.getIsApproved()) || (!order.getRequiresManagerApproval() || (order.getRequiresManagerApproval() && order.getIsManagerApproved())) || !isRoutingEnabled)))
        {
            return false;
        }
        return true;
    }
}
