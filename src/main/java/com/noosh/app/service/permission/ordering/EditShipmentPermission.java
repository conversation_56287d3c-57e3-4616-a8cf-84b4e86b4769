package com.noosh.app.service.permission.ordering;

import com.noosh.app.service.permission.Permission;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/21/17
 */
@Service
public class EditShipmentPermission extends Permission {
    @Inject
    private UpdateShipmentDeliveryPermission updateShipmentDeliveryPermission;
    @Inject
    private UpdateShipmentReceivePermission updateShipmentReceivePermission;
    @Inject
    private UpdateShipmentRequestPermission updateShipmentRequestPermission;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        return updateShipmentDeliveryPermission.check(object, workgroupId, userId, projectId)
                || updateShipmentReceivePermission.check(object, workgroupId, userId, projectId)
                || updateShipmentRequestPermission.check(object, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        return false;  //To change body of implemented methods use File | Settings | File Templates.
    }
}
