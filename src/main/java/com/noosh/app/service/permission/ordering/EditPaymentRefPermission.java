package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/15/17
 */
@Service
public class EditPaymentRefPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private ProjectService projectService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.CREATE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO)object;
        ProjectDTO project = order.getParent();
        long statusId = order.getOrderState().getObjectStateId();
        if ( statusId == ObjectStateID.ORDER_CANCELLED
                || statusId == ObjectStateID.ORDER_REJECTED
                || statusId == ObjectStateID.ORDER_COMPLETED
                || statusId == ObjectStateID.ORDER_RETRACTED) {
            return false;
        }
        // outsourcer can edit both buy and sell orders
        if (project == null) {
            project = projectService.findProjectById(projectId);
            order.setParent(project);
        }
        if (project.isOutsourcerProject() )
            return true;
        // only buyer can edit payment
        if ( order.isUserBuyer() )
            return true;

        return false;
    }
}
