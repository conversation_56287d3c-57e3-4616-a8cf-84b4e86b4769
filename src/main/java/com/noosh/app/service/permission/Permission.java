package com.noosh.app.service.permission;

/**
 * @Author: neals
 * @Date: 08/03/2016
 */
public abstract class Permission {

    /**
     * every check should checkState(object) first in child class
     * @param object
     * @return
     */
    public abstract boolean check(Object object, Long workgroupId, Long userId, long projectId);

    public abstract boolean checkState(Object object, Long workgroupId, Long userId, long projectId);

}