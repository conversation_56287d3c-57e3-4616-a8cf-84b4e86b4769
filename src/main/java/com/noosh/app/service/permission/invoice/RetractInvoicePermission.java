package com.noosh.app.service.permission.invoice;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Map;

@Service
public class RetractInvoicePermission extends Permission {

    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, projectId)) {
            return false;
        }
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.RETRACT_INVOICE, workgroupId, userId, projectId);
    }

    public boolean check(Object object, Long workgroupId, Long userId, long projectId, Map<String, Boolean> permMap) {
        if (!permissionService.check(PermissionID.VIEW_ORDER, projectId, permMap)) {
            return false;
        }
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.check(PermissionID.RETRACT_INVOICE, projectId, permMap);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        InvoiceDTO invoice = (InvoiceDTO) object;

        if ( !invoice.isUserSupplier() )
            return false;

        if (invoice.getOrderVersion().getOrderState().getObjectStateId() == ObjectStateID.ORDER_COMPLETED)
            return false;

        if ((invoice.getOrder().isAccepted() || invoice.getOrder().isCancelled()) &&
                invoice.isUserSupplier() && invoice.getStateId() == ObjectStateID.INVOICE_PENDING_ACCEPTANCE )
            return true;

        return false;
    }
}
