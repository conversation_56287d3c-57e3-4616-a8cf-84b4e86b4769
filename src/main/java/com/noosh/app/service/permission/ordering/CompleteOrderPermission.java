package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.order.Order;
import com.noosh.app.mapper.OrderStateMapper;
import com.noosh.app.repository.jpa.order.OrderRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/12/17
 */
@Service
public class CompleteOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private OrderRepository orderRepository;
    @Inject
    private OrderStateMapper orderStateMapper;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        // no reason to continue if statemachine returns false
        if (checkState(object, workgroupId, userId, projectId)
                && (!hasPendingChangeOrders(order.getOrderId()))) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        boolean closeOrderNegotiation = preferenceService.check(
                PreferenceID.WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION, order.getBuyerWorkgroupId());

        if (!closeOrderNegotiation && !order.isUserBuyer() )
            return false;

        // client of an outsourcing project cannot complete/close the order
        // because the buy and sell can get out-of-sync.  This can happen when
        // 1. client close the order
        // 2. the outsourcer need to revise the quote - which cancel the current quote and sell order
        // this cannot be done since the sell order had already been closed!
        // thus, client should not be able to close the order
        //if (order.isOutsourcingSellOrder())
        //   return false;

//        long stateId = order.getState().getId();
//        if (order.isAccepted() && stateId != ObjectStateID.ORDER_COMPLETED ) {
        if (order.isAccepted() ) {
            return true;
        }

        return false;
    }

    private boolean hasPendingChangeOrders(Long orderId) {
        List<Order> changeOrders = orderRepository.findByParentOrderIdOrderByIdAsc(orderId);
        if (changeOrders != null) {
            for (Order o : changeOrders) {
                OrderVersionDTO orderVersionDTO = new OrderVersionDTO();
                orderVersionDTO.setOrderState(orderStateMapper.toDTO(o.getOrderStateSet().stream().filter(
                        s -> s.getIsCurrent() != null && s.getIsCurrent()).findAny().get()));
                if (orderVersionDTO.isPending()) {
                    return true;
                }
            }
        }
        return false;
    }
}
