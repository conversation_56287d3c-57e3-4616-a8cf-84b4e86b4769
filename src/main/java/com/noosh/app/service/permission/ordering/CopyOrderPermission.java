package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.service.project.ProjectService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/13/17
 */
@Service
public class CopyOrderPermission extends CreateQuickOrderPermission {
    @Inject
    ProjectService projectService;

    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return super.check(object, workgroupId, userId, projectId);
    }

    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof OrderVersionDTO) {
            OrderVersionDTO order = (OrderVersionDTO) object;
            ProjectDTO parentOwner = order.getParent();
            if (order.getParent().isClientProject() || order.getParent().isSupplierProject()) {
                parentOwner = projectService.findProjectById(order.getParent().getMasterProjectId());
            }
            if (order.isOutsourcingSellOrder(parentOwner))
                return false;
            if (!order.isUserBuyer()) {
                return false;
            } else {
                return super.checkState(object, workgroupId, userId, projectId);
            }
        }
        return true;
    }
}
