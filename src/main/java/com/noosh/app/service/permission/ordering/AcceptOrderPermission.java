package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.OrderClassificationID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.constant.ReAcceptanceStatusID;
import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.order.OrderItemDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.service.account.SupplierWorkgroupService;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.security.PermissionService;
import java.util.Map;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.List;

/**
 * @Author: neals
 * @Date: 08/03/2016
 */
@Service
public class AcceptOrderPermission extends Permission {

    @Inject
    private PermissionService permissionService;
    @Inject
    private SupplierWorkgroupService supplierWorkgroupService;
    @Inject
    private PreferenceService preferenceService;


    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;

        return permissionService.checkAll(PermissionID.MARK_ORDER_ACCEPTED, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;

        //TODO:
        //MH:HACK: see if all price/value are populated. If not, disallow acceptance.
        //For v1.x - introduce another state for this.
        List<OrderItemDTO> orderItems = order.getOrderItemDTOs();
        if (orderItems != null && orderItems.size() > 0) {
            for (OrderItemDTO orderItemDTO : orderItems) {
                Short includeAUP = orderItemDTO.getSpec().getIncludeAup();
                if (orderItemDTO.getQuantity() == null || orderItemDTO.getValue() == null
                        || (orderItemDTO.getAddPrice() == null && includeAUP != null && includeAUP.shortValue() > 1)) {
                    return false;
                }
                if (orderItemDTO.getAllowBreakouts()) {
                    List<BreakoutDTO> breakouts = orderItemDTO.getBreakouts();
                    if (breakouts != null && breakouts.size() > 0) {
                        for (BreakoutDTO breakoutDTO : breakouts) {
                            if (breakoutDTO.getBreakoutType().getIsRequired()) {
                                if (breakoutDTO.getHasQuantity()) {
                                    if (breakoutDTO.getValue() == null || breakoutDTO.getValue().longValue() < 0) {
                                        return false;
                                    }
                                }
                                if (breakoutDTO.getPrice() == null) {
                                    return false;
                                }
                            }
                        }
                    }
                }
            }
        }

        if (!order.isPending()) return false;

        if (order.getCreateDate().compareTo(order.getOrderState().getCreateDate()) > 0  && order.getReAcceptanceStatusId() != ReAcceptanceStatusID.BYPASS_RE_ACCEPTANCE)
            return false;

        // check if supplier is approved
        if (order.isUserBuyer() && !supplierWorkgroupService.isSupplierApproved(order.getBuyerWorkgroupId(),
                order.getSupplierWorkgroupId(),
                order.getParent().getClientWorkgroupId(),
                ObjectClassID.ORDER)) {
            return false;
        }

        if ((order.isUserBuyer() && order.isPendingBuyerAcceptance()) || (order.isUserSupplier() && order.isPendingSupplierAcceptance())) {
            return true;
        }

        // If it is invoice adjustment sell order, current user is outsourcer, and enable the preference, allow the user to accept the order on client behalf
        if (order.getOrderClassificationId() != null && order.getOrderClassificationId() == OrderClassificationID.INVOICE_ADJUSTMENT
                && order.isOutsourcingSellOrder(order.getParent()) && order.isUserSupplier()) {
            return preferenceService.check(PreferenceID.WORKGROUP_OPTION_INVOICE_ADJUSTMENT_OUTSOURCER_ACCEPT_CLIENT_ORDER, order.getSupplierWorkgroupId());
        }
        return false;
    }
}
