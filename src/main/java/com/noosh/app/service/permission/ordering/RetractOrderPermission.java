package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 10/24/17
 */
@Service
public class RetractOrderPermission extends Permission {

    @Inject
    private PermissionService permissionService;
    @Inject
    private CreateOrderPermission createOrderPermission;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        if (createOrderPermission.isExternalOrdering(order.getParent().getPortalWorkgroupId()))
            return false;
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.RETRACT_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO)object;
        if (order.isUserSupplier() && !order.isUserBuyer()) {
            return false;
        } else if (!(order.isPending() || order.isPendingSubmission())) {
            return false;
        } /* else if (order.getIsGenerated()) {
            return false;
        } */

        return true;
    }
}
