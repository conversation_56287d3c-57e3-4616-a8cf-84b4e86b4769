package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/14/17
 */
@Service
public class DeliverOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object,workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.MARK_ORDER_DELIVERED, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO)object;
        long stateId = order.getOrderState().getObjectStateId();
        if (order.isUserBuyer() &&
                (stateId == ObjectStateID.ORDER_ACCEPTED
                        || stateId == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                        || stateId == ObjectStateID.ORDER_SHIPPED
                        || stateId == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED
                        || stateId==ObjectStateID.ORDER_FINALIZED))
            return true;
        else
            return false;
    }
}
