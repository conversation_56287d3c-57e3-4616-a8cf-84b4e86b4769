package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.service.account.SupplierWorkgroupService;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 10/12/17
 */
@Service
public class EditOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private SupplierWorkgroupService supplierWorkgroupService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.EDIT_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO)object;
        ProjectDTO parent = order.getParent();
        long stateId = order.getOrderState().getObjectStateId();
        if (stateId == ObjectStateID.ORDER_DRAFT)
            return true;

        if (stateId==ObjectStateID.ORDER_COMPLETED) return false;
        if (stateId==ObjectStateID.ORDER_RETRACTED) return false;
        if (stateId==ObjectStateID.ORDER_REJECTED) return false;
        if (stateId==ObjectStateID.ORDER_ACCEPTED) return false;
        if (stateId==ObjectStateID.ORDER_SHIPPED) return false;
        if (stateId==ObjectStateID.ORDER_PARTIALLY_SHIPPED) return false;
        if (stateId==ObjectStateID.ORDER_DELIVERED) return false;
        if (stateId==ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED) return false;
        if (stateId==ObjectStateID.ORDER_CANCELLED) return false;
        if (stateId==ObjectStateID.ORDER_FINALIZED) return false;
        //if (stateId==ObjectStateID.ORDER_SUPPLIER_TO_ACCEPT && order.isUserBuyer()) return false;
        //if (stateId==ObjectStateID.ORDER_BUYER_TO_ACCEPT && order.isUserSupplier()) return false;
        // check if supplier is approved
        if (order.isUserBuyer() && !supplierWorkgroupService.isSupplierApproved(order.getBuyerWorkgroupId(),
                order.getSupplierWorkgroupId(),
                order.getParent().getClientWorkgroupId(),
                ObjectClassID.ORDER)) {
            return false;
        }

        return true;
    }
}
