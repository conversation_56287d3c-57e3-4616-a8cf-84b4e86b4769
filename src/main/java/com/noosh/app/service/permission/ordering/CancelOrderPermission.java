package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.collaboration.Psf;
import com.noosh.app.commons.entity.preference.Preference;
import com.noosh.app.repository.collaboration.PsfRepository;
import com.noosh.app.repository.jpa.preference.PreferenceRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/13/17
 */
@Service
public class CancelOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private CreateOrderPermission createOrderPermission;
    @Inject
    private PsfRepository psfRepository;
    @Inject
    private PreferenceRepository preferenceRepository;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        if (createOrderPermission.isExternalOrdering(order.getBuyerWorkgroupId()))
            return false;
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;

        return permissionService.checkAll(PermissionID.CANCEL_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        Psf psf = psfRepository.findById(order.getParent().getPsfId()).orElse(null);
        boolean isSupplierDirect = (psf != null && psf.getIsSupplierDirect());
        // generated orders cannot be cancelled unless they are supplier direct orders
        if (order.getIsGenerated() && !isSupplierDirect) {
            return false;
        }
        // only orders that are accepted and not completed can be cancelled
        long stateId = order.getOrderState().getObjectStateId();
        if (order.isAccepted() && stateId != ObjectStateID.ORDER_COMPLETED) {
            return true;
        }

        return false;
    }

    public boolean checkSupplierGroup(Object object, long currentWorkgroupId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        boolean isInSupplierGroup = currentWorkgroupId == order.getSupplierWorkgroupId();
        boolean retVal = false;

        if (isInSupplierGroup) {
            Preference prefs = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP,
                    order.getBuyerWorkgroupId());
            if (prefs.check("WORKGROUP_OPTION_CANCEL_ORDER")) {
                String prefStr = (String) prefs.getValue(PreferenceID.BU_SUP_CANCEL_ORDER);
                if (prefStr == null || prefStr.equals("0")) {
                    retVal = true;
                }
            } else {
                retVal = true;
            }
        }
        return retVal;
    }
}
