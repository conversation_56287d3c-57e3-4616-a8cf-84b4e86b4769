package com.noosh.app.service.permission.rfq;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.preference.Preference;
import com.noosh.app.repository.jpa.preference.PreferenceRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
public class SendRfqPermission extends Permission {

    @Autowired
    private ProjectService projectService;
    @Autowired
    private PreferenceRepository preferenceRepository;
    @Autowired
    private PreferenceService preferenceService;

    public boolean isOutsourcingRfqEnabled(ProjectDTO project, ProjectDTO masterProject) {
        if (project.isClientProject() || masterProject.isOutsourcerProject()) {
            Map<String, String> masterPrefs = preferenceService.findGroupPrefs(masterProject.getOwnerWorkgroupId());
            return preferenceService.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING_WITH_RFQ, masterPrefs);
        }

        return false;
    }

    public boolean checkState(Object object) {
        // can only close if part of a client project
        ProjectDTO project = (ProjectDTO) object;
        ProjectDTO masterProject = project;
        if (!project.isMaster()) {
            masterProject = projectService.findProjectById(project.getMasterProjectId());
        }

        if(isOutsourcingRfqEnabled(project, masterProject)) {
            return true;
        }

        if (!project.isClientProject()) {
            return false;
        }
        // if the other project is new outsourcing project
        // can't  do rfq
        if (masterProject.isOutsourcerProject()) {
            return false;
        }

        return true;
    }

    public boolean checkShowRfqDetail(Object object) {
        boolean isShow = true;
        // can only close if part of a client project
        ProjectDTO project = (ProjectDTO) object;
        ProjectDTO masterProject = project;
        if (!project.isMaster()) {
            masterProject = projectService.findProjectById(project.getMasterProjectId());
        }
        if (project.isClientProject()) {
            Preference masterPrefs = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP, masterProject.getOwnerWorkgroupId());
            isShow = masterPrefs.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING_WITH_RFQ);
        } else {
            if (masterProject.isOutsourcerProject()) {
                Preference masterPrefs = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP, masterProject.getOwnerWorkgroupId());
                isShow = masterPrefs.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING_WITH_RFQ);
            }
        }

        return (isShow || (project.isBrokerProject() && project.isClientOnNoosh()) || (project.isClientProject() && masterProject.isBrokerProject()));
    }

    @Override
    public boolean check(Object object, long workgroupId, long userId, long projectId) {
        // TODO: see if we migrate checkState(Object object) to here
        return false;
    }

    @Override
    public boolean checkState(Object object, long workgroupId, long userId, long projectId) {
        // TODO: see if we migrate checkState(Object object) to here
        return false;
    }
}
