package com.noosh.app.service.costcenter;


import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.costcenter.*;
import com.noosh.app.commons.dto.customfield.UserFieldDTO;
import com.noosh.app.commons.dto.order.OrderStateDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeUpdateDTO;
import com.noosh.app.commons.entity.costcenter.CostCenterAllocation;
import com.noosh.app.commons.entity.order.Order;
import com.noosh.app.commons.entity.order.OrderItem;
import com.noosh.app.commons.entity.order.OrderState;
import com.noosh.app.commons.entity.order.OrderVersion;
import com.noosh.app.commons.entity.property.CustomField;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.commons.entity.property.PropertyParam;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.feign.ProjectOpenFeignClient;
import com.noosh.app.mapper.OrderStateMapper;
import com.noosh.app.mapper.OrderVersionMapper;
import com.noosh.app.repository.costCenter.CostCenterAllocationRepository;
import com.noosh.app.repository.jpa.order.OrderItemRepository;
import com.noosh.app.repository.jpa.order.OrderRepository;
import com.noosh.app.repository.jpa.order.OrderStateRepository;
import com.noosh.app.repository.jpa.order.OrderVersionRepository;
import com.noosh.app.repository.jpa.property.CustomFieldRepository;
import com.noosh.app.repository.jpa.property.PropertyAttributeRepository;
import com.noosh.app.repository.jpa.property.PropertyParamRepository;
import com.noosh.app.repository.jpa.property.PropertyRepository;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.mybatis.costcenter.CostCenterMyBatisMapper;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.service.customfield.CustomFieldService;
import com.noosh.app.service.permission.ordering.CostCenterAllocationPermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.NooshOneUrlUtil;
import jakarta.inject.Inject;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yangx
 * @Date: 6/18/20
 */

@Service
@Transactional
public class CostCenterService {

    @Inject
    private OrderMyBatisMapper orderMyBatisMapper;
    @Inject
    private CostCenterAllocationPermission costCenterAllocationPermission;
    @Inject
    private ObjectStateRepository objectStateRepository;
    @Inject
    private OrderVersionRepository orderVersionRepository;
    @Inject
    private OrderStateRepository orderStateRepository;
    @Inject
    private OrderItemRepository orderItemRepository;
    @Inject
    private OrderRepository orderRepository;
    @Inject
    private OrderVersionMapper orderVersionMapper;
    @Inject
    private ProjectOpenFeignClient projectOpenFeignClient;
    @Inject
    private CustomFieldService customFieldService;
    @Inject
    private ProjectService projectService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private CostCenterAllocationRepository costCenterAllocationRepository;
    @Inject
    private CustomFieldRepository customFieldRepository;
    @Inject
    private PropertyRepository propertyRepository;
    @Inject
    private PropertyParamRepository propertyParamRepository;
    @Inject
    private PropertyAttributeRepository propertyAttributeRepository;
    @Inject
    private PermissionService permissionService;
    @Inject
    private WorkgroupRepository workgroupRepository;
    @Inject
    private OrderStateMapper orderStateMapper;
    @Inject
    private PropertyMyBatisMapper propertyMyBatisMapper;
    @Inject
    private CostCenterMyBatisMapper costCenterMyBatisMapper;

    public OrderCostCenterDetailDTO getCostCenterForOrder(Long orderId, Long projectId, Long workgroupId, Long userId, boolean isAggregatedOrder) {
        OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(orderId, true);
        if (orderVersion == null) throw new NotFoundException("Order is not found!");
        OrderVersionDTO orderVersionDTO = orderVersionMapper.toDTO(orderVersion);
        if (orderVersion.getOrderTypeId() != OrderTypeID.CHANGE_ORDER) {
            try {
                String collaborationJson = projectOpenFeignClient.getContainableByParentId(
                        orderId, ObjectClassID.ORDER, projectId, ObjectClassID.OBJECT_CLASS_PROJECT);
            } catch (Exception e) {
                throw new NotFoundException("Collaboration not found!");
            }
        } else {
            if (orderVersion.getOrder().getParentOrderId() != null) {
                Order parentOrder = orderRepository.findById(orderVersion.getOrder().getParentOrderId()).orElse(null);
                if (parentOrder == null) {
                    throw new NotFoundException("Parent order not found!");
                }
                try {
                    String collaborationJson = projectOpenFeignClient.getContainableByParentId(
                            parentOrder.getId(), ObjectClassID.ORDER, projectId, ObjectClassID.OBJECT_CLASS_PROJECT);
                } catch (Exception e) {
                    throw new NotFoundException("Collaboration not found!");
                }
            } else {
                throw new NotFoundException("Order is not valid!");
            }
        }
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        orderVersionDTO.setParent(projectDTO);
        OrderStateDTO currentOrderState = orderVersionDTO.getOrderState();
        if (currentOrderState == null) {
            currentOrderState = orderStateMapper.toDTO(orderStateRepository.findByOrderIdAndIsCurrent(orderVersionDTO.getOrderId(), true));
            orderVersionDTO.setOrderState(currentOrderState);
        }
        if (!costCenterAllocationPermission.check(orderVersionDTO, workgroupId, userId, projectId)) {
            throw new NoPermissionException("You don't have permission to view cost center for this order");
        }
        return getOrderCostCenter(orderVersionDTO, workgroupId, projectId, userId, isAggregatedOrder);
    }

    public OrderCostCenterDetailDTO getOrderCostCenter(OrderVersionDTO orderVersionDTO, Long workgroupId, Long projectId, Long userId, boolean isAggregatedOrder) {
        OrderStateDTO currentOrderState = orderVersionDTO.getOrderState();
        if (currentOrderState == null) {
            currentOrderState = orderStateMapper.toDTO(orderStateRepository.findByOrderIdAndIsCurrent(orderVersionDTO.getOrderId(), true));
            orderVersionDTO.setOrderState(currentOrderState);
        }
        ProjectDTO projectDTO = orderVersionDTO.getParent();
        if (projectDTO == null) {
            projectDTO = projectService.findProjectById(projectId);
            orderVersionDTO.setParent(projectDTO);
        }
        List<OrderItem> orderItems = orderItemRepository.findByOrderVersionId(orderVersionDTO.getId());
        OrderCostCenterDetailDTO orderCostCenterDetailDTO = new OrderCostCenterDetailDTO();

        String back = NooshOneUrlUtil.composeViewOrderLinkToEnterprise(projectId, orderVersionDTO.getOrderId());
        orderCostCenterDetailDTO.setOrderCostCenters(buildCostCenterList(orderItems, orderVersionDTO, projectId, back));

        orderCostCenterDetailDTO.setOrderName(orderVersionDTO.getOrderTitle());
        orderCostCenterDetailDTO.setOrderId(orderVersionDTO.getOrderId());
        orderCostCenterDetailDTO.setIsOriginal(!orderVersionDTO.isChangeOrder());
        orderCostCenterDetailDTO.setIsClosing(orderVersionDTO.isClosingChangeOrder());
        orderCostCenterDetailDTO.setIsSellOrder(orderVersionDTO.isUserSupplier());
        orderCostCenterDetailDTO.setIsChangeOrder(orderVersionDTO.isChangeOrder());
        orderCostCenterDetailDTO.setBaseOrderId(orderVersionDTO.getOrder().getParentOrderId());
        orderCostCenterDetailDTO.setOrderAcceptDate(orderVersionDTO.getAcceptDate());
        ObjectState objectState = objectStateRepository.findById(currentOrderState.getObjectStateId()).orElse(null);
        orderCostCenterDetailDTO.setOrderStateStrId(orderVersionDTO.isAccepted() || objectState == null ? null : objectState.getDescriptionStrId().toString());
        if (orderVersionDTO.isChangeOrder()) {
            HashMap<String, String> params = new HashMap<>();
            params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
            params.put("objectId", "" + projectId);
            params.put("changeOrderId", "" + orderVersionDTO.getOrderId());
            orderCostCenterDetailDTO.setOrderExternalLink(
                    NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_CHANGE_ORDER, params));
        } else {
            orderCostCenterDetailDTO.setOrderExternalLink(
                    NooshOneUrlUtil.composeViewOrderLinkToEnterprise(projectId, orderVersionDTO.getOrderId()));
        }

        if (permissionService.checkAll(PermissionID.EDIT_ORDER_COST_CENTER, workgroupId, userId, projectId)) {
            orderCostCenterDetailDTO.setCanEditCostCenter(true);
        }

        if (permissionService.checkAll(PermissionID.ADD_ORDER_COST_CENTER, workgroupId, userId, projectId)) {
            orderCostCenterDetailDTO.setCanAddCostCenter(true);
        }

        if((workgroupId == orderVersionDTO.getSupplierWorkgroupId().longValue()
                && orderVersionDTO.getBuyerWorkgroupId().longValue() != orderVersionDTO.getSupplierWorkgroupId().longValue())
                || currentOrderState.getObjectStateId() == ObjectStateID.ORDER_REJECTED
                || currentOrderState.getObjectStateId() == ObjectStateID.ORDER_RETRACTED ){
            orderCostCenterDetailDTO.setCanEditCostCenter(false);
            orderCostCenterDetailDTO.setCanAddCostCenter(false);
        }

        Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(orderVersionDTO.getBuyerWorkgroupId());
        String costCenterEnabled = null;

        if (buyerPrefs.containsKey("WORKGROUP_OPTION_COSTCENTER")) {
            costCenterEnabled = buyerPrefs.get("WORKGROUP_OPTION_COSTCENTER");
        }

        if (costCenterEnabled != null && costCenterEnabled.equals("1")) {
            if (orderVersionDTO.getBuyerWorkgroupId().longValue() == projectDTO.getOwnerWorkgroupId().longValue()) {
                if (permissionService.checkAll(PermissionID.EDIT_ORDER_COST_CENTER, workgroupId, userId, projectId)) {
                    String editCostCenterExternalLink = NooshOneUrlUtil.composeEditCostCenterAllocationLinkToEnterprise(projectId, orderVersionDTO.getOrderId());
                    if (orderVersionDTO.getOrderTypeId() == OrderTypeID.CHANGE_ORDER) {
                        editCostCenterExternalLink = NooshOneUrlUtil.composeEditChangeCenterAllocationLinkToEnterprise(
                                projectId, orderVersionDTO.getOrderId(), orderVersionDTO.getOrderId(), orderVersionDTO.getVersion());
                    } else if (isAggregatedOrder) {
                        editCostCenterExternalLink = NooshOneUrlUtil.composeEditAggregatedCenterAllocationLinkToEnterprise(
                                projectId, orderVersionDTO.getOrderId(), orderVersionDTO.getOrderId(), orderVersionDTO.getVersion());
                    }

                    orderCostCenterDetailDTO.setEditCostCenterExternalLink(editCostCenterExternalLink);
                }
            }
        }

        return orderCostCenterDetailDTO;
    }

    public List<OrderCostCenterDTO> buildCostCenterList(List<OrderItem> orderItems, OrderVersionDTO orderVersion,
                                                        Long projectId, String backUrl) {
        List<OrderCostCenterDTO> costCenterDTOs = new LinkedList<OrderCostCenterDTO>();
        if (orderVersion == null || orderItems == null || orderItems.size() == 0) return costCenterDTOs;

        long orderId = orderVersion.getOrderId();

        List<CostCenterAllocation> taxCostCenterAllocations =
                costCenterAllocationRepository.findByObjectIdAndObjectClassIdAndTypeId(orderId, ObjectClassID.ORDER,
                        CostcenterAllocTypeID.TAX);

        List<CostCenterAllocation> shippingCostCenterAllocations =
                costCenterAllocationRepository.findByObjectIdAndObjectClassIdAndTypeId(orderId, ObjectClassID.ORDER,
                        CostcenterAllocTypeID.SHIPPING);

        Map<String, String> buyerWorkgroupPreference = preferenceService.findGroupPrefs(orderVersion.getBuyerWorkgroupId());

        boolean isItemizedTaxAndShipping = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, buyerWorkgroupPreference);

        // order items
        if (orderItems.size() > 0) {
            for (OrderItem item : orderItems) {
                List<CostCenterAllocation> orderItemCostCenterAllocations =
                        costCenterAllocationRepository.findByObjectIdAndObjectClassId(item.getId(), ObjectClassID.ORDER_ITEM);
                OrderCostCenterDTO orderItemCostCenterDTO = buildOrderCostCenterDTO(orderItemCostCenterAllocations, item, orderVersion, false, false, projectId, backUrl);

                // if itemized tax and shipping is enabled
                if (isItemizedTaxAndShipping) {
                    OrderCostCenterDTO orderItemTaxCostCenterDTO = buildOrderCostCenterDTO(taxCostCenterAllocations, item, orderVersion, true, false, projectId, backUrl);
                    orderItemCostCenterDTO.setOrderItemTax(orderItemTaxCostCenterDTO);
                    OrderCostCenterDTO orderItemShippingCostCenterDTO =buildOrderCostCenterDTO(shippingCostCenterAllocations, item, orderVersion, false, true, projectId, backUrl);
                    orderItemCostCenterDTO.setOrderItemShipping(orderItemShippingCostCenterDTO);
                }

                costCenterDTOs.add(orderItemCostCenterDTO);
            }
        }

        // order tax
        costCenterDTOs.add(buildOrderCostCenterDTO(taxCostCenterAllocations, null, orderVersion, true, false, projectId, backUrl));

        // order shipping
        costCenterDTOs.add(buildOrderCostCenterDTO(shippingCostCenterAllocations, null, orderVersion, false, true, projectId, backUrl));

        return costCenterDTOs;
    }

    private OrderCostCenterDTO buildOrderCostCenterDTO(List<CostCenterAllocation> costCenterAllocations,
                                                       OrderItem item, OrderVersionDTO orderVersion,
                                                       boolean isTax, boolean isShipping, Long projectId, String backUrl) {

        OrderCostCenterDTO orderCostCenterDTO = new OrderCostCenterDTO();
        String itemName = null;
        BigDecimal cost = null;

        if (orderVersion != null && item != null && !(isTax || isShipping)) {
            // order item
            cost = item.getValue();

            if (item.getSpec().getIsItemVersion() != null && item.getSpec().getIsItemVersion() == (short) 1) {
                itemName = item.getSpec().getSpecReference().getRefNumber() + "-" + item.getSpec().getVersionNumber();
            } else {
                itemName = item.getSpec().getSpecName() + " (" + item.getSpec().getSpecReference().getRefNumber() + ")";
            }

            orderCostCenterDTO.setItemExternalLink(NooshOneUrlUtil.composeViewSpecLinkForOrderItemToEnterprise(
                    item.getSpecNodeId(), item.getSpecId(), projectId, backUrl));
            orderCostCenterDTO.setItemId(item.getId());
            orderCostCenterDTO.setSpecId(item.getSpecId());
            orderCostCenterDTO.setQuantity(item.getQuantity() != null ? item.getQuantity().doubleValue() : (double) 0);

        } else if (orderVersion != null && item != null && isTax) {
            // order item tax, itemized tax and shipping is enabled
            cost = item.getTax();
            itemName = CostCenterItemName.TAX;

        } else if (orderVersion != null && item != null && isShipping){
            // order item shipping, itemized tax and shipping is enabled
            cost = item.getShipping();
            itemName = CostCenterItemName.SHIPPING;

        } else if (orderVersion != null && item == null && isTax) {
            // order tax
            cost = orderVersion.getTax();
            itemName = CostCenterItemName.TAX;

        } else if (orderVersion != null && item == null && isShipping){
            // order shipping
            cost = orderVersion.getShipping();
            itemName = CostCenterItemName.SHIPPING;
        }

        orderCostCenterDTO.setItemValue(cost != null ? cost.doubleValue() : 0.0);
        orderCostCenterDTO.setItemName(itemName);


        List<CostCenterAllocationDTO> costCenterAllocationDTOS = buildCostCenterAllocationDTOS(costCenterAllocations, orderVersion, cost);
        orderCostCenterDTO.setCostCenters(costCenterAllocationDTOS);

        return orderCostCenterDTO;
    }

    private List<CostCenterAllocationDTO> buildCostCenterAllocationDTOS(List<CostCenterAllocation> costCenterAllocations,
                                                                        OrderVersionDTO orderVersion,
                                                                        BigDecimal cost) {
        if (costCenterAllocations == null || costCenterAllocations.size() ==0) {
            return null;
        }

        List<CostCenterAllocationDTO> costCenterAllocationDTOS = new ArrayList<CostCenterAllocationDTO>();
        for (CostCenterAllocation costCenterAllocation : costCenterAllocations) {
            CostCenterAllocationDTO costCenterAllocationDTO = new CostCenterAllocationDTO();
            costCenterAllocationDTO.setCostCenterAllocationId(costCenterAllocation.getId());

            // amount = cost * costCenterAllocationPercent / 100
            Double amount = cost != null && costCenterAllocation.getPercent() != null
                    ? cost.multiply(new BigDecimal(costCenterAllocation.getPercent()))
                    .divide(new BigDecimal(100), 5, RoundingMode.HALF_UP).doubleValue() : null;
            costCenterAllocationDTO.setAmount(amount);

            costCenterAllocationDTO.setCostCenterId(costCenterAllocation.getCostCenterId());
            costCenterAllocationDTO.setCostCenterName(costCenterAllocation.getCostCenter() != null
                    ? costCenterAllocation.getCostCenter().getName() : "");
            costCenterAllocationDTO.setCostCenterDesc(costCenterAllocation.getCostCenter() != null ? costCenterAllocation.getCostCenter().getDescription() : "");
            costCenterAllocationDTO.setPercent(costCenterAllocation.getPercent());

            if (costCenterAllocation.getCustomPropertyId() != null && costCenterAllocation.getProperty() != null) {
                costCenterAllocationDTO.setCustomFields(getCustomFieldsForCostcenter(costCenterAllocation.getProperty().getPropertyAttributeSet(),
                        orderVersion.getBuyerWorkgroupId()));
            } else {
                costCenterAllocationDTO.setCustomFields(new ArrayList<PropertyAttributeDTO>());
            }
            costCenterAllocationDTOS.add(costCenterAllocationDTO);
        }
        return costCenterAllocationDTOS;
    }

    public ProjectCostCenterDetailDTO getCostCenterForProject(Long projectId, Long workgroupId, Long userId) {
        ProjectCostCenterDetailDTO projectCostCenterDetailDTO = new ProjectCostCenterDetailDTO();
        Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(workgroupId);
        String costCenterEnabled = null;

        if (buyerPrefs.containsKey("WORKGROUP_OPTION_COSTCENTER")) {
            costCenterEnabled = buyerPrefs.get("WORKGROUP_OPTION_COSTCENTER");
        }
        if (costCenterEnabled == null || costCenterEnabled.equals("0")) {
            return projectCostCenterDetailDTO;
        }

        if (permissionService.checkAll(PermissionID.ADD_PROJECT_COST_CENTER, workgroupId, userId, projectId)) {
            projectCostCenterDetailDTO.setCanEditCostCenter(true);
            projectCostCenterDetailDTO.setCanAddCostCenter(true);
        }

        List<CostCenterAllocationDTO> costCenterDTOs = new LinkedList<CostCenterAllocationDTO>();
        List<CostCenterAllocation> projectCosts =
                costCenterAllocationRepository.findByObjectIdAndObjectClassId(projectId, ObjectClassID.OBJECT_CLASS_PROJECT);

        if (projectCosts != null && projectCosts.size() > 0) {
            for (CostCenterAllocation costCenterAllocation : projectCosts) {
                CostCenterAllocationDTO allocationDTO = new CostCenterAllocationDTO();
                allocationDTO.setPercent(costCenterAllocation.getPercent());
                allocationDTO.setCostCenterAllocationId(costCenterAllocation.getId());
                allocationDTO.setCostCenterId(costCenterAllocation.getCostCenterId());
                allocationDTO.setCostCenterDesc(costCenterAllocation.getCostCenter() != null ?
                        costCenterAllocation.getCostCenter().getDescription() : null);
                allocationDTO.setCostCenterName(costCenterAllocation.getCostCenter() != null ?
                        costCenterAllocation.getCostCenter().getName() : null);
                if (costCenterAllocation.getCustomPropertyId() != null && costCenterAllocation.getProperty() != null) {
                    allocationDTO.setCustomFields(getCustomFieldsForCostcenter(costCenterAllocation.getProperty().getPropertyAttributeSet(), workgroupId));
                } else {
                    allocationDTO.setCustomFields(new ArrayList<PropertyAttributeDTO>());
                }
                costCenterDTOs.add(allocationDTO);
            }
            projectCostCenterDetailDTO.setCostCenters(costCenterDTOs);
        }

        projectCostCenterDetailDTO.setProjectExternalLink(NooshOneUrlUtil.getProjectLink(projectId));

        return projectCostCenterDetailDTO;
    }

    private List<PropertyAttributeDTO> getCustomFieldsForCostcenter(Set<PropertyAttribute> propertyAttributes, Long workgroupId) {
        List<PropertyAttributeDTO> propertyAttributeDTOs = new ArrayList<PropertyAttributeDTO>();
        if (propertyAttributes != null && propertyAttributes.size() > 0) {
            List<UserFieldDTO> customFields = customFieldService.getFieldsByWorkgroupId(workgroupId, CustomFieldClassID.COST_CENTER, false);
            for (PropertyAttribute attribute : propertyAttributes) {
                PropertyAttributeDTO propertyAttributeDTO = new PropertyAttributeDTO(
                        attribute.getId(), attribute.getNumberValue(), attribute.getDateValue(),
                        attribute.getStringValue(), attribute.getPropertyParam().getPrDataTypeId(), attribute.getPropertyParam().getParamName());
                propertyAttributeDTO.setParamId(attribute.getPrPropertyParamId());
                propertyAttributeDTOs.add(propertyAttributeDTO);UserFieldDTO dto = customFields.stream().filter(c -> c.getParamId().longValue() == attribute.getPrPropertyParamId()).findAny().orElse(null);
                if (dto != null) {
                    customFields.remove(dto);
                }
            }
            if (customFields.size() > 0) {
                for (UserFieldDTO userFieldDTO : customFields) {
                    PropertyAttributeDTO propertyAttributeDTO = new PropertyAttributeDTO(
                            null, null, null, null, userFieldDTO.getPrDataTypeId(), userFieldDTO.getParamName());
                    propertyAttributeDTO.setParamId(userFieldDTO.getParamId());
                    propertyAttributeDTOs.add(propertyAttributeDTO);
                }
            }
        }
        return propertyAttributeDTOs;
    }


    public void saveCostCenterAllocation(List<CostCenterAllocationUpdateDTO> dtos, Long currentWorkgroupId,
                                         Long currentUserId, Long projectId, Long objectId,
                                         Long objectClassId, boolean isCopyToOrder, boolean isTax, boolean isShipping) {
        Workgroup currentWorkgroup = workgroupRepository.findById(currentWorkgroupId).orElse(null);
        if (currentWorkgroup == null) {
            throw new NotFoundException("Can't find the workgroup by id: " + currentWorkgroupId);
        }
        if (objectClassId == ObjectClassID.OBJECT_CLASS_PROJECT) {
            if (!permissionService.checkAll(PermissionID.ADD_PROJECT_COST_CENTER, currentWorkgroupId,
                    currentUserId, projectId)) {
                throw new NoPermissionException("You don't have permission to add project cost center!");
            }
        } else {
            if (!permissionService.checkAll(PermissionID.ADD_ORDER_COST_CENTER, currentWorkgroupId, currentUserId, projectId)) {
                throw new NoPermissionException("You don't have permission to add order cost center!");
            }
        }
        Map<Long, PropertyParam> paramTypeMap = new HashMap<Long, PropertyParam>();
        //Delete the existing items if it is not in the update dto
//        List<Long> newIds = new ArrayList<Long>();
//        for (CostCenterAllocationUpdateDTO dto : dtos) {
//            if (dto.getId() != null) {
//                newIds.add(dto.getId());
//            }
//        }
        // Delete all because need to keep the order the same as the frontend
        List<CostCenterAllocation> existingList = costCenterAllocationRepository.findByObjectIdAndObjectClassIdAndTypeId(
                objectId, objectClassId, isTax ? CostcenterAllocTypeID.TAX :
                        (isShipping ? CostcenterAllocTypeID.SHIPPING : CostcenterAllocTypeID.ORDER_ITEM));
        if (existingList != null && existingList.size() > 0) {
            // delete property attribute
            List<Long> propertyIds = existingList.stream().filter(o -> o.getCustomPropertyId() != null).mapToLong(o -> o.getCustomPropertyId()).boxed().collect(Collectors.toList());
            List<Long> costCenterIds = existingList.stream().mapToLong(o -> o.getId()).boxed().collect(Collectors.toList());
            if (propertyIds != null && propertyIds.size() > 0) {
                propertyMyBatisMapper.deleteAttributeWithPropertyIds(propertyIds);
            }

            costCenterMyBatisMapper.deleteCostCenterWithIds(costCenterIds);

            if (propertyIds != null && propertyIds.size() > 0) {
                propertyMyBatisMapper.deletePropertyWithPropertyIds(propertyIds);
            }
        }

        if (dtos != null && dtos.size() > 0) {
            for (CostCenterAllocationUpdateDTO dto : dtos) {
                CostCenterAllocation costCenterAllocation = new CostCenterAllocation();
                //Do the create
                costCenterAllocation.setCreateUserId(currentUserId);
                costCenterAllocation.setCreateDate(LocalDateTime.now());
                costCenterAllocation.setObjectClassId(objectClassId);
                costCenterAllocation.setObjectId(objectId);
                costCenterAllocation.setCostCenterId(dto.getCostCenterId());
                costCenterAllocation.setPercent(dto.getPercent());
                costCenterAllocation.setModDate(LocalDateTime.now());
                costCenterAllocation.setModUserId(currentUserId);
                costCenterAllocation.setTypeId(isTax ? CostcenterAllocTypeID.TAX :
                        (isShipping ? CostcenterAllocTypeID.SHIPPING : CostcenterAllocTypeID.ORDER_ITEM));
                costCenterAllocationRepository.save(costCenterAllocation);

                if (dto.getCustomFields() != null && dto.getCustomFields().size() > 0) {
                    //Create property first
                    Property property = new Property();
                    property.setCreateUserId(currentUserId);
                    property.setModUserId(currentUserId);
                    property.setCreateDate(LocalDateTime.now());
                    property.setModDate(LocalDateTime.now());
                    property.setObjectId(costCenterAllocation.getId());
                    property.setObjectClassId(ObjectClassID.COST_CENTER);
                    property.setOwnerAcWorkgroupId(currentWorkgroupId);
                    property.setPropertyName("PROPS");
                    property.setPrPropertyTypeId(PropertyTypeID.PROPERTY_TYPE_OXF);
                    propertyRepository.save(property);
                    for (PropertyAttributeUpdateDTO propertyAttributeDTO : dto.getCustomFields()) {
                        //set property attribute id to null
                        propertyAttributeDTO.setPrPropertyAttributeId(null);
                        createOrUpdatePropertyAttribute(propertyAttributeDTO, currentUserId, property.getId(),
                                paramTypeMap, currentWorkgroupId, currentWorkgroup.getDefaultCurrencyId());
                    }
                    costCenterAllocation.setCustomPropertyId(property.getId());
                    costCenterAllocationRepository.save(costCenterAllocation);
                }
            }
        }
        if (isCopyToOrder && objectClassId == ObjectClassID.OBJECT_CLASS_PROJECT) {
            //First load current project level cost center
            List<CostCenterAllocation> projectCostCenters =
                    costCenterAllocationRepository.findByObjectIdAndObjectClassId(objectId, ObjectClassID.OBJECT_CLASS_PROJECT);
            // Load current original buy order items
            List<Long> orderItemIds = orderMyBatisMapper.getOriginalOrderItemIdList(objectId, "buy",
                    false, Arrays.asList((long)-1));
            if (orderItemIds != null && orderItemIds.size() > 0) {
                //Delete all current cost center
                List<CostCenterAllocation> costCenterAllocations =
                        costCenterAllocationRepository.findByObjectIdInAndObjectClassId(orderItemIds, ObjectClassID.ORDER_ITEM);
                if (costCenterAllocations != null && costCenterAllocations.size() > 0) {
                    //Delete custom fields first
                    List<Long> propertyIds = costCenterAllocations.stream().filter(o -> o.getCustomPropertyId() != null).mapToLong(o -> o.getCustomPropertyId()).boxed().collect(Collectors.toList());
                    List<Long> costCenterIds = costCenterAllocations.stream().mapToLong(o -> o.getId()).boxed().collect(Collectors.toList());
                    if (propertyIds != null && propertyIds.size() > 0) {
                        propertyMyBatisMapper.deleteAttributeWithPropertyIds(propertyIds);
                    }

                    costCenterMyBatisMapper.deleteCostCenterWithIds(costCenterIds);

                    if (propertyIds != null && propertyIds.size() > 0) {
                        propertyMyBatisMapper.deletePropertyWithPropertyIds(propertyIds);
                    }
                }

                Map<Long, Set<PropertyAttribute>> attributesMap = new HashMap<Long, Set<PropertyAttribute>>();
                Map<Long, Property> properties = new HashMap<Long, Property>();

                for (Long itemId : orderItemIds) {
                    //Copy project level to order item
                    if (projectCostCenters != null && projectCostCenters.size() > 0) {
                        for (CostCenterAllocation projectLevel : projectCostCenters) {
                            //Copy
                            CostCenterAllocation newCopy = new CostCenterAllocation();
                            BeanUtils.copyProperties(projectLevel, newCopy, "id");
                            newCopy.setCreateUserId(currentUserId);
                            newCopy.setModUserId(currentUserId);
                            newCopy.setCreateDate(LocalDateTime.now());
                            newCopy.setModDate(LocalDateTime.now());
                            newCopy.setObjectClassId(ObjectClassID.ORDER_ITEM);
                            newCopy.setObjectId(itemId);
                            newCopy.setCustomPropertyId(null);
                            costCenterAllocationRepository.save(newCopy);

                            //Copy custom field
                            if (projectLevel.getCustomPropertyId() != null) {
                                Property property = new Property();
                                if (properties.containsKey(projectLevel.getCustomPropertyId())) {
                                    property = properties.get(projectLevel.getCustomPropertyId());
                                } else {
                                    property = projectLevel.getProperty();
                                    if (property == null) {
                                        property = propertyRepository.findById(projectLevel.getCustomPropertyId()).orElse(null);
                                    }
                                    if (property == null) {
                                        throw new NotFoundException("Can't find the property object by id: " + projectLevel.getCustomPropertyId());
                                    }
                                    properties.put(projectLevel.getCustomPropertyId(), property);
                                }
                                Property newProperty = new Property();
                                BeanUtils.copyProperties(property, newProperty, "propertyAttributeSet");
                                newProperty.setCreateUserId(currentUserId);
                                newProperty.setModUserId(currentUserId);
                                newProperty.setCreateDate(LocalDateTime.now());
                                newProperty.setModDate(LocalDateTime.now());
                                newProperty.setObjectId(newCopy.getId());
                                newProperty.setId(null);
                                propertyRepository.save(newProperty);
                                newCopy.setCustomPropertyId(newProperty.getId());
                                costCenterAllocationRepository.save(newCopy);

                                Set<PropertyAttribute> propertyAttributes = new HashSet<PropertyAttribute>();
                                if (attributesMap.containsKey(property.getId())) {
                                    propertyAttributes = attributesMap.get(property.getId());
                                } else {
                                    propertyAttributes = property.getPropertyAttributeSet();
                                    if (propertyAttributes == null || propertyAttributes.size() == 0) {
                                        propertyAttributes = propertyAttributeRepository.findByPrPropertyId(property.getId());
                                    }
                                    attributesMap.put(property.getId(), propertyAttributes);
                                }
                                if (propertyAttributes != null && propertyAttributes.size() > 0) {
                                    for (PropertyAttribute propertyAttribute : propertyAttributes) {
                                        PropertyAttribute newPropertyAttribute = new PropertyAttribute();
                                        BeanUtils.copyProperties(propertyAttribute, newPropertyAttribute, "id");
                                        newPropertyAttribute.setPrPropertyId(newProperty.getId());
                                        newPropertyAttribute.setCreateDate(LocalDateTime.now());
                                        newPropertyAttribute.setModDate(LocalDateTime.now());
                                        newPropertyAttribute.setCreateUserId(currentUserId);
                                        newPropertyAttribute.setModUserId(currentUserId);
                                        propertyAttributeRepository.save(newPropertyAttribute);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    private PropertyAttribute createOrUpdatePropertyAttribute(PropertyAttributeUpdateDTO propertyAttributeDTO, Long currentUserId,
                                                              Long propertyId, Map<Long, PropertyParam> paramTypeMap, Long currentWorkgroupId, Long currentWgCurrencyId) {
        //If propertyAttributeDTO.getParamId() is null, means it is new parameter, so should create first
        PropertyParam propertyParam = new PropertyParam();
        if (propertyAttributeDTO.getParamId() == null) {
            if (propertyAttributeDTO.getParamName() == null) {
                throw new IllegalArgumentException("Please pass the custom fields name if custom field is new created!");
            }
            //First check if there already has the same property name in DB
            long dataType = DataTypeID.STRING;
            if (propertyAttributeDTO.getParamName().endsWith("_date")) {
                dataType = DataTypeID.DATE;
            } else if (propertyAttributeDTO.getParamName().endsWith("_bool")) {
                dataType = DataTypeID.BOOLEAN;
            } else if (propertyAttributeDTO.getParamName().endsWith("_money")
                    || propertyAttributeDTO.getParamName().endsWith("_num")) {
                dataType = DataTypeID.DOUBLE;
            } else if (propertyAttributeDTO.getParamName().endsWith("_int")) {
                dataType = DataTypeID.LONG;
            } else if (propertyAttributeDTO.getParamName().endsWith("_str")) {
                dataType = DataTypeID.STRING;
            }
            propertyParam = propertyParamRepository.findByNameAndPropertyAndTypeId(propertyAttributeDTO.getParamName(),
                    PropertyTypeID.PROPERTY_TYPE_OXF, dataType);
            if (propertyParam == null) {
                propertyParam.setParamName(propertyAttributeDTO.getParamName());
                propertyParam.setPrPropertyTypeId(PropertyTypeID.PROPERTY_TYPE_OXF);
                propertyParam.setPrDataTypeId(dataType);
                propertyParam.setIsReportField((short) 0);
                propertyParam.setCreateDate(LocalDateTime.now());
                propertyParam.setCreateUserId(currentUserId);
                propertyParamRepository.saveAndFlush(propertyParam);
            }
            paramTypeMap.put(propertyParam.getPrPropertyParamId(), propertyParam);
            propertyAttributeDTO.setParamId(propertyParam.getPrPropertyParamId());
        }

        PropertyAttribute propertyAttribute = new PropertyAttribute();
        if (propertyAttributeDTO.getPrPropertyAttributeId() != null) {
            propertyAttribute = propertyAttributeRepository.findById(propertyAttributeDTO.getPrPropertyAttributeId()).orElse(null);
            if (propertyAttribute == null) {
                throw new NotFoundException("Can't find the custom field with id:" + propertyAttributeDTO.getPrPropertyAttributeId());
            }
        } else {
            // Check if there is duplicate param
            PropertyAttribute newAttribute = propertyAttributeRepository.findByPrPropertyIdAndPrPropertyParamId(propertyId, propertyAttributeDTO.getParamId());
            if (newAttribute != null) {
                propertyAttribute = newAttribute;
            }
        }

        propertyAttribute.setModDate(LocalDateTime.now());
        propertyAttribute.setModUserId(currentUserId);
        if (propertyAttributeDTO.getParamId() == null) {
            throw new IllegalArgumentException("Please pass the valid param id!");
        }

        long dataTypeId = DataTypeID.STRING;
        if (paramTypeMap.containsKey(propertyAttributeDTO.getParamId())) {
            propertyParam = paramTypeMap.get(propertyAttributeDTO.getParamId());
            dataTypeId = propertyParam.getPrDataTypeId();
        } else {
            propertyParam = propertyParamRepository.findById(propertyAttributeDTO.getParamId()).orElse(null);
            if (propertyParam == null) {
                throw new NotFoundException("Can't find the param by id:" + propertyAttribute.getPrPropertyParamId());
            }
            dataTypeId = propertyParam.getPrDataTypeId();
            paramTypeMap.put(propertyAttributeDTO.getParamId(), propertyParam);
        }

        if (propertyAttributeDTO.getTypeValue() != null && propertyAttributeDTO.getTypeValue().length() > 0) {

            try {
                switch ((int) dataTypeId) {
                    case (int) DataTypeID.STRING:
                        propertyAttribute.setStringValue(propertyAttributeDTO.getTypeValue());
                        break;
                    case (int) DataTypeID.LONG:
                        propertyAttribute.setNumberValue(new BigDecimal(propertyAttributeDTO.getTypeValue()));
                        break;
                    case (int) DataTypeID.DOUBLE:
                        propertyAttribute.setNumberValue(new BigDecimal(propertyAttributeDTO.getTypeValue()));
                        break;
                    case (int) DataTypeID.DATE:
                        DateTimeFormatter formatter = DateTimeFormatter
                                .ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                        propertyAttribute.setDateValue(LocalDateTime.parse(
                                propertyAttributeDTO.getTypeValue(), formatter));
                        break;
                    case (int) DataTypeID.BOOLEAN:
                        propertyAttribute.setNumberValue("true".equalsIgnoreCase(propertyAttributeDTO.getTypeValue()) ? new BigDecimal(1) : new BigDecimal(0));
                        break;
                    default: //DataTypeID.STRING:
                        propertyAttribute.setStringValue(propertyAttributeDTO.getTypeValue());
                }
            } catch (Exception e) {
                throw new IllegalArgumentException(e.getMessage() + " Please pass the valid attribute value!");
            }

        } else {
            propertyAttribute.setStringValue(null);
            propertyAttribute.setNumberValue(null);
        }

        if (propertyAttribute.getId() == null) {
            propertyAttribute.setPrPropertyParamId(propertyAttributeDTO.getParamId());
            propertyAttribute.setCreateUserId(currentUserId);
            propertyAttribute.setCreateDate(LocalDateTime.now());
            propertyAttribute.setPrPropertyId(propertyId);
        }

        propertyAttributeRepository.save(propertyAttribute);

        // Handle for money
        CustomField customField = customFieldRepository.findByWgIdAndFieldClassIdAndParamId(currentWorkgroupId,
                CustomFieldClassID.COST_CENTER, propertyAttributeDTO.getParamId());
        if (customField != null && customField.getCustomFieldControlId() == CustomFieldControlID.MONEY) {
            // Find money parameter
            PropertyParam moneyCurrency = propertyParamRepository.findByParamNameAndPrDataTypeId(propertyParam.getParamName() + "CurrencyId", DataTypeID.LONG);
            if (moneyCurrency == null || moneyCurrency.getPrPropertyParamId() == null) {
                // Create new one
                moneyCurrency = new PropertyParam();
                moneyCurrency.setCreateUserId(currentUserId);
                moneyCurrency.setCreateDate(LocalDateTime.now());
                moneyCurrency.setPrPropertyTypeId(PropertyTypeID.PROPERTY_TYPE_OXF);
                moneyCurrency.setParamName(propertyParam.getParamName() + "CurrencyId");
                moneyCurrency.setPrDataTypeId(DataTypeID.LONG);
                propertyParamRepository.save(moneyCurrency);
            }
            PropertyAttribute moneyAttribute = propertyAttributeRepository.findByPrPropertyIdAndPrPropertyParamId(propertyId, moneyCurrency.getPrPropertyParamId());
            if (moneyAttribute == null || moneyAttribute.getId() == null) {
                moneyAttribute = new PropertyAttribute();
                moneyAttribute.setPrPropertyParamId(moneyCurrency.getPrPropertyParamId());
                moneyAttribute.setCreateUserId(currentUserId);
                moneyAttribute.setCreateDate(LocalDateTime.now());
                moneyAttribute.setPrPropertyId(propertyId);
            }
            moneyAttribute.setModUserId(currentUserId);
            moneyAttribute.setModDate(LocalDateTime.now());
            moneyAttribute.setNumberValue(new BigDecimal(currentWgCurrencyId));
            propertyAttributeRepository.save(moneyAttribute);
        }
        return propertyAttribute;

    }

}
