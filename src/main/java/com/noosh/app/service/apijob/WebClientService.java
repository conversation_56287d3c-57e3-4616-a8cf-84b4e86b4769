package com.noosh.app.service.apijob;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MimeType;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class WebClientService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String LOAD_BALANCER_PROTOCOL = "lb://";
    public static final String HTTP_PROTOCOL = "http://";
    public static final String HTTPS_PROTOCOL = "https://";

    @Autowired
    private Environment environment;

    @Autowired
    @Qualifier("externalWebClientBuilder")
    private WebClient.Builder externalWebClientBuilder;

    @Autowired
    @Qualifier("internalWebClientBuilder")
    private WebClient.Builder internalWebClientBuilder;

    public Mono<String> createWebApiWithJsonBody(String protocol, String host, String path, HttpMethod httpMethod,
                                                 MultiValueMap<String, String> headerMap, String JSONBody) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return callWebApi(protocol,
                host,
                path,
                httpMethod,
                (headerMap != null) ? mapper.writeValueAsString(headerMap) : null,
                JSONBody
        );
    }


    public Mono<String> createWebApi(String protocol, String host, String path, HttpMethod httpMethod,
                                     MultiValueMap<String, String> headerMap, Map<String, String> body) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        return callWebApi(protocol,
                host,
                path,
                httpMethod,
                (headerMap != null) ? mapper.writeValueAsString(headerMap) : null,
                (body != null) ? mapper.writeValueAsString(body) : null
        );
    }

    private Mono<String> callWebApi(String protocol, String host, String path, HttpMethod method,
                                    String headerString, String bodyString) {
        try {
            //Url
            String url = protocol + host;

            //Web Client
            WebClient webClient;
            if (LOAD_BALANCER_PROTOCOL.equalsIgnoreCase(protocol)) {
                webClient = internalWebClientBuilder.baseUrl(url).build();
            }
            else if (HTTP_PROTOCOL.equalsIgnoreCase(protocol) || HTTPS_PROTOCOL.equalsIgnoreCase(protocol)) {
                webClient = externalWebClientBuilder.baseUrl(url).build();
            }
            else {
                throw new IllegalArgumentException("Not support protocol");
            }

            //Method
            if (method == null) {
                throw new IllegalArgumentException("Invalid method");
            }

            //Headers, can take 2 formats for multi value headers
            HttpHeaders headers = new HttpHeaders();
            if (headerString != null && !headerString.isEmpty()) {
                MultiValueMap<String, String> headersMap = extractHeaders(headerString);
                headers.putAll(headersMap);
            }
            MimeType contentType = headers.getContentType();
            //Potential Bug with DELETE and multipart
            if (method.matches(HttpMethod.DELETE.name()) && MediaType.MULTIPART_FORM_DATA.equalsTypeAndSubtype(contentType)) {
                List<String> chunked = new LinkedList<>();
                chunked.add("chunked");
                headers.put(HttpHeaders.TRANSFER_ENCODING, chunked);
            }

            //Body
            //If Content-Type Header is is application/x-www-form-urlencoded or multipart/form-data, will build map
            //Else use raw string
            MultiValueMap<String, String> bodyMap = new LinkedMultiValueMap<>();
            if (bodyString != null && !bodyString.isEmpty()) {
                if (MediaType.APPLICATION_FORM_URLENCODED.equalsTypeAndSubtype(contentType) ||
                        MediaType.MULTIPART_FORM_DATA.equalsTypeAndSubtype(contentType)) {
                    bodyMap = extractBody(bodyString);
                }
            }

            AtomicInteger statusCode = new AtomicInteger();
            Mono<String> responseBodyMono = webClient.method(method)
                    .uri(path)
                    .headers(httpHeaders -> httpHeaders.putAll(headers))
                    .body(
                            MediaType.APPLICATION_FORM_URLENCODED.equalsTypeAndSubtype(contentType) ? BodyInserters.fromFormData(bodyMap) :
                                    (MediaType.MULTIPART_FORM_DATA.equalsTypeAndSubtype(contentType) ? BodyInserters.fromMultipartData(bodyMap) :
                                            ((bodyString != null && !bodyString.isEmpty()) ? BodyInserters.fromValue(bodyString) : BodyInserters.empty()))
                    )
                    .exchangeToMono(clientResponse -> {
                        statusCode.set(clientResponse.statusCode().value());
                        return clientResponse.bodyToMono(String.class).defaultIfEmpty("");
                    })
                    .onErrorResume(exception -> {
                        statusCode.set(1);//want to differentiate between error from Request vs processing
                        String errorCauseMessage = "";
                        if (exception.getCause() != null) {
                            errorCauseMessage = exception.getCause().toString();
                        }
                        String shortenStackTrace = getShortenStackTrace(exception);
                        //concat both cause and stacktrace, more info the better
                        String responseError = !errorCauseMessage.isBlank() ? (errorCauseMessage + "\n" + shortenStackTrace) : shortenStackTrace;
                        logger.error(responseError);
                        return Mono.just(responseError);
                    });

            responseBodyMono.subscribe((subscribedBody) -> {//the consumer to invoke on each value
                handleResponse(statusCode.get(), subscribedBody);
            });
            return responseBodyMono;
        }
        catch (Exception exception) {
            logger.info("Error has occurred");
            String errorCauseMessage = "";
            if (exception.getCause() != null) {
                errorCauseMessage = exception.getCause().toString();
            }
            String shortenStackTrace = getShortenStackTrace(exception);
            //concat both cause and stacktrace, more info the better
            String responseError = !errorCauseMessage.isBlank() ? (errorCauseMessage + "\n" + shortenStackTrace) : shortenStackTrace;
            logger.error(responseError);
            return Mono.just(responseError);
        }
    }

    private void handleResponse(Integer responseStatusCode, String responseBody) {
        if (HttpStatus.SC_OK != responseStatusCode.intValue()) {
            logger.error(responseStatusCode + ":" +responseBody);
        }
    }

    @SuppressWarnings("unchecked")
    private MultiValueMap<String, String> extractHeaders(String jsonString) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        try {
            multiValueMap = mapper.readValue(jsonString, LinkedMultiValueMap.class);
        }
        catch (JsonProcessingException e) {//try converting to HashMap, then to MultiValueMap
            HashMap<String, Object> map = mapper.readValue(jsonString, HashMap.class);
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = map.get(key);//either String or ArrayList
                if (value instanceof String) {
                    String str = (String) value;
                    str = str.replaceAll(",\\s*", ",");//remove whitespace and assume comma separates multiple values
                    List<String> myList = new ArrayList<>(Arrays.asList(str.split(",")));
                    multiValueMap.put(key, myList);
                }
                else if (value instanceof List) {
                    ArrayList<String> myList = (ArrayList<String>) value;
                    multiValueMap.put(key, myList);
                }
                else {
                    throw e;
                }
            }
        }
        return multiValueMap;
    }

    @SuppressWarnings("unchecked")
    private MultiValueMap<String, String> extractBody(String jsonString) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
        try {
            multiValueMap = mapper.readValue(jsonString, LinkedMultiValueMap.class);
        }
        catch (JsonProcessingException e) {//try converting to HashMap, then to MultiValueMap
            HashMap<String, Object> map = mapper.readValue(jsonString, HashMap.class);
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                String key = entry.getKey();
                Object value = map.get(key);//either String or ArrayList
                if (value instanceof String) {
                    String str = (String) value;
                    List<String> myList = new ArrayList<>();
                    myList.add(str);
                    multiValueMap.put(key, myList);
                }
                else if (value instanceof List) {
                    ArrayList<String> myList = (ArrayList<String>) value;
                    multiValueMap.put(key, myList);
                }
                else {
                    throw e;
                }
            }
        }
        return multiValueMap;
    }

    private String getShortenStackTrace(Throwable exception) {
        int maxLines = 15;
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        exception.printStackTrace(pw);
        String stacktrace = sw.toString();

        String[] lines = stacktrace.split("\n");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < Math.min(lines.length, maxLines); i++) {
            sb.append(lines[i]).append("\n");
        }
        return sb.toString();
    }
}
