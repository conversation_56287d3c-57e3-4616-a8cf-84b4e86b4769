package com.noosh.app.service.rating;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.collaboration.SyContainableDTO;
import com.noosh.app.commons.dto.rating.SupplierRatingDTO;
import com.noosh.app.commons.dto.rating.SupplierRatingItemDTO;
import com.noosh.app.commons.dto.rating.SupplierRatingOrderDTO;
import com.noosh.app.commons.dto.rating.SupplierRatingOrderItemDTO;
import com.noosh.app.commons.dto.task.TaskUpdateDTO;
import com.noosh.app.commons.dto.tracking.TrackingDTO;
import com.noosh.app.commons.entity.order.OrderItem;
import com.noosh.app.commons.entity.order.OrderVersion;
import com.noosh.app.commons.entity.rating.*;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.rating.RateByOrderItemVO;
import com.noosh.app.commons.vo.rating.SupplierRatingDetailVO;
import com.noosh.app.commons.vo.rating.SupplierScoreVO;
import com.noosh.app.commons.vo.spec.SpecListVO;
import com.noosh.app.exception.ForbiddenException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.feign.SpecOpenFeignClient;
import com.noosh.app.feign.TaskOpenFeignClient;
import com.noosh.app.feign.ProjectOpenFeignClient;
import com.noosh.app.feign.TrackingOpenFeignClient;
import com.noosh.app.mapper.rating.SupplierRatingMapper;
import com.noosh.app.repository.jpa.order.OrderItemRepository;
import com.noosh.app.repository.jpa.order.OrderVersionRepository;
import com.noosh.app.repository.rating.*;
import com.noosh.app.service.collaboration.CollaborationService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 9/26/19
 */
@Service
@Transactional
public class RatingService {
    private final Logger log = LoggerFactory.getLogger(RatingService.class);
    @Inject
    private ProjectOpenFeignClient projectOpenFeignClient;
    @Inject
    private SrRatingRepository ratingRepository;
    @Inject
    private SectionRepository sectionRepository;
    @Inject
    private SupplierRatingMapper supplierRatingMapper;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private CollaborationService collaborationService;
    @Inject
    private SrQuestionRepository srQuestionRepository;
    @Inject
    private SrRatingAverageRepository srRatingAverageRepository;
    @Inject
    private SrRatingItemRepository ratingItemRepository;
    @Inject
    private TrackingOpenFeignClient trackingOpenFeignClient;
    @Inject
    private TaskOpenFeignClient taskOpenFeignClient;
    @Inject
    private OrderItemRepository orderItemRepository;
    @Inject
    private OrderVersionRepository orderVersionRepository;
    @Inject
    private SpecOpenFeignClient specOpenFeignClient;

    public SupplierRatingDetailVO findRating (Long orderId, Long workgroupId, Long projectId, Long changeOrderId) throws Exception {
        SupplierRatingDetailVO supplierRatingDetailVO = new SupplierRatingDetailVO();
        ObjectMapper objectMapper = new ObjectMapper();
        // If current project is supplier, then should use outsourcer project id to query rating
        Long queryProjectId = projectId;
        Long queryWorkgroupId = workgroupId;

        //Find project name
        String projectJson = projectOpenFeignClient.getProjectById(projectId);
        if (projectJson != null && projectJson.length() > 0) {
            JSONObject json = new JSONObject(projectJson);
            JSONObject project = json.getJSONObject("data");
            Long ownerWorkgroupId = project.getLong("ownerWorkgroupId");
            Long masterProjectId = project.has("masterProjectId") ? project.getLong("masterProjectId") : null;
            String projectTitle = project.getString("name") + " ("
                    + project.getString("projectNumber") + ")";
            if (ownerWorkgroupId.longValue() != queryWorkgroupId) {
                throw new NotFoundException("Project and work group is not compatible!");
            }
            supplierRatingDetailVO.setProjectName(projectTitle);
            if (masterProjectId != null) {
                queryProjectId = masterProjectId;
            }
            HashMap<String, String> params = new HashMap<>();
            params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
            params.put("objectId", "" + projectId);
            supplierRatingDetailVO.setProjectLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_PROJECT_HOME, params));
        }

        if (changeOrderId == null) {
            supplierRatingDetailVO.setOrderExternalLink(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(projectId, orderId));
        } else {
            supplierRatingDetailVO.setOrderExternalLink(NooshOneUrlUtil.composeViewChangeOrderLinkToEnterprise(projectId, orderId, changeOrderId));
        }

        supplierRatingDetailVO.setCanCreateOrEdit(true);
        OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(orderId, true);
        if ((orderVersion.getSupplierWorkgroupId() == workgroupId.longValue()) &&
                (orderVersion.getBuyerWorkgroupId() != null)) {
            queryWorkgroupId = orderVersion.getBuyerWorkgroupId();
            Map<String, String> prefs = preferenceService.findGroupPrefs(queryWorkgroupId);
            supplierRatingDetailVO.setCanCreateOrEdit(prefs.containsKey("SR_SUPPLIERS_CAN_EDIT") ? "1".equals(prefs.get("SR_SUPPLIERS_CAN_EDIT")) : false);
        }

        //First load by order
        List<SrRating> ratingsForOrder = ratingRepository.findByObjectIdAndByWorkGroupId(orderId, ObjectClassID.ORDER,
                queryWorkgroupId);
        supplierRatingDetailVO.setRateByOrder(ratingsForOrder.stream().map(supplierRatingMapper::toSupplierRating).collect(Collectors.toList()));
        //If rate by order is null, then try to rate by order items
        if (ratingsForOrder == null || ratingsForOrder.size() == 0) {
            //Load order item
            List<OrderItem> orderItems = orderItemRepository.findByOrderVersionId(orderVersion.getId());
            List<RateByOrderItemVO> rateByOrderItemVOs = new ArrayList<RateByOrderItemVO>();
            List<Long> specIds = new ArrayList<>();
            orderItems.stream().forEach(o -> specIds.add(o.getSpecId()));
            String specJsonString = specOpenFeignClient.getSpecListByIds(specIds, projectId);
            SpecListVO specListVO = new SpecListVO();
            if (specJsonString != null && specJsonString.length() > 0) {
                JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ServerResponse.class, SpecListVO.class);
                ServerResponse<SpecListVO> specResponse = objectMapper.readValue(specJsonString, javaType);
                specListVO = specResponse.getData();
            }
            for (OrderItem orderItem : orderItems) {
                List<SrRating> ratingsForOrderItem = ratingRepository.findByObjectIdAndByWorkGroupId(orderItem.getId(),
                        ObjectClassID.ORDER_ITEM, queryWorkgroupId);
                if (specListVO.getList() == null || specListVO.getList().size() == 0) {
                    throw new NotFoundException("Spec is not found!");
                }
                RateByOrderItemVO rateByOrderItemVO = supplierRatingMapper.toRateByOrderItemVO(orderItem, ratingsForOrderItem, queryProjectId, orderId,
                        specListVO.getList().stream().filter(s -> s.getId().longValue() == orderItem.getSpecId()).findAny().get(), changeOrderId != null);
                rateByOrderItemVOs.add(rateByOrderItemVO);
            }
            supplierRatingDetailVO.setRateByOrderItem(rateByOrderItemVOs);
        }
        return supplierRatingDetailVO;
    }

    public Map findAllQuestion(Long buyerWorkgroupId) {
        Map questionMap = new HashMap();
        Map<String, String> prefs = preferenceService.findGroupPrefs(buyerWorkgroupId);
        Long questionnaireId = prefs.containsKey("SR_ACTIVE_QUESTIONNAIRE") ? Long.parseLong(prefs.get("SR_ACTIVE_QUESTIONNAIRE")) : (long) -1;

        if (questionnaireId != null && questionnaireId != (long) -1) {
            boolean naAllowed = prefs.containsKey("SR_RATING_NA_ALLOWED") ? "1".equals(prefs.get("SR_RATING_NA_ALLOWED")) : false;
            Long granularity = prefs.containsKey("SR_RATING_GRANULARITY") ? Long.parseLong(prefs.get("SR_RATING_GRANULARITY")) : (long) -1;
            List<SrQuestion> questionList = srQuestionRepository.findByQuestionNaireId(questionnaireId);
            if (granularity == null || granularity == (long) -1) {
                granularity = (long) 3;
            }
            questionMap.put("naAllowed", naAllowed);
            questionMap.put("granularity", granularity);
            questionMap.put("questionList", questionList);
            questionMap.put("questionnaireId", questionnaireId);
        }
        return questionMap;
    }

    @Transactional(readOnly = false)
    public SrRating createOrUpdateRating(SupplierRatingDTO supplierRatingDTO, Long workgroupId, boolean isComplete, Long nooshUserId) throws Exception {
        SrRating srRating = null;
        String projectJson = projectOpenFeignClient.getProjectById(supplierRatingDTO.getProjectId());
        ObjectMapper objectMapper = new ObjectMapper();
        String projectTitle = "";
        String projectNumber = "";
        String orderTitle = "";
        Long queryWorkgroupId = workgroupId;
        if (projectJson != null && projectJson.length() > 0) {
            JSONObject json = new JSONObject(projectJson);
            JSONObject project = json.getJSONObject("data");
            projectTitle = project.getString("name");
            projectNumber = project.getString("projectNumber");
        }

        OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(supplierRatingDTO.getOrderId(), true);
        if (orderVersion == null) {
            throw new NotFoundException("Order not found!");
        }
        if ((orderVersion.getSupplierWorkgroupId() == workgroupId.longValue()) &&
                (orderVersion.getBuyerWorkgroupId() != null)) {
            queryWorkgroupId = orderVersion.getBuyerWorkgroupId();
        }
        orderTitle = orderVersion.getOrderTitle();

        if (supplierRatingDTO.getIsCreate()) {
            //Do Create
            Map questionMap = findAllQuestion(queryWorkgroupId);
            if (questionMap.isEmpty()) {
                throw new NotFoundException("No question map found for this workgroup " + queryWorkgroupId);
            }

            if (supplierRatingDTO.getRateByOrder() != null && supplierRatingDTO.getIsRateByOrder() && supplierRatingDTO.getRateByOrder().size() > 0) {
                for (SupplierRatingOrderDTO supplierRatingOrderDTO : supplierRatingDTO.getRateByOrder()) {
                    Map<Long, Long> questionValue = new HashMap<Long, Long>();
                    Map<Long, String> questionComment = new HashMap<Long, String>();

                    for (SupplierRatingItemDTO supplierRatingItemDTO : supplierRatingOrderDTO.getRatingItem()) {
                        questionValue.put(supplierRatingItemDTO.getQuestionId(), supplierRatingItemDTO.getGrade());
                        questionComment.put(supplierRatingItemDTO.getQuestionId(), supplierRatingItemDTO.getComments());
                    }
                    //Before create, check if there already has the rating
                    SrRating existRating = ratingRepository.findByObjectIdAndObjectClassId(supplierRatingDTO.getOrderId(),
                            ObjectClassID.ORDER);
                    if (existRating != null) throw new ForbiddenException("There already exist the rating for order " + supplierRatingDTO.getOrderId());
                    srRating = createRating((List<SrQuestion>) questionMap.get("questionList"), nooshUserId, supplierRatingDTO.getOrderId(), (long) -1,
                            supplierRatingDTO.getIsRateByOrder() ? ObjectClassID.ORDER : ObjectClassID.ORDER_ITEM, (Long) questionMap.get("questionnaireId"),
                            queryWorkgroupId, supplierRatingDTO.getSupplierWorkgroupId(), ((Boolean) questionMap.get("naAllowed")) ? (short) 1 : (short) 0,
                            ((Long) questionMap.get("granularity")).shortValue(), supplierRatingOrderDTO.getRatingComment(), 50, questionComment, questionValue);
                }
            } else if ((!supplierRatingDTO.getIsRateByOrder()) && supplierRatingDTO.getRateByOrderItem() != null
                    && supplierRatingDTO.getRateByOrderItem().size() > 0) {
                for (SupplierRatingOrderItemDTO supplierRatingOrderItemDTO : supplierRatingDTO.getRateByOrderItem()) {
                    Map<Long, Long> questionValue = new HashMap<Long, Long>();
                    Map<Long, String> questionComment = new HashMap<Long, String>();

                    for (SupplierRatingItemDTO supplierRatingItemDTO : supplierRatingOrderItemDTO.getRatingItem()) {
                        questionValue.put(supplierRatingItemDTO.getQuestionId(), supplierRatingItemDTO.getGrade());
                        questionComment.put(supplierRatingItemDTO.getQuestionId(), supplierRatingItemDTO.getComments());
                    }
                    //Before create, check if there already has the rating
                    SrRating existRating = ratingRepository.findByObjectIdAndObjectClassId(supplierRatingOrderItemDTO.getOrderItemId(),
                            ObjectClassID.ORDER_ITEM);
                    if (existRating != null) throw new ForbiddenException("There already exist the rating for order " + supplierRatingDTO.getOrderId());
                    srRating = createRating((List<SrQuestion>) questionMap.get("questionList"), nooshUserId, supplierRatingDTO.getOrderId(),
                            supplierRatingOrderItemDTO.getOrderItemId(), supplierRatingDTO.getIsRateByOrder()
                                    ? ObjectClassID.ORDER : ObjectClassID.ORDER_ITEM, (Long) questionMap.get("questionnaireId"),
                            queryWorkgroupId, supplierRatingDTO.getSupplierWorkgroupId(), ((Boolean) questionMap.get("naAllowed"))
                                    ? (short) 1 : (short) 0, ((Long) questionMap.get("granularity")).shortValue(),
                            supplierRatingOrderItemDTO.getRatingComment(), 50, questionComment, questionValue);

                }
            }
            if (srRating != null) {
                createTrackingForRating(nooshUserId, orderTitle, projectTitle, TrackingTypeID.QUESTIONNAIRE_EDITED,
                        supplierRatingDTO, objectMapper, projectNumber);
                if (isComplete) {
                    srRating.setCompleteDate(LocalDateTime.now());
                    ratingRepository.save(srRating);
                    completeRatingTask(supplierRatingDTO, srRating, orderTitle, projectTitle, objectMapper, projectNumber, queryWorkgroupId, nooshUserId);
                }
            }
        } else if (!isComplete) {
            //Do update
            srRating = updateRating(supplierRatingDTO, isComplete);
            createTrackingForRating(nooshUserId, orderTitle, projectTitle,
                    srRating.getCompleteDate() == null ? TrackingTypeID.QUESTIONNAIRE_EDITED : TrackingTypeID.QUESTIONNAIRE_REVISED,
                    supplierRatingDTO, objectMapper, projectNumber);
        } else {
            srRating = updateRating(supplierRatingDTO, isComplete);
            completeRatingTask(supplierRatingDTO, srRating, orderTitle, projectTitle, objectMapper, projectNumber, queryWorkgroupId, nooshUserId);
        }
        return srRating;
    }

    private void completeRatingTask(SupplierRatingDTO supplierRatingDTO, SrRating srRating, String orderTitle,
                                    String projectTitle, ObjectMapper objectMapper, String projectNumber,
                                    Long queryWorkgroupId, Long nooshUserId) throws Exception {
        //Complete task
        try {
            String taskJson = taskOpenFeignClient.getTaskByType(TaskTypeID.COMPLETE_SUPPLIER_RATING, ObjectClassID.ORDER,
                    supplierRatingDTO.getOrderId());
            if (taskJson != null && taskJson.length() > 0 && taskJson.indexOf("taskTypeId") > 0) {
//                JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ServerResponse.class, TaskVO.class);
//                ServerResponse<TaskVO> taskresponse = objectMapper.readValue(taskJson, javaType);
                JSONObject json = new JSONObject(taskJson);
                JSONObject taskData = (JSONObject) json.getJSONArray("data").get(0);
                Long taskId = taskData.getLong("id");
                Long statusId = taskData.getLong("statusId");
                if (statusId.longValue() != ObjectStateID.TASK_COMPLETED) {
                    TaskUpdateDTO taskUpdateDTO = new TaskUpdateDTO();
                    taskUpdateDTO.setProjectId(supplierRatingDTO.getProjectId());
                    taskUpdateDTO.setTaskId(taskId);
                    taskUpdateDTO.setPercentComplete((long) 100);
                    taskUpdateDTO.setTaskStatusId(ObjectStateID.TASK_COMPLETED);
                    taskOpenFeignClient.updateTask(taskUpdateDTO, nooshUserId);
                }
            }
        } catch (Exception e) {
            log.error("Loading task failed " + e.getMessage());
        }

        //processUpdateRatingAverage
        Map<String, String> prefs = preferenceService.findGroupPrefs(queryWorkgroupId);
        Long spanCount = prefs.containsKey("SR_RATING_AVERAGE_NUMBER") ? Long.parseLong(prefs.get("SR_RATING_AVERAGE_NUMBER")) : (long) 0;
        List<Long> ids = new ArrayList<Long>();
        ids.add(ObjectClassID.ORDER);
        List<SrRatingAverage> srRatingAverages = srRatingAverageRepository.findByWorkgroupIdAndObjectId(
                queryWorkgroupId, supplierRatingDTO.getSupplierWorkgroupId(), ids);
        if (srRatingAverages != null && srRatingAverages.size() > 0) {
            for (SrRatingAverage sr : srRatingAverages) {
                sr.setAverageGrade(srRating.getAverageGrade());
                srRatingAverageRepository.save(sr);
            }
        } else {
            SrRatingAverage srRatingAverage = new SrRatingAverage();
            srRatingAverage.setAverageGrade(srRating.getAverageGrade());
            srRatingAverage.setObjectClassId(ObjectClassID.ORDER);
            srRatingAverage.setRatedByWorkGroupId(queryWorkgroupId);
            srRatingAverage.setRateForWorkgroupId(supplierRatingDTO.getSupplierWorkgroupId());
            srRatingAverage.setCount(spanCount);
            srRatingAverage.setComputedDate(LocalDateTime.now());
            srRatingAverage.setCreateUserId(nooshUserId);
            srRatingAverage.setModUserId(nooshUserId);
            srRatingAverageRepository.save(srRatingAverage);
        }

        createTrackingForRating(nooshUserId, orderTitle, projectTitle, TrackingTypeID.QUESTIONNAIRE_COMPLETED,
                supplierRatingDTO, objectMapper, projectNumber);
    }

    public SrRating updateRating(SupplierRatingDTO supplierRatingDTO, boolean isComplete) throws Exception {
        SrRating srRating = null;
        if (supplierRatingDTO.getIsRateByOrder() && supplierRatingDTO.getRateByOrder() != null && supplierRatingDTO.getRateByOrder().size() > 0) {
            for (SupplierRatingOrderDTO supplierRatingOrderDTO : supplierRatingDTO.getRateByOrder()) {
                List<SupplierRatingItemDTO> supplierRatingItemDTOs = supplierRatingOrderDTO.getRatingItem();
                srRating = ratingRepository.findById(supplierRatingOrderDTO.getRateId()).orElse(null);
                if (srRating == null) {
                    throw new NotFoundException("Rating not found");
                }
                srRating.setComments(supplierRatingOrderDTO.getRatingComment());
                return updateRating(srRating, isComplete, supplierRatingItemDTOs);
            }
        } else if ((!supplierRatingDTO.getIsRateByOrder()) && supplierRatingDTO.getRateByOrderItem() != null && supplierRatingDTO.getRateByOrderItem().size() > 0) {
            for (SupplierRatingOrderItemDTO supplierRatingOrderItemDTO : supplierRatingDTO.getRateByOrderItem()) {
                List<SupplierRatingItemDTO> supplierRatingItemDTOs = supplierRatingOrderItemDTO.getRatingItem();
                srRating = ratingRepository.findById(supplierRatingOrderItemDTO.getRateId()).orElse(null);
                if (srRating == null) {
                    throw new NotFoundException("Rating not found");
                }
                srRating.setComments(supplierRatingOrderItemDTO.getRatingComment());
                updateRating(srRating, isComplete, supplierRatingItemDTOs);
            }
        }
        return srRating;
    }

    private SrRating updateRating(SrRating srRating, boolean isComplete, List<SupplierRatingItemDTO> supplierRatingItemDTOs) throws Exception {
        long averageGrade = 0;
        long totalAdjustment = 0;
        if (supplierRatingItemDTOs != null && supplierRatingItemDTOs.size() > 0) {
            for (SupplierRatingItemDTO supplierRatingItemDTO : supplierRatingItemDTOs) {
                if (supplierRatingItemDTO.getId() == null && (supplierRatingItemDTO.getGrade() != null
                        || supplierRatingItemDTO.getComments() != null || supplierRatingItemDTO.getQuestionId() != null)) {
                    throw new Exception("Please pass the rating item id for update!");
                }
                if (srRating.getSrRatingItemList().stream().filter(
                        sr -> sr.getId().longValue() == supplierRatingItemDTO.getId()).count() == 0) {
                    throw new NotFoundException("Rating Item not found!");
                }
                SrRatingItem ratingItem = srRating.getSrRatingItemList().stream().filter(
                        sr -> sr.getId().longValue() == supplierRatingItemDTO.getId()).findAny().orElseGet(null);
                if (ratingItem != null) {
                    if (supplierRatingItemDTO.getComments() != null) {
                        ratingItem.setComments(supplierRatingItemDTO.getComments());
                    }
                    if (supplierRatingItemDTO.getGrade() != null) {
                        ratingItem.setGrade(supplierRatingItemDTO.getGrade());
                    }
                    ratingItemRepository.saveAndFlush(ratingItem);
//                    supplierRatingMybatisMapper.updateRatingItem(ratingItem.getComments(), ratingItem.getId(), ratingItem.getGrade());
                } else {
                    throw new NotFoundException("Rating Item not found!");
                }
            }
        }

        for (SrRatingItem srRatingItem : srRating.getSrRatingItemList()) {
            if (srRatingItem != null) {
                if (srRatingItem.getGrade() >= 0) {
                    averageGrade += srRatingItem.getGrade() * srRatingItem.getSrQuestion().getWeight();
                } else {
                    totalAdjustment += srRatingItem.getSrQuestion().getWeight();
                }
            }
        }
        if (totalAdjustment < 100) {
            srRating.setAverageGrade((long) Math.round(averageGrade / (100 - totalAdjustment)));
        } else {
            srRating.setAverageGrade((long) 0);
        }
        if (isComplete) {
            srRating.setCompleteDate(LocalDateTime.now());
        }
        ratingRepository.saveAndFlush(srRating);
//        supplierRatingMybatisMapper.updateRatingGrade(srRating.getId(), srRating.getAverageGrade(), isComplete);
        return srRating;
    }

    public SrRating createRating(List<SrQuestion> questionList, long userId, long orderId,
                                 long orderItemId,
                                 long objectClassId,
                                 long questionnaireId,
                                 long byWorkGroupId,
                                 long forWorkGroupId,
                                 short naAllowed,
                                 short granularity,
                                 long defaultGradeValue,
                                 String comment) {
        return createRating(questionList, userId, orderId, orderItemId, objectClassId, questionnaireId, byWorkGroupId,
                forWorkGroupId, naAllowed, granularity, comment, defaultGradeValue, null, null);
    }

    @Transactional(readOnly = false)
    public SrRating createRating(List<SrQuestion> questionList, long userId, long orderId,
                                 long orderItemId,
                                 long objectClassId,
                                 long questionnaireId,
                                 long byWorkGroupId,
                                 long forWorkGroupId,
                                 short naAllowed,
                                 short granularity,
                                 String comments,
                                 long defaultGradeValue, Map<Long, String> questionComment, Map<Long, Long> questionGrade) {
        try {
            SrRating rating = new SrRating();
            rating.setQuestionnaireId(questionnaireId);
            rating.setRatedByWorkGroupId(byWorkGroupId);
            rating.setRateForWorkGroupId(forWorkGroupId);
            rating.setObjectId(objectClassId == ObjectClassID.ORDER ? orderId : orderItemId);
            rating.setObjectClassId(objectClassId);
            rating.setGranularity(granularity);
            rating.setNaAllowed(naAllowed);
            rating.setComments(comments);
            rating.setCreateUserId(userId);
            rating.setModUserId(userId);
            ratingRepository.save(rating);
//        int insert = supplierRatingMybatisMapper.insertRating(questionnaireId, byWorkGroupId, forWorkGroupId,
//                objectClassId == ObjectClassID.ORDER ? orderId : orderItemId, objectClassId, comments, userId, defaultGradeValue, granularity, naAllowed);
//        SrRating rating = ratingRepository.findByObjectIdAndByWorkGroupId(objectClassId == ObjectClassID.ORDER ?
//                orderId : orderItemId, objectClassId, byWorkGroupId, forWorkGroupId);
            long averageGrade = 0;
            long totalAdjustment = 0;
            List<SrRatingItem> ratingItemList = new ArrayList<>();
            for (SrQuestion question : questionList) {
                SrRatingItem ratingItem = new SrRatingItem();
                ratingItem.setQuestionId(question.getId());
                ratingItem.setRatingId(rating.getId());
                ratingItem.setGrade(questionGrade != null && questionGrade.size() > 0 ? questionGrade.get(question.getId()) : defaultGradeValue);
                ratingItem.setComments(questionComment != null && questionComment.size() > 0 ? questionComment.get(question.getId()) : null);
                ratingItem.setModUserId(userId);
                ratingItem.setCreateUserId(userId);
                if (ratingItem.getGrade() >= 0) {
                    averageGrade += ratingItem.getGrade() * question.getWeight();
                } else {
                    totalAdjustment += question.getWeight();
                }
                ratingItemRepository.save(ratingItem);
                ratingItemList.add(ratingItem);
            }

            if (totalAdjustment < 100) {
                rating.setAverageGrade((long) Math.round(averageGrade / (100 - totalAdjustment)));
            } else {
                rating.setAverageGrade((long) 0);
            }
            ratingRepository.saveAndFlush(rating);
            rating.setSrRatingItemList(ratingItemList);

            return rating;
        } catch (Exception e) {
            if (e instanceof org.springframework.dao.DataIntegrityViolationException) {
                throw new ForbiddenException("There already exist the rating for order :" + orderId);
            } else {
                throw new UnexpectedException(e.getMessage());
            }
        }
    }

    private void createTrackingForRating(Long nooshUserId, String orderTitle, String projectTitle, Long trackingTypeId,
                                         SupplierRatingDTO supplierRatingDTO, ObjectMapper objectMapper, String projectNumber)
            throws Exception {
        TrackingDTO tracking = new TrackingDTO();
        tracking.setTrTrackingTypeId(trackingTypeId);
        tracking.setCreateUserId(nooshUserId);
        tracking.setEnactingUserId(nooshUserId);
        LinkedHashMap map = new LinkedHashMap();
        map.put("order.title", orderTitle);
        map.put("projectName", projectTitle);
        tracking.setI18nDataMap(map);
        tracking.setAcSourceTypeId(SourceTypeID.NOOSH_ONE);
        tracking.setObjectClassId(ObjectClassID.ORDER);
        tracking.setObjectId(supplierRatingDTO.getOrderId());
        String trackingResponseJson = trackingOpenFeignClient.createTracking(tracking);
        if (trackingResponseJson != null && trackingResponseJson.length() > 0) {
            JSONObject json = new JSONObject(trackingResponseJson);
            Long trackingId = json.getJSONObject("data").getLong("trTrackingId");
            //Do insert for containable
            List<SyContainableDTO> syContainableDTOs = collaborationService.findOneByObjectIdAndObjectClassId(
                    supplierRatingDTO.getProjectId(), ObjectClassID.OBJECT_CLASS_PROJECT);
            if (syContainableDTOs != null && syContainableDTOs.size() > 0) {
                SyContainableDTO syContainableDTO = new SyContainableDTO();
                syContainableDTO.setCreateUserId(nooshUserId);
                syContainableDTO.setObjectClassId(ObjectClassID.OBJECT_CLASS_TRACKING);
                syContainableDTO.setObjectId(trackingId);
                syContainableDTO.setItemOcObjectStateId(ObjectStateID.CONTAINABLE_ACTIVE);
                syContainableDTO.setParentObjectId(supplierRatingDTO.getProjectId());
                syContainableDTO.setParentObjectClassId(ObjectClassID.OBJECT_CLASS_PROJECT);
                syContainableDTO.setParentSyContainableId(syContainableDTOs.get(0).getSyContainableId());
                syContainableDTO.setIsPublic((short) 0);
                syContainableDTO.setIsLinking((short) 0);
                syContainableDTO.setxParentTitle(projectTitle + "(" + projectNumber + ")");
                collaborationService.createContainableLinkToProject(syContainableDTO);
            }
        }
    }

    public List<SrSection> findSection() {
        return sectionRepository.findAll();
    }

    public boolean isSupplierRatingEnabledAndActive(long workgroupId) {
        List<String> prefids = new ArrayList<>();
        prefids.add("WORKGROUP_OPTION_SUPPLIER_RATING");
        prefids.add("SR_ACTIVE");

        Map<String, String> prefs = preferenceService.findGroupPrefs(workgroupId, prefids);
        boolean supplierRatingEnabled = preferenceService.check("WORKGROUP_OPTION_SUPPLIER_RATING", prefs);
        boolean supplierActive = preferenceService.check("SR_ACTIVE", prefs);
        return supplierRatingEnabled && supplierActive;
    }

    public SupplierScoreVO findSupplierScore(Long byWorkgroupId, Long forWorkgroupId) {
        SupplierScoreVO result = null;
        if (isSupplierRatingEnabledAndActive(byWorkgroupId)) {
            SrRatingAverage avg = srRatingAverageRepository.findSupplierWorkgroupRating(byWorkgroupId, forWorkgroupId, ObjectClassID.ORDER);
            if (avg != null && avg.getAverageGrade() != null) {
                result = new SupplierScoreVO();
                result.setAverageGrade(avg.getAverageGrade());
            }
        }
        return result;
    }

    public Map<Long, SupplierScoreVO> findSupplierScores(Long byWorkgroupId, List<Long> forWorkgroupIds) {
        Map<Long, SupplierScoreVO> result = new HashMap<>();
        if (forWorkgroupIds == null || forWorkgroupIds.isEmpty()) {
            return result;
        }

        if (isSupplierRatingEnabledAndActive(byWorkgroupId)) {

            List<SrRatingAverage> saList = srRatingAverageRepository.findSupplierWorkgroupRatings(byWorkgroupId, forWorkgroupIds, ObjectClassID.ORDER);
            saList.forEach(sa -> {
                if (sa.getAverageGrade() != null) {
                    SupplierScoreVO ss = new SupplierScoreVO();
                    ss.setAverageGrade(sa.getAverageGrade());
                    result.put(sa.getRateForWorkgroupId(), ss);
                }
            });
        }

        return result;
    }
}
