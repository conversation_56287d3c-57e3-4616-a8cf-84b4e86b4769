package com.noosh.app.service.quote;

import com.noosh.app.commons.constant.EnterpriseLinkDestination;
import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.spec.JobStatusDTO;
import com.noosh.app.commons.vo.quote.RfqAndQuoteDeskoidProposalVO;
import com.noosh.app.commons.dto.quote.QuoteWidgetDTO;
import com.noosh.app.commons.dto.quote.RfqWidgetDTO;
import com.noosh.app.commons.entity.quote.Proposal;
import com.noosh.app.commons.entity.quote.QuoteItem;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.vo.quote.ListItemProposalVO;
import com.noosh.app.commons.vo.quote.ListItemQuoteVO;
import com.noosh.app.commons.vo.quote.ListItemRfqVO;
import com.noosh.app.commons.vo.quote.RfqAndQuoteListVO;
import com.noosh.app.mapper.quote.ProposalMapper;
import com.noosh.app.repository.jpa.quote.ProposalRepository;
import com.noosh.app.repository.jpa.quote.QuoteItemRepository;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.mybatis.quote.QuoteMyBatisMapper;
import com.noosh.app.repository.mybatis.spec.SpecMyBatisMapper;
import com.noosh.app.service.permission.quote.SendQuotePermission;
import com.noosh.app.service.permission.rfq.SendRfqPermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.MutiCurrencyUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


@Service
@Transactional
public class QuoteService {

    @Autowired
    private QuoteItemRepository quoteItemRepository;
    @Autowired
    private QuoteMyBatisMapper quoteMyBatisMapper;
    @Autowired
    private ObjectStateRepository objectStateRepository;
    @Autowired
    private ProposalRepository proposalRepository;
    @Autowired
    private ProposalMapper proposalMapper;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private RfqService rfqService;
    @Autowired
    private SendRfqPermission sendRfqPermission;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private SendQuotePermission sendQuotePermission;
    @Autowired
    private MutiCurrencyUtil mutiCurrencyUtil;
    @Autowired
    private SpecMyBatisMapper specMyBatisMapper;

    @Transactional(readOnly = true)
    public RfqAndQuoteListVO getRfqAndQuoteListVO(Long projectId, Long workgroupId, Long userId) {
        ProjectDTO parentProject = projectService.findProjectById(projectId);

        RfqAndQuoteListVO vo = new RfqAndQuoteListVO();

        vo.setProjectName(parentProject.getTitle());
        vo.setProjectExternalLink(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(projectId));
        // RFQ
        if (permissionService.checkAll(PermissionID.VIEW_RFQ, workgroupId, userId, projectId)) {
            List<RfqWidgetDTO> rfqWidgetDTOList = rfqService.getAllRfqListByProjectId(projectId, workgroupId, null);
            rfqWidgetDTOList.forEach(rfq -> {
                // init rfq
                ListItemRfqVO listItemRfqVO = new ListItemRfqVO();
                listItemRfqVO.setName(rfq.getRfqTitle());
                listItemRfqVO.setState(rfq.getState());
                listItemRfqVO.setStateStrId(rfq.getStateStrId());
                listItemRfqVO.setDate(rfq.getSubmittedDate());
                listItemRfqVO.setExternalLink(rfq.getExternalLink());

                // init quote
                List<QuoteWidgetDTO> quoteDtoList = getAllRfqQuotesByProjectIdAndRfqId(
                        projectId, rfq.getRfqId(), workgroupId, userId, parentProject);
                List<ListItemQuoteVO> quotes = quoteDtoList.stream()
                        .map(quote -> getListItemQuoteVO(quote, parentProject.isClientProject()))
                        .collect(Collectors.toList());
                listItemRfqVO.getQuotes().addAll(quotes);

                vo.getListWithRfq().add(listItemRfqVO);
            });

            if (sendRfqPermission.checkState(parentProject)) {
                HashMap<String, String> params = new HashMap<>();
                params.put("objectId", "" + projectId);
                params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
                params.put("selectSpecMode", "rfe");
                vo.setCreateRfqLink(NooshOneUrlUtil.composeLinkToEnterprise("/noosh/procurement/estimating/quote/selectSpecs", params));
            }
        }

        // QUOTE
        if (permissionService.checkAll(PermissionID.VIEW_QUOTE, workgroupId, userId, projectId)) {

            List<QuoteWidgetDTO> quoteWithoutRfq = getAllRfqQuotesByProjectIdAndRfqId(
                    projectId, null, workgroupId, userId, parentProject);
            List<ListItemQuoteVO> quotes = quoteWithoutRfq.stream()
                    .map(quote -> getListItemQuoteVO(quote, parentProject.isClientProject()))
                    .collect(Collectors.toList());
            vo.getListWithoutRfq().addAll(quotes);

            if (sendQuotePermission.check(parentProject, workgroupId, userId, projectId)) {
                HashMap<String, String> params = new HashMap<>();
                params.put("objectId", "" + projectId);
                params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
                vo.setCreateQuoteLink(NooshOneUrlUtil.composeLinkToEnterprise("/noosh/procurement/estimating/quote/selectRfq", params));
            }
        }

        // PROPOSAL
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        Boolean isProposalEnable = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROPOSAL_WRITER, groupPrefs);
        isProposalEnable = isProposalEnable
                && parentProject.isBrokerOutsourcerProject()
                && permissionService.checkAll(PermissionID.VIEW_PROPOSAL, workgroupId, userId, projectId);
        if (isProposalEnable) {
            AtomicLong quoteCount = new AtomicLong();

            vo.getListWithRfq().forEach(rfqVO -> {
                rfqVO.getQuotes().forEach(quoteVO -> {
                    if (quoteVO.isPending() || quoteVO.isDraft() || quoteVO.isAccepted()) {
                        quoteCount.getAndIncrement();
                    }
                    quoteVO.getProposals().addAll(getListItemProposalVO(quoteVO, projectId));
                });
            });

            vo.getListWithoutRfq().forEach(quoteVO -> {
                if (quoteVO.isPending() || quoteVO.isDraft() || quoteVO.isAccepted()) {
                    quoteCount.getAndIncrement();
                }
                quoteVO.getProposals().addAll(getListItemProposalVO(quoteVO, projectId));
            });

            if (quoteCount.longValue() > 0) {
                HashMap<String, String> params = new HashMap<>();
                params.put("objectId", "" + projectId);
                params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
                vo.setCreateProposalLink(NooshOneUrlUtil.composeLinkToEnterprise("/noosh/procurement/estimating/quote/selectQuoteForProposal", params));
            }
        }
        return vo;
    }

    private void setQuoteAdditionalData(QuoteWidgetDTO quoteWidgetDTO, long currentUserWorkGroupId) {
        BigDecimal itemTotal = new BigDecimal("0");
        BigDecimal exItemTotal = new BigDecimal("0");
        List<QuoteItem> quoteItems = quoteItemRepository.findByQuoteId(quoteWidgetDTO.getId());
        for (QuoteItem item : quoteItems) {
            itemTotal = itemTotal.add(item.getItemTotal());
            exItemTotal = exItemTotal.add(item.getExItemTotal() != null ? item.getExItemTotal() : BigDecimal.ZERO);
        }

        quoteWidgetDTO.setItemTotal(itemTotal);
        quoteWidgetDTO.setItemTotalCurrencyId(quoteItems != null && quoteItems.size() > 0 ? quoteItems.get(0).getItemTotalCurrencyId() : null);
        quoteWidgetDTO.setExItemTotal(exItemTotal);
        quoteWidgetDTO.setExItemTotalCurrencyId(quoteItems != null && quoteItems.size() > 0 ? quoteItems.get(0).getExItemTotalCurrencyId() : null);

        if (quoteWidgetDTO.getClientWorkgroupId() != null && quoteWidgetDTO.getClientWorkgroupId() == currentUserWorkGroupId) {
            quoteWidgetDTO.setClient(true);
        }
        if (quoteWidgetDTO.getStateId() != null) {
            ObjectState objectState = objectStateRepository.findById(quoteWidgetDTO.getStateId()).orElse(null);
            quoteWidgetDTO.setStateStrId(objectState != null && objectState.getDescriptionStrId() != null
                    ? objectState.getDescriptionStrId().toString() : null);
        }
        quoteWidgetDTO.setProjectExternalLink(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(quoteWidgetDTO.getProjectId()));

        HashMap<String, String> params = new HashMap<>();
        params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
        params.put("objectId", "" + quoteWidgetDTO.getProjectId());
        params.put("quoteId", "" + quoteWidgetDTO.getId());
        quoteWidgetDTO.setExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_QUOTE, params));
        params.put("renderSpecs", "" + true);
        quoteWidgetDTO.setPrintDetailLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_QUOTE, params));

    }

    public List<QuoteWidgetDTO> getAllRfqQuotesByProjectIdAndRfqId(Long projectId, Long rfqId, long workgroupId, List<Long> quoteStatusIdFilter) {
        List<QuoteWidgetDTO> nonQuoteList = quoteMyBatisMapper.getAllQuotesByProjectIdAndRrfqId(projectId, rfqId, quoteStatusIdFilter);
        nonQuoteList.forEach(quote -> setQuoteAdditionalData(quote, workgroupId));
        return nonQuoteList;
    }

    public List<QuoteWidgetDTO> getAllRfqQuotesByProjectIdAndRfqId(Long projectId, Long rfqId, Long workgroupId, Long currentUserId, ProjectDTO project) {
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        List<QuoteWidgetDTO> nonQuoteList = quoteMyBatisMapper.getAllQuotesByProjectIdAndRrfqId(projectId, rfqId, null);
        nonQuoteList.forEach(quote -> setQuoteAdditionalData(quote, workgroupId));
        //permission check
        Map<String, Boolean> permissionMap = permissionService.getPermissionMap(Arrays.asList(PermissionID.CREATE_ORDER, PermissionID.CREATE_QUICK_ORDER), workgroupId, currentUserId, Arrays.asList(projectId));
        boolean canCreateOrder = permissionService.check(PermissionID.CREATE_ORDER, projectId, permissionMap);
        boolean canCreateQuickOrder = permissionService.check(PermissionID.CREATE_QUICK_ORDER, projectId, permissionMap);
        if (canCreateOrder || canCreateQuickOrder) {
            nonQuoteList.forEach(quote -> setCreateBuyOrderLink(quote, project, groupPrefs));
        }
        return nonQuoteList;
    }

    private List<ListItemProposalVO> getListItemProposalVO(ListItemQuoteVO quoteVO, long projectId) {
        List<Proposal> proposals = proposalRepository.findByQuoteId(quoteVO.getQuoteId());

        List<ListItemProposalVO> listItemProposalVOS = proposals.stream()
                .map(proposal -> {
                    ListItemProposalVO listItemProposalVO = proposalMapper.toListItemProposalVO(proposal, projectId);
                    listItemProposalVO.setValue(quoteVO.getValue());
                    listItemProposalVO.setValueCurrencyId(quoteVO.getValueCurrencyId());
                    listItemProposalVO.setExValue(quoteVO.getExValue());
                    listItemProposalVO.setExValueCurrencyId(quoteVO.getExValueCurrencyId());
                    return listItemProposalVO;
                })
                .collect(Collectors.toList());
        return listItemProposalVOS;
    }

    private ListItemQuoteVO getListItemQuoteVO(QuoteWidgetDTO quote, boolean isClientProject) {
        ListItemQuoteVO quoteVO = new ListItemQuoteVO();
        quoteVO.setQuoteId(quote.getId());
        quoteVO.setName(quote.getTitle() + " (" + quote.getId() + ")");
        quoteVO.setState(quote.getState());
        quoteVO.setStateId(quote.getStateId());
        quoteVO.setStateStrId(quote.getStateStrId());
        if(quote.getSubmitDate() != null) {
            quoteVO.setDate(quote.getSubmitDate());
        }
        quoteVO.setValue(quote.getTotal());
        quoteVO.setValueCurrencyId(quote.getTotalCurrencyId());
        quoteVO.setExValue(quote.getExTotal());
        quoteVO.setExValueCurrencyId(quote.getExTotalCurrencyId());
        quoteVO.setExternalLink(quote.getExternalLink());
        quoteVO.setPrintLink(quote.getPrintDetailLink());

        quoteVO.setIsMutiCurrency(mutiCurrencyUtil.isMutiCurrency(quote));
        quoteVO.setIsShowBothCurrency(mutiCurrencyUtil.isShowBothCurrency(quote.getBuyerWorkgroupId(), isClientProject));
        quoteVO.setCreateBuyOrderLink(quote.getCreateBuyOrderLink());
        return quoteVO;
    }

    @Deprecated // not sure if we need this
    public String getPageTitle(ProjectDTO parent, boolean isProposalEnable) {
        ProjectDTO masterProject = parent;
        if (!parent.isMaster()) {
            masterProject = projectService.findProjectById(parent.getMasterProjectId());
        }

        String pageTitle = "Quotes";
        if (parent.isBrokerProject()) {
            if (isProposalEnable) {
                if (parent.isClientNotOnNoosh()) {
                    pageTitle = "Quotes and Proposals";
                } else {
                    pageTitle = "RFQs, Quotes and Proposals";
                }
            } else {
                pageTitle = "RFQs and Quotes";
            }
        } else if (parent.isOutsourcerProject() && isProposalEnable) {
            pageTitle = "Quotes and Proposals";
        } else if (parent.isClientProject() && masterProject.isBrokerProject()) {
            pageTitle = "RFQs and Quotes";
        }
        return pageTitle;
    }


    public List<RfqAndQuoteDeskoidProposalVO> getRfqAndQuoteDeskoidProposalVOList(Long quoteId, Long ProjectId) {
        List<Proposal> proposals = proposalRepository.findByQuoteId(quoteId);
        if (proposals != null && proposals.size() > 0) {
            List<RfqAndQuoteDeskoidProposalVO> rfqAndQuoteDeskoidProposalVOS = proposals.stream().map(proposalMapper::toRfqAndQuoteDeskoidProposalVO).collect(Collectors.toList());
            rfqAndQuoteDeskoidProposalVOS.forEach(rfqAndQuoteDeskoidProposalVO -> {
                HashMap<String, String> params = new HashMap<>();
                params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
                params.put("objectId", "" + ProjectId);
                params.put("proposalId", "" + rfqAndQuoteDeskoidProposalVO.getId());
                rfqAndQuoteDeskoidProposalVO.setExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_PROPOSAL, params));
            });
            return rfqAndQuoteDeskoidProposalVOS;
        } else {
            return new ArrayList<>();
        }
    }

    private void setCreateBuyOrderLink(QuoteWidgetDTO quoteWidgetDTO, ProjectDTO project, Map<String, String> groupPrefs) {
        boolean isAccepted = quoteWidgetDTO.isAccepted();
        boolean canCreateBuyOrder;
        //status check
        Boolean isEnableCreatBuyOrderFromPending = preferenceService.check(PreferenceID.PC_QUOTE_ALLOW_BUY_ORDER_FROM_PENDING, groupPrefs);
        if (isEnableCreatBuyOrderFromPending) {
            boolean isPending = quoteWidgetDTO.isPending();
            canCreateBuyOrder = isAccepted || isPending;
        } else {
            canCreateBuyOrder = isAccepted;
        }
        if (!canCreateBuyOrder || !project.isBrokerOutsourcerProject()) {
            canCreateBuyOrder = false;
        }
        if (canCreateBuyOrder) {
            if (hasUnboughtItems(quoteWidgetDTO)) {
                //set buy order link
                HashMap<String, String> params = new HashMap<>();
                params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
                params.put("objectId", "" + quoteWidgetDTO.getProjectId());
                params.put("quoteId", "" + quoteWidgetDTO.getId());
                params.put("acName", "ACTION_BUY_ORDERS_FOR_QUOTE");
                quoteWidgetDTO.setCreateBuyOrderLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.ACTION_BUY_ORDERS_FOR_QUOTE, params));
            }
        }
    }

    private boolean hasUnboughtItems(QuoteWidgetDTO quoteWidgetDTO) {
        // for each item,
        // check order counter where the quote's supplier is the buyer
        // since we are looking for buy order, the buy order does not have clientId
        // if non exists, buy order is needed.
        List<QuoteItem> quoteItems = quoteItemRepository.findByQuoteId(quoteWidgetDTO.getId());
        for (QuoteItem quoteItem : quoteItems) {
            Long jobId = quoteItem.getJobId();
            List<JobStatusDTO> jobStatusDTOs = specMyBatisMapper.getAllJobStatusByIdAndWorkgroupId(jobId, quoteWidgetDTO.getSupplierWorkgroupId());
            JobStatusDTO js = null;
            if (jobStatusDTOs != null && jobStatusDTOs.size() > 0) {
                for (JobStatusDTO jobStatusDTO : jobStatusDTOs) {
                    if (quoteWidgetDTO.getSupplierWorkgroupId() != null && quoteWidgetDTO.getSupplierWorkgroupId().equals(jobStatusDTO.getBuyerAcWorkgroupId()) &&
                            quoteWidgetDTO.getSupplierWorkgroupId().equals(jobStatusDTO.getOwnerAcWorkgroupId()) && !jobStatusDTO.getIsForClient()) {
                        js = jobStatusDTO;
                        break;
                    }
                }
                if (js != null) {
                    long orderCount = js.getOrderAcceptedCount() + js.getOrderCompletedCount() + js.getOrderPendingCount();
                    if (orderCount == 0) {
                        // has status and orders
                        return true;
                    }
                } else {
                    return true;
                }
            } else {
                return true;
            }
        }
        return false;
    }

}
