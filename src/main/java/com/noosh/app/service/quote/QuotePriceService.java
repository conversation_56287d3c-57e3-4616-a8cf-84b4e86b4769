package com.noosh.app.service.quote;

import com.noosh.app.commons.entity.quote.QuotePrice;
import com.noosh.app.repository.jpa.quote.QuotePriceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 3/14/2022
 */
@Service
@Transactional(readOnly = true)
public class QuotePriceService {

    @Autowired
    private QuotePriceRepository quotePriceRepository;

    public QuotePrice getAggregatedQuotePrice(QuotePrice quotePrice) {

        QuotePrice thisQuotePrice;
        if (quotePrice.getParentQuotePriceId() != null && quotePrice.getParentQuotePriceId() > 0) {
            thisQuotePrice = quotePriceRepository.findById(quotePrice.getParentQuotePriceId()).orElse(null);
        } else {
            thisQuotePrice = quotePrice;
        }
        List<QuotePrice> includedChildren = quotePriceRepository.findAllByParentQuotePriceId(thisQuotePrice.getId()) ;
        int childrenCount = includedChildren != null ? includedChildren.size() : 0;
        if  (childrenCount == 0)
            return thisQuotePrice;

        // all the children are already in asc order
        // aggregrated from the first child to the last
        // sum all pricings, latest quantity override previous
        QuotePrice aggrBean = new QuotePrice();
        aggrBean.setQuoteItem(thisQuotePrice.getQuoteItem());

        BigDecimal preMarkup = BigDecimal.ZERO;
        BigDecimal markupFixed = BigDecimal.ZERO;
        BigDecimal price = BigDecimal.ZERO;
        BigDecimal tax = BigDecimal.ZERO;
        BigDecimal shipping = BigDecimal.ZERO;
        for (QuotePrice child : includedChildren) {
            // add child's prices to the aggr
            if (child.getPreMarkup() != null) {
                preMarkup = preMarkup.add(child.getPreMarkup());
            }
            if (child.getMarkupFixed() != null) {
                markupFixed = markupFixed.add(child.getMarkupFixed());
            }
            if (child.getPrice() != null) {
                price = price.add(child.getPrice());
            }
            if (child.getTax() != null) {
                tax = tax.add(child.getTax());
            }
            if (child.getShipping() != null) {
                shipping = shipping.add(child.getShipping());
            }
            aggrBean.setCustomPropertyId(child.getCustomPropertyId());
        }
        // set all attributes - including custom attr
        aggrBean.setQuoteItem(thisQuotePrice.getQuoteItem());
        // set the aggr prices
        aggrBean.setPreMarkup(preMarkup);
        aggrBean.setPrice(price);
        aggrBean.setMarkupFixed(markupFixed);
        aggrBean.setTax(tax);
        aggrBean.setShipping(shipping);
        return aggrBean;
    }
    
}
