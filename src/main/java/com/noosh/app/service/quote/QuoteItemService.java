package com.noosh.app.service.quote;

import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.entity.quote.QuoteItem;
import com.noosh.app.commons.entity.quote.QuotePrice;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.spec.SpecNode;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.jpa.spec.SpecRepository;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 3/14/2022
 */

@Service
@Transactional(readOnly = true)
public class QuoteItemService {


    @Autowired
    private QuotePriceService quotePriceService;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private SpecRepository specRepository;
    @Autowired
    private WorkgroupRepository workgroupRepository;

    public List<Spec> getSpecs(QuoteItem quoteItem, boolean includeOnlyVisible) {
        Set set = new HashSet();
        set.add(specRepository.findById(quoteItem.getSpecId()).orElse(null));

        List<QuotePrice> quotePrices = quoteItem.getQuotePrices();
        for (QuotePrice quotePrice : quotePrices) {
            if (quotePrice.getSourceSpecId() != null && quotePrice.getSourceSpecId() > 0) {
                Spec spec = specRepository.findById(quotePrice.getSourceSpecId()).orElse(null);
                SpecNode specNode = spec.getSpecNode();
                if (specNode == null) {
                    specNode = new SpecNode();
                    specNode.setId(quotePrice.getSourceSpecNodeId());
                    spec.setSpecNode(specNode);
                }
                // add to set when
                // 1. include all (ie not restricted to include only visible quote prices, OR
                // 2. include visible only && quotePrice is visible to client
                if (!includeOnlyVisible || (includeOnlyVisible && quotePrice.isVisibleToBuyer()))
                    set.add(spec);
            }
        }
        List<Spec> specs = new ArrayList<>();
        Collections.sort(specs, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                Spec s1 = (Spec) o1;
                Spec s2 = (Spec) o2;
                return s1.getSpecName().compareTo(s2.getSpecName());
            }
        });
        return specs;

    }

    public Set<Long> getQuantities(Spec spec, QuoteItem quoteItem){

        if (spec.getId().longValue() != quoteItem.getSpecId() )
            return getSourceQuantities(spec, quoteItem);

        Set<Long> set = new HashSet();
        List<QuotePrice> quotePrices = quoteItem.getQuotePrices();
        if (quotePrices == null) return null;
        int priceCount = quotePrices.size();
        for (int i = 0; i < priceCount; i++) {
            QuotePrice quotePrice =  quotePrices.get(i);
            // include options of the same source spec
            // and generic options
            if ((quotePrice.getSourceSpecId() != null && quotePrice.getSourceSpecId() == spec.getId().longValue()) ||
                    (quotePrice.getEstimateItemPriceId() == null && quotePrice.getOrderItemId() == null) ||  (quotePrice.getOriQuoteItemId() == null) ) {
                Long qty = quotePriceService.getAggregatedQuotePrice(quotePrice).getQuantity();
                qty = qty != null ? qty : 0;
                set.add(qty);
            }
        }
        return set;
    }

    public Set<Long> getSourceQuantities(Spec spec, QuoteItem quoteItem) {
        List<QuotePrice> quotePrices = spec == null
                ? quoteItem.getQuotePrices()
                : getQuotePrices(spec, quoteItem);

        if (quotePrices == null) return null;
        Set<Long> set = new HashSet();
        for (QuotePrice quotePrice : quotePrices) {
            // no generic options
            boolean isFromRateCard = quotePrice.isFromRatecard() && (null != quotePrice.getPrice() && quotePrice.getPrice().compareTo(BigDecimal.ZERO) != 0);
            if (quotePrice.getEstimateItemPriceId() <= 0 && quotePrice.getOrderItemId() <= 0 && quotePrice.getOriQuoteItemId() <=0 && !isFromRateCard) {
                continue;
            }
            Long qty = quotePrice.getQuantity();
            qty = qty != null ? qty : 0;
            set.add(qty);
        }
        return set;
    }

    public List<QuotePrice> getQuotePrices(Spec spec, QuoteItem quoteItem) {
        List<QuotePrice> list = new ArrayList();
        List<QuotePrice> quotePrices = quoteItem.getQuotePrices();
        if (quotePrices != null) {
            int priceCount = quotePrices.size();
            long specId = spec.getId().longValue() ;
            for (int i = 0; i < priceCount; i++) {
                QuotePrice quotePrice = quotePrices.get(i);
                if (quotePrice.getSourceSpecId() == specId) {
                    list.add(quotePrice);
                }
            }
        }
        return list;
    }

    public List<QuotePrice> getQuotePricesForQuantity(Spec spec, QuoteItem quoteItem, ProjectDTO ownerProject, boolean includeGenericOptions
            , boolean includeOnlyVisible, boolean useAggregation) {
        List<QuotePrice> qutePriceBeans = getQuotePricesForQuantity(spec, quoteItem, ownerProject, includeGenericOptions, includeOnlyVisible, useAggregation, false, false);
        return qutePriceBeans;
    }

    private List<QuotePrice> getQuotePricesForQuantity(Spec spec, QuoteItem quoteItem,
                                                       ProjectDTO ownerProject, boolean includeGenericOptions,
                                                       boolean includeOnlyVisible, boolean useAggregation,
                                                       boolean stablesSimpleQuote, boolean isQuoted){
        List<QuotePrice> quotePrices = spec != null
                && !includeGenericOptions ? getQuotePrices(spec, quoteItem) : quoteItem.getQuotePrices();

        // order by quote price id
        quotePrices = quotePrices.stream().sorted(Comparator.comparing(QuotePrice::getId)).collect(Collectors.toList());

        List<QuotePrice> list = new ArrayList<>();
        int priceCount = quotePrices.size();

        boolean isMultiMode = false;
        if (priceCount > 0) {
            long ownerGroupId = ownerProject.getOwnerWorkgroupId();
            isMultiMode = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CREATE_QUOTE_WITH_MULTI_OPTIONS, ownerGroupId);
        }
        for (QuotePrice quotePrice : quotePrices) {
            if (stablesSimpleQuote) {
                boolean isExcluded = excludeEstimatePrice(quotePrice);
                if(isExcluded){
                    continue;
                }
            }
            QuotePrice rootQuotePrice = quotePrice;
            if (useAggregation)
                quotePrice = quotePriceService.getAggregatedQuotePrice(quotePrice);

            if (includeOnlyVisible && !quotePrice.isVisibleToBuyer()) {
                continue;
            }

            boolean isFromRateCard = quotePrice.isFromRatecard() && (null != quotePrice.getPrice() && quotePrice.getPrice().compareTo(BigDecimal.ZERO) != 0);
            if (!includeGenericOptions
                    && quotePrice.getEstimateItemPriceId() == null
                    && quotePrice.getOrderItemId() == null
                    && quotePrice.getOriQuoteItemId() == null
                    && !isFromRateCard) {
                continue;
            }
            if (spec == null) {
                list.add(quotePrice);
            } else {
                if (isMultiMode) {
                    // spec comparision is always comparing the root spec
                    if ((rootQuotePrice.getSourceSpecId() != null && spec.getId().longValue() == rootQuotePrice.getSourceSpecId())
                            || (rootQuotePrice.getSourceSpecId() != null && spec.getId().longValue() == rootQuotePrice.getSourceSpecId()
                            && rootQuotePrice.getEstimateItemPriceId() == null && rootQuotePrice.getOrderItemId() == null)
                            || (rootQuotePrice.getSourceSpecId() != null && spec.getId().longValue() == rootQuotePrice.getSourceSpecId() && rootQuotePrice.getOriQuoteItemId() == null)) {
                        list.add(quotePrice);
                    }
                } else {
                    // spec comparision is always comparing the root spec
                    //For NKB144656, to fix the issue that showing duplicate quote price. The root cause is If the spec with option (source is Estimate) AND with the same quantity, then it would get error using 'this.quoteItem.getSpecId()'
                    //For Spec that without Source, rootQuotePrice.getSourceSpecId is -1
                    if (rootQuotePrice.getSourceSpecId() == null || (rootQuotePrice.getSourceSpecId() != null && quoteItem.getSpecId().longValue() == rootQuotePrice.getSourceSpecId())) {
                        if ((rootQuotePrice.getSourceSpecId() != null && spec.getId().longValue() == rootQuotePrice.getSourceSpecId())
                                || (spec.getId().longValue() == quoteItem.getSpecId()
                                && rootQuotePrice.getEstimateItemPriceId() == null && rootQuotePrice
                                .getOrderItemId() == null)
                                || (spec.getId().longValue() == quoteItem.getSpecId() && rootQuotePrice
                                .getOriQuoteItemId() == null)) {
                            list.add(quotePrice);
                        }
                    } else {
                        if ((rootQuotePrice.getSourceSpecId() != null && spec.getId().longValue() == rootQuotePrice.getSourceSpecId())
                                || (rootQuotePrice.getSourceSpecId() != null && spec.getId().longValue() == rootQuotePrice.getSourceSpecId()
                                && rootQuotePrice.getEstimateItemPriceId() == null && rootQuotePrice
                                .getOrderItemId() == null)
                                || (rootQuotePrice.getSourceSpecId() != null && spec.getId().longValue() == rootQuotePrice.getSourceSpecId() && rootQuotePrice
                                .getOriQuoteItemId() == null)) {
                            list.add(quotePrice);
                        }
                    }
                }
            }
        }
        return list;
    }

    private boolean excludeEstimatePrice(QuotePrice quotePrice){
        boolean isClude = false;
        if(quotePrice.getEstimateItemPriceId() == null){
            return true;
        }
        double quantity = quotePrice.getQuantity();
        EstimateItemPrice estimateItemPrice = quotePrice.getEstimateItemPrice();
        if(null==estimateItemPrice){
            return true;
        }

        BigDecimal estimatePrice = estimateItemPrice.getPrice();
        if(null==estimatePrice){
            return true;
        }
        double estimatePriceValue = estimatePrice.doubleValue();
        if(quantity==0||estimatePriceValue==0){
            return true;
        }

        return isClude;
    }

    public String findSupplierName(QuotePrice quotePrice, boolean isOutsourcer) {
        // if one has been manually entered, return it
        if (quotePrice.getSourceWorkgroupId() == null) {
            return quotePrice.getSupplierName();
        }

        // else, retrieve it. if supplier, use real name. if not, find alias
        String supplierName = null;
        if (isOutsourcer) {
            Optional<Workgroup> workgroupOptional = workgroupRepository.findById(quotePrice.getSourceWorkgroupId());
            if (workgroupOptional.isPresent()) {
                Workgroup workgroup = workgroupOptional.get();
                supplierName = workgroup.getName();
            }
        } else {
            if (quotePrice.getSupplierName() != null) {
                supplierName = quotePrice.getSupplierName();
            } else {
                Optional<Workgroup> workgroupOptional = workgroupRepository.findById(quotePrice.getSourceWorkgroupId());
                if (workgroupOptional.isPresent()) {
                    Workgroup workgroup = workgroupOptional.get();
                    supplierName = workgroup.getName();
                }
            }
        }
        return supplierName;
    }
}
