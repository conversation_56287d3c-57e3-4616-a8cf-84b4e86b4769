package com.noosh.app.service.quote;

import com.noosh.app.commons.constant.EnterpriseLinkDestination;
import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.dto.quote.RfqWidgetDTO;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.mybatis.quote.RfqMyBatisMapper;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;


@Service
@Transactional
public class RfqService {
    @Autowired
    private RfqMyBatisMapper rfqMyBatisMapper;
    @Autowired
    private ObjectStateRepository objectStateRepository;


    private void setAdditionalDataForRfq(RfqWidgetDTO rfqWidgetDTO, long currentUserWorkGroupId) {
        if (rfqWidgetDTO.getClientWorkgroupId() == currentUserWorkGroupId) {
            rfqWidgetDTO.setShowSupplierWorkgroup(true);
        }

        if (rfqWidgetDTO.getStateId() != null) {
            ObjectState objectState = objectStateRepository.findById(rfqWidgetDTO.getStateId()).orElse(null);
            rfqWidgetDTO.setStateStrId(objectState != null && objectState.getDescriptionStrId() != null
                    ? objectState.getDescriptionStrId().toString() : null);
        }

        rfqWidgetDTO.setProjectExternalLink(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(rfqWidgetDTO.getProjectId()));

        HashMap<String, String> params = new HashMap<>();
        params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
        params.put("objectId", "" + rfqWidgetDTO.getProjectId());
        params.put("rfqId", "" + rfqWidgetDTO.getRfqId());
        rfqWidgetDTO.setExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_RFQ, params));
    }

    public List<RfqWidgetDTO> getAllRfqListByProjectId(Long projectId, long workgroupId, List<Long> rfqStatusIdFilter) {
        List<RfqWidgetDTO> rfqWidgetDTOList = rfqMyBatisMapper.getAllRfqByProjectId(projectId, rfqStatusIdFilter);
        rfqWidgetDTOList.forEach(rfq -> setAdditionalDataForRfq(rfq, workgroupId));
        return rfqWidgetDTOList;
    }
}
