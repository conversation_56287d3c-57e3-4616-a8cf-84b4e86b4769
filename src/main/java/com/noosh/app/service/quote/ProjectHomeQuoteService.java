package com.noosh.app.service.quote;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.quote.QuoteWidgetDTO;
import com.noosh.app.commons.dto.quote.RfqWidgetDTO;
import com.noosh.app.commons.vo.quote.RfqAndQuoteDeskoidQuoteVO;
import com.noosh.app.commons.vo.quote.RfqAndQuoteDeskoidRfqWithQuoteVO;
import com.noosh.app.commons.vo.quote.RfqAndQuoteDeskoidVO;
import com.noosh.app.mapper.quote.QuoteMapper;
import com.noosh.app.service.permission.quote.SendQuotePermission;
import com.noosh.app.service.permission.rfq.SendRfqPermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Shan
 * @since 12/6/2022
 */
@Service
public class ProjectHomeQuoteService {

    @Autowired
    private QuoteService quoteService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private RfqService rfqService;
    @Autowired
    private SendRfqPermission sendRfqPermission;
    @Autowired
    private SendQuotePermission sendQuotePermission;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private QuoteMapper quoteMapper;

    public RfqAndQuoteDeskoidVO getRfqAndQuoteList(Long projectId, Long workgroupId, Long userId, List<Long> rfqStatusIdFilter, List<Long> quoteStatusIdFilter) {

        List<RfqAndQuoteDeskoidRfqWithQuoteVO> rfqWithQuoteVOList = new ArrayList<>();
        if (permissionService.checkAll(PermissionID.VIEW_RFQ, workgroupId, userId, projectId)) {
            List<RfqWidgetDTO> rfqWidgetDTOList = rfqService.getAllRfqListByProjectId(projectId, workgroupId, rfqStatusIdFilter);
            rfqWidgetDTOList.stream().forEach(r -> {
                RfqAndQuoteDeskoidRfqWithQuoteVO rfqWithQuoteVO = new RfqAndQuoteDeskoidRfqWithQuoteVO();
                rfqWithQuoteVO.setRfqWidgetDTO(quoteMapper.toRfqAndQuoteDeskoidRfqVO(r));
                List<QuoteWidgetDTO> quoteList = quoteService.getAllRfqQuotesByProjectIdAndRfqId(projectId, r.getRfqId(), workgroupId, quoteStatusIdFilter);
                if (quoteList != null && quoteList.size() > 0) {
                    rfqWithQuoteVO.setQuoteList(quoteList.stream().map(quoteMapper::toRfqAndQuoteDeskoidQuoteVO).collect(Collectors.toList()));
                } else {
                    rfqWithQuoteVO.setQuoteList(new ArrayList<>());
                }
                rfqWithQuoteVOList.add(rfqWithQuoteVO);
            });
        }

        List<RfqAndQuoteDeskoidQuoteVO> nonRfqQuoteVOList = new ArrayList<>();
        if (permissionService.checkAll(PermissionID.VIEW_QUOTE, workgroupId, userId, projectId)) {
            List<QuoteWidgetDTO> nonRfqQuoteList = quoteService.getAllRfqQuotesByProjectIdAndRfqId(projectId, null, workgroupId, quoteStatusIdFilter);
            if (nonRfqQuoteList != null && nonRfqQuoteList.size() > 0) {
                nonRfqQuoteVOList = nonRfqQuoteList.stream().map(quoteMapper::toRfqAndQuoteDeskoidQuoteVO).collect(Collectors.toList());
            }
        }


        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        Boolean isProposalEnable = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROPOSAL_WRITER, groupPrefs);
        ProjectDTO parentProject = projectService.findProjectById(projectId);
        isProposalEnable = isProposalEnable &&
                parentProject.isBrokerOutsourcerProject() &&
                permissionService.checkAll(PermissionID.VIEW_PROPOSAL, workgroupId, userId, projectId);
        if (isProposalEnable) {
            rfqWithQuoteVOList.forEach(rfqWithQuoteVO -> {
                rfqWithQuoteVO.getQuoteList().forEach(quoteVO -> {
                    quoteVO.setProposalWidgetDTOs(quoteService.getRfqAndQuoteDeskoidProposalVOList(quoteVO.getId(), quoteVO.getProjectId()));
                });
            });
            nonRfqQuoteVOList.forEach(quoteVO -> {
                quoteVO.setProposalWidgetDTOs(quoteService.getRfqAndQuoteDeskoidProposalVOList(quoteVO.getId(), quoteVO.getProjectId()));
            });
        }

        Boolean isRfqEnable = sendRfqPermission.checkState(parentProject);

        RfqAndQuoteDeskoidVO result = new RfqAndQuoteDeskoidVO();
        result.setRfqWithQuote(rfqWithQuoteVOList);
        result.setQuoteWithoutRfq(nonRfqQuoteVOList);
        result.setIsProposalEnable(isProposalEnable);
        result.setIsRfqEnable(isRfqEnable);
        result.setIsShowPrintDetail(!sendRfqPermission.checkShowRfqDetail(parentProject));

        if (sendRfqPermission.checkState(parentProject)) {
            HashMap<String, String> params = new HashMap<>();
            params.put("objectId", "" + projectId);
            params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
            params.put("selectSpecMode", "rfe");
            result.setCreateRfqExternalUrl(NooshOneUrlUtil.composeLinkToEnterprise("/noosh/procurement/estimating/quote/selectSpecs", params));
        }

        if (sendQuotePermission.check(parentProject, workgroupId, userId, projectId)) {
            HashMap<String, String> params = new HashMap<>();
            params.put("objectId", "" + projectId);
            params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
            result.setCreateQuoteExternalUrl(NooshOneUrlUtil.composeLinkToEnterprise("/noosh/procurement/estimating/quote/selectRfq", params));
        }

        return result;
    }
}
