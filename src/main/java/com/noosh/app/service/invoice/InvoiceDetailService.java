package com.noosh.app.service.invoice;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.breakout.BreakoutTypeDTO;
import com.noosh.app.commons.dto.costcenter.CostCenterAllocationDTO;
import com.noosh.app.commons.dto.costcenter.InvoiceOrderCostCenterDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDetailDTO;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.commons.dto.invoice.InvoiceItemDTO;
import com.noosh.app.commons.dto.invoice.InvoiceListDTO;
import com.noosh.app.commons.dto.invoice.RequestDTO;
import com.noosh.app.commons.dto.order.AggregatedOrderVersionDTO;
import com.noosh.app.commons.dto.order.OrderDTO;
import com.noosh.app.commons.dto.order.OrderItemDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.feign.WorkgroupOpenFeignClient;
import com.noosh.app.mapper.OrderVersionMapper;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.account.Address;
import com.noosh.app.commons.entity.account.Contact;
import com.noosh.app.commons.entity.invoice.Invoice;
import com.noosh.app.commons.entity.job.PcJob;
import com.noosh.app.commons.entity.order.OrBillingRecipient;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.entity.security.ServiceProvider;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.security.WorkgroupSd;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.spec.SpecNode;
import com.noosh.app.commons.entity.terms.AcTerms;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.address.AddressVO;
import com.noosh.app.commons.vo.breakout.InvoiceBreakoutVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.invoice.*;
import com.noosh.app.commons.vo.invoice.order.OrderVO;
import com.noosh.app.commons.vo.invoice.order.SelectOrderVO;
import com.noosh.app.commons.vo.order.TermsVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.repository.billing.BillingRecipientRepository;
import com.noosh.app.repository.jpa.account.AccountUserRepository;
import com.noosh.app.repository.jpa.account.ContactRepository;
import com.noosh.app.repository.jpa.invoice.InvoiceRepository;
import com.noosh.app.repository.jpa.job.JobRepository;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.jpa.security.ServiceProviderRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.jpa.security.WorkgroupSdRepository;
import com.noosh.app.repository.jpa.spec.SpecNodeRepository;
import com.noosh.app.repository.jpa.spec.SpecRepository;
import com.noosh.app.repository.mybatis.invoice.InvoiceMyBatisMapper;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.repository.terms.AcTermsRepository;
import com.noosh.app.service.AggregatedOrderService;
import com.noosh.app.service.InvoiceService;
import com.noosh.app.service.OrderService;
import com.noosh.app.service.costcenter.CostCenterService;
import com.noosh.app.service.permission.invoice.*;
import com.noosh.app.service.permission.ordering.CostCenterAllocationPermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.terms.TermsService;
import com.noosh.app.service.util.DateUtil;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.NooshOneUrlUtil;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @auther mario
 * @date 7/21/2020
 */
@Service
@Transactional(readOnly = true)
public class InvoiceDetailService {

    @Autowired
    private ProjectService projectService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private AggregatedOrderService aggregatedOrderService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private AccountUserRepository accountUserRepository;
    @Autowired
    private SpecRepository specRepository;
    @Autowired
    private SpecNodeRepository specNodeRepository;
    @Autowired
    private ContactRepository contactRepository;
    @Autowired
    private JobRepository jobRepository;
    @Autowired
    private ObjectStateRepository objectStateRepository;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private WorkgroupSdRepository workgroupSdRepository;
    @Autowired
    private ServiceProviderRepository serviceProviderRepository;
    @Autowired
    private AcTermsRepository acTermsRepository;
    @Autowired
    private OrderMyBatisMapper orderMyBatisMapper;
    @Autowired
    private InvoiceMyBatisMapper invoiceMyBatisMapper;
    @Autowired
    private EditInvoicePermission editInvoicePermission;
    @Autowired
    private DeleteInvoicePermission deleteInvoicePermission;
    @Autowired
    private RejectInvoicePermission rejectInvoicePermission;
    @Autowired
    private RetractInvoicePermission retractInvoicePermission;
    @Autowired
    private AcceptInvoicePermission acceptInvoicePermission;
    @Autowired
    private ApproveInvoicePermission approveInvoicePermission;
    @Autowired
    private CreateInvoicePermission createInvoicePermission;
    @Autowired
    private CostCenterAllocationPermission costCenterAllocationPermission;
    @Autowired
    private I18NUtils i18NUtils;
    @Autowired
    private BillingRecipientRepository billingRecipientRepository;
    @Autowired
    private OrderVersionMapper orderVersionMapper;
    @Autowired
    private InvoiceRepository invoiceRepository;
    @Autowired
    private SendInvoicePermission sendInvoicePermission;
    @Autowired
    private WorkgroupOpenFeignClient workgroupOpenFeignClient;
    @Autowired
    private CostCenterService costCenterService;
    @Autowired
    private TermsService termsService;

    private Map<String, Boolean> getPermissionMap(Long workgroupId, Long userId, Long projectId) {
        List<Long> permissionIds = new ArrayList<>();
        permissionIds.add(PermissionID.VIEW_ORDER);
        permissionIds.add(PermissionID.CREATE_INVOICE);
        permissionIds.add(PermissionID.EDIT_INVOICE);
        permissionIds.add(PermissionID.DELETE_INVOICE);
        permissionIds.add(PermissionID.SEND_INVOICE);
        permissionIds.add(PermissionID.VIEW_INVOICE);
        permissionIds.add(PermissionID.ACCEPT_INVOICE);
        permissionIds.add(PermissionID.REJECT_INVOICE);
        permissionIds.add(PermissionID.RETRACT_INVOICE);
        permissionIds.add(PermissionID.APPROVE_INVOICE);

        List<Long> projectIds = new ArrayList<>();
        projectIds.add(projectId);

        return permissionService.getPermissionMap(permissionIds, workgroupId, userId, projectIds);
    }

    public InvoiceDetailVO detail(Long userId, Long workgroupId, Long projectId, Long invoiceId, String locale) {
        Map<String, Boolean> permMap = getPermissionMap(workgroupId, userId, projectId);
        if (!permissionService.check(PermissionID.VIEW_INVOICE, projectId, permMap)) {
            throw new NoPermissionException("Your don't have permission to view invoice detail");
        }

        if (!permissionService.check(PermissionID.VIEW_ORDER, projectId, permMap)) {
            throw new NoPermissionException("Your don't have permission to view invoice detail");
        }

        InvoiceDTO invoiceDTO = invoiceMyBatisMapper.findInvoiceDetail(projectId, invoiceId);
        if (invoiceDTO == null) {
            throw new NotFoundException("invoice Id not found");
        }
        boolean isDualCurrency = invoiceDTO.getRate() != null && invoiceDTO.getRate().compareTo(BigDecimal.ZERO) > 0 && invoiceDTO.getExCurrencyId() != null;
        invoiceDTO.setDualCurrency(isDualCurrency);

        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        OrderDTO orderDTO = orderMyBatisMapper.findOrderById(projectId, invoiceDTO.getOrderId());
        OrderVersionDTO orderVersionDTO = orderService.findSimpleOrderById(invoiceDTO.getOrderId());
        // aggregated
        OrderVersionDTO originalOrder;
        if (orderVersionDTO.isChangeOrder()) {
            originalOrder = orderService.findSimpleOrderById(orderDTO.getParentOrderId());
        } else {
            originalOrder = orderVersionDTO;
        }
        originalOrder.setParent(projectDTO);
        invoiceDTO.setParent(projectDTO);
        invoiceDTO.setOrder(orderDTO);
        invoiceDTO.setOrderVersion(orderVersionDTO);
        orderDTO.setOrderVersion(originalOrder);
        OrderVersionDTO copyOfOriginal = orderVersionMapper.toCopy(originalOrder);
        OrderVersionDTO aggrOrder = aggregatedOrderService.getAggregatedOrder(copyOfOriginal, null, workgroupId, userId);

        InvoiceDetailVO invoiceDetailVO = new InvoiceDetailVO();
        invoiceDetailVO.setProjectName(projectDTO.getTitle());
        invoiceDetailVO.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(projectId));
        invoiceDetailVO.setIsFinal(invoiceDTO.getFinal());
        invoiceDetailVO.setIsNonBillable(invoiceDTO.getNonBillable());
        invoiceDetailVO.setIsAccepted(invoiceDTO.getApproved());
        invoiceDetailVO.setShowCostAllocation(costCenterAllocationPermission.check(orderVersionDTO, workgroupId, userId, projectId));
        invoiceDetailVO.setIsDualCurrency(invoiceDTO.isDualCurrency());
        invoiceDetailVO.setIsSell(orderVersionDTO.isSellOrder(projectDTO));
        if (isDualCurrency) {
            invoiceDetailVO.setRate(invoiceDTO.getRate());
            invoiceDetailVO.setExCurrencyId(invoiceDTO.getExCurrencyId());
        }
        // invoice item and breakout
        List<InvoiceItemDTO> invoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoiceDTO.getId(), workgroupId);
        this.populateBreakouts(invoiceItems);

        invoiceDTO.setInvoiceItemDTOList(invoiceItems);

        // invoice summary
        InvoiceInfoVO invoiceInfoVO = this.getInvoiceSummary(workgroupId, projectDTO, orderDTO, invoiceDTO, locale);
        invoiceDetailVO.setInfo(invoiceInfoVO);

        // get accepted invoice and item
        List<InvoiceDTO> acceptedInvoiceList = invoiceMyBatisMapper.findInvoiceForOrder(projectId, invoiceDTO.getOrderId());
        List<InvoiceDTO> acceptedList = new ArrayList();
        if (!acceptedInvoiceList.isEmpty()) {
            for (InvoiceDTO invoice : acceptedInvoiceList) {
                if (!invoice.isAccepted())
                    continue;
                if (invoice.getAcceptedDate() != null && invoiceDTO.getSubmitDate() != null
                    && invoice.getAcceptedDate().compareTo(invoiceDTO.getSubmitDate()) >= 0)
                    continue;
                acceptedList.add(invoice);
            }
        }
        acceptedList = acceptedList.stream().map(invoice -> {
            List<InvoiceItemDTO> acceptedInvoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoice.getId(), workgroupId);
            invoice.setInvoiceItemDTOList(acceptedInvoiceItems);
            return invoice;
        }).collect(Collectors.toList());

        Map<String, String> prefs = preferenceService.findUserPrefs(invoiceDTO.getBuyerWorkgroupId(), userId);

        InvoiceDTO totalAcceptedInvoiceBean = acceptedList.size() > 0
                ? createAggregatedInvoice(userId, acceptedList, invoiceDTO, invoiceDTO.getSubmitDate(), false, prefs)
                : createInvoice(userId, orderDTO, invoiceDTO, false, prefs);
        acceptedList.add(invoiceDTO);
        InvoiceDTO totalInvoiceBean = createAggregatedInvoice(userId, acceptedList , invoiceDTO, null, true, prefs);

        boolean closeOrderNegotiation = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION, prefs);
        String taxLabelString = preferenceService.getString(PreferenceID.PC_TAX_LABEL_STRING, prefs, null);
        if (taxLabelString == null) {
            taxLabelString = i18NUtils.getMessage(StringID.TAX_DEFAULT_LABEL_STR, locale);
        }
        invoiceDetailVO.setTaxLabelString(taxLabelString);

        List<InvoiceItemVO> invoiceItemVOList = getInvoiceItemVOList(invoiceDTO, orderVersionDTO, aggrOrder, invoiceItems, totalAcceptedInvoiceBean, totalInvoiceBean, closeOrderNegotiation, locale);

        InvoiceItemsTotalVO invoiceItemsTotalVO = getInvoiceItemsTotalVO(invoiceDTO, aggrOrder, totalAcceptedInvoiceBean, totalInvoiceBean, closeOrderNegotiation);

        invoiceDetailVO.setItems(invoiceItemVOList);
        invoiceDetailVO.setItemsTotal(invoiceItemsTotalVO);

        // Terms & Conditions
        invoiceDetailVO.setSupplierTerms(termsService.findTermAndCondition(invoiceDTO.getTermsId(), TermsTypeID.INVOICE, invoiceDTO.getSupplierWorkgroupId()));
        // Custom fields
        invoiceDetailVO.setCustomPropertyId(invoiceDTO.getCustomPropertyId());
        // Invoice Details Buttons
        invoiceDetailVO.setButtons(getInvoiceDetailsButtons(invoiceDTO, invoiceDetailVO, workgroupId, userId, projectId, locale, permMap, prefs, orderVersionDTO));

        setCustomAttributes(invoiceDetailVO); // set custom fields for invoice and invoice item
        return invoiceDetailVO;
    }

    public List<InvoiceItemVO> getInvoiceItemVOList(InvoiceDTO invoiceDTO, OrderVersionDTO orderVersionDTO, OrderVersionDTO aggrOrder, List<InvoiceItemDTO> invoiceItems, InvoiceDTO totalAcceptedInvoice, InvoiceDTO totalInvoice, boolean closeOrderNegotiation, String locale) {
        BigDecimal invoiceItemTotal = BigDecimal.ZERO;
        BigDecimal exInvoiceItemTotal = BigDecimal.ZERO;
        List<InvoiceItemVO> invoiceItemVOS = new ArrayList<>();
        for (InvoiceItemDTO invoiceItemDTO : invoiceItems) {
            InvoiceItemVO invoiceItemVO = new InvoiceItemVO();
            invoiceItemVO.setId(invoiceItemDTO.getInvoiceItemId());
            Spec spec = specRepository.findById(invoiceItemDTO.getSpecId()).orElse(null);
            SpecNode specNode = specNodeRepository.findBySpSpecId(spec.getId());
            invoiceItemVO.setTitle((spec.getIsItemVersion() == null || spec.getIsItemVersion() == 0 ? false : true)
                    ? getProductNumber(spec) : (spec.getSpecName() + "(" + spec.getSpecReference().getRefNumber() + ")"));
            invoiceItemVO.setJobId(invoiceItemDTO.getJobId());
            invoiceItemVO.setViewSpecLink(NooshOneUrlUtil.composeViewSpecLinkToEnterprise(specNode.getId(), invoiceDTO.getParent().getId()));
            if (!(spec.getIsItemVersion() != null && spec.getIsItemVersion() == 1)) {
                invoiceItemVO.setIcon("images/spec/other.gif");
            } else {
                if (!(spec.getSpecReference().getIsMaster() != null && spec.getSpecReference().getIsMaster() ==1)) {
                    invoiceItemVO.setIcon("images/spec/other.gif");
                } else {
                    invoiceItemVO.setIcon("images/icons/item_master.gif");
                }
            }

            // item associated shipments records
            List<DropdownVO<Long>> shipmentRecords = new ArrayList<>();
            List<Long> selectedShipmentRecordIds = new ArrayList<>();
            invoiceItemVO.setAssociatedShipmentRecords(showShipmentRecords(invoiceItemDTO.getInvoiceItemId(), invoiceItemDTO.getJobId(), shipmentRecords, selectedShipmentRecordIds, locale));
            invoiceItemVO.setShipmentRecords(shipmentRecords);
            invoiceItemVO.setSelectedShipmentRecordIds(selectedShipmentRecordIds);

            int itemIndex = invoiceItemDTO.getItemIndex() - 1;
            if (itemIndex < 0) {
                continue;
            }
            OrderItemDTO aggrOrderItem = aggrOrder.getOrderItemDTOs().get(itemIndex);
            InvoiceItemDTO acceptedItem = totalAcceptedInvoice.getInvoiceItemDTOList().get(itemIndex);
            InvoiceItemDTO totalItem = totalInvoice.getInvoiceItemDTOList().get(itemIndex);

            // Total Quantity
            InvoiceItemPriceVO totalQty = new InvoiceItemPriceVO();
            totalQty.setOrder(aggrOrderItem.getQuantity());
            totalQty.setPreviousInvoiced(acceptedItem.getQuantity());
            totalQty.setThisInvoice(invoiceItemDTO.getQuantity());
            totalQty.setTotalInvoiced(totalItem.getQuantity());
            invoiceItemVO.setTotalQty(totalQty);

            // Price Breakouts
            if (invoiceItemDTO.getIncludeBreakouts() && invoiceItemDTO.getBreakouts() != null
                    && invoiceItemDTO.getBreakouts().size() > 0) {

                invoiceItemVO.setTimeMaterials((spec.getSpecType().getIsTimeMaterials() != null && spec.getSpecType().getIsTimeMaterials() == (short)1) ? Boolean.TRUE : Boolean.FALSE);
                invoiceItemVO.setBreakoutRatesStrId(aggrOrderItem.getUofm().getBreakoutRatesStrId());
                invoiceItemVO.setBreakoutUnitsStrId(aggrOrderItem.getUofm().getBreakoutUnitsStrId());
                List<InvoiceBreakoutVO> breakoutVOS = new ArrayList<>();
                invoiceItemDTO.getBreakouts().stream().forEach(breakout -> {
                    BreakoutTypeDTO breakoutType = breakout.getBreakoutType();

                    InvoiceBreakoutVO breakoutVO = new InvoiceBreakoutVO();
                    breakoutVO.setId(breakout.getId());
                    breakoutVO.setBreakoutTypeId(breakout.getBreakoutTypeId());
                    breakoutVO.setName(breakoutType.getName());
                    breakoutVO.setNestingLevel(breakout.getNestingLevel());
                    breakoutVO.setIsQuantity(breakoutType.getIsQuantity());

                    BreakoutDTO aggrItemBreakout = getBreakoutByTypeId(aggrOrderItem, breakoutType.getId());
                    BreakoutDTO acceptedItemBreakout = getBreakoutByTypeId(acceptedItem, breakoutType.getId());
                    BreakoutDTO totalItemBreakout = getBreakoutByTypeId(totalItem, breakoutType.getId());

                    breakoutVO.setOrder(aggrItemBreakout != null ? aggrItemBreakout.getPrice() : BigDecimal.ZERO);
                    breakoutVO.setOrderCurrencyId(aggrItemBreakout != null ? aggrItemBreakout.getPriceCurrencyId() : null);
                    breakoutVO.setOrderUnitsValue(aggrItemBreakout != null ? aggrItemBreakout.getUnits() : BigDecimal.ZERO);
                    breakoutVO.setOrderRatesValue(aggrItemBreakout != null ? aggrItemBreakout.getRates() : BigDecimal.ZERO);
                    breakoutVO.setOrderRatesValueCurrencyId(aggrItemBreakout != null ? aggrItemBreakout.getRatesCurrencyId() : null);
                    breakoutVO.setPreviousInvoiced(acceptedItemBreakout != null ? acceptedItemBreakout.getPrice() : BigDecimal.ZERO);
                    breakoutVO.setPreviousInvoicedCurrencyId(acceptedItemBreakout != null ? acceptedItemBreakout.getPriceCurrencyId() : null);
                    breakoutVO.setPreviousInvoiceUnitsValue(acceptedItemBreakout != null ? acceptedItemBreakout.getUnits() : BigDecimal.ZERO);
                    breakoutVO.setPreviousInvoiceRatesValue(acceptedItemBreakout != null ? acceptedItemBreakout.getRates() : BigDecimal.ZERO);
                    breakoutVO.setPreviousInvoiceRatesValueCurrencyId(acceptedItemBreakout != null ? acceptedItemBreakout.getRatesCurrencyId() : null);
                    breakoutVO.setThisInvoice(breakout != null ? breakout.getPrice() : BigDecimal.ZERO);
                    breakoutVO.setThisInvoiceCurrencyId(breakout != null ? breakout.getPriceCurrencyId() : null);
                    breakoutVO.setThisInvoiceUnitsValue(breakout != null ? breakout.getUnits() : BigDecimal.ZERO);
                    breakoutVO.setThisInvoiceRatesValue(breakout != null ? breakout.getRates() : BigDecimal.ZERO);
                    breakoutVO.setThisInvoiceRatesValueCurrencyId(breakout != null ? breakout.getRatesCurrencyId() : null);
                    breakoutVO.setTotalInvoiced(totalItemBreakout != null ? totalItemBreakout.getPrice() : BigDecimal.ZERO);
                    breakoutVO.setTotalInvoicedCurrencyId(totalItemBreakout != null ? totalItemBreakout.getPriceCurrencyId() : null);

                    // quantity breakout
                    breakoutVO.setOrderValue(aggrItemBreakout.getValue());
                    breakoutVO.setPreviousInvoicedValue(acceptedItemBreakout.getValue());
                    breakoutVO.setTotalInvoicedValue(totalItemBreakout.getValue());
                    breakoutVO.setOrderedUnitPrice(getPricePerUnits(1, aggrItemBreakout));
                    breakoutVO.setOrderedUnitPriceCurrencyId(breakout.getPriceCurrencyId());

                    //dual currency
                    if (invoiceDTO.isDualCurrency()) {
                        breakoutVO.setExOrder(aggrItemBreakout != null ? getValue(aggrItemBreakout.getExPrice()) : BigDecimal.ZERO);
                        breakoutVO.setExOrderCurrencyId(aggrItemBreakout != null ? invoiceDTO.getExCurrencyId() : null);
                        breakoutVO.setExPreviousInvoiced(acceptedItemBreakout != null ? getValue(acceptedItemBreakout.getExPrice()) : BigDecimal.ZERO);
                        breakoutVO.setExPreviousInvoicedCurrencyId(acceptedItemBreakout != null ? invoiceDTO.getExCurrencyId() : null);
                        breakoutVO.setExThisInvoice(breakout != null ? getValue(breakout.getExPrice()) : BigDecimal.ZERO);
                        breakoutVO.setExThisInvoiceCurrencyId(breakout != null ? invoiceDTO.getExCurrencyId() : null);
                        breakoutVO.setExTotalInvoiced(totalItemBreakout != null ? getValue(totalItemBreakout.getExPrice()) : BigDecimal.ZERO);
                        breakoutVO.setExTotalInvoicedCurrencyId(totalItemBreakout != null ? invoiceDTO.getExCurrencyId() : null);

                        breakoutVO.setExOrderedUnitPrice(getExPricePerUnits(1, aggrItemBreakout));
                        breakoutVO.setExOrderedUnitPriceCurrencyId(invoiceDTO.getExCurrencyId());
                    }

                    // Set parent/descendent price breakout type ID
                    breakoutVO.setParentTypeId(breakoutType.getParentTypeId());
                    if (totalItemBreakout.getDescendents() != null) {
                        List<Long> descendentTypeIds = new ArrayList<>();
                        for (BreakoutDTO descendentDTO : totalItemBreakout.getDescendents()) {
                            descendentTypeIds.add(descendentDTO.getBreakoutTypeId());
                        }
                        if (!descendentTypeIds.isEmpty()) {
                            breakoutVO.setDescendentTypeIds(descendentTypeIds);
                        }
                    }

                    breakoutVOS.add(breakoutVO);
                });
                invoiceItemVO.setBreakouts(breakoutVOS);
            }

            // Item Cost
            InvoiceItemPriceVO itemCost = new InvoiceItemPriceVO();
            itemCost.setOrder(aggrOrderItem.getValue());
            itemCost.setOrderCurrencyId(aggrOrderItem.getValueCurrencyId());
            itemCost.setPreviousInvoiced(acceptedItem.getAmount());
            itemCost.setPreviousInvoicedCurrencyId(acceptedItem.getAmountCurrencyId());
            itemCost.setThisInvoice(invoiceItemDTO.getAmount());
            itemCost.setThisInvoiceCurrencyId(invoiceItemDTO.getAmountCurrencyId());
            itemCost.setTotalInvoiced(totalItem.getAmount());
            itemCost.setTotalInvoicedCurrencyId(totalItem.getAmountCurrencyId());
            //dual currency
            if (invoiceDTO.isDualCurrency()) {
                itemCost.setExOrder(getValue(aggrOrderItem.getExValue()));
                itemCost.setExOrderCurrencyId(invoiceDTO.getExCurrencyId());
                itemCost.setExPreviousInvoiced(getValue(acceptedItem.getExAmount()));
                itemCost.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                itemCost.setExThisInvoice(getValue(invoiceItemDTO.getExAmount()));
                itemCost.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
                itemCost.setExTotalInvoiced(getValue(totalItem.getExAmount()));
                itemCost.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
            }
            invoiceItemVO.setItemCost(itemCost);

            // Tax and Shipping
            if (orderVersionDTO.getItemized()) {
                // item tax
                InvoiceItemPriceVO itemTax = new InvoiceItemPriceVO();
                itemTax.setOrder(aggrOrderItem.getTax());
                itemTax.setOrderCurrencyId(aggrOrderItem.getTaxCurrencyId());
                itemTax.setPreviousInvoiced(acceptedItem.getTax());
                itemTax.setPreviousInvoicedCurrencyId(acceptedItem.getTaxCurrencyId());
                itemTax.setThisInvoice(invoiceItemDTO.getTax());
                itemTax.setThisInvoiceCurrencyId(invoiceItemDTO.getTaxCurrencyId());
                itemTax.setTotalInvoiced(totalItem.getTax());
                itemTax.setTotalInvoicedCurrencyId(totalItem.getTaxCurrencyId());
                //dual currency
                if (invoiceDTO.isDualCurrency()) {
                    itemTax.setExOrder(getValue(aggrOrderItem.getExTax()));
                    itemTax.setExOrderCurrencyId(invoiceDTO.getExCurrencyId());
                    itemTax.setExPreviousInvoiced(getValue(acceptedItem.getExTax()));
                    itemTax.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                    itemTax.setExThisInvoice(getValue(invoiceItemDTO.getExTax()));
                    itemTax.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
                    itemTax.setExTotalInvoiced(getValue(totalItem.getExTax()));
                    itemTax.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                }
                invoiceItemVO.setItemTax(itemTax);

                // item shipping
                InvoiceItemPriceVO itemShipping = new InvoiceItemPriceVO();
                itemShipping.setOrder(aggrOrderItem.getShipping());
                itemShipping.setOrderCurrencyId(aggrOrderItem.getShippingCurrencyId());
                itemShipping.setPreviousInvoiced(acceptedItem.getShipping());
                itemShipping.setPreviousInvoicedCurrencyId(acceptedItem.getShippingCurrencyId());
                itemShipping.setThisInvoice(invoiceItemDTO.getShipping());
                itemShipping.setThisInvoiceCurrencyId(invoiceItemDTO.getShippingCurrencyId());
                itemShipping.setTotalInvoiced(totalItem.getShipping());
                itemShipping.setTotalInvoicedCurrencyId(totalItem.getShippingCurrencyId());
                //dual currency
                if (invoiceDTO.isDualCurrency()) {
                    itemShipping.setExOrder(getValue(aggrOrderItem.getExShipping()));
                    itemShipping.setExOrderCurrencyId(invoiceDTO.getExCurrencyId());
                    itemShipping.setExPreviousInvoiced(getValue(acceptedItem.getExShipping()));
                    itemShipping.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                    itemShipping.setExThisInvoice(getValue(invoiceItemDTO.getExShipping()));
                    itemShipping.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
                    itemShipping.setExTotalInvoiced(getValue(totalItem.getExShipping()));
                    itemShipping.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                }
                invoiceItemVO.setItemShipping(itemShipping);

                // item subtotal
                InvoiceItemPriceVO itemSubTotal = new InvoiceItemPriceVO();
                itemSubTotal.setOrder(aggrOrderItem.getSubTotal());
                itemSubTotal.setOrderCurrencyId(aggrOrderItem.getValueCurrencyId());
                itemSubTotal.setPreviousInvoiced(acceptedItem.getSubTotal());
                itemSubTotal.setPreviousInvoicedCurrencyId(acceptedItem.getAmountCurrencyId());
                itemSubTotal.setThisInvoice(invoiceItemDTO.getSubTotal());
                itemSubTotal.setThisInvoiceCurrencyId(invoiceItemDTO.getAmountCurrencyId());
                itemSubTotal.setTotalInvoiced(totalItem.getSubTotal());
                itemSubTotal.setTotalInvoicedCurrencyId(totalItem.getAmountCurrencyId());
                //dual currency
                if (invoiceDTO.isDualCurrency()) {
                    itemSubTotal.setExOrder(getValue(aggrOrderItem.getExSubTotal()));
                    itemSubTotal.setExOrderCurrencyId(invoiceDTO.getExCurrencyId());
                    itemSubTotal.setExPreviousInvoiced(getValue(acceptedItem.getExSubTotal()));
                    itemSubTotal.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                    itemSubTotal.setExThisInvoice(getValue(invoiceItemDTO.getExSubTotal()));
                    itemSubTotal.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
                    itemSubTotal.setExTotalInvoiced(getValue(totalItem.getExSubTotal()));
                    itemSubTotal.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                }
                invoiceItemVO.setItemSubTotal(itemSubTotal);
            }

            // Discount/Surcharge
            if (invoiceDTO.getFinal() && closeOrderNegotiation) {
                InvoiceItemPriceVO discountOrSurcharge = new InvoiceItemPriceVO();
                discountOrSurcharge.setOrder(null);
                discountOrSurcharge.setPreviousInvoiced(acceptedItem.getDiscountOrSurcharge());
                discountOrSurcharge.setPreviousInvoicedCurrencyId(acceptedItem.getDiscountOrSurchargeCurrencyId());
                discountOrSurcharge.setThisInvoice(invoiceItemDTO.getDiscountOrSurcharge());
                discountOrSurcharge.setThisInvoiceCurrencyId(invoiceItemDTO.getDiscountOrSurchargeCurrencyId());
                discountOrSurcharge.setTotalInvoiced(totalItem.getDiscountOrSurcharge());
                discountOrSurcharge.setTotalInvoicedCurrencyId(totalItem.getDiscountOrSurchargeCurrencyId());
                //dual currency
                if (invoiceDTO.isDualCurrency()) {
                    discountOrSurcharge.setExOrder(null);
                    discountOrSurcharge.setExPreviousInvoiced(getValue(acceptedItem.getExDiscountOrSurcharge()));
                    discountOrSurcharge.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                    discountOrSurcharge.setExThisInvoice(getValue(invoiceItemDTO.getExDiscountOrSurcharge()));
                    discountOrSurcharge.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
                    discountOrSurcharge.setExTotalInvoiced(getValue(totalItem.getExDiscountOrSurcharge()));
                    discountOrSurcharge.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                }
                invoiceItemVO.setDiscountOrSurcharge(discountOrSurcharge);
            }

            invoiceItemTotal = invoiceItemTotal.add(getValue(invoiceItemDTO.getAmount()));
            //dual currency
            if (invoiceDTO.isDualCurrency()) {
                exInvoiceItemTotal = exInvoiceItemTotal.add(getValue(invoiceItemDTO.getExAmount()));
            }
            if (invoiceDTO.getFinal()) {
                invoiceItemTotal = invoiceItemTotal.add(getValue(invoiceItemDTO.getDiscountOrSurcharge()));
                //dual currency
                if (invoiceDTO.isDualCurrency()) {
                    exInvoiceItemTotal = exInvoiceItemTotal.add(getValue(invoiceItemDTO.getExDiscountOrSurcharge()));
                }
            }

            invoiceItemVO.setCustomPropertyId(invoiceItemDTO.getCustomPropertyId());
            invoiceItemVOS.add(invoiceItemVO);
        }
        invoiceDTO.setInvoiceItemTotal(invoiceItemTotal);
        if (invoiceDTO.isDualCurrency()) {
            invoiceDTO.setExInvoiceItemTotal(exInvoiceItemTotal);
        }
        return invoiceItemVOS;
    }

    public BigDecimal getPricePerUnits(long perNumUnits, BreakoutDTO breakout) {
        //if (getHasQuantity() == false || getPrice() == null || getValue() == 0)
        if (breakout.getPrice() == null)
            return null;

        if (!breakout.getHasQuantity() && breakout.getAncestor() == null)
            return null;

        Long quantity = breakout.getValue();
        if (!breakout.getHasQuantity()) {
            // find the quantity from nearest ancestor
            BreakoutDTO myAncestor = breakout.getAncestor();
            while (myAncestor != null) {
                if (myAncestor.getHasQuantity()) {
                    quantity = myAncestor.getValue() ;
                    break;
                }
                if (myAncestor.getAncestor() == null)
                    return null;

                myAncestor = myAncestor.getAncestor() ;
            }
        }
        if (quantity == null || quantity == 0)
            return null;

        BigDecimal bigDecimal = null;
        if (breakout.getPrice() != null) {
            bigDecimal = new BigDecimal(breakout.getPrice().doubleValue() / quantity * perNumUnits);
            bigDecimal = bigDecimal.setScale(5, BigDecimal.ROUND_HALF_UP);
        }
        return bigDecimal;
    }

    public BigDecimal getExPricePerUnits(long perNumUnits, BreakoutDTO breakout) {
        //if (getHasQuantity() == false || getPrice() == null || getValue() == 0)
        if (breakout.getExPrice() == null)
            return null;

        if (!breakout.getHasQuantity() && breakout.getAncestor() == null)
            return null;

        Long quantity = breakout.getValue();
        if (!breakout.getHasQuantity()) {
            // find the quantity from nearest ancestor
            BreakoutDTO myAncestor = breakout.getAncestor();
            while (myAncestor != null) {
                if (myAncestor.getHasQuantity()) {
                    quantity = myAncestor.getValue() ;
                    break;
                }
                if (myAncestor.getAncestor() == null)
                    return null;

                myAncestor = myAncestor.getAncestor() ;
            }
        }
        if (quantity == null || quantity == 0)
            return null;

        BigDecimal bigDecimal = null;
        if (breakout.getExPrice() != null) {
            bigDecimal = new BigDecimal(breakout.getExPrice().doubleValue() / quantity * perNumUnits);
            bigDecimal = bigDecimal.setScale(5, BigDecimal.ROUND_HALF_UP);
        }
        return bigDecimal;
    }

    public InvoiceItemsTotalVO getInvoiceItemsTotalVO(InvoiceDTO invoiceDTO, OrderVersionDTO aggrOrder, InvoiceDTO totalAcceptedInvoice, InvoiceDTO totalInvoice, boolean closeOrderNegotiation) {
        InvoiceItemsTotalVO invoiceItemsTotalVO = new InvoiceItemsTotalVO();
        // total SUBTOTAL of the items
        InvoiceItemPriceVO subTotal = new InvoiceItemPriceVO();
        subTotal.setOrder(aggrOrder.getOrderItemSubtotal());
        subTotal.setOrderCurrencyId(aggrOrder.getOrderItemSubtotalCurrencyId());
        subTotal.setPreviousInvoiced(totalAcceptedInvoice.getInvoiceItemTotal());
        subTotal.setPreviousInvoicedCurrencyId(totalAcceptedInvoice.getInvoiceItemTotalCurrencyId());
        subTotal.setThisInvoice(invoiceDTO.getInvoiceItemTotal());
        subTotal.setThisInvoiceCurrencyId(invoiceDTO.getInvoiceItemTotalCurrencyId());
        subTotal.setTotalInvoiced(totalInvoice.getInvoiceItemTotal());
        subTotal.setTotalInvoicedCurrencyId(totalInvoice.getInvoiceItemTotalCurrencyId());
        //dual currency
        if (invoiceDTO.isDualCurrency()) {
            subTotal.setExOrder(getValue(aggrOrder.getExOrderItemSubtotal()));
            subTotal.setExOrderCurrencyId(invoiceDTO.getExCurrencyId());
            subTotal.setExPreviousInvoiced(getValue(totalAcceptedInvoice.getExInvoiceItemTotal()));
            subTotal.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
            subTotal.setExThisInvoice(getValue(invoiceDTO.getExInvoiceItemTotal()));
            subTotal.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
            subTotal.setExTotalInvoiced(getValue(totalInvoice.getExInvoiceItemTotal()));
            subTotal.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
        }
        invoiceItemsTotalVO.setSubTotal(subTotal);

        // total TAX of the items
        InvoiceItemPriceVO tax = new InvoiceItemPriceVO();
        tax.setOrder(aggrOrder.getTax());
        tax.setOrderCurrencyId(aggrOrder.getTaxCurrencyId());
        tax.setPreviousInvoiced(totalAcceptedInvoice.getTax());
        tax.setPreviousInvoicedCurrencyId(totalAcceptedInvoice.getTaxCurrencyId());
        tax.setThisInvoice(invoiceDTO.getTax());
        tax.setThisInvoiceCurrencyId(invoiceDTO.getTaxCurrencyId());
        tax.setTotalInvoiced(totalInvoice.getTax());
        tax.setTotalInvoicedCurrencyId(totalInvoice.getTaxCurrencyId());
        //dual currency
        if (invoiceDTO.isDualCurrency()) {
            tax.setExOrder(getValue(aggrOrder.getExTax()));
            tax.setExOrderCurrencyId(invoiceDTO.getExCurrencyId());
            tax.setExPreviousInvoiced(getValue(totalAcceptedInvoice.getExTax()));
            tax.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
            tax.setExThisInvoice(getValue(invoiceDTO.getExTax()));
            tax.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
            tax.setExTotalInvoiced(getValue(totalInvoice.getExTax()));
            tax.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
        }
        invoiceItemsTotalVO.setTax(tax);

        // total SHIPPING of the items
        InvoiceItemPriceVO shipping = new InvoiceItemPriceVO();
        shipping.setOrder(aggrOrder.getShipping());
        shipping.setOrderCurrencyId(aggrOrder.getShippingCurrencyId());
        shipping.setPreviousInvoiced(totalAcceptedInvoice.getShipping());
        shipping.setPreviousInvoicedCurrencyId(totalAcceptedInvoice.getShippingCurrencyId());
        shipping.setThisInvoice(invoiceDTO.getShipping());
        shipping.setThisInvoiceCurrencyId(invoiceDTO.getShippingCurrencyId());
        shipping.setTotalInvoiced(totalInvoice.getShipping());
        shipping.setTotalInvoicedCurrencyId(totalInvoice.getShippingCurrencyId());
        //dual currency
        if (invoiceDTO.isDualCurrency()) {
            shipping.setExOrder(getValue(aggrOrder.getExShipping()));
            shipping.setExOrderCurrencyId(invoiceDTO.getExCurrencyId());
            shipping.setExPreviousInvoiced(getValue(totalAcceptedInvoice.getExShipping()));
            shipping.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
            shipping.setExThisInvoice(getValue(invoiceDTO.getExShipping()));
            shipping.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
            shipping.setExTotalInvoiced(getValue(totalInvoice.getExShipping()));
            shipping.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
        }
        invoiceItemsTotalVO.setShipping(shipping);

        // Additional Discount/Surcharge
        if (invoiceDTO.getFinal() && closeOrderNegotiation) {
            InvoiceItemPriceVO discountOrSurcharge = new InvoiceItemPriceVO();
            discountOrSurcharge.setOrder(null);
            discountOrSurcharge.setPreviousInvoiced(totalAcceptedInvoice.getDiscountOrSurcharge());
            discountOrSurcharge.setPreviousInvoicedCurrencyId(totalAcceptedInvoice.getDiscountOrSurchargeCurrencyId());
            discountOrSurcharge.setThisInvoice(invoiceDTO.getDiscountOrSurcharge());
            discountOrSurcharge.setThisInvoiceCurrencyId(invoiceDTO.getDiscountOrSurchargeCurrencyId());
            discountOrSurcharge.setTotalInvoiced(totalInvoice.getDiscountOrSurcharge());
            discountOrSurcharge.setTotalInvoicedCurrencyId(totalInvoice.getDiscountOrSurchargeCurrencyId());
            //dual currency
            if (invoiceDTO.isDualCurrency()) {
                discountOrSurcharge.setExOrder(null);
                discountOrSurcharge.setExPreviousInvoiced(getValue(totalAcceptedInvoice.getExDiscountOrSurcharge()));
                discountOrSurcharge.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
                discountOrSurcharge.setExThisInvoice(getValue(invoiceDTO.getExDiscountOrSurcharge()));
                discountOrSurcharge.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
                discountOrSurcharge.setExTotalInvoiced(getValue(totalInvoice.getExDiscountOrSurcharge()));
                discountOrSurcharge.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
            }
            invoiceItemsTotalVO.setDiscountOrSurcharge(discountOrSurcharge);
        }

        // GRAND TOTAL
        InvoiceItemPriceVO grandTotal = new InvoiceItemPriceVO();
        grandTotal.setOrder(aggrOrder.getGrandTotal());
        grandTotal.setOrderCurrencyId(aggrOrder.getGrandTotalCurrencyId());
        grandTotal.setPreviousInvoiced(totalAcceptedInvoice.getGrandTotal());
        grandTotal.setPreviousInvoicedCurrencyId(totalAcceptedInvoice.getGrandTotalCurrencyId());
        grandTotal.setThisInvoice(invoiceDTO.getGrandTotal());
        grandTotal.setThisInvoiceCurrencyId(invoiceDTO.getGrandTotalCurrencyId());
        grandTotal.setTotalInvoiced(totalInvoice.getGrandTotal());
        grandTotal.setTotalInvoicedCurrencyId(totalInvoice.getGrandTotalCurrencyId());
        //dual currency
        if (invoiceDTO.isDualCurrency()) {
            grandTotal.setExOrder(getValue(aggrOrder.getExGrandTotal()));
            grandTotal.setExOrderCurrencyId(invoiceDTO.getExCurrencyId());
            grandTotal.setExPreviousInvoiced(getValue(totalAcceptedInvoice.getExGrandTotal()));
            grandTotal.setExPreviousInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
            grandTotal.setExThisInvoice(getValue(invoiceDTO.getExGrandTotal()));
            grandTotal.setExThisInvoiceCurrencyId(invoiceDTO.getExCurrencyId());
            grandTotal.setExTotalInvoiced(getValue(totalInvoice.getExGrandTotal()));
            grandTotal.setExTotalInvoicedCurrencyId(invoiceDTO.getExCurrencyId());
        }
        invoiceItemsTotalVO.setGrandTotal(grandTotal);

        return invoiceItemsTotalVO;
    }

    private InvoiceInfoVO getInvoiceSummary(Long workgroupId, ProjectDTO projectDTO, OrderDTO orderDTO, InvoiceDTO invoiceDTO, String locale) {
        // summary
        InvoiceInfoVO invoiceInfoVO = new InvoiceInfoVO();
        invoiceInfoVO.setProjectNo(projectDTO.getProjectNumber());
        invoiceInfoVO.setProjectName(projectDTO.getName());

        invoiceInfoVO.setOrderRef(orderDTO.getReference());
        invoiceInfoVO.setOrderTitle(orderDTO.getTitle() != null ? orderDTO.getTitle() : orderDTO.getReference());
        invoiceInfoVO.setOrderId(orderDTO.getOrderId());
        invoiceInfoVO.setOrderTypeId(orderDTO.getOrderTypeId());
        invoiceInfoVO.setBuyWorkgroupId(orderDTO.getBuyerWorkgroupId());
        invoiceInfoVO.setOrderPaymentMethod(i18NUtils.getMessage(orderDTO.getDescriptionStrId(), locale));
        invoiceInfoVO.setOrderPaymentMethodStrId(orderDTO.getDescriptionStrId() != null ? orderDTO.getDescriptionStrId().toString() : null);
        invoiceInfoVO.setOrderPaymentRefNo(orderDTO.getPaymentReference());

        String invoiceTo = null;
        Address address = new Address();
        Contact contact = invoiceDTO.getInvoiceCMContactId() != null ? contactRepository.findById(invoiceDTO.getInvoiceCMContactId()).orElse(null) : null;
        if (contact != null) {
            AccountUser accountUser = null;
            if(contact.getContactUserId() != null) {
                accountUser = accountUserRepository.findById(contact.getContactUserId()).orElse(null);
                if(accountUser.getWorkgroup().getName() != null) {
                    invoiceTo = contact.getFirstName() + " " + contact.getLastName() + " of " + accountUser.getWorkgroup().getName() + "\n";
                } else {
                    invoiceTo = contact.getFirstName() + " " + contact.getLastName() + "\n";
                }
            }
            address = contact.getAddress();
            if (address == null && accountUser != null) {
                address = accountUser.getPerson().getAddress();
            }
        } else {
            if (invoiceDTO.getBuyerUserId() != null) {
                AccountUser accountUser = accountUserRepository.findById(invoiceDTO.getBuyerUserId()).orElse(null);
                Workgroup workgroup = workgroupRepository.findById(invoiceDTO.getBuyerWorkgroupId()).orElse(null);
                invoiceTo = accountUser.getPerson().getFullName() + " of " + workgroup.getName() + "\n";
                address = accountUser.getPerson().getAddress();
            }
        }

        String invoiceToCopy = invoiceTo;
        if (address != null) {
            if (address.getLine1() != null) {
                invoiceTo = invoiceTo + address.getLine1() + "\n";
            }
            if (address.getLine2() != null) {
                invoiceTo = invoiceTo + address.getLine2() + "\n";
            }
            if (address.getLine3() != null) {
                invoiceTo = invoiceTo + address.getLine3() + "\n";
            }
            if (address.getCity() != null) {
                invoiceTo = invoiceTo + address.getCity() + ",";
            }
            if (address.getState() != null) {
                invoiceTo = invoiceTo + address.getState() + " ";
            }
            if (address.getPostal() != null) {
                invoiceTo = invoiceTo + address.getPostal() + "\n";
            }
            invoiceToCopy = invoiceTo;
            invoiceInfoVO.setInvoiceCountryNameStrId(address.getCountry().getNameStrId() != null ? address.getCountry().getNameStrId().toString() : null);
            invoiceTo = invoiceTo + i18NUtils.getMessage(address.getCountry().getNameStrId(), locale);
        }
        invoiceInfoVO.setInvoiceTo(invoiceTo);
        invoiceInfoVO.setInvoiceToWithoutCountry(invoiceToCopy);



        String preparedBy = null;
        if (invoiceDTO.getPrepareUserId() != null) {
            AccountUser accountUser = accountUserRepository.findById(invoiceDTO.getPrepareUserId()).orElse(null);
            preparedBy = accountUser.getPerson().getFullName() + " of " + accountUser.getWorkgroup().getName() + "\n";

            invoiceInfoVO.setCreatorWrokgroupName(accountUser.getPerson().getFullName() + " of " + accountUser.getWorkgroup().getName());

            Address addr = accountUser.getPerson().getAddress();
            String prepareByCopy = preparedBy;
            if (addr != null) {
                if (addr.getLine1() != null) {
                    preparedBy = preparedBy + addr.getLine1() + "\n";
                }
                if (addr.getLine2() != null) {
                    preparedBy = preparedBy + addr.getLine2() + "\n";
                }
                if (addr.getLine3() != null) {
                    preparedBy = preparedBy + addr.getLine3() + "\n";
                }
                if (addr.getCity() != null) {
                    preparedBy = preparedBy + addr.getCity() + ",";
                }
                if (addr.getState() != null) {
                    preparedBy = preparedBy + addr.getState() + " ";
                }
                if (addr.getPostal() != null) {
                    preparedBy = preparedBy + addr.getPostal() + "\n";
                }
                prepareByCopy = preparedBy;
                invoiceInfoVO.setInvoicePrepareByNameStrId(addr.getCountry().getNameStrId() != null ? addr.getCountry().getNameStrId().toString() : null);
                preparedBy = preparedBy + i18NUtils.getMessage(addr.getCountry().getNameStrId(), locale);
            }
            invoiceInfoVO.setInvoicePrepareBy(preparedBy);
            invoiceInfoVO.setInvoicePrepareByWithoutCountry(prepareByCopy);

        }

        if (invoiceDTO.getNonBillable()) {
            invoiceInfoVO.setNonBillableReason(getNonBillableReason(invoiceDTO, locale));
            invoiceInfoVO.setNonBillableReasonStrId(getNonBillableReasonStrId(invoiceDTO));
        }
        invoiceInfoVO.setInvoiceNo(invoiceDTO.getOwnerReference());
        invoiceInfoVO.setInvoiceRefNo(invoiceDTO.getReference());
        invoiceInfoVO.setInvoiceDate(invoiceDTO.getInvoiceDate());
        invoiceInfoVO.setInvoiceDueDate(invoiceDTO.getDueDate());
        invoiceInfoVO.setInvoiceStatus(i18NUtils.getObjectStateMessage(invoiceDTO.getStateId(), locale));
        if (invoiceDTO.getStateId() != null) {
            ObjectState objectState = objectStateRepository.findById(invoiceDTO.getStateId()).orElse(null);
            if (objectState != null) {
                invoiceInfoVO.setInvoiceStatusStrId(objectState.getDescriptionStrId().toString());
            }
        }

        invoiceInfoVO.setIsPending(invoiceDTO.isPending());
        invoiceInfoVO.setIsApproved(invoiceDTO.getApproved());

        if (invoiceDTO.isPending() && invoiceDTO.getApproved()) {
            invoiceInfoVO.setInvoiceStatus(invoiceInfoVO.getInvoiceStatus() + " (Approved)");
        }
        invoiceInfoVO.setInvoiceFormattedStatus(getFormattedStatusName(invoiceDTO));
        if (!invoiceDTO.isDraft()) {
            invoiceInfoVO.setInvoiceSubmitDate(invoiceDTO.getSubmitDate());
        }
        if (invoiceDTO.isPending()) {
            invoiceInfoVO.setInvoiceUpdateDate(invoiceDTO.getModDate());
        }
        if (invoiceDTO.isAccepted()) {
            invoiceInfoVO.setInvoiceAcceptDate(invoiceDTO.getAcceptedDate());
        }
        if (invoiceDTO.isRejected() || invoiceDTO.isRetracted()) {
            invoiceInfoVO.setInvoiceReasons(invoiceDTO.getStateChangeComment());
        }
        invoiceInfoVO.setInvoiceComments(invoiceDTO.getComments());

        // Company Logo
        WorkgroupSd workgroupSd = workgroupSdRepository.findByWorkgroupId(workgroupId);
        Map<String, String> prefs = preferenceService.findGroupPrefs(workgroupId);

        String logoImgHref = null;
        if(workgroupSd.getLogoOneUrl() != null){
            // NooshOne
            logoImgHref = NooshOneUrlUtil.composeLinkToS3(workgroupSd.getLogoOneUrl());
        }else if(workgroupSd.getLogoUrl() != null && (workgroupSd.getLogoUrl().indexOf("/logos/noosh.gif") == -1)){
            // Enterprise
            logoImgHref = NooshOneUrlUtil.composeLinkToEnterprise(workgroupSd.getLogoUrl());
            // code for old logo issue
            if(workgroupSd.getLogoUrl().startsWith("noosh/")){
                logoImgHref = NooshOneUrlUtil.composeLinkToS3(workgroupSd.getLogoUrl());
            }
        }
        ServiceProvider serviceProvider = serviceProviderRepository.findByWorkgroupId(workgroupId);
        // if NGE is enabled, use NGE's logo
        if(serviceProvider != null && serviceProvider.getLogoImg() != null && !("").equals(serviceProvider.getLogoImg())
                && preferenceService.check(PreferenceID.WORKGROUP_OPTION_NL_SITE_MANAGER, prefs)){
            logoImgHref = NooshOneUrlUtil.composeLinkToS3(serviceProvider.getLogoImg());
        }
        // default logo
        if (logoImgHref == null) {
            logoImgHref = "/assets/images/noosh_blue.png";
        }
        invoiceInfoVO.setCreatorCompanyLogo(logoImgHref);

        return invoiceInfoVO;
    }

    /**
     * show shipment records according to invoice item id
     */
    public String showShipmentRecords(long invoiceItemId, long jobId, List<DropdownVO<Long>> shipmentRecords, List<Long> selectedShipmentRecordIds, String locale){
        List<RequestDTO> results = new ArrayList();
        if(jobId > -1)
            results = invoiceMyBatisMapper.findShipmentRecordsByInvoiceItemIdAndJobId(invoiceItemId, jobId);
        String selectedRequests= "";
        for(RequestDTO request : results) {
            String shippedDate = "";
            if(request.getId() != null && request.getShipmentRecord() != null) {
                if (request.getShippedDate() != null) {
                    shippedDate = ", " + DateUtil.getLocaleDateStr(request.getShippedDate(), locale);
                }
                request.setShipmentRecord(request.getShipmentRecord()+shippedDate);
                shipmentRecords.add(new DropdownVO(request.getId(), request.getShipmentRecord()));

                if(request.getInvoiceItemId() !=null && request.getInvoiceItemId().toString().equals(Long.toString(invoiceItemId))){
                    selectedShipmentRecordIds.add(request.getId());
                    if(selectedRequests.equals("")){
                        selectedRequests += request.getShipmentRecord();
                    }else {
                        selectedRequests = selectedRequests + "\n" + request.getShipmentRecord();
                    }
                }
            }
        }
        return selectedRequests;
    }

    public String getNonBillableReason(InvoiceDTO invoiceDTO, String locale) {
        Long reasonId = invoiceDTO.getNonBillableReasonId();
        if (reasonId != null) {
            for (int i = 0; i < InvoiceDTO.INVOICE_NON_BILLABLE_REASONS.length; i++) {
                Object[] reason = InvoiceDTO.INVOICE_NON_BILLABLE_REASONS[i];
                if (((String) reason[0]).equals("" + reasonId)) {
                    return i18NUtils.getMessage(((Long) reason[1]).longValue(), locale);
                }
            }
        }
        return "";
    }

    public String getNonBillableReasonStrId(InvoiceDTO invoiceDTO) {
        Long reasonId = invoiceDTO.getNonBillableReasonId();
        if (reasonId != null) {
            for (int i = 0; i < InvoiceDTO.INVOICE_NON_BILLABLE_REASONS.length; i++) {
                Object[] reason = InvoiceDTO.INVOICE_NON_BILLABLE_REASONS[i];
                if (((String) reason[0]).equals("" + reasonId)) {
                    return ((Long) reason[1]).toString();
                }
            }
        }
        return "";
    }

    public String getFormattedStatusName(InvoiceDTO invoiceDTO) {
        long statusId = invoiceDTO.getStateId();
        String spanClass;
        if (statusId== ObjectStateID.INVOICE_DRAFT )
            spanClass="status_draft";
        else if (statusId==ObjectStateID.INVOICE_ACCEPTED )
            spanClass="status_sent";
        else if (statusId==ObjectStateID.INVOICE_RETRACTED || statusId == ObjectStateID.INVOICE_REJECTED )
            spanClass="status_rejected";
        else
            spanClass="status_normal";

        return spanClass;
    }

    public String getProductNumber(Spec spec) {
        if (spec.getSpecReference().getRefNumber() == null || spec.getVersionNumber() == null)
            return null;

        return spec.getSpecReference().getRefNumber() + "-" + spec.getVersionNumber();
    }

    public InvoiceDTO createAggregatedInvoice(Long userId,
                                              List<InvoiceDTO> acceptedInvoiceList,
                                              InvoiceDTO invoiceDTO,
                                              LocalDateTime acceptedMaxDate,
                                              boolean includeUnAcceptedInvoice,
                                              Map<String, String> prefs) {
        if (acceptedInvoiceList == null || acceptedInvoiceList.isEmpty())
            return null;

        OrderDTO order =  invoiceDTO.getOrder();

        InvoiceDTO aInvoice = createInvoice(userId, order, invoiceDTO, false, prefs);
        List<InvoiceItemDTO> aItems = aInvoice.getInvoiceItemDTOList();
        int itemLength = aItems.size();
        boolean hasFinalInvoice = false;

        BigDecimal totalTax = BigDecimal.ZERO;
        BigDecimal totalShipping =  BigDecimal.ZERO;
        BigDecimal totalMiscCost =  BigDecimal.ZERO;
        BigDecimal totalDiscountSurcharge = BigDecimal.ZERO;

        BigDecimal exTotalTax = BigDecimal.ZERO;
        BigDecimal exTotalShipping =  BigDecimal.ZERO;
        BigDecimal exTotalMiscCost =  BigDecimal.ZERO;
        BigDecimal exTotalDiscountSurcharge = BigDecimal.ZERO;

        for (int ind=0; ind < acceptedInvoiceList.size(); ind++) {
            InvoiceDTO invoice = acceptedInvoiceList.get(ind);

            if (invoice.getOrderId() != order.getOrderId())
                continue;
            if (!includeUnAcceptedInvoice) {
                if (!invoice.isAccepted())
                    continue;
                if (acceptedMaxDate != null && invoice.getAcceptedDate().compareTo(acceptedMaxDate)>= 0)
                    continue;
            }

            // add up numeric standard fields
            totalTax = totalTax.add(getValue(invoice.getTax()));
            totalShipping = totalShipping.add(getValue(invoice.getShipping()));
            totalMiscCost = totalMiscCost.add(getValue(invoice.getMiscCost()));
            totalDiscountSurcharge = totalDiscountSurcharge.add(getValue(invoice.getDiscountOrSurcharge()));

            //dual currency
            if (invoiceDTO.isDualCurrency()) {
                exTotalTax = exTotalTax.add(getValue(invoice.getExTax()));
                exTotalShipping = exTotalShipping.add(getValue(invoice.getExShipping()));
                exTotalMiscCost = exTotalMiscCost.add(getValue(invoice.getExMiscCost()));
                exTotalDiscountSurcharge = exTotalDiscountSurcharge.add(getValue(invoice.getExDiscountOrSurcharge()));
            }

            if (invoice.getFinal())
                hasFinalInvoice = true;

            BigDecimal invoiceItemTotal = BigDecimal.ZERO;
            BigDecimal exInvoiceItemTotal = BigDecimal.ZERO;
            Long invoiceItemTotalCurrencyId = null;
            List<InvoiceItemDTO> items = invoice.getInvoiceItemDTOList();
            for (int iInd = 0; iInd < itemLength; iInd++) {
                InvoiceItemDTO aItem = aItems.get(iInd);
                InvoiceItemDTO item = getItemByIndex(items, aItem.getItemIndex()); //items.get(iInd);
                if (item != null) {
                    // now other stuff
                    aItem.setQuantity(aItem.getQuantity().add(getValue(item.getQuantity())));
                    aItem.setAmount(aItem.getAmount().add(getValue(item.getAmount())));
                    aItem.setTax(aItem.getTax().add(getValue(item.getTax())));
                    aItem.setShipping(aItem.getShipping().add(getValue(item.getShipping())));
                    aItem.setDiscountOrSurcharge(aItem.getDiscountOrSurcharge().add(getValue(item.getDiscountOrSurcharge())));
                    //dual currency
                    if (invoiceDTO.isDualCurrency()) {
                        aItem.setExAmount(getValue(aItem.getExAmount()).add(getValue(item.getExAmount())));
                        aItem.setExAmountCurrencyId(invoiceDTO.getExCurrencyId());
                        aItem.setExTax(getValue(aItem.getExTax()).add(getValue(item.getExTax())));
                        aItem.setExTaxCurrencyId(invoiceDTO.getExCurrencyId());
                        aItem.setExShipping(getValue(aItem.getExShipping()).add(getValue(item.getExShipping())));
                        aItem.setExShippingCurrencyId(invoiceDTO.getExCurrencyId());
                        aItem.setExDiscountOrSurcharge(getValue(aItem.getExDiscountOrSurcharge()).add(getValue(item.getExDiscountOrSurcharge())));
                        aItem.setExDiscountOrSurchargeCurrencyId(invoiceDTO.getExCurrencyId());
                    }

                    //NKB144056 there is a case that accepted invoice item don't have breakouts but new create invoice item has breakouts
                    if (aItem.getIncludeBreakouts() && item.getIncludeBreakouts()) {
                        List<BreakoutDTO> aBreakouts = aItem.getBreakouts();
                        List<BreakoutDTO> breakouts = item.getBreakouts();
                        if (breakouts != null && breakouts.size() > 0) {
                            for (int bkInd = 0; bkInd < aBreakouts.size(); bkInd++) {
                                BreakoutDTO aBreakout = aBreakouts.get(bkInd);
                                BreakoutDTO breakout = breakouts.get(bkInd);
                                if (aBreakout.getHasQuantity())
                                    aBreakout.setValue(aBreakout.getValue() + breakout.getValue());
                                aBreakout.setPrice(getValue(aBreakout.getPrice()).add(getValue(breakout.getPrice())));
                                //dual currency
                                if (invoiceDTO.isDualCurrency()) {
                                    aBreakout.setExPrice(getValue(aBreakout.getExPrice()).add(getValue(breakout.getExPrice())));
                                    aBreakout.setExPriceCurrencyId(invoiceDTO.getExCurrencyId());
                                }
                                aBreakout.setCustom1(breakout.getCustom1());
                            }
                        }
                    }
                }

                invoiceItemTotal = invoiceItemTotal.add(getValue(aItem.getAmount()));
                //dual currency
                if (invoiceDTO.isDualCurrency()) {
                    exInvoiceItemTotal = exInvoiceItemTotal.add(getValue(aItem.getExAmount()));
                }
                invoiceItemTotalCurrencyId = aItem.getAmountCurrencyId();
                if (invoiceDTO.getFinal()) {
                    invoiceItemTotal = invoiceItemTotal.add(getValue(aItem.getDiscountOrSurcharge()));
                    //dual currency
                    if (invoiceDTO.isDualCurrency()) {
                        exInvoiceItemTotal = exInvoiceItemTotal.add(getValue(aItem.getExDiscountOrSurcharge()));
                    }
                }
            }
            aInvoice.setInvoiceItemTotal(invoiceItemTotal);
            aInvoice.setInvoiceItemTotalCurrencyId(invoiceItemTotalCurrencyId);

            //dual currency
            if (invoiceDTO.isDualCurrency()) {
                aInvoice.setExInvoiceItemTotal(exInvoiceItemTotal);
                aInvoice.setExInvoiceItemTotalCurrencyId(invoiceDTO.getExCurrencyId());
            }
        }

        aInvoice.setTax(totalTax);
        aInvoice.setTaxCurrencyId(!acceptedInvoiceList.isEmpty() ? acceptedInvoiceList.get(0).getTaxCurrencyId() : null);
        aInvoice.setShipping(totalShipping);
        aInvoice.setShippingCurrencyId(!acceptedInvoiceList.isEmpty() ? acceptedInvoiceList.get(0).getShippingCurrencyId() : null);
        aInvoice.setMiscCost(totalMiscCost);
        aInvoice.setMiscCostCurrencyId(!acceptedInvoiceList.isEmpty() ? acceptedInvoiceList.get(0).getMiscCostCurrencyId() : null);
        aInvoice.setDiscountOrSurcharge(totalDiscountSurcharge) ;
        aInvoice.setDiscountOrSurchargeCurrencyId(!acceptedInvoiceList.isEmpty() ? acceptedInvoiceList.get(0).getDiscountOrSurchargeCurrencyId() : null);
        //dual currency
        if (invoiceDTO.isDualCurrency()) {
            aInvoice.setExTax(exTotalTax);
            aInvoice.setExTaxCurrencyId(invoiceDTO.getExCurrencyId());
            aInvoice.setExShipping(exTotalShipping);
            aInvoice.setExShippingCurrencyId(invoiceDTO.getExCurrencyId());
            aInvoice.setExMiscCost(exTotalMiscCost);
            aInvoice.setExMiscCostCurrencyId(invoiceDTO.getExCurrencyId());
            aInvoice.setExDiscountOrSurcharge(exTotalDiscountSurcharge) ;
            aInvoice.setExDiscountOrSurchargeCurrencyId(invoiceDTO.getExCurrencyId());
        }

        aInvoice.setFinal(hasFinalInvoice); ;
        aInvoice.setTemplate(false); ;

        boolean finalinvoiceValuePref = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SEND_FINAL_INVOICE, prefs);
        aInvoice.setSendOnClosedOrder(finalinvoiceValuePref);
        return aInvoice;

    }

    public BigDecimal getValue(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    public InvoiceDTO createInvoice(Long userId, OrderDTO order, InvoiceDTO template, boolean populateWithOrderData, Map<String, String> prefs) {
        boolean includeBreakoutPref = preferenceService.check(PreferenceID.PC_INVOICE_INCLUDE_PRICE_BREAKOUT, prefs);
        boolean finalinvoiceValuePref = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SEND_FINAL_INVOICE, prefs);
        // aggregated order
//        OrderDTO aggrOrder = order.isAggregatedOrder() ? order : order.getAggregatedOrder();
        OrderDTO aggrOrder = order;
        InvoiceDTO invoiceBean = new InvoiceDTO();
        setDualCurrencyFlag(invoiceBean, order);

        invoiceBean.setCustomPropertyId(BusinessObject.NOT_INITIALIZED);

        invoiceBean.setId(-1L);
        invoiceBean.setParent(order.getParent()) ;
        invoiceBean.setOrder(order);
        invoiceBean.setPrepareUserId(userId);
        invoiceBean.setBuyerUserId(aggrOrder.getOrderVersion().getBuyerUserId());
        invoiceBean.setBuyerWorkgroupId(aggrOrder.getBuyerWorkgroupId());
        invoiceBean.setSupplierUserId(aggrOrder.getOrderVersion().getSupplierUserId());
        invoiceBean.setSupplierWorkgroupId(aggrOrder.getSupplierWorkgroupId());
        invoiceBean.setFinal(false);
        invoiceBean.setTemplate(false);
        invoiceBean.setSendOnClosedOrder(finalinvoiceValuePref);

        List<OrBillingRecipient> recipients = billingRecipientRepository.findByOrderId(order.getOrderVersion().getOrderId());
        if (recipients != null && recipients.size() > 0)
            invoiceBean.setInvoiceCMContactId(recipients.get(0).getContactId());

        if (template == null) {
            invoiceBean.setOwnerReference(null);
            invoiceBean.setReference(null);
            invoiceBean.setInvoiceDate(LocalDateTime.now(ZoneId.of("UTC")));
            invoiceBean.setDueDate(null);
            invoiceBean.setComments(null);
            invoiceBean.setApproved(false);
            invoiceBean.setNonBillable(false);
            invoiceBean.setNonBillableReasonId( -1L);
        }
        invoiceBean.setSubmitDate(null);
        invoiceBean.setStateId(ObjectStateID.INVOICE_DRAFT);
        invoiceBean.setAcceptedDate(null);
        invoiceBean.setTax(BigDecimal.ZERO);
        invoiceBean.setTaxCurrencyId(aggrOrder.getOrderVersion().getTaxCurrencyId());
        invoiceBean.setShipping(BigDecimal.ZERO);
        invoiceBean.setShippingCurrencyId(aggrOrder.getOrderVersion().getShippingCurrencyId());
        invoiceBean.setMiscCost(BigDecimal.ZERO);
        invoiceBean.setMiscCostCurrencyId(aggrOrder.getOrderVersion().getMiscCostCurrencyId());
        invoiceBean.setDiscountOrSurcharge(BigDecimal.ZERO);
        invoiceBean.setDiscountOrSurchargeCurrencyId(aggrOrder.getOrderVersion().getDorsCurrencyId());

        if (invoiceBean.isDualCurrency()) {
            invoiceBean.setExTax(BigDecimal.ZERO);
            invoiceBean.setExTaxCurrencyId(aggrOrder.getExCurrencyId());
            invoiceBean.setExShipping(BigDecimal.ZERO);
            invoiceBean.setExShippingCurrencyId(aggrOrder.getExCurrencyId());
            invoiceBean.setExMiscCost(BigDecimal.ZERO);
            invoiceBean.setExMiscCostCurrencyId(aggrOrder.getExCurrencyId());
            invoiceBean.setExDiscountOrSurcharge(BigDecimal.ZERO);
            invoiceBean.setExDiscountOrSurchargeCurrencyId(aggrOrder.getExCurrencyId());
        }

        if (populateWithOrderData) {
            invoiceBean.setTax(BigDecimal.valueOf(aggrOrder.getTax()));
            invoiceBean.setShipping(BigDecimal.valueOf(aggrOrder.getShipping())) ;
            invoiceBean.setMiscCost(aggrOrder.getOrderVersion().getMiscCost());
            invoiceBean.setDiscountOrSurcharge(aggrOrder.getOrderVersion().getDors());

            if (invoiceBean.isDualCurrency()) {
                invoiceBean.setExTax(BigDecimal.valueOf(aggrOrder.getExTax()));
                invoiceBean.setExTaxCurrencyId(aggrOrder.getExCurrencyId());
                invoiceBean.setExShipping(BigDecimal.valueOf(aggrOrder.getExShipping()));
                invoiceBean.setExShippingCurrencyId(aggrOrder.getExCurrencyId());
                invoiceBean.setExMiscCost(aggrOrder.getOrderVersion().getExMiscCost());
                invoiceBean.setExMiscCostCurrencyId(aggrOrder.getExCurrencyId());
                invoiceBean.setExDiscountOrSurcharge(aggrOrder.getOrderVersion().getExDors());
                invoiceBean.setExDiscountOrSurchargeCurrencyId(aggrOrder.getExCurrencyId());
            }
        }

        List<OrderItemDTO> orderItems = aggrOrder.getOrderVersion().getOrderItemDTOs();
        List<InvoiceItemDTO> invoiceItemDTOList = new ArrayList<>();
        for (int ind=0; ind < orderItems.size(); ind++) {
            OrderItemDTO orderItem = orderItems.get(ind);

            PcJob job = jobRepository.findById(orderItem.getId()).orElse(null);
            InvoiceItemDTO invoiceItem = new InvoiceItemDTO();

            invoiceItem.setCustomPropertyId(BusinessObject.NOT_INITIALIZED);

            invoiceItem.setInvoiceItemId(-1L);
            invoiceItem.setInvoiceId(invoiceBean.getId()) ;
            invoiceItem.setSpecId(orderItem.getSpecId());
            invoiceItem.setSpecDTO(orderItem.getSpec());
            invoiceItem.setJobId(orderItem.getJobId());
            invoiceItem.setItemIndex(orderItem.getItemIndex());
            invoiceItem.setAmount(BigDecimal.ZERO);
            invoiceItem.setTax(BigDecimal.ZERO);
            invoiceItem.setShipping(BigDecimal.ZERO);
            invoiceItem.setQuantity(BigDecimal.ZERO);
            invoiceItem.setDiscountOrSurcharge(BigDecimal.ZERO);

            if (invoiceBean.isDualCurrency()) {
                invoiceItem.setExAmount(BigDecimal.ZERO);
                invoiceItem.setExAmountCurrencyId(aggrOrder.getExCurrencyId());
                invoiceItem.setExTax(BigDecimal.ZERO);
                invoiceItem.setExTaxCurrencyId(aggrOrder.getExCurrencyId());
                invoiceItem.setExShipping(BigDecimal.ZERO);
                invoiceItem.setExShippingCurrencyId(aggrOrder.getExCurrencyId());
                invoiceItem.setExDiscountOrSurcharge(BigDecimal.ZERO);
                invoiceItem.setExDiscountOrSurchargeCurrencyId(aggrOrder.getExCurrencyId());
            }

            if (populateWithOrderData) {
                invoiceItem.setAmount(orderItem.getValue());
                invoiceItem.setTax(orderItem.getTax());
                invoiceItem.setShipping(orderItem.getShipping());
                invoiceItem.setQuantity(orderItem.getQuantity()) ;
                invoiceItem.setDiscountOrSurcharge(orderItem.getDors());

                if (invoiceBean.isDualCurrency()) {
                    invoiceItem.setExAmount(orderItem.getExValue());
                    invoiceItem.setExTax(orderItem.getExTax());
                    invoiceItem.setExShipping(orderItem.getExShipping());
                    invoiceItem.setExDiscountOrSurcharge(orderItem.getExDors());
                }
            }

            if (includeBreakoutPref && orderItem.getAllowBreakouts()) {
                invoiceItem.setIncludeBreakouts(true);
                invoiceItem.setBreakoutTypeId(orderItem.getBreakoutTypeId());
                // breakout types
                Map breakoutTypeMap = new HashMap();
                List<BreakoutTypeDTO> breakoutTypes = invoiceMyBatisMapper.findAllWithRootBreakoutTypeIdAndSpecTypeIdAndSupplierGroupId(
                        invoiceItem.getBreakoutTypeId(), invoiceItem.getSpecDTO().getSpSpecTypeId(), invoiceBean.getSupplierWorkgroupId());
                for (BreakoutTypeDTO breakoutType : breakoutTypes) {
                    breakoutTypeMap.put(breakoutType.getId(), breakoutType);
                }
                // price breakouts
                List<BreakoutDTO> oBreakouts = orderItem.getBreakouts();
                if (oBreakouts != null && oBreakouts.size() > 0) {
                    List<BreakoutDTO> breakouts = new ArrayList<>(oBreakouts.size());
                    Map breakoutsMap = new HashMap();
                    for (int breakoutIndex = 0; breakoutIndex < oBreakouts.size(); breakoutIndex++) {
                        BreakoutDTO oBreakout = oBreakouts.get(breakoutIndex);
                        BreakoutTypeDTO breakoutType = (BreakoutTypeDTO) breakoutTypeMap.get(oBreakouts.get(breakoutIndex).getBreakoutTypeId());

                        BreakoutDTO breakout = new BreakoutDTO();
                        boolean hasQuantity = oBreakout.getHasQuantity();
                        breakout.setHasQuantity(hasQuantity);
                        breakout.setCustom1(oBreakout.getCustom1());
                        if (hasQuantity) {
                            if (populateWithOrderData)
                                breakout.setValue(oBreakouts.get(breakoutIndex).getValue());
                            else
                                breakout.setValue(0L);
                        }
                        breakout.setPrice(BigDecimal.ZERO);
                        if (invoiceBean.isDualCurrency()) {
                            breakout.setExPrice(BigDecimal.ZERO);
                        }
                        if (populateWithOrderData) {
                            breakout.setPrice(oBreakouts.get(breakoutIndex).getPrice());
                            if (invoiceBean.isDualCurrency()) {
                                breakout.setExPrice(oBreakouts.get(breakoutIndex).getExPrice());
                            }
                        }
                        breakout.setBreakoutTypeId(breakoutType.getId());
                        breakout.setBreakoutType(breakoutType);
                        if (breakoutType == null || breakoutType.getLevel() == null) {
                            breakout.setNestingLevel(0L);
                        } else {
                            breakout.setNestingLevel(breakoutType.getLevel().longValue() - 1);
                        }
                        breakout.setObjectId(invoiceItem.getInvoiceId());
                        breakout.setObjectClassId(ObjectClassID.INVOICE_ITEM);
                        breakout.setLeafNode(true);

                        if (breakoutType != null) {
                            breakoutsMap.put(breakoutType.getId(), breakout);
                        }
                        breakouts.add(breakout);
                    }
                    // add breakout to item
                    invoiceItem.setBreakouts(breakouts);

                    // build the hierarchical linked list
                    List<BreakoutDTO> descendents = new ArrayList<>();
                    for (int breakoutIndex = 0; breakoutIndex < breakouts.size(); breakoutIndex++) {
                        BreakoutDTO currBreakout = breakouts.get(breakoutIndex);
                        BreakoutTypeDTO currBreakoutType = currBreakout.getBreakoutType();
                        if (currBreakoutType != null) {
                            BreakoutDTO parentBreakout = (BreakoutDTO) breakoutsMap.get(currBreakoutType.getParentTypeId());
                            if (parentBreakout != null) {
                                descendents.add(currBreakout);
                                parentBreakout.setDescendents(descendents);
                                currBreakout.setAncestor(parentBreakout);
                            }
                        }
                    }
                }
            }
            invoiceItemDTOList.add(invoiceItem);
        }
        invoiceBean.setInvoiceItemDTOList(invoiceItemDTOList);
        return invoiceBean;
    }

    public void populateBreakouts(List<InvoiceItemDTO> invoiceItemDTOList) {
        if (invoiceItemDTOList == null || invoiceItemDTOList.isEmpty()) {
            return;
        }

        // build list of items that include breakouts
        List<InvoiceItemDTO> list = new ArrayList();
        for (InvoiceItemDTO invoiceItemDTO : invoiceItemDTOList) {
            if (invoiceItemDTO.getIncludeBreakouts()) {
                list.add(invoiceItemDTO);
            }
        }
        if (list.isEmpty()) {
            return;
        }

        // find & set breakouts for the items (that include breakouts)
        Map map = findObjectBreakoutMapByObjects(list);
        for (int ind=0; ind < list.size(); ind++) {
            InvoiceItemDTO item = list.get(ind);
            if ( !item.getIncludeBreakouts() ) {
                break;
            }

            List bkList = (List) map.get(item.getInvoiceItemId());
            item.setBreakouts(bkList);
        }
    }

    /**
     * Find breakouts for array of beans of the same classId
     * @return map where
     *     key = Long of object id
     *     value = List of breakouts
     */
    public Map findObjectBreakoutMapByObjects(List<InvoiceItemDTO> invoiceItems) {

        Map map = new HashMap();
        if (invoiceItems == null || invoiceItems.isEmpty())
            return map;

        //initialize the object/bklist map
        List<Long> invoiceItemIds = new ArrayList<>();
        for (InvoiceItemDTO invoiceItemDTO : invoiceItems) {
            map.put(invoiceItemDTO.getInvoiceItemId(), new ArrayList());
            invoiceItemIds.add(invoiceItemDTO.getInvoiceItemId());
        }

        List<BreakoutDTO> breakoutDTOList =  invoiceMyBatisMapper.findBreakoutByInvoiceItemIds(invoiceItemIds);

        if (breakoutDTOList.size() > 0) {
            for (BreakoutDTO breakoutDTO: breakoutDTOList) {
                List list = (List)map.get(new Long(breakoutDTO.getObjectId()));
                list.add(breakoutDTO);
            }
            for (InvoiceItemDTO invoiceItem : invoiceItems) {
                List list = (List)map.get(invoiceItem.getInvoiceItemId());
                // for each bean
                // build the hierarchical linked list for breakout
                Map typesMap = new HashMap();
                Map breakoutsMap = new HashMap();
                for (int breakoutIndex = 0; breakoutIndex < list.size(); breakoutIndex++) {
                    BreakoutDTO breakout = (BreakoutDTO)list.get(breakoutIndex);
                    BreakoutTypeDTO breakoutType = breakout.getBreakoutType();
                    typesMap.put(breakoutType.getId(), breakoutType);
                    breakoutsMap.put(breakoutType.getId(), breakout);
                }
                List<BreakoutDTO> breakoutDTOS = new ArrayList<>();
                for (int breakoutIndex = 0; breakoutIndex < list.size(); breakoutIndex++) {
                    BreakoutDTO currBreakout = (BreakoutDTO)list.get(breakoutIndex);
                    BreakoutTypeDTO currBreakoutType = (BreakoutTypeDTO)typesMap.get(currBreakout.getBreakoutTypeId());
                    BreakoutDTO parentBreakout = (BreakoutDTO)breakoutsMap.get(currBreakoutType.getParentTypeId());
                    if (parentBreakout != null) {
                        breakoutDTOS.add(currBreakout);
                        parentBreakout.setDescendents(breakoutDTOS);
                        currBreakout.setAncestor(parentBreakout);
                    }
                }
            }
        }
        return map;
    }

    private Map<String, String> getInvoiceDetailsButtons(InvoiceDTO invoiceDTO, InvoiceDetailVO invoiceDetailVO,
                                                         Long workgroupId, Long userId, Long projectId, String locale,
                                                         Map<String, Boolean> permMap, Map<String, String> prefs, OrderVersionDTO orderVersionDTO) {
        Map<String, String> buttons = new HashMap<>();

        InvoiceListDTO invoiceListDTO = toInvoiceListDTO(invoiceDTO);
        if (invoiceDTO.isUserSupplier()) {
            // edit invoice
            if (editInvoicePermission.check(invoiceListDTO, workgroupId, userId, projectId, permMap)) {
                buttons.put("editInvoiceExternalLink", (NooshOneUrlUtil.composeEditInvoiceLinkToEnterprise(projectId, invoiceDTO.getId())));
            }
            if (invoiceDTO.isDraft()) {
                // delete invoice
                if (deleteInvoicePermission.check(invoiceListDTO, workgroupId, userId, projectId, permMap)) {
                    buttons.put("deleteInvoiceExternalLink", (NooshOneUrlUtil.composeDeleteInvoiceLinkToEnterprise(projectId, invoiceDTO.getId())));
                }
            }
            if (invoiceDTO.isPending()) {
                // retract invoice
                if (retractInvoicePermission.check(invoiceDTO, workgroupId, userId, projectId, permMap)) {
                    buttons.put("retractInvoiceExternalLink", (NooshOneUrlUtil.composeRetractInvoiceLinkToEnterprise(projectId, invoiceDTO.getId())));
                }
            }
            // replace invoice
            if (invoiceDTO.isRejected()) {
                if (createInvoicePermission.check(invoiceDTO.getParent(), workgroupId, userId, projectId, permMap)) {
                    if (!createInvoicePermission.check(invoiceDTO.getOrderVersion(), workgroupId, userId, projectId, permMap)) {
                        invoiceDetailVO.setReplaceErrorMessage(i18NUtils.getMessage(StringID.INVOICE_CONSTRAINT_CREATE_PERMISSION, locale));
                        invoiceDetailVO.setReplaceErrorMessageStrId(String.valueOf(StringID.INVOICE_CONSTRAINT_CREATE_PERMISSION));
                    }
                    buttons.put("replaceInvoiceExternalLink", (NooshOneUrlUtil.composeReplaceInvoiceLinkToEnterprise(projectId, invoiceDTO.getId())));
                }
            }
        }
        if (invoiceDTO.isUserBuyer()) {
            if (invoiceService.requiresApproval(invoiceDTO)) {
                // approve invoice
                if(approveInvoicePermission.check(invoiceDTO, workgroupId, userId, projectId, permMap)) {
                    buttons.put("approveInvoiceExternalLink", (NooshOneUrlUtil.composeApproveInvoiceLinkToEnterprise(projectId, invoiceDTO.getId())));
                }
            }
            if (invoiceDTO.isPending()) {
                // accept invoice
                if (acceptInvoicePermission.check(invoiceDTO, workgroupId, userId, projectId, permMap)) {
                    buttons.put("acceptInvoiceExternalLink", (NooshOneUrlUtil.composeAcceptInvoiceLinkToEnterprise(projectId, invoiceDTO.getId())));
                }
                // reject invoice
                if (rejectInvoicePermission.check(invoiceDTO, workgroupId, userId, projectId, permMap)) {
                    buttons.put("rejectInvoiceExternalLink", (NooshOneUrlUtil.composeRejectInvoiceLinkToEnterprise(projectId, invoiceDTO.getId())));
                }
            }
        }
        // cancel invoice
        int invoiceSection = 0;
        if (invoiceDTO.getOrder() != null && invoiceDTO.getParent() != null
                && invoiceDTO.getParent().isOutsourcerProject()) {
            invoiceSection = invoiceDTO.getOrderVersion().isOutsourcingSellOrder(invoiceDTO.getParent()) ? 1 : 0;
        }
        buttons.put("invoiceListExternalLink", (NooshOneUrlUtil.composeInvoiceListLinkToEnterprise(projectId, invoiceSection)));

        // create invoice adjustment button
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        if (!projectDTO.isMaster()) {
            projectDTO = projectService.findProjectById(projectDTO.getMasterProjectId());
        }
        boolean invoiceAdjustmentEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_INVOICE_ADJUSTMENT, projectDTO.getOwnerWorkgroupId());
        if (invoiceAdjustmentEnabled && invoiceDTO.getFinal() && invoiceDTO.isAccepted() && orderVersionDTO.getOrderClassificationId() != OrderClassificationID.INVOICE_ADJUSTMENT) {
            buttons.put("createInvoiceAdjustmentOrderExternalLink", (NooshOneUrlUtil.composeCreateInvoiceAdjustmentOrderLinkToEnterprise(projectId, orderVersionDTO.getOrderId())));
        }
        return buttons;
    }

    private InvoiceListDTO toInvoiceListDTO(InvoiceDTO invoiceDTO) {
        InvoiceListDTO invoiceListDTO = new InvoiceListDTO();
        invoiceListDTO.setId(invoiceDTO.getId());
        invoiceListDTO.setSupplierWorkgroupId(invoiceDTO.getSupplierWorkgroupId());
        invoiceListDTO.setStateId(invoiceDTO.getStateId());
        invoiceListDTO.setIsFinal(invoiceDTO.getFinal());
        return invoiceListDTO;
    }

    public BreakoutDTO getBreakoutByTypeId(Object object, long breakoutTypeId) {
        List<BreakoutDTO> breakouts = new ArrayList<>();
        if (object instanceof OrderItemDTO) {
            OrderItemDTO orderItem = (OrderItemDTO)object;
            breakouts = orderItem.getBreakouts();
        } else if(object instanceof InvoiceItemDTO) {
            InvoiceItemDTO invoiceItem = (InvoiceItemDTO)object;
            breakouts = invoiceItem.getBreakouts();
        }
        if (breakouts != null && breakouts.size() > 0) {
            for (BreakoutDTO breakout : breakouts) {
                if (breakout.getBreakoutTypeId() == breakoutTypeId)
                    return breakout;
            }
        }
        return null;
    }

    /**
     * select orders when create invoice
     * @param workgroupId
     * @param userId
     * @param projectId
     * @param page
     * @return
     */
    public SelectOrderVO selectOrders(Long workgroupId, Long userId, Long projectId, PageVO page) {
        ProjectDTO project = projectService.findProjectById(projectId);

        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<OrderDTO> dtos = orderMyBatisMapper.findOrdersByProjectId(project.getId(), project.getOwnerWorkgroupId(), project.isClientNotOnNoosh(), true);
        page.setTotal(pageInfo.getTotal());

        // create invoice permission
        boolean createInvoiceButton = permissionService.checkAll(PermissionID.CREATE_INVOICE, workgroupId, userId, projectId);
        if (project.isBrokerOutsourcerProject() && project.isClientOnNoosh()) {
            boolean clientPrefs = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING, project.getClientWorkgroupId());
            if (!clientPrefs) {
                createInvoiceButton = false;
            }
        }
        List<OrderVO> vos = new ArrayList<>();
        dtos.forEach(dto -> {
            OrderVO vo = new OrderVO();
            AggregatedOrderVersionDTO aggregatedOrderDTO = aggregatedOrderService.findSimpleAggregatedOrderDTO(dto.getOrderId(), workgroupId, userId);
            boolean canCreateInvoice = createInvoicePermission.check(aggregatedOrderDTO.getAggregatedOrder(), workgroupId, userId, projectId);
            vo.setCanCreateInvoice(canCreateInvoice);
            vo.setOrderId(aggregatedOrderDTO.getAggregatedOrder().getOrderId());
            vo.setReference(dto.getReference());
            vo.setTitle(dto.getTitle());
            vo.setBuyerWorkgroup(aggregatedOrderDTO.getAggregatedOrder().getBuyerWorkgroup().getName());
            vo.setStatusStrId(dto.getDescriptionStrId());
            vo.setAcceptedDate(aggregatedOrderDTO.getAggregatedOrder().getLatestAcceptDate());
            vo.setOrderAmount(aggregatedOrderDTO.getAggregatedOrder().getGrandTotal());
            OrderVersionDTO originalOrder = aggregatedOrderDTO.getOriginalOrder();
            boolean isDualCurrency = originalOrder.getRate() != null && originalOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrder.getExCurrencyId() != null;
            if (isDualCurrency) {
                vo.setExOrderAmount(aggregatedOrderDTO.getAggregatedOrder().getExGrandTotal());
                vo.setExCurrencyId(originalOrder.getExCurrencyId());
            }
            vos.add(vo);
        });

        SelectOrderVO selectOrderVO = new SelectOrderVO();
        selectOrderVO.setCanCreateInvoice(createInvoiceButton);
        selectOrderVO.setOrders(vos);
        return selectOrderVO;
    }

    /**
     * create or update invoice init information
     * @param userId
     * @param workgroupId
     * @param projectId
     * @param orderId
     * @param invoiceId
     * @param locale
     * @return
     */
    public InvoiceInitInfoVO initInformation(Long userId, Long workgroupId, Long projectId, Long orderId, Long invoiceId, Long originalInvoiceId, String locale, Boolean isReplace) {
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        OrderDTO orderDTO = orderMyBatisMapper.findOrderById(projectId, orderId);
        OrderVersionDTO orderVersionDTO = orderService.findSimpleOrderById(orderId);
        // aggregated
        OrderVersionDTO originalOrder;
        if (orderVersionDTO.isChangeOrder()) {
            originalOrder = orderService.findSimpleOrderById(orderDTO.getParentOrderId());
        } else {
            originalOrder = orderVersionDTO;
        }
        originalOrder.setParent(projectDTO);
        orderDTO.setOrderVersion(originalOrder);
        OrderVersionDTO aggrOrder = aggregatedOrderService.getAggregatedOrder(originalOrder, null, workgroupId, userId);

        InvoiceInitInfoVO initInfoVO = new InvoiceInitInfoVO();
        initInfoVO.setProjectNumber(projectDTO.getMasterProjectId() != null ? projectDTO.getMasterProjectId().toString() : projectDTO.getId().toString());
        initInfoVO.setOrderReference(orderDTO.getReference());
        initInfoVO.setOrderTitle(orderDTO.getTitle() != null ? orderDTO.getTitle() : orderDTO.getReference());
        initInfoVO.setPaymentMethodStrId(orderDTO.getDescriptionStrId());
        initInfoVO.setPaymentReferenceNO(orderDTO.getPaymentReference());
        initInfoVO.setIsItemizedTaxAndShipping(originalOrder.getItemized());
        initInfoVO.setBuyWorkgroupId(orderDTO.getBuyerWorkgroupId());
        initInfoVO.setIsSell(orderVersionDTO.isSellOrder(projectDTO));

        // Reason Dropdown
        List<DropdownVO> reasonOptions = new ArrayList<>();
        reasonOptions.add(new DropdownVO("-1", "&lt;Please select a reason&gt;"));
        reasonOptions.add(new DropdownVO("1", i18NUtils.getMessage(StringID.INVOICE_NON_BILLABLE_REASON_PREPAYMENT, locale)));
        reasonOptions.add(new DropdownVO("2", i18NUtils.getMessage(StringID.INVOICE_NON_BILLABLE_REASON_CHANGE_ORDERS, locale)));
        reasonOptions.add(new DropdownVO("99", i18NUtils.getMessage(StringID.INVOICE_NON_BILLABLE_REASON_OTHER, locale)));
        initInfoVO.setReasonOptions(reasonOptions);

        Map<String, String> prefs = preferenceService.findUserPrefs(orderDTO.getBuyerWorkgroupId(), userId);
        String taxLabelString = preferenceService.getString(PreferenceID.PC_TAX_LABEL_STRING, prefs, null);
        if (taxLabelString == null) {
            taxLabelString = i18NUtils.getMessage(StringID.TAX_DEFAULT_LABEL_STR, locale);
        }
        initInfoVO.setTaxLabelString(taxLabelString);

        boolean shipmentRecordRequired;
        if (orderDTO.getOrderVersion().isOutsourcingSellOrder(projectDTO)) {
            Map<String, String> supplierPrefs = preferenceService.findGroupPrefs(orderDTO.getSupplierWorkgroupId());
            shipmentRecordRequired = preferenceService.check(PreferenceID.PC_INVOICE_SELL_REQUIRE_SHIPMENT, supplierPrefs);
        } else {
            shipmentRecordRequired = preferenceService.check(PreferenceID.PC_INVOICE_BUY_REQUIRE_SHIPMENT, prefs);
        }
        initInfoVO.setShipmentRecordRequired(shipmentRecordRequired);

        boolean noValidationAgainstOrderTotal = preferenceService.check(PreferenceID.PC_INVOICE_NO_VALIDATION, prefs);
        initInfoVO.setNoValidationAgainstOrderTotal(noValidationAgainstOrderTotal);

        InvoiceDTO invoiceDTO = new InvoiceDTO();
        if (invoiceId != null) { // edit the existing one
            Optional<Invoice> invoiceOptional = invoiceRepository.findById(invoiceId);
            if (invoiceOptional.isPresent()) {
                Invoice invoice = invoiceOptional.get();
                initInfoVO.setIsNonBillable(invoice.getNonBillable());
                initInfoVO.setNonBillableReasonId(invoice.getNonBillableReasonId());
                initInfoVO.setInvoiceNumber(invoice.getOwnerReference());
                initInfoVO.setReferenceNumber(invoice.getReference());
                initInfoVO.setInvoiceDate(invoice.getInvoiceDate());
                initInfoVO.setInvoiceDueDate(invoice.getDueDate());
                initInfoVO.setComments(invoice.getComments());
                initInfoVO.setCustomPropertyId(invoice.getCustomPropertyId());
                invoiceDTO = invoiceMyBatisMapper.findInvoiceDetail(projectId, invoiceId);

                // invoice item and breakout
                List<InvoiceItemDTO> invoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoiceDTO.getId(), workgroupId);
                populateBreakouts(invoiceItems);
                invoiceDTO.setInvoiceItemDTOList(invoiceItems);
                initInfoVO.setIsFinalInvoice(invoice.getFinal());
            }
        } else { // create a new one
            initInfoVO.setInvoiceDate(LocalDateTime.now());
            initInfoVO.setNonBillableReasonId(-1L);
            boolean createInvoice = createInvoicePermission.check(aggrOrder, workgroupId, userId, projectId);
            if (!createInvoice) throw new NoPermissionException("You don't have the permission to create the invoice.");

            if (originalInvoiceId != null) { // replace or clone the existing one
                Optional<Invoice> invoiceOptional = invoiceRepository.findById(originalInvoiceId);
                if (invoiceOptional.isPresent()) {
                    Invoice invoice = invoiceOptional.get();
                    String invoiceNumber = invoice.getOwnerReference();
                    if (isReplace != null && isReplace) {
                        invoiceNumber = "Replacement for " + invoiceNumber;
                    }
                    initInfoVO.setInvoiceNumber(invoiceNumber);
                    initInfoVO.setIsNonBillable(invoice.getNonBillable());
                    initInfoVO.setNonBillableReasonId(invoice.getNonBillableReasonId());
                    initInfoVO.setReferenceNumber(invoice.getReference());
                    initInfoVO.setInvoiceDueDate(invoice.getDueDate());
                    initInfoVO.setComments(invoice.getComments());
                    invoiceDTO = invoiceMyBatisMapper.findInvoiceDetail(projectId, originalInvoiceId);
                    // invoice item and breakout
                    List<InvoiceItemDTO> invoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoiceDTO.getId(), workgroupId);
                    populateBreakouts(invoiceItems);
                    invoiceDTO.setInvoiceItemDTOList(invoiceItems);
                    invoiceDTO.setStateId(ObjectStateID.INVOICE_DRAFT);
                    invoiceDTO.setId(-1L);
                    invoiceDTO.setSubmitDate(null);
                }
            }
            else { // create a brand new one
                invoiceDTO = createInvoice(userId, orderDTO, null, false, prefs);

                //carry over order/item user fields to new invoice
                boolean userFieldEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_USER_FIELDS, prefs);
                if (userFieldEnabled) {
                    initInfoVO.setCustomPropertyId(aggrOrder.getCustomPropertyId());
                    for (OrderItemDTO orderItemDTO : aggrOrder.getOrderItemDTOs()) {
                        invoiceDTO.getInvoiceItemDTOList().stream()
                                .filter(invoiceItem -> invoiceItem.getItemIndex() == orderItemDTO.getItemIndex())
                                .findFirst()
                                .ifPresent(invoiceItem -> invoiceItem.setCustomPropertyId(orderItemDTO.getCustomPropertyId()));
                    }
                }
            }
        }
        invoiceDTO.setOrderId(orderId);
        invoiceDTO.setOrder(orderDTO);
        setDualCurrencyFlag(invoiceDTO, orderDTO);
        invoiceDTO.setParent(projectDTO);
        initInfoVO.setStatusId(invoiceDTO.getStateId());
        //dual currency
        initInfoVO.setIsDualCurrency(invoiceDTO.isDualCurrency());
        initInfoVO.setExCurrencyId(invoiceDTO.getExCurrencyId());
        initInfoVO.setRate(invoiceDTO.getRate());
        if (invoiceDTO.getStateId() != null) {
            ObjectState objectState = objectStateRepository.findById(invoiceDTO.getStateId()).orElse(null);
            if (objectState != null) {
                initInfoVO.setInvoiceStatusStrId(objectState.getDescriptionStrId().toString());
            }
        }

        // get invoice to and prepared by
        getInvoiceToAndPreparedBy(userId, orderId, orderDTO, invoiceDTO, initInfoVO);

        // get accepted invoice and item
        List<InvoiceDTO> acceptedInvoiceList = invoiceMyBatisMapper.findInvoiceForOrder(projectId, orderId);
        List<InvoiceDTO> acceptedList = new ArrayList();
        if (!acceptedInvoiceList.isEmpty()) {
            for (InvoiceDTO invoice : acceptedInvoiceList) {
                if (!invoice.isAccepted())
                    continue;
                if (invoice.getAcceptedDate() != null && invoiceDTO.getSubmitDate() != null
                        && invoice.getAcceptedDate().compareTo(invoiceDTO.getSubmitDate()) >= 0)
                    continue;
                acceptedList.add(invoice);
            }
        }

        acceptedList = acceptedList.stream().map(invoice -> {
            List<InvoiceItemDTO> acceptedInvoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoice.getId(), workgroupId);
            populateBreakouts(acceptedInvoiceItems);
            invoice.setInvoiceItemDTOList(acceptedInvoiceItems);
            return invoice;
        }).collect(Collectors.toList());

        InvoiceDTO totalAcceptedInvoice = acceptedList.size() > 0
                ? createAggregatedInvoice(userId, acceptedList, invoiceDTO, invoiceDTO.getSubmitDate(), false, prefs)
                : createInvoice(userId, orderDTO, invoiceDTO, false, prefs);

        acceptedList.add(invoiceDTO);

        InvoiceDTO totalInvoice = createAggregatedInvoice(userId, acceptedList , invoiceDTO, null, true, prefs);

        boolean closeOrderNegotiation = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION, prefs);
        List<InvoiceItemDTO> invoiceItems = invoiceDTO.getInvoiceItemDTOList();

        // get invoice item list
        List<InvoiceItemVO> invoiceItemVOList = getInvoiceItemVOList(invoiceDTO, orderVersionDTO, aggrOrder, invoiceItems, totalAcceptedInvoice, totalInvoice, closeOrderNegotiation, locale);
        // get invoice item total tax, shipping, grand etc
        InvoiceItemsTotalVO invoiceItemsTotalVO = getInvoiceItemsTotalVO(invoiceDTO, aggrOrder, totalAcceptedInvoice, totalInvoice, closeOrderNegotiation);

        initInfoVO.setInvoiceItems(invoiceItemVOList);
        initInfoVO.setInvoiceItemsTotal(invoiceItemsTotalVO);

        InvoiceListDTO invoiceListDTO = toInvoiceListDTO(invoiceDTO);
        // buttons
        Map<String, Boolean> buttons = getButtons(invoiceId, orderVersionDTO, invoiceListDTO, invoiceDTO, workgroupId, userId, projectId);
        initInfoVO.setButtons(buttons);

        // reset ids for replace/clone case
        //initInfoVO.setStatusId(ObjectStateID.INVOICE_DRAFT);
        if (invoiceId == null && originalInvoiceId != null) {
            for (InvoiceItemVO invoiceItemVO : initInfoVO.getInvoiceItems()) {
                invoiceItemVO.setId(null);
                if (invoiceItemVO.getBreakouts() != null) {
                    for (InvoiceBreakoutVO invoiceBreakoutVO : invoiceItemVO.getBreakouts()) {
                        invoiceBreakoutVO.setId(null);
                    }
                }
            }
        }
        setCustomAttributes(initInfoVO); //set custom fields for invoice & invoice item
        return initInfoVO;
    }

    private void getInvoiceToAndPreparedBy(Long userId, Long orderId, OrderDTO orderDTO, InvoiceDTO invoiceDTO, InvoiceInitInfoVO initInfoVO) {
        // Invoice To
        List<OrBillingRecipient> billingRecipients = billingRecipientRepository.findByOrderId(orderId);
        AccountUser user = accountUserRepository.findById(userId).orElse(null);
        if (billingRecipients != null) {
            List<AddressVO> invoiceToList = new ArrayList<>();
            if (billingRecipients.size() <= 1) {
                Address address;
                AddressVO vo = new AddressVO();
                if (invoiceDTO.getInvoiceCMContactId() != null) {
                    Contact contact = contactRepository.findById(invoiceDTO.getInvoiceCMContactId()).orElse(null);
                    AccountUser accountUser = accountUserRepository.findById(contact.getContactUserId()).orElse(null);
                    vo.setFullName(accountUser.getPerson().getFullName());
                    if(accountUser.getWorkgroup().getName() != null) {
                        vo.setWorkgroupName(accountUser.getWorkgroup().getName());
                    }
                    address = contact.getAddress();
                    if (address == null) {
                        address = accountUser.getPerson().getAddress();
                    }
                } else {
                    AccountUser buyerUser = accountUserRepository.findById(orderDTO.getOrderVersion().getBuyerUserId()).orElse(null);
                    vo.setFullName(buyerUser.getPerson().getFullName());
                    vo.setWorkgroupName(buyerUser.getWorkgroup().getName());
                    address = buyerUser.getPerson().getAddress();
                }
                vo.setLine1(address.getLine1());
                vo.setLine2(address.getLine2());
                vo.setLine3(address.getLine3());
                vo.setCity(address.getCity());
                vo.setState(address.getState());
                vo.setPostal(address.getPostal());
                vo.setCountryStrId(address.getCountry().getNameStrId());
                invoiceToList.add(vo);
            } else {
                billingRecipients.stream().forEach(billingRecipient -> {
                    AddressVO vo = new AddressVO();
                    Contact contact = billingRecipient.getContact();
                    vo.setFullName(contact.getContactFullName());
                    vo.setWorkgroupName(contact.getAccountUser().getWorkgroup().getName());
                    vo.setLine1(contact.getAddress().getLine1());
                    vo.setLine2(contact.getAddress().getLine2());
                    vo.setLine3(contact.getAddress().getLine3());
                    vo.setCity(contact.getAddress().getCity());
                    vo.setState(contact.getAddress().getState());
                    vo.setPostal(contact.getAddress().getPostal());
                    vo.setCountryStrId(contact.getAddress().getCountry().getNameStrId());
                    invoiceToList.add(vo);
                });
            }
            initInfoVO.setInvoiceToList(invoiceToList);
        }

        // Prepared By
        AddressVO addressVO = new AddressVO();
        addressVO.setFullName(user.getPerson().getFullName());
        addressVO.setWorkgroupName(user.getWorkgroup().getName());
        Address address = user.getPerson().getAddress();
        addressVO.setLine1(address.getLine1());
        addressVO.setLine2(address.getLine2());
        addressVO.setLine3(address.getLine3());
        addressVO.setCity(address.getCity());
        addressVO.setState(address.getState());
        addressVO.setPostal(address.getPostal());
        addressVO.setCountryStrId(address.getCountry().getNameStrId());
        initInfoVO.setPreparedBy(addressVO);
    }

    private Map<String, Boolean> getButtons(Long invoiceId, OrderVersionDTO orderVersionDTO, InvoiceListDTO invoiceListDTO,
                                            InvoiceDTO invoiceDTO, Long workgroupId, Long userId, long projectId) {


        List<Long> permissionIds = Arrays.asList(PermissionID.CREATE_INVOICE, PermissionID.EDIT_INVOICE, PermissionID.SEND_INVOICE,
                PermissionID.DELETE_INVOICE, PermissionID.VIEW_ORDER);
        Map<String, Boolean>  permissionMap = permissionService.getPermissionMap(permissionIds, workgroupId, userId, Arrays.asList(projectId));

        Map<String, Boolean> buttons = new HashMap<>();

        // update
        buttons.put("updateButton",true);
        buttons.put("sendButton",true);
        if (invoiceId == null && !createInvoicePermission.check(orderVersionDTO, workgroupId, userId, projectId, permissionMap)) {
            buttons.put("updateButton",false);
        } else if(!editInvoicePermission.check(invoiceListDTO, workgroupId, userId, projectId, permissionMap)) {
            buttons.put("updateButton",false);
        }

        // send invoice
        if (sendInvoicePermission.check(invoiceDTO, workgroupId, userId, projectId, permissionMap) && invoiceDTO.isDraft()) {
            buttons.put("sendButton",true);
        } else {
            buttons.put("sendButton",false);
        }

        // delete invoice
        if (deleteInvoicePermission.check(invoiceListDTO, workgroupId, userId, projectId) && invoiceDTO.isDraft()) {
            buttons.put("deleteButton",true);
        } else {
            buttons.put("deleteButton",false);
        }

        // cancel
        buttons.put("cancelButton",true);

        return buttons;
    }

    public List<InvoiceOrderCostCenterDTO> getInvoiceCostCenter(Long projectId, Long workgroupId, Long userId, Long invoiceId, Long orderId, String locale) {
        InvoiceDetailVO invoiceDetailVO = detail(userId, workgroupId, projectId, invoiceId, locale);
        OrderDTO orderDTO = orderMyBatisMapper.findOrderById(projectId, orderId);
        List<Long> changeOrderIds = orderMyBatisMapper.findChangeOrderIds(orderId);
        OrderVersionDTO originalOrder = orderService.findSimpleOrderById(orderId);
        if (!invoiceDetailVO.getShowCostAllocation()) {
            throw new NoPermissionException("Your don't have permission to view cost center");
        }
        List<InvoiceOrderCostCenterDTO> invoiceOrderCostCenterDTOs = new ArrayList<InvoiceOrderCostCenterDTO>();
        // aggregated
        boolean isAggregated = false;
        if (changeOrderIds != null && changeOrderIds.size() > 0) {
            originalOrder = orderService.findSimpleOrderById(orderDTO.getParentOrderId());
            isAggregated = true;
        }

        OrderCostCenterDetailDTO orderCostCenterDetailDTO = new OrderCostCenterDetailDTO();
        if (!isAggregated) {
            orderCostCenterDetailDTO = costCenterService.getCostCenterForOrder(originalOrder.getOrderId(),
                    projectId, workgroupId, userId, isAggregated);
        } else {
            orderCostCenterDetailDTO = aggregatedOrderService.getAggregatedCostCenterForOrder(
                    orderId, projectId, workgroupId, userId);
        }
        List<OrderCostCenterDTO> orderCostCenters = orderCostCenterDetailDTO.getOrderCostCenters();
        if (orderCostCenters != null && orderCostCenters.size() > 0) {
            for (OrderCostCenterDTO dto : orderCostCenters) {
                InvoiceOrderCostCenterDTO invoiceOrderCostCenterDTO = new InvoiceOrderCostCenterDTO();
                if (!("TAX".equalsIgnoreCase(dto.getItemName()) || "SHIPPING".equalsIgnoreCase(dto.getItemName()))) {
                    invoiceOrderCostCenterDTO.setInvoiceValue(invoiceDetailVO.getItemsTotal().getSubTotal().getTotalInvoiced() != null
                            ? invoiceDetailVO.getItemsTotal().getSubTotal().getTotalInvoiced().doubleValue() : (double) 0.0);
                    invoiceOrderCostCenterDTO.setItemName("itemsTotal");
                } else if ("TAX".equalsIgnoreCase(dto.getItemName())) {
                    invoiceOrderCostCenterDTO.setInvoiceValue(invoiceDetailVO.getItemsTotal().getTax().getTotalInvoiced() != null
                            ? invoiceDetailVO.getItemsTotal().getTax().getTotalInvoiced().doubleValue() : (double) 0.0);
                    invoiceOrderCostCenterDTO.setItemName("TAX");
                } else {
                    invoiceOrderCostCenterDTO.setInvoiceValue(invoiceDetailVO.getItemsTotal().getShipping().getTotalInvoiced() != null
                            ? invoiceDetailVO.getItemsTotal().getShipping().getTotalInvoiced().doubleValue() : (double) 0.0);
                    invoiceOrderCostCenterDTO.setItemName("SHIPPING");
                }
                invoiceOrderCostCenterDTO.setItemValue(dto.getItemValue());

                List<CostCenterAllocationDTO> allocationDTOs = dto.getCostCenters();
                if (allocationDTOs != null && allocationDTOs.size() > 0) {
                    for (CostCenterAllocationDTO allocationDTO : allocationDTOs) {
                        allocationDTO.setInvoiceAmount(allocationDTO.getPercent() != null
                                ? new BigDecimal(invoiceOrderCostCenterDTO.getInvoiceValue()).multiply(new BigDecimal(allocationDTO.getPercent()))
                                .divide(new BigDecimal(100), 5, RoundingMode.HALF_UP).doubleValue() : null);
                    }
                }
                invoiceOrderCostCenterDTO.setCostCenters(allocationDTOs);
                invoiceOrderCostCenterDTOs.add(invoiceOrderCostCenterDTO);
            }
        }
        return invoiceOrderCostCenterDTOs;
    }

    private void setCustomAttributes(InvoiceInitInfoVO invoiceVO) {
        List<Long> propertyIds = new ArrayList<>();
        if (hasPropertyId(invoiceVO.getCustomPropertyId())) {
            propertyIds.add(invoiceVO.getCustomPropertyId());
        }
        if (invoiceVO.getInvoiceItems() != null) {
            invoiceVO.getInvoiceItems().forEach(invoiceItemVO -> {
                if (hasPropertyId(invoiceItemVO.getCustomPropertyId())) {
                    propertyIds.add(invoiceItemVO.getCustomPropertyId());
                }
            });
        }
        if (!propertyIds.isEmpty()) {
            Map<Long, Map<String, Object>> customAttributesMap = workgroupOpenFeignClient.findCustomAttribute(propertyIds);
            if (hasPropertyId(invoiceVO.getCustomPropertyId())) {
                invoiceVO.setCustomAttributes(customAttributesMap.get(invoiceVO.getCustomPropertyId()));
            }
            if (invoiceVO.getInvoiceItems() != null) {
                invoiceVO.getInvoiceItems().forEach(invoiceItemVO -> {
                    if (hasPropertyId(invoiceItemVO.getCustomPropertyId())) {
                        invoiceItemVO.setCustomAttributes(customAttributesMap.get(invoiceItemVO.getCustomPropertyId()));
                    }
                });
            }
        }
    }

    private void setCustomAttributes(InvoiceDetailVO invoiceVO) {
        List<Long> propertyIds = new ArrayList<>();
        if (hasPropertyId(invoiceVO.getCustomPropertyId())) {
            propertyIds.add(invoiceVO.getCustomPropertyId());
        }
        if (invoiceVO.getItems() != null) {
            invoiceVO.getItems().forEach(invoiceItemVO -> {
                if (hasPropertyId(invoiceItemVO.getCustomPropertyId())) {
                    propertyIds.add(invoiceItemVO.getCustomPropertyId());
                }
            });
        }
        if (!propertyIds.isEmpty()) {
            Map<Long, Map<String, Object>> customAttributesMap = workgroupOpenFeignClient.findCustomAttribute(propertyIds);
            if (hasPropertyId(invoiceVO.getCustomPropertyId())) {
                invoiceVO.setCustomAttributes(customAttributesMap.get(invoiceVO.getCustomPropertyId()));
            }
            if (invoiceVO.getItems() != null) {
                invoiceVO.getItems().forEach(invoiceItemVO -> {
                    if (hasPropertyId(invoiceItemVO.getCustomPropertyId())) {
                        invoiceItemVO.setCustomAttributes(customAttributesMap.get(invoiceItemVO.getCustomPropertyId()));
                    }
                });
            }
        }
    }

    private boolean hasPropertyId(Long propertyId) {
        return propertyId != null && propertyId > 0;
    }

    public InvoiceDetailVO getShipmentRecords(Long workgroupId, Long projectId, Long invoiceId, String locale) {
        InvoiceDTO invoiceDTO = invoiceMyBatisMapper.findInvoiceDetail(projectId, invoiceId);
        if (invoiceDTO == null) {
            throw new NotFoundException("invoice Id not found");
        }
        List<InvoiceItemDTO> invoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoiceDTO.getId(), workgroupId);
        InvoiceDetailVO invoiceVO = new InvoiceDetailVO();
        List<InvoiceItemVO> invoiceItemVOS = new ArrayList<>();
        invoiceVO.setItems(invoiceItemVOS);
        for (InvoiceItemDTO invoiceItemDTO : invoiceItems) {
            InvoiceItemVO invoiceItemVO = new InvoiceItemVO();
            invoiceItemVO.setId(invoiceItemDTO.getInvoiceItemId());
            invoiceItemVO.setJobId(invoiceItemDTO.getJobId());
            invoiceItemVO.setCustomPropertyId(invoiceItemDTO.getCustomPropertyId());
            invoiceItemVOS.add(invoiceItemVO);
        }
        setCustomAttributes(invoiceVO);
        for (InvoiceItemVO invoiceItemVO : invoiceVO.getItems()) {
            List<Long> selectedShipmentRecordIds = getSelectedRequestIdList(invoiceItemVO.getCustomAttributes());
            // item associated shipments records
            String associatedShipmentRecords = composeShipmentRecordsOnDetailPage(invoiceItemVO.getJobId(), locale, selectedShipmentRecordIds);
            invoiceItemVO.setAssociatedShipmentRecords(associatedShipmentRecords);
            invoiceItemVO.setSelectedShipmentRecordIds(selectedShipmentRecordIds);
        }
        return invoiceVO;
    }

    public InvoiceDetailVO getEditableShipmentRecords(Long userId, Long workgroupId, Long projectId, Long orderId, Long invoiceId, String locale) {
        //create or get an existing invoice
        InvoiceDTO invoiceDTO = getInvoiceDTO(userId, workgroupId, projectId, orderId, invoiceId);
        // get accepted invoice and item
        List<InvoiceDTO> acceptedDTOList = getAcceptedInvoice(workgroupId, projectId, orderId, invoiceDTO.getSubmitDate());
        List<InvoiceDetailVO> acceptedVOList = covertAndSetCustomAttributes(acceptedDTOList);
        Map<Long, Set<Long>> excludedRequestIdMap = getExcludedRequestIdSet(acceptedVOList);

        InvoiceDetailVO invoiceVO = new InvoiceDetailVO();
        List<InvoiceItemVO> invoiceItemVOS = new ArrayList<>();
        invoiceVO.setItems(invoiceItemVOS);
        for (InvoiceItemDTO invoiceItemDTO : invoiceDTO.getInvoiceItemDTOList()) {
            InvoiceItemVO invoiceItemVO = new InvoiceItemVO();
            invoiceItemVO.setId(invoiceItemDTO.getInvoiceItemId());
            invoiceItemVO.setJobId(invoiceItemDTO.getJobId());
            invoiceItemVO.setCustomPropertyId(invoiceItemDTO.getCustomPropertyId());
            invoiceItemVOS.add(invoiceItemVO);
        }
        setCustomAttributes(invoiceVO);
        for (InvoiceItemVO invoiceItemVO : invoiceVO.getItems()) {
            List<Long> selectedShipmentRecordIds = getSelectedRequestIdList(invoiceItemVO.getCustomAttributes());
            List<RequestDTO> availableRequests = new ArrayList<>();
            List<DropdownVO<Long>> shipmentRecords = new ArrayList<>();
            String associatedShipmentRecords = composeShipmentRecordsOnEditPage(invoiceItemVO.getJobId(), locale, shipmentRecords, availableRequests, excludedRequestIdMap.get(invoiceItemVO.getJobId()), selectedShipmentRecordIds);
            invoiceItemVO.setAssociatedShipmentRecords(associatedShipmentRecords);
            invoiceItemVO.setSelectedShipmentRecordIds(selectedShipmentRecordIds);
            invoiceItemVO.setShipmentRecords(shipmentRecords);
            invoiceItemVO.setShipmentRecordsWithQty(availableRequests);
        }
        return invoiceVO;
    }

    private List<InvoiceDetailVO> covertAndSetCustomAttributes(List<InvoiceDTO> acceptedDTOList) {
        List<InvoiceDetailVO> acceptedVOList = new ArrayList<>();
        if (acceptedDTOList == null || acceptedDTOList.isEmpty()) {
            return acceptedVOList;
        }
        List<Long> propertyIds = new ArrayList<>();
        for (InvoiceDTO invoiceDTO : acceptedDTOList) {
            InvoiceDetailVO invoiceVO = new InvoiceDetailVO();
            List<InvoiceItemVO> invoiceItemVOS = new ArrayList<>();
            invoiceVO.setItems(invoiceItemVOS);
            acceptedVOList.add(invoiceVO);
            for (InvoiceItemDTO invoiceItemDTO : invoiceDTO.getInvoiceItemDTOList()) {
                InvoiceItemVO invoiceItemVO = new InvoiceItemVO();
                invoiceItemVO.setId(invoiceItemDTO.getInvoiceItemId());
                invoiceItemVO.setJobId(invoiceItemDTO.getJobId());
                invoiceItemVO.setCustomPropertyId(invoiceItemDTO.getCustomPropertyId());
                invoiceItemVOS.add(invoiceItemVO);
                if (hasPropertyId(invoiceItemVO.getCustomPropertyId())) {
                    propertyIds.add(invoiceItemVO.getCustomPropertyId());
                }
            }
        }
        if (!propertyIds.isEmpty()) {
            Map<Long, Map<String, Object>> customAttributesMap = workgroupOpenFeignClient.findCustomAttribute(propertyIds);
            for (InvoiceDetailVO invoiceDetailVO : acceptedVOList) {
                for (InvoiceItemVO invoiceItemVO : invoiceDetailVO.getItems()) {
                    if (hasPropertyId(invoiceItemVO.getCustomPropertyId())) {
                        invoiceItemVO.setCustomAttributes(customAttributesMap.get(invoiceItemVO.getCustomPropertyId()));
                    }
                }
            }
        }
        return acceptedVOList;
    }

    private List<Long> getSelectedRequestIdList(Map customAttributes) {
        if (null != customAttributes && customAttributes.containsKey("REQUEST_IDS_str")) {
            String requestIds = (String) customAttributes.get("REQUEST_IDS_str");
            if (StringUtils.isNotBlank(requestIds)) {
                return Arrays.stream(requestIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
            }
        }
        return new ArrayList<>();
    }
    private Map<Long, Set<Long>> getExcludedRequestIdSet(List<InvoiceDetailVO> acceptedList) {
        Map<Long, Set<Long>> excludedRequestIdMap = new HashMap<>();
        if (acceptedList != null && !acceptedList.isEmpty()) {
            acceptedList.forEach(invoiceVO -> {
                List<InvoiceItemVO> invoiceItems = invoiceVO.getItems();
                if (invoiceItems != null && !invoiceItems.isEmpty()) {
                    invoiceItems.forEach(invoiceItemDTO -> {
                        if (invoiceItemDTO.getJobId() != null) {
                            Set<Long> excludedRequestIdSet =
                                    getExcludedRequestIdSet(invoiceItemDTO.getCustomAttributes());
                            if (excludedRequestIdMap.get(invoiceItemDTO.getJobId()) != null) {
                                excludedRequestIdSet.addAll(excludedRequestIdMap.get(invoiceItemDTO.getJobId()));
                            }
                            excludedRequestIdMap.put(invoiceItemDTO.getJobId(), excludedRequestIdSet);
                        }
                    });
                }
            });
        }
        return excludedRequestIdMap;
    }
    private Set<Long> getExcludedRequestIdSet(Map customAttributes) {
        if (null != customAttributes && customAttributes.containsKey("REQUEST_IDS_str")) {
            String requestIds = (String) customAttributes.get("REQUEST_IDS_str");
            if (StringUtils.isNotBlank(requestIds)) {
                return Arrays.stream(requestIds.split(",")).map(Long::valueOf).collect(Collectors.toSet());
            }
        }
        return new HashSet<>();
    }

    private InvoiceDTO getInvoiceDTO(Long userId, Long workgroupId, Long projectId, Long orderId, Long invoiceId) {
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        OrderDTO orderDTO = orderMyBatisMapper.findOrderById(projectId, orderId);
        OrderVersionDTO orderVersionDTO = orderService.findSimpleOrderById(orderId);
        // aggregated
        OrderVersionDTO originalOrder;
        if (orderVersionDTO.isChangeOrder()) {
            originalOrder = orderService.findSimpleOrderById(orderDTO.getParentOrderId());
        } else {
            originalOrder = orderVersionDTO;
        }
        originalOrder.setParent(projectDTO);
        orderDTO.setOrderVersion(originalOrder);
        InvoiceDTO invoiceDTO;
        if (invoiceId != null) { // edit the existing one
            Optional<Invoice> invoiceOptional = invoiceRepository.findById(invoiceId);
            if (invoiceOptional.isPresent()) {
                invoiceDTO = new InvoiceDTO();
                setDualCurrencyFlag(invoiceDTO, orderDTO);
                invoiceDTO.setId(invoiceId);
                invoiceDTO.setSubmitDate(invoiceOptional.get().getSubmitDate());
                List<InvoiceItemDTO> invoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoiceId, workgroupId);
                invoiceDTO.setInvoiceItemDTOList(invoiceItems);
            } else {
                throw new NotFoundException("invoice Id not found");
            }
        } else { // create a new one
            invoiceDTO = createInvoice(userId, orderDTO, null, false, new HashMap<>());
        }
        return invoiceDTO;
    }

    private List<InvoiceDTO> getAcceptedInvoice(Long workgroupId, Long projectId, Long orderId, LocalDateTime submitDate) {
        List<InvoiceDTO> acceptedInvoiceList = invoiceMyBatisMapper.findInvoiceForOrder(projectId, orderId);
        List<InvoiceDTO> acceptedList = new ArrayList();
        if (!acceptedInvoiceList.isEmpty()) {
            for (InvoiceDTO invoice : acceptedInvoiceList) {
                if (!invoice.isAccepted())
                    continue;
                if (invoice.getAcceptedDate() != null && submitDate != null
                        && invoice.getAcceptedDate().compareTo(submitDate) >= 0)
                    continue;
                acceptedList.add(invoice);
            }
        }
        acceptedList = acceptedList.stream().map(invoice -> {
            List<InvoiceItemDTO> acceptedInvoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoice.getId(),
                    workgroupId);
            invoice.setInvoiceItemDTOList(acceptedInvoiceItems);
            return invoice;
        }).collect(Collectors.toList());
        return acceptedList;
    }

    public String composeShipmentRecordsOnDetailPage(long jobId, String locale, List<Long> selectedRequestIds) {
        List<RequestDTO> results = new ArrayList();
        if (jobId > -1)
            results = invoiceMyBatisMapper.findShipmentRecordsByJobIdAndRequestIds(jobId, selectedRequestIds);
        String selectedRequests = "";
        for (RequestDTO request : results) {
            String shippedDate = "";
            if (request.getId() != null && request.getShipmentRecord() != null) {
                if (request.getShippedDate() != null) {
                    shippedDate = ", " + DateUtil.getLocaleDateStr(request.getShippedDate(), locale);
                }
                request.setShipmentRecord(request.getShipmentRecord() + shippedDate);
                if (selectedRequests.equals("")) {
                    selectedRequests += request.getShipmentRecord();
                } else {
                    selectedRequests = selectedRequests + "\n" + request.getShipmentRecord();
                }
            }
        }
        return selectedRequests;
    }

    public String composeShipmentRecordsOnEditPage(long jobId, String locale, List<DropdownVO<Long>> shipmentRecords, List<RequestDTO> requestDTOList, Set<Long> excludedRequestIdSet, List<Long> selectedShipmentRecordIds) {
        List<RequestDTO> results = new ArrayList();
        if (jobId > -1)
            results = invoiceMyBatisMapper.findShipmentRecordsByJobIdAndRequestIds(jobId, null);//find all available shipment records for both sell and buy invoice
        String selectedRequests = "";
        for (RequestDTO request : results) {
            String shippedDate = "";
            if (request.getId() != null && request.getShipmentRecord() != null) {
                if (excludedRequestIdSet == null || !excludedRequestIdSet.contains(request.getId())) {//exclude from already attached request id set
                    if (request.getShippedDate() != null) {
                        shippedDate = ", " + DateUtil.getLocaleDateStr(request.getShippedDate(), locale);
                    }
                    request.setShipmentRecord(request.getShipmentRecord() + shippedDate + ", " + request.getQuantity() + ", Requested");
                    shipmentRecords.add(new DropdownVO(request.getId(), request.getShipmentRecord()));
                    requestDTOList.add(request);
                    if (selectedShipmentRecordIds != null && selectedShipmentRecordIds.contains(request.getId())) {
                        if (selectedRequests.equals("")) {
                            selectedRequests += request.getShipmentRecord();
                        } else {
                            selectedRequests = selectedRequests + "\n" + request.getShipmentRecord();
                        }
                    }
                }
            }
        }
        return selectedRequests;
    }

    private InvoiceItemDTO getItemByIndex(List<InvoiceItemDTO> items, int index) {
        if (items != null) {
            for (InvoiceItemDTO item : items) {
                if (item.getItemIndex() == index) {
                    return item;
                }
            }
        }
        return null;
    }

    public void setDualCurrencyFlag(InvoiceDTO invoiceDTO, OrderDTO order) {
        OrderVersionDTO originalOrder = order.getOrderVersion();
        boolean isDualCurrency = isDualCurrency(originalOrder);
        invoiceDTO.setDualCurrency(isDualCurrency);
        if (isDualCurrency) {
            invoiceDTO.setExCurrencyId(originalOrder.getExCurrencyId());
            invoiceDTO.setRate(originalOrder.getRate());
        }
    }

        public boolean isDualCurrency(OrderVersionDTO order) {
            boolean isDualCurrency = false;
            if (order.getRate() != null && order.getRate().compareTo(BigDecimal.ZERO) > 0 && order.getExCurrencyId() != null && order.getExCurrencyId() != -1) {
            if (order.getOrderTypeId() == OrderTypeID.QUICK_ORDER || order.getOrderTypeId() == OrderTypeID.ORDER) {
                isDualCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, order.getSupplierWorkgroupId());
            } else if (order.getOrderTypeId() == OrderTypeID.QUOTE_ORDER) {
                isDualCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, order.getBuyerWorkgroupId());
            }
        }
        return isDualCurrency;
    }
}
