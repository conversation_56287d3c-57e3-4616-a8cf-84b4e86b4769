package com.noosh.app.service.invoice;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.dto.contacts.ContactDTO;
import com.noosh.app.commons.entity.workgroup.Workgroup;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.invoice.InvoicePreferencesVO;
import com.noosh.app.repository.jpa.workgroup.WorkgroupRepository;
import com.noosh.app.repository.mybatis.contacts.ContactMyBatisMapper;
import com.noosh.app.commons.dto.invoice.InvoicePreferencesDTO;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Invoice Admin Service for managing invoice preferences and billing contacts
 * Based on InvoiceController.java business logic
 */
@Service
public class InvoiceAdminService {
    
    // Invoice preference keys based on InvoiceController
    private static final String PC_INVOICE_REQUIRES_APPROVAL = "PC_INVOICE_REQUIRES_APPROVAL";
    private static final String PC_INVOICE_AUTO_ACCEPT_FINAL_INVOICE = "PC_INVOICE_AUTO_ACCEPT_FINAL_INVOICE";
    private static final String PC_INVOICE_SHOW_COSTCENTER = "PC_INVOICE_SHOW_COSTCENTER";
    private static final String PC_INVOICE_NO_VALIDATION = "PC_INVOICE_NO_VALIDATION";
    private static final String PC_INVOICE_ALLOW_CO_CREATION = "PC_INVOICE_ALLOW_CO_CREATION";
    private static final String PC_INVOICE_BUY_REQUIRE_SHIPMENT = "PC_INVOICE_BUY_REQUIRE_SHIPMENT";
    private static final String PC_INVOICE_SELL_REQUIRE_SHIPMENT = "PC_INVOICE_SELL_REQUIRE_SHIPMENT";
    private static final String WORKGROUP_OPTION_COSTCENTER = "WORKGROUP_OPTION_COSTCENTER";

    @Autowired
    private WorkgroupRepository workgroupRepository;

    @Autowired
    private PreferenceService preferenceService;
    
    @Autowired
    private ContactMyBatisMapper contactMyBatisMapper;
    
    /**
     * Get invoice preferences for a workgroup
     * Based on prefs() method in InvoiceController
     */
    public InvoicePreferencesVO getInvoicePreferences(Long workgroupId) {
        // Get all preferences for the workgroup
        List<String> preferenceKeys = Arrays.asList(
            PC_INVOICE_REQUIRES_APPROVAL,
            PC_INVOICE_AUTO_ACCEPT_FINAL_INVOICE,
            PC_INVOICE_SHOW_COSTCENTER,
            PC_INVOICE_NO_VALIDATION,
            PC_INVOICE_ALLOW_CO_CREATION,
            PC_INVOICE_BUY_REQUIRE_SHIPMENT,
            PC_INVOICE_SELL_REQUIRE_SHIPMENT,
            WORKGROUP_OPTION_COSTCENTER
        );
        
        Map<String, String> prefs = preferenceService.findGroupPrefs(workgroupId, preferenceKeys);
        
        // Convert Map to DTO
        InvoicePreferencesDTO dto = new InvoicePreferencesDTO();
        dto.setWorkgroupId(workgroupId);
        dto.setRequiresApproval(parseBoolean(prefs.get(PC_INVOICE_REQUIRES_APPROVAL)));
        dto.setAutoAcceptFinalInvoice(parseBoolean(prefs.get(PC_INVOICE_AUTO_ACCEPT_FINAL_INVOICE)));
        if (parseBoolean(WORKGROUP_OPTION_COSTCENTER)) {
            dto.setShowCostCenter(parseBoolean(prefs.get(PC_INVOICE_SHOW_COSTCENTER)));
        }
        dto.setNoValidation(parseBoolean(prefs.get(PC_INVOICE_NO_VALIDATION)));
        dto.setAllowChangeOrderCreation(parseBoolean(prefs.get(PC_INVOICE_ALLOW_CO_CREATION)));
        Workgroup workgroup = workgroupRepository.findById(workgroupId).get();
        if (workgroup.getWorkGroupTypeId() == 1000001 || workgroup.getWorkGroupTypeId() == 1000004) {
            dto.setBuyRequireShipment(parseBoolean(prefs.get(PC_INVOICE_BUY_REQUIRE_SHIPMENT)));
            dto.setSellRequireShipment(parseBoolean(prefs.get(PC_INVOICE_SELL_REQUIRE_SHIPMENT)));
        }
        return toVO(dto);
    }
    
    /**
     * Update invoice preferences for a workgroup
     * Based on prefs() method with ACTION_UPDATE_PREFERENCES in InvoiceController
     */
    public void updateInvoicePreferences(InvoicePreferencesDTO preferencesDTO, Long userId) {
        Map<String, Object> prefsMap = preferencesDTO.toPreferencesMap();
        Map<String, String> stringPrefsMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : prefsMap.entrySet()) {
            if (entry.getValue() != null) {
                if (entry.getValue() instanceof Boolean) {
                    stringPrefsMap.put(entry.getKey(), (Boolean) entry.getValue() ? "1" : "0");
                } else {
                    stringPrefsMap.put(entry.getKey(), entry.getValue().toString());
                }
            }
        }
        preferenceService.updatePreferencesForWorkgroup(preferencesDTO.getWorkgroupId(), userId, stringPrefsMap);
    }

    
    /**
     * Get billing contacts for a workgroup with pagination
     * Based on list() method in InvoiceController with PageHelper
     */
    public Page<ContactDTO> getBillingContactsWithPagination(Long workgroupId, PageVO page) {
        // Start pagination
        Page<ContactDTO> pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        List<ContactDTO> contacts = contactMyBatisMapper.findBillingContacts(workgroupId);
        page.setTotal(pageInfo.getTotal());
        return pageInfo;
    }
    
    /**
     * Get billing contact details by ID
     * Based on view() method in InvoiceController
     */
    public ContactDTO findContactWithAddress(Long contactId) {
        return contactMyBatisMapper.findContactWithAddress(contactId);
    }
    
    /**
     * Helper method to parse boolean from string preference value
     */
    private boolean parseBoolean(String value) {
        if (value == null) {
            return false;
        }
        return "true".equalsIgnoreCase(value) || "1".equals(value);
    }

    /**
     * Convert InvoicePreferencesDTO to InvoicePreferencesVO
     */
    private static InvoicePreferencesVO toVO(InvoicePreferencesDTO dto) {
        if (dto == null) return null;

        InvoicePreferencesVO vo = new InvoicePreferencesVO();
        vo.setWorkgroupId(dto.getWorkgroupId());
        vo.setRequiresApproval(dto.getRequiresApproval());
        vo.setAutoAcceptFinalInvoice(dto.getAutoAcceptFinalInvoice());
        vo.setShowCostCenter(dto.getShowCostCenter());
        vo.setNoValidation(dto.getNoValidation());
        vo.setAllowChangeOrderCreation(dto.getAllowChangeOrderCreation());
        vo.setBuyRequireShipment(dto.getBuyRequireShipment());
        vo.setSellRequireShipment(dto.getSellRequireShipment());
        return vo;
    }
}