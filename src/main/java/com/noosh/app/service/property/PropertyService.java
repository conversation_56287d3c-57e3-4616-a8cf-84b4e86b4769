package com.noosh.app.service.property;

import com.noosh.app.commons.constant.DataTypeID;
import com.noosh.app.commons.constant.PropertyTypeID;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.commons.entity.property.PropertyParam;
import com.noosh.app.repository.jpa.property.PropertyAttributeRepository;
import com.noosh.app.repository.jpa.property.PropertyParamRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@Transactional
public class PropertyService {
    private final Logger log = LoggerFactory.getLogger(PropertyService.class);

    @Autowired
    private PropertyAttributeRepository propertyAttributeRepository;

    @Autowired
    private PropertyParamRepository propertyParamRepository;

    public void insertPropertyAttForPreference(Property property, String stringValue, String paramName, Long userId) throws Exception {
        PropertyParam propertyParam = propertyParamRepository.findByParamNameAndPrPropertyTypeId(paramName,
                PropertyTypeID.PROPERTY_TYPE_PREFERENCE);

        if (propertyParam == null) {
            //Insert user preference property Param
            PropertyParam newPropertyParam = new PropertyParam();
            newPropertyParam.setParamName(paramName);
            newPropertyParam.setPrPropertyTypeId(PropertyTypeID.PROPERTY_TYPE_PREFERENCE);
            newPropertyParam.setIsReportField((short) 0);
            newPropertyParam.setPrDataTypeId(DataTypeID.STRING);
            newPropertyParam.setCreateUserId(userId);
            propertyParam = propertyParamRepository.saveAndFlush(newPropertyParam);
        }

        PropertyAttribute newPro = new PropertyAttribute();
        newPro.setPrPropertyId(property.getPrPropertyId());
        newPro.setPrPropertyParamId(propertyParam.getPrPropertyParamId());
        newPro.setStringValue(stringValue);
        newPro.setCreateUserId(userId);
        propertyAttributeRepository.save(newPro);
    }


}

