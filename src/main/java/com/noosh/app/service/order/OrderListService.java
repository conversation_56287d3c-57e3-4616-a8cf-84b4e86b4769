package com.noosh.app.service.order;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.customfield.UserFieldDTO;
import com.noosh.app.commons.dto.order.*;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.quote.QuoteItemDTO;
import com.noosh.app.commons.dto.quote.QuoteItemPriceDTO;
import com.noosh.app.commons.dto.shipment.UofmDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.account.Address;
import com.noosh.app.commons.entity.benchmark.BenchmarkItem;
import com.noosh.app.commons.entity.job.PcJob;
import com.noosh.app.commons.entity.order.OrderItem;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.commons.entity.quote.QuoteItem;
import com.noosh.app.commons.entity.quote.QuotePrice;
import com.noosh.app.commons.entity.security.ClientWorkgroup;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.shipment.Carrier;
import com.noosh.app.commons.entity.shipment.Delivery;
import com.noosh.app.commons.entity.shipment.Method;
import com.noosh.app.commons.entity.shipment.Request;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.terms.AcTerms;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.order.*;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.exception.NoPreferenceException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.mapper.uofm.UofmMapper;
import com.noosh.app.repository.benchmarkItem.BenchmarkItemRepository;
import com.noosh.app.repository.jpa.account.AccountUserRepository;
import com.noosh.app.repository.jpa.order.OrderItemRepository;
import com.noosh.app.repository.jpa.property.PropertyRepository;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.jpa.spec.SpecRepository;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.repository.jpa.security.ClientWorkgroupRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.repository.quote.QuoteItemRepository;
import com.noosh.app.repository.quote.QuotePriceRepository;
import com.noosh.app.repository.shipment.AddressRepository;
import com.noosh.app.repository.terms.AcTermsRepository;
import com.noosh.app.repository.uofm.UofmRepository;
import com.noosh.app.service.AggregatedOrderService;
import com.noosh.app.service.OrderService;
import com.noosh.app.service.account.SupplierWorkgroupService;
import com.noosh.app.service.customfield.CustomFieldService;
import com.noosh.app.service.permission.ordering.CreateQuickOrderPermission;
import com.noosh.app.service.permission.ordering.EditChangeOrderPermission;
import com.noosh.app.service.permission.ordering.EditOrderPermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.rating.RatingService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.DateUtil;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @auther mario
 * @date 2/25/2020
 */
@Service
public class OrderListService {

    public static final String REACT_ORDER_LISTING_PAGESIZE = "REACT_ORDER_LISTING_PAGESIZE";
    public static final String REACT_ORDER_LISTING_COG_MENU = "REACT_ORDER_LISTING_COG_MENU";
    public static final String REACT_ORDER_LISTING_PRICE_FILTER = "REACT_ORDER_LISTING_PRICE_FILTER";

    public final static String ORDER_BUY = "buy";
    public final static String ORDER_SELL = "sell";

    public final static String ORDER = "Order";
    public final static String ORIGINAL_ORDER = "Original";
    public final static String CHANGE_ORDER = "Change";
    public final static String CLOSING_CHANGE_ORDER = "Closing Change";

    @Inject
    private AggregatedOrderService aggregatedOrderService;
    @Inject
    private ProjectService projectService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private SupplierWorkgroupService supplierWorkgroupService;
    @Inject
    private OrderMyBatisMapper orderMyBatisMapper;
    @Inject
    private AccountUserRepository accountUserRepository;
    @Inject
    private BenchmarkItemRepository benchmarkItemRepository;
    @Inject
    private OrderItemRepository orderItemRepository;
    @Inject
    private QuoteItemRepository quoteItemRepository;
    @Inject
    private QuotePriceRepository quotePriceRepository;
    @Inject
    private WorkgroupRepository workgroupRepository;
    @Inject
    private ClientWorkgroupRepository clientWorkgroupRepository;
    @Inject
    private I18NUtils i18NUtils;
    @Inject
    private ObjectStateRepository objectStateRepository;
    @Inject
    private SpecRepository specRepository;
    @Inject
    private PropertyMyBatisMapper propertyMyBatisMapper;
    @Inject
    private AddressRepository addressRepository;
    @Inject
    private UofmMapper uofmMapper;
    @Inject
    private UofmRepository uofmRepository;
    @Inject
    private OrderService orderService;
    @Inject
    private CustomFieldService customFieldService;
    @Inject
    private PropertyRepository propertyRepository;
    @Inject
    private RatingService ratingService;
    @Inject
    private CreateQuickOrderPermission createQuickOrderPermission;
    @Inject
    private PermissionService permissionService;
    @Inject
    private AcTermsRepository acTermsRepository;

    public OrderListFilterVO getRfeListFilter(long projectId, long userId, long workgroupId) {
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);

        OrderListFilterVO filterVO = new OrderListFilterVO();
        String pageSize = preferenceService.getString(REACT_ORDER_LISTING_PAGESIZE, prefs, "10");
        String cogMenu = preferenceService.getString(REACT_ORDER_LISTING_COG_MENU, prefs, "-1");
        if (cogMenu != null && (cogMenu.contains("[") || cogMenu.contains("]"))) {
            // remove "[" and "]"
            cogMenu = cogMenu.substring(1, cogMenu.length()-1);
        }
        filterVO.setPageSize(Integer.valueOf(pageSize));
        filterVO.setCogMenu(cogMenu);

        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        Workgroup workgroup = workgroupRepository.findById(workgroupId).orElse(null);
        filterVO.setProjectName(projectDTO.getTitle());
        filterVO.setProjectExternalLink(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(projectId));
        filterVO.setIsBrokerOutsourcerProject(projectDTO.isBrokerOutsourcerProject());
        filterVO.setIsSupplierProject(projectDTO.isSupplierProject());
        filterVO.setIsSupplierWorkgroup(workgroup.getWorkGroupTypeId() == WorkgroupTypeID.SUPPLIER);
        if (projectDTO.isBrokerOutsourcerProject()) {
            filterVO.setOrderType(ORDER_BUY);
        } else if(projectDTO.isSupplierProject()) {
            filterVO.setOrderType(ORDER_SELL);
        } else {
            filterVO.setOrderType(ORDER_BUY);
        }

        if(projectDTO.isBrokerOutsourcerProject()) {
            // Markup Summary Link
            filterVO.setMarkupSummaryExternalLink(NooshOneUrlUtil.composeListOrdersToEnterprise(projectId, (long) 2));
            // Detailed Markup Analysis Link
            filterVO.setMarkupAnalysisExternalLink(NooshOneUrlUtil.composeListOrdersToEnterprise(projectId, (long) 3));
        }

        return filterVO;
    }

    public void updatePageSize(Long workgroupId, Long userId, Integer pageSize) throws Exception {
        Map<String, String> prefs = new HashMap<>();
        prefs.put(REACT_ORDER_LISTING_PAGESIZE, String.valueOf(pageSize));
        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    public void updateCogMenu(Long workgroupId, Long userId, List<Long> cogFilter) throws Exception {
        String cogFilterStr = null;
        if(cogFilter != null) {
            cogFilterStr = String.valueOf(cogFilter);
            // remove "[" and "]"
            cogFilterStr = cogFilterStr.substring(1, cogFilterStr.length()-1);
        }
        Map<String, String> prefs = new HashMap<>();
        prefs.put(REACT_ORDER_LISTING_COG_MENU, cogFilterStr);
        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    public OrderListVO getOrderList(PageVO page, Long workgroupId, Long userId, Long projectId, String orderType, List<Long> cogFilter, String locale) throws Exception {
        updatePageSize(workgroupId, userId, page.getSize());
        updateCogMenu(workgroupId, userId, cogFilter);

        ProjectDTO projectDTO = projectService.findProjectById(projectId);

        boolean includePendingSubmissionCO = false;

        if (projectDTO.isClientOnNoosh() || projectDTO.isOutsourcerProject()) {
            // buy or sell
        } else if (projectDTO.isBuyerProject()) {
            orderType = ORDER_BUY;
        } else if(projectDTO.isSupplierProject()) {
            orderType = ORDER_SELL;
            includePendingSubmissionCO = true;
        }

        OrderListVO orderListVO = new OrderListVO();
        orderListVO.setOrderType(orderType);
        orderListVO.setIsBuyerProject(projectDTO.isBuyerProject());
        orderListVO.setIsSupplierProject(projectDTO.isSupplierProject());
        orderListVO.setIsBrokerOutsourcerProject(projectDTO.isBrokerOutsourcerProject());
        orderListVO.setIsClientProject(projectDTO.isClientProject());

        // find original orders
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<OrderDTO> originalOrderList = orderMyBatisMapper.getOriginalOrderList(projectId, orderType, projectDTO.isClientNotOnNoosh(), cogFilter);
        page.setTotal(pageInfo.getTotal());

        // canEditOrder and canEditChangeOrder are only for draft order and draft change order, so skip the order state check
        boolean canEditOrder = permissionService.checkAll(PermissionID.EDIT_ORDER, workgroupId, userId, projectId);
        boolean canEditChangeOrder = permissionService.checkAll(PermissionID.EDIT_CHANGE_ORDER, workgroupId, userId, projectId);

        Map<String, String> wgPrefs = preferenceService.findGroupPrefs(workgroupId,
                Arrays.asList(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY));
        orderListVO.setIsDualCurrency(preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, wgPrefs));
        orderListVO.setHideBaseCurrency(preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY, wgPrefs));

        if(!originalOrderList.isEmpty()) {
            // create a csv of all referenceIds
            List<Long> orderIdList = new ArrayList<>();
            for (int i = 0; i < originalOrderList.size(); i++) {
                orderIdList.add(originalOrderList.get(i).getOrderId());
            }

            List<OrderDTO> allChangeOrders = orderMyBatisMapper.getChangeOrderListByParent(projectId, orderIdList, includePendingSubmissionCO, cogFilter);

            Double projectTotalAwarded = 0d;
            Double projectTotalAwardedAndPending = 0d;

            List<OriginalOrderVO> originalOrderVOS = new ArrayList<>();


            for (OrderDTO originalOrder : originalOrderList) {
                OriginalOrderVO originalOrderVO = new OriginalOrderVO();
                boolean isDualCurrency = originalOrder.getRate() != null && originalOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrder.getExCurrencyId() != null;
                populateOriginalOrder(originalOrder, originalOrderVO, projectDTO, projectId, locale, canEditOrder, orderType);
                Double grandTotalValue = originalOrderVO.getAmount();
                Double exGrandTotalValue = 0d;
                if (isDualCurrency) {
                    exGrandTotalValue = originalOrderVO.getExAmount();
                }

                if (isProjectTotalAwardedAccepted(originalOrderVO.getStateId())) {
                    projectTotalAwarded += originalOrderVO.getAmount();
                }
                if (isProjectTotalAwardedAndPending(originalOrderVO.getStateId())) {
                    projectTotalAwardedAndPending += originalOrderVO.getAmount();
                }

                List<ChangeOrderVO> changeOrderVOS = new ArrayList<>();
                for(OrderDTO changeOrder : allChangeOrders) {
                    if (originalOrder.getOrderId() == changeOrder.getParentOrderId()) {
                        // Check draft change order, excluding draft from other side
                        boolean isIncludingDraft = true;
                        if (changeOrder.isDraft()) {
                            if (changeOrder.getStateCreateUserId() != null) {
                                AccountUser creator = accountUserRepository.findById(changeOrder.getStateCreateUserId()).orElse(null);
                                if (creator != null && creator.getWorkgroupId().longValue() != workgroupId) {
                                    isIncludingDraft = false;
                                }
                            }
                        }
                        if (isIncludingDraft) {
                            ChangeOrderVO changeOrderVO = populateChangeOrder(changeOrder, projectId, locale, canEditChangeOrder);
                            if (isNotGrey(originalOrder, changeOrder)) {
                                changeOrderVO.setIsNotGrey(true);
                                grandTotalValue += changeOrderVO.getChangePrice();
                                if (isDualCurrency) {
                                    exGrandTotalValue += changeOrderVO.getExChangePrice();
                                }
                            }
                            // Total excludes a discount or surcharge of, don't set it when is supplier project and is in pending submission status
                            if (changeOrder.getIsClosing() && !changeOrder.isRejected() && !(projectDTO.isSupplierProject() && changeOrder.isPendingSubmission())) {
                                originalOrderVO.setDiscountOrSurchargeTotal(changeOrderVO.getDiscountOrSurchargeTotal());
                                if (isDualCurrency) {
                                    originalOrderVO.setExDiscountOrSurchargeTotal(changeOrderVO.getExDiscountOrSurchargeTotal());
                                }
                            }
                            changeOrderVO.setChangePriceCurrencyId(originalOrderVO.getAmountCurrencyId());
                            changeOrderVO.setDiscountOrSurchargeTotalCurrencyId(originalOrderVO.getAmountCurrencyId());
                            changeOrderVOS.add(changeOrderVO);
                            if (!changeOrder.isDraft()) {
                                if (isProjectTotalAwardedAccepted(changeOrderVO.getStateId())) {
                                    projectTotalAwarded += changeOrderVO.getChangePrice();
                                }
                                if (isProjectTotalAwardedAndPending(changeOrderVO.getStateId())) {
                                    projectTotalAwardedAndPending += changeOrderVO.getChangePrice();
                                }
                            }
                        }
                    }
                }
                if(changeOrderVOS.isEmpty()) {
                    originalOrderVO.setOrderType(ORDER);
                }
                originalOrderVO.setGrandTotalValue(grandTotalValue);
                originalOrderVO.setGrandTotalValueCurrencyId(originalOrderVO.getAmountCurrencyId());
                if (isDualCurrency) {
                    originalOrderVO.setExGrandTotalValue(exGrandTotalValue);
                    originalOrderVO.setExGrandTotalValueCurrencyId(originalOrderVO.getExCurrencyId());
                }
                originalOrderVO.setChangeOrders(changeOrderVOS);
                if(showAggregated(originalOrderVO)) {
                    //Set order with changes link
                    originalOrderVO.setOrderWithChangesExternalLink(NooshOneUrlUtil.composeOrderWithChangesLinkToEnterprise(projectId, originalOrder.getOrderId()));
                }

                originalOrderVOS.add(originalOrderVO);
            }
            orderListVO.setOriginalOrders(originalOrderVOS);
            orderListVO.setProjectTotalAwarded(projectTotalAwarded);
            orderListVO.setProjectTotalAwardedAndPending(projectTotalAwardedAndPending);
        }

        // Create Quick Order Link
        if (createQuickOrderPermission.check(projectDTO, workgroupId, userId, projectId)) {
            boolean canCreateQuickOrder = true;
            if (projectDTO.isClientProject() && projectDTO.getMasterProjectId() != null) {
                // Find owner project
                ProjectDTO ownerProject = projectService.findProjectById(projectDTO.getMasterProjectId());
                if (ownerProject.isOutsourcerProject()) {
                    canCreateQuickOrder = false;
                }
            }
            if (canCreateQuickOrder) {
                orderListVO.setCreateQuickOrderExternalLink(NooshOneUrlUtil.composeCreateQuickOrderToEnterprise(projectId));
            }
        }

        return orderListVO;
    }

    private void populateOriginalOrder(OrderDTO originalOrder,
                                  OriginalOrderVO originalOrderVO,
                                  ProjectDTO projectDTO,
                                  Long projectId,
                                  String locale, boolean canEditOrder, String orderType) {

        originalOrderVO.setOrderId(originalOrder.getOrderId());
        originalOrderVO.setReference(originalOrder.getReference());
        originalOrderVO.setOrderName(originalOrder.getTitle() != null ? originalOrder.getTitle() : originalOrder.getReference());
        originalOrderVO.setOrderType(ORIGINAL_ORDER);
        originalOrderVO.setIsInvoiceAdjustmentOrder(originalOrder.getOrderClassificationId() != null && originalOrder.getOrderClassificationId() == OrderClassificationID.INVOICE_ADJUSTMENT);
        originalOrderVO.setPaymentReference(originalOrder.getPaymentReference());
        originalOrderVO.setIsCompleted(originalOrder.isCompleted());
        originalOrderVO.setStateId(originalOrder.getStateId());
        originalOrderVO.setState(i18NUtils.getObjectStateMessage(originalOrder.getStateId(), locale));
        if (originalOrder.getStateId() != null) {
            ObjectState objectState = objectStateRepository.findById(originalOrder.getStateId()).orElse(null);
            originalOrderVO.setStateStrId(objectState != null ? objectState.getDescriptionStrId().toString() : null);
        }

        originalOrderVO.setCompletionDate(originalOrder.getCompletionDate());
        originalOrderVO.setLastChangeDate(originalOrder.getLastModDate());
        if (originalOrder.getSupplierWorkgroupId() != null) {
            Workgroup supplierWorkgroup = workgroupRepository.findById(originalOrder.getSupplierWorkgroupId()).get();
            originalOrderVO.setSupplierWorkgroupName(supplierWorkgroup.getName());
            originalOrderVO.setSupplierFlag(supplierWorkgroupService.getSupplierFlagVO(originalOrder.isDraft() || originalOrder.isPendingSubmission(),
                    originalOrder.getParentOrderId(), originalOrder.getOrderTypeId() ==OrderTypeID.CHANGE_ORDER ? ObjectClassID.CHANGE_ORDER : ObjectClassID.ORDER,
                    projectDTO.getOwnerWorkgroupId(), supplierWorkgroup.getId()));
            originalOrderVO.setSupplierScore(ratingService.findSupplierScore(projectDTO.getOwnerWorkgroupId(), supplierWorkgroup.getId()));
        }

        Workgroup buyerWorkgroup = workgroupRepository.findById(originalOrder.getBuyerWorkgroupId()).get();
        //NO-898, if there has offline client, then use offline client name
        if (projectDTO.isClientNotOnNoosh() && originalOrder.getSupplierWorkgroupId() != null
                && originalOrder.getSupplierWorkgroupId().longValue() == originalOrder.getBuyerWorkgroupId()
                && originalOrder.getBuClientId() != null && originalOrder.getBuClientId().longValue() > 0) {
            ClientWorkgroup clientWorkgroup = clientWorkgroupRepository.findById(originalOrder.getBuClientId()).orElse(null);
            if (clientWorkgroup != null) {
                originalOrderVO.setBuyerWorkgroupId(clientWorkgroup.getId());
                originalOrderVO.setBuyerWorkgroupName(clientWorkgroup.getName());
            } else {
                originalOrderVO.setBuyerWorkgroupName(projectDTO.getClientAccount());
            }
        } else {
            originalOrderVO.setBuyerWorkgroupId(buyerWorkgroup.getId());
            originalOrderVO.setBuyerWorkgroupName(buyerWorkgroup.getName());
        }
        List<OrderItem> orderItems = orderItemRepository.findByOrderVersionId(originalOrder.getOrderVersionId());

        Double total = orderItems.stream().filter(item -> item.getValue() != null)
                .mapToDouble(orderItem -> orderItem.getValue().doubleValue())
                .sum() + originalOrder.getTax() + originalOrder.getShipping();
        originalOrderVO.setAmount(total);
        if (orderItems != null && orderItems.size() > 0) {
            originalOrderVO.setAmountCurrencyId(orderItems.get(0).getValueCurrencyId());
        }

        if (originalOrder.getRate() != null && originalOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrder.getExCurrencyId() != null) {
            originalOrderVO.setExCurrencyId(originalOrder.getExCurrencyId());
            Double exTax = originalOrder.getExTax() != null ? originalOrder.getExTax() : 0d;
            Double exShipping = originalOrder.getExShipping() != null ? originalOrder.getExShipping() : 0d;
            Double exTotal = orderItems.stream().filter(item -> item.getExValue() != null)
                    .mapToDouble(orderItem -> orderItem.getExValue().doubleValue())
                    .sum() + exTax + exShipping;
            originalOrderVO.setExAmount(exTotal);
            originalOrderVO.setExAmountCurrencyId(originalOrder.getExCurrencyId());
        }

        HashMap<String, String> params = new HashMap<>();
        params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
        params.put("objectId", "" + projectId);
        params.put("orderId", "" + originalOrder.getOrderId());
        if (originalOrder.isDraft() && canEditOrder) {
            originalOrderVO.setOriginalOrderExternalLink(NooshOneUrlUtil.composeEditDraftOrderLinkToEnterprise(projectId, originalOrder.getOrderId(),
                    originalOrder.getVersionNumber(), orderType));
        } else {
            originalOrderVO.setOriginalOrderExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_ORDER, params));
        }

        // Set print link
        params.put("renderSpecs", "true");
        originalOrderVO.setPrintExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_ORDER, params));

        // Set rate supplier link
        Map<String, PropertyAttribute> propertyAttributeMap = preferenceService.getOwnerAttributesByDefaultValueForWorkgroup(originalOrder.getBuyerWorkgroupId(), "1");
        Long triggerStateId = Long.parseLong(preferenceService.getValueForWorkgroup("SR_RATING_TRIGGER", originalOrder.getBuyerWorkgroupId()));
        if (propertyAttributeMap.containsKey(PreferenceID.WORKGROUP_OPTION_SUPPLIER_RATING)
                && propertyAttributeMap.containsKey("SR_ACTIVE")
                && ((triggerStateId != (long) -1
                && (originalOrder.isAccepted() || originalOrder.getStateId().longValue() == ObjectStateID.ORDER_CANCELLED))
                || (triggerStateId == (long) -1))
                && !projectDTO.isSupplierProject()) {
            originalOrderVO.setSupplierRatingExternalLink(NooshOneUrlUtil.composeSupplierRatingLinkToEnterprise(projectId, originalOrder.getOrderId()));
        }

        // handle order custom fields
        if (originalOrder.getCustomPropertyId() != null) {
            List<PropertyAttributeDTO> estimateItemCustomFields = propertyMyBatisMapper.findPropertyAttributes(originalOrder.getCustomPropertyId());
            List<PropertyAttributeVO> attributeVOs = new ArrayList<PropertyAttributeVO>();
            if (estimateItemCustomFields != null && estimateItemCustomFields.size() > 0) {
                for (PropertyAttributeDTO propertyAttributeDTO : estimateItemCustomFields) {
                    PropertyAttributeVO propertyAttributeVO = new PropertyAttributeVO(propertyAttributeDTO.getPrPropertyAttributeId(),
                            propertyAttributeDTO.getValue(), propertyAttributeDTO.getParamName());
                    attributeVOs.add(propertyAttributeVO);
                }
                originalOrderVO.setCustomFields(attributeVOs);
            }
        }

        originalOrderVO.setSourcingStrategyExternalLink(NooshOneUrlUtil.composeEditSourcingLinkToEnterprise(projectId, originalOrder.getOrderId()));

        // Handle total saving amount
        List<BenchmarkItem> benchmarkItems = benchmarkItemRepository.findByOrderId(originalOrder.getOrderId());
        if (benchmarkItems != null) {
            double totalSaving = benchmarkItems.stream().filter(b -> b.getSavingAmt() != null)
                    .mapToDouble(b -> b.getSavingAmt().doubleValue()).sum();
            originalOrderVO.setTotalSaving(totalSaving);
        }

    }

    // populate Change Order
    private ChangeOrderVO populateChangeOrder(OrderDTO changeOrder, Long projectId, String locale, boolean canEditChangeOrder) {
        ChangeOrderVO changeOrderVO = new ChangeOrderVO();
        changeOrderVO.setOrderId(changeOrder.getOrderId());
        changeOrderVO.setReference(changeOrder.getReference());
        changeOrderVO.setOrderName(changeOrder.getTitle() != null
                ? changeOrder.getTitle() : changeOrder.getReference());
        changeOrderVO.setCompletionDate(changeOrder.getCompletionDate());
        changeOrderVO.setLastChangeDate(changeOrder.getLastModDate());
        changeOrderVO.setCustomPropertyId(changeOrder.getCustomPropertyId());

        HashMap<String, String> params = new HashMap<>();
        params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
        params.put("objectId", "" + projectId);
        params.put("orderId", "" + changeOrder.getOrderId());

        // order type
        if(changeOrder.getOrderTypeId() == OrderTypeID.CHANGE_ORDER) {
            changeOrderVO.setOrderType(changeOrder.getIsClosing() ? CLOSING_CHANGE_ORDER : CHANGE_ORDER);
            params.put("changeOrderId", "" + changeOrder.getOrderId());
            if (changeOrder.isDraft() && canEditChangeOrder) {
                changeOrderVO.setChangeOrderExternalLink(NooshOneUrlUtil.composeEditDraftChangeOrderLinkToEnterprise(projectId,
                        changeOrder.getParentOrderId(), changeOrder.getVersionNumber(), changeOrder.getOrderId()));
            } else {
                changeOrderVO.setChangeOrderExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_CHANGE_ORDER, params));
            }
        }

        changeOrderVO.setStateId(changeOrder.getStateId());
        changeOrderVO.setState(i18NUtils.getObjectStateMessage(changeOrder.getStateId(), locale));
        if (changeOrder.getStateId() != null) {
            ObjectState objectState = objectStateRepository.findById(changeOrder.getStateId()).orElse(null);
            changeOrderVO.setStateStrId(objectState != null ? objectState.getDescriptionStrId().toString() : null);
        }

        List<OrderItem> orderItems = orderItemRepository.findByOrderVersionId(changeOrder.getOrderVersionId());
        Double total = 0d;
        Double discountOrSurchargeTotal = changeOrder.getDiscountOrSurcharge() != null ? changeOrder.getDiscountOrSurcharge() : 0d;
        for( OrderItem item : orderItems) {
            if (item.getValue() != null) {
                total += item.getValue().doubleValue();
            }
            if (item.getDors() != null) {
                discountOrSurchargeTotal += item.getDors().doubleValue();
            }
        }
        total = total + changeOrder.getTax() + changeOrder.getShipping();
        changeOrderVO.setChangePrice(total);
        changeOrderVO.setDiscountOrSurchargeTotal(discountOrSurchargeTotal);

        if (changeOrder.getRate() != null && changeOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && changeOrder.getExCurrencyId() != null) {
            Double exTotal = 0d;
            Double exDiscountOrSurchargeTotal = changeOrder.getExDiscountOrSurcharge() != null ? changeOrder.getExDiscountOrSurcharge() : 0d;
            for( OrderItem item : orderItems) {
                if (item.getExValue() != null) {
                    exTotal += item.getExValue().doubleValue();
                }
                if (item.getExDors() != null) {
                    exDiscountOrSurchargeTotal += item.getExDors().doubleValue();
                }
            }
            Double exTax = changeOrder.getExTax() != null ? changeOrder.getExTax() : 0d;
            Double exShipping = changeOrder.getExShipping() != null ? changeOrder.getExShipping() : 0d;
            exTotal = exTotal + exTax + exShipping;
            changeOrderVO.setExChangePrice(exTotal);
            changeOrderVO.setExChangePriceCurrencyId(changeOrder.getExCurrencyId());
            changeOrderVO.setExDiscountOrSurchargeTotal(exDiscountOrSurchargeTotal);
            changeOrderVO.setExDiscountOrSurchargeTotalCurrencyId(changeOrder.getExCurrencyId());
        }

        if (changeOrderVO.getCustomPropertyId() != null) {
            List<PropertyAttributeDTO> estimateItemCustomFields = propertyMyBatisMapper.findPropertyAttributes(changeOrderVO.getCustomPropertyId());
            List<PropertyAttributeVO> attributeVOs = new ArrayList<PropertyAttributeVO>();
            if (estimateItemCustomFields != null && estimateItemCustomFields.size() > 0) {
                for (PropertyAttributeDTO propertyAttributeDTO : estimateItemCustomFields) {
                    PropertyAttributeVO propertyAttributeVO = new PropertyAttributeVO(propertyAttributeDTO.getPrPropertyAttributeId(),
                            propertyAttributeDTO.getValue(), propertyAttributeDTO.getParamName());
                    attributeVOs.add(propertyAttributeVO);
                }
                changeOrderVO.setCustomFields(attributeVOs);
            }
        }

        return changeOrderVO;
    }

    public boolean showAggregated(OriginalOrderVO originalOrderVO) {
        List<ChangeOrderVO> changeOrders = originalOrderVO.getChangeOrders();
        if (changeOrders == null || changeOrders.size()==0) return false;

        // check if accepted
        if (isAccepted(originalOrderVO.getStateId())) {
            for (ChangeOrderVO changeOrderVO : changeOrders) {
                if (isAccepted(changeOrderVO.getStateId()))
                    return true;
            }
        }

        if (originalOrderVO.getStateId()==ObjectStateID.ORDER_CANCELLED) {
            for (ChangeOrderVO changeOrderVO : changeOrders) {
                if (changeOrderVO.getStateId() == ObjectStateID.ORDER_CANCELLED)
                    return true;
            }
        }
        return false;
    }

    /**
     * Control whether the front change order line is grayed out
     * @param originalOrder
     * @param changeOrder
     * @return
     */
    public boolean isNotGrey(OrderDTO originalOrder, OrderDTO changeOrder) {
        if (changeOrder == null) {
            return false;
        }
        // check if accepted
        if (isAccepted(originalOrder.getStateId()) && isAccepted(changeOrder.getStateId())) {
            return true;
        }

        if (originalOrder.getStateId()==ObjectStateID.ORDER_CANCELLED && changeOrder.getStateId() == ObjectStateID.ORDER_CANCELLED) {
            return true;
        }
        return false;
    }

    public boolean isAccepted(Long stateId) {
        if (stateId == null) return false;
        return (stateId == ObjectStateID.ORDER_ACCEPTED
                || stateId == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                || stateId == ObjectStateID.ORDER_SHIPPED
                || stateId == ObjectStateID.ORDER_DELIVERED
                || stateId == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED
                || stateId == ObjectStateID.ORDER_COMPLETED
                || stateId == ObjectStateID.ORDER_FINALIZED);
    }

    public boolean isPending(Long id, Long stateId) {
        if (isNew(id))
            return false;
        return (isPendingBuyerAcceptance(stateId) || isPendingSupplierAcceptance(stateId));
    }

    public boolean isProjectTotalAwardedAccepted(Long stateId) {
        if (stateId == null) return false;
        return (stateId == ObjectStateID.ORDER_SHIPPED
                || stateId == ObjectStateID.ORDER_ACCEPTED
                || stateId == ObjectStateID.ORDER_DELIVERED
                || stateId == ObjectStateID.ORDER_COMPLETED
                || stateId == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                || stateId == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED);
    }

    public boolean isProjectTotalAwardedAndPending(Long stateId) {
        if (stateId == null) return false;
        return (stateId == ObjectStateID.ORDER_SHIPPED
                || stateId == ObjectStateID.ORDER_ACCEPTED
                || stateId == ObjectStateID.ORDER_OUTSOURCER_TO_ACCEPT
                || stateId == ObjectStateID.ORDER_CLIENT_TO_ACCEPT
                || stateId == ObjectStateID.ORDER_SUPPLIER_TO_ACCEPT
                || stateId == ObjectStateID.ORDER_BUYER_TO_ACCEPT
                || stateId == ObjectStateID.ORDER_DELIVERED
                || stateId == ObjectStateID.ORDER_COMPLETED
                || stateId == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                || stateId == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED);
    }

    public boolean isNew(Long id) {
        return id == null || id < 0;
    }

    public boolean isPendingBuyerAcceptance(Long stateId) {
        if (stateId == null) return false;
        return (stateId == ObjectStateID.ORDER_BUYER_TO_ACCEPT || stateId == ObjectStateID.ORDER_CLIENT_TO_ACCEPT);
    }

    public boolean isPendingSupplierAcceptance(Long stateId) {
        if (stateId == null) return false;
        return (stateId == ObjectStateID.ORDER_SUPPLIER_TO_ACCEPT || stateId == ObjectStateID.ORDER_OUTSOURCER_TO_ACCEPT);
    }

    public boolean isUserBuyer(Long buyerWorkgroupId, Long ownerWorkgroupId) {
        return buyerWorkgroupId.longValue() == ownerWorkgroupId.longValue();
    }

    public boolean hasOfflineBuyer(boolean isClientNotOnNoosh, Long buyerWorkgroupId, Long supplierWorkgroupId, Long clientId) {
        if (isClientNotOnNoosh
                && buyerWorkgroupId == supplierWorkgroupId.longValue()
                && clientId != null && clientId > 0)
            return true;

        return false;
    }

    @Transactional(readOnly = true)
    public Map getMarkupSummary(Long projectId, List<Long> priceFilter) {
        Map markupSummaryDetailMap = new HashMap();
        Map<String, Double> markupSummaryMap = new HashMap<String, Double>();
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        List<OrderDTO> originalOrderList = orderMyBatisMapper.getOriginalOrderList(projectId, "all",
                projectDTO.isClientNotOnNoosh(), Arrays.asList((long)-1));
        boolean enabledMargin = "1".equalsIgnoreCase(preferenceService.getValueForWorkgroup(PreferenceID.WORKGROUP_OPTION_ENABLE_MARGIN, projectDTO.getOwnerWorkgroupId()));

        if (originalOrderList != null) {
            for (OrderDTO orderDTO : originalOrderList) {
                String orderKey = "order";
                if (isAccepted(orderDTO.getStateId())) {
                    orderKey += "Accepted";
                } else if (isPending(orderDTO.getOrderVersionId(), orderDTO.getStateId())) {
                    orderKey += "Pending";
                } else {
                    continue;
                }

                orderKey += (isUserBuyer(orderDTO.getBuyerWorkgroupId(), projectDTO.getOwnerWorkgroupId())
                        && !hasOfflineBuyer(projectDTO.isClientNotOnNoosh(), orderDTO.getBuyerWorkgroupId(),
                        orderDTO.getSupplierWorkgroupId(), orderDTO.getBuClientId())) ? "Buying" : "Selling";

                boolean isIncludingTax = false, isIncludingShipping = false, isIncludingItem = false;
                if (priceFilter != null && !priceFilter.isEmpty()) {
                    if (priceFilter.contains((long)-1)) {
                        isIncludingTax = true;
                        isIncludingItem = true;
                        isIncludingShipping = true;
                    } else {
                        if (priceFilter.contains((long) 0)) {
                            isIncludingItem = true;
                        }
                        if (priceFilter.contains((long) 1)) {
                            isIncludingTax = true;
                        }
                        if (priceFilter.contains((long) 2)) {
                            isIncludingShipping = true;
                        }
                    }
                }

                Double itemTotal = orderItemRepository.findByOrderVersionId(orderDTO.getOrderVersionId())
                        .stream()
                        .filter(item -> item.getValue() != null)
                        .mapToDouble(orderItem -> orderItem.getValue().doubleValue())
                        .sum();
                Double total = (isIncludingItem ? itemTotal : 0.0) + (isIncludingTax ? orderDTO.getTax() : 0.0 ) + (isIncludingShipping ? orderDTO.getShipping() : 0.0);

                markupSummaryMap.put(orderKey, markupSummaryMap.get(orderKey) != null ? markupSummaryMap.get(orderKey).doubleValue() + total : total);

                List<OrderDTO> allChangeOrders = orderMyBatisMapper.getChangeOrderListByParent(projectId, Arrays.asList(orderDTO.getOrderId()), false, Arrays.asList((long)-1));
                if (allChangeOrders != null) {
                    for (OrderDTO changeOrder : allChangeOrders) {
                        String changeKey = "changeOrder";
                        if (isAccepted(changeOrder.getStateId())) {
                            changeKey += "Accepted";
                        } else if (isPending(changeOrder.getOrderVersionId(), changeOrder.getStateId())) {
                            changeKey += "Pending";
                        } else {
                            continue;
                        }
                        changeKey += (isUserBuyer(orderDTO.getBuyerWorkgroupId(), projectDTO.getOwnerWorkgroupId())
                                && !hasOfflineBuyer(projectDTO.isClientNotOnNoosh(), orderDTO.getBuyerWorkgroupId(),
                                orderDTO.getSupplierWorkgroupId(), orderDTO.getBuClientId())) ? "Buying" : "Selling";

                        Double changeItemTotal = orderItemRepository.findByOrderVersionId(changeOrder.getOrderVersionId())
                                .stream()
                                .filter(item -> item.getValue() != null)
                                .mapToDouble(orderItem -> orderItem.getValue().doubleValue())
                                .sum();
                        Double changeTotal = (isIncludingItem ? changeItemTotal : 0.0) + (isIncludingTax ? changeOrder.getTax() : 0.0 ) + (isIncludingShipping ? changeOrder.getShipping() : 0.0);

                        markupSummaryMap.put(changeKey, markupSummaryMap.get(changeKey) != null ?
                                markupSummaryMap.get(changeKey).doubleValue() + changeTotal : changeTotal);
                    }
                }
            }
            setData("order", markupSummaryMap, markupSummaryDetailMap, enabledMargin);
            setData("changeOrder", markupSummaryMap, markupSummaryDetailMap, enabledMargin);

            addMoney("orderAcceptedSelling", "changeOrderAcceptedSelling", "totalAcceptedSelling", markupSummaryMap);
            addMoney("orderPendingSelling", "changeOrderPendingSelling", "totalPendingSelling", markupSummaryMap);
            addMoney("totalAcceptedSelling", "totalPendingSelling", "totalSelling", markupSummaryMap);

            addMoney("orderAcceptedBuying", "changeOrderAcceptedBuying", "totalAcceptedBuying", markupSummaryMap);
            addMoney("orderPendingBuying", "changeOrderPendingBuying", "totalPendingBuying", markupSummaryMap);
            addMoney("totalAcceptedBuying", "totalPendingBuying", "totalBuying", markupSummaryMap);

            subtractMoney("totalAcceptedSelling", "totalAcceptedBuying", "totalAcceptedMarkup", markupSummaryMap);
            subtractMoney("totalPendingSelling", "totalPendingBuying", "totalPendingMarkup", markupSummaryMap);
            subtractMoney("totalSelling", "totalBuying", "totalMarkup", markupSummaryMap);
            if (enabledMargin) {
                percentMoneyEnabledMargin("totalAcceptedMarkup", "totalAcceptedSelling", "totalAcceptedPercent", markupSummaryMap);
                percentMoneyEnabledMargin("totalPendingMarkup", "totalPendingSelling", "totalPendingPercent", markupSummaryMap);
                percentMoneyEnabledMargin("totalMarkup", "totalSelling", "totalPercent", markupSummaryMap);
            } else {
                percentMoney("totalAcceptedMarkup", "totalAcceptedBuying", "totalAcceptedPercent", markupSummaryMap);
                percentMoney("totalPendingMarkup", "totalPendingBuying", "totalPendingPercent", markupSummaryMap);
                percentMoney("totalMarkup", "totalBuying", "totalPercent", markupSummaryMap);
            }
            MarkupSummaryVO totalSelling = new MarkupSummaryVO();
            totalSelling.setAccepted(markupSummaryMap.get("orderAcceptedSelling") == null
                    && markupSummaryMap.get("changeOrderAcceptedSelling") == null
                        ? null : markupSummaryMap.get("totalAcceptedSelling"));
            totalSelling.setPending(markupSummaryMap.get("orderPendingSelling") == null
                    && markupSummaryMap.get("changeOrderPendingSelling") == null
                        ? null : markupSummaryMap.get("totalPendingSelling"));
            totalSelling.setTotal(markupSummaryMap.get("totalSelling"));
            markupSummaryDetailMap.put("totalSelling", totalSelling);

            MarkupSummaryVO totalBuying = new MarkupSummaryVO();
            totalBuying.setAccepted(markupSummaryMap.get("orderAcceptedBuying") == null
                    && markupSummaryMap.get("changeOrderAcceptedBuying") == null
                        ? null : markupSummaryMap.get("totalAcceptedBuying"));
            totalBuying.setPending(markupSummaryMap.get("orderPendingBuying") == null
                    && markupSummaryMap.get("changeOrderPendingBuying") == null
                    ? null : markupSummaryMap.get("totalPendingBuying"));
            totalBuying.setTotal(markupSummaryMap.get("totalBuying"));
            markupSummaryDetailMap.put("totalBuying", totalBuying);

            MarkupSummaryVO totalMarkup = new MarkupSummaryVO();
            totalMarkup.setAccepted(markupSummaryMap.get("totalAcceptedMarkup"));
            totalMarkup.setPending(markupSummaryMap.get("totalPendingMarkup"));
            totalMarkup.setTotal(markupSummaryMap.get("totalMarkup"));
            totalMarkup.setAcceptedPercent(markupSummaryMap.get("totalAcceptedPercent"));
            totalMarkup.setPendingPercent(markupSummaryMap.get("totalPendingPercent"));
            totalMarkup.setTotalPercent(markupSummaryMap.get("totalPercent"));
            markupSummaryDetailMap.put("totalMarkup", totalMarkup);
        }
        return markupSummaryDetailMap;
    }

    private Map<String, Double> addMoney(String key1, String key2, String key3, Map<String, Double> markupSummaryMap) {
        double money1 = markupSummaryMap.get(key1) != null ? markupSummaryMap.get(key1).doubleValue() : 0;
        double money2 = markupSummaryMap.get(key2) != null ? markupSummaryMap.get(key2).doubleValue() : 0;
        markupSummaryMap.put(key3, money1 + money2);
        return markupSummaryMap;
    }

    private Map<String, Double> subtractMoney(String key1, String key2, String key3, Map<String, Double> markupSummaryMap) {
        double money1 = markupSummaryMap.get(key1) != null ? markupSummaryMap.get(key1).doubleValue() : 0;
        double money2 = markupSummaryMap.get(key2) != null ? markupSummaryMap.get(key2).doubleValue() : 0;
        markupSummaryMap.put(key3, money1 - money2);
        return markupSummaryMap;
    }

    private Map<String, Double> percentMoney(String key1, String key2, String key3, Map<String, Double> markupSummaryMap) {
        BigDecimal money1 = markupSummaryMap.get(key1) != null ? BigDecimal.valueOf(markupSummaryMap.get(key1).doubleValue()) : null;
        BigDecimal money2 = markupSummaryMap.get(key2) != null ? BigDecimal.valueOf(markupSummaryMap.get(key2).doubleValue()) : null;
        markupSummaryMap.put(key3, money1 == null || money2 == null ? null : (money1.compareTo(BigDecimal.ZERO) != 0 && money2.compareTo(BigDecimal.ZERO) != 0
                ? money1.divide(money2, 5, BigDecimal.ROUND_HALF_UP).doubleValue() : null));
        return markupSummaryMap;
    }

    private Map<String, Double> percentMoneyEnabledMargin(String key1, String key2, String key3, Map<String, Double> markupSummaryMap) {
        BigDecimal money1 = markupSummaryMap.get(key1) != null ? BigDecimal.valueOf(markupSummaryMap.get(key1).doubleValue()) : null;
        BigDecimal money2 = markupSummaryMap.get(key2) != null ? BigDecimal.valueOf(markupSummaryMap.get(key2).doubleValue()) : null;
        markupSummaryMap.put(key3, money1 == null || money2 == null ? null : (money1.compareTo(BigDecimal.ZERO) != 0 && money2.compareTo(BigDecimal.ZERO) != 0
                ? money1.divide(money2, 5, BigDecimal.ROUND_HALF_UP).doubleValue() : null));
        return markupSummaryMap;
    }

    private void setData(String key, Map<String, Double> markupSummaryMap, Map<String, MarkupSummaryVO> markupSummaryDetailMap, boolean enabledMargin) {
        addMoney(key + "AcceptedSelling", key + "PendingSelling", key + "TotalSelling", markupSummaryMap);
        addMoney(key + "AcceptedBuying", key + "PendingBuying", key + "TotalBuying", markupSummaryMap);
        subtractMoney(key + "AcceptedSelling", key + "AcceptedBuying", key + "AcceptedMarkup", markupSummaryMap);
        subtractMoney(key + "PendingSelling", key + "PendingBuying", key + "PendingMarkup", markupSummaryMap);
        addMoney(key + "AcceptedMarkup", key + "PendingMarkup", key + "TotalMarkup", markupSummaryMap);
        if (enabledMargin) {
            percentMoneyEnabledMargin(key + "AcceptedMarkup", key + "AcceptedSelling", key + "AcceptedPercent", markupSummaryMap);
            percentMoneyEnabledMargin(key + "PendingMarkup", key + "PendingSelling", key + "PendingPercent", markupSummaryMap);
            percentMoneyEnabledMargin(key + "TotalMarkup", key + "TotalSelling", key + "TotalPercent", markupSummaryMap);
        } else {
            percentMoney(key + "AcceptedMarkup", key + "AcceptedBuying", key + "AcceptedPercent", markupSummaryMap);
            percentMoney(key + "PendingMarkup", key + "PendingBuying", key + "PendingPercent", markupSummaryMap);
            percentMoney(key + "TotalMarkup", key + "TotalBuying", key + "TotalPercent", markupSummaryMap);
        }

        MarkupSummaryVO orderSelling = new MarkupSummaryVO();
        orderSelling.setAccepted(markupSummaryMap.get(key + "AcceptedSelling"));
        orderSelling.setPending(markupSummaryMap.get(key + "PendingSelling"));
        orderSelling.setTotal(markupSummaryMap.get(key + "TotalSelling"));
        markupSummaryDetailMap.put(key + "Selling", orderSelling);

        MarkupSummaryVO orderBuying = new MarkupSummaryVO();
        orderBuying.setAccepted(markupSummaryMap.get(key + "AcceptedBuying"));
        orderBuying.setPending(markupSummaryMap.get(key + "PendingBuying"));
        orderBuying.setTotal(markupSummaryMap.get(key + "TotalBuying"));
        markupSummaryDetailMap.put(key + "Buying", orderBuying);

        MarkupSummaryVO orderMarkup = new MarkupSummaryVO();
        orderMarkup.setAccepted(markupSummaryMap.get(key + "AcceptedMarkup"));
        orderMarkup.setPending(markupSummaryMap.get(key + "PendingMarkup"));
        orderMarkup.setTotal(markupSummaryMap.get(key + "TotalMarkup"));
        orderMarkup.setAcceptedPercent(markupSummaryMap.get(key + "AcceptedPercent"));
        orderMarkup.setPendingPercent(markupSummaryMap.get(key + "PendingPercent"));
        orderMarkup.setTotalPercent(markupSummaryMap.get(key + "TotalPercent"));
        markupSummaryDetailMap.put(key + "Markup", orderMarkup);
    }

    @Transactional(readOnly = true)
    public OrderDetailAnalysisDTO getDetailMarkupAnalysis(boolean defaultToQuotedCost, boolean filterIncludePending,
                                        boolean filterIncludeArchived, Long workgroupId, Long userId, Long projectId) {
        OrderDetailAnalysisDTO orderDetailAnalysisDTO = new OrderDetailAnalysisDTO();
        // find all orders
        // sort into jobs, sellMap (job->sellOrderList), buyMap (job->buyOrderList),
        // taking only accepted/pending orders
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        List<OrderDTO> originalOrderList = orderMyBatisMapper.getOriginalOrderList(projectId, "all",
                projectDTO.isClientNotOnNoosh(), Arrays.asList((long) -1));
        Set<PcJob> jobSet = new HashSet();
        Map<Long, BigDecimal> sellMap = new HashMap<Long, BigDecimal>();
        Map<Long, BigDecimal> sellTaxMap = new HashMap<Long, BigDecimal>();
        Map<Long, BigDecimal> sellShippingMap = new HashMap<Long, BigDecimal>();
        Map<Long, BigDecimal> buyMap = new HashMap<Long, BigDecimal>();
        Map<Long, BigDecimal> buyTaxMap = new HashMap<Long, BigDecimal>();
        Map<Long, BigDecimal> buyShippingMap = new HashMap<Long, BigDecimal>();
        Map<Long, Integer> sellPendingMap = new HashMap<Long, Integer>();
        Map<Long, Integer> buyPendingMap = new HashMap<Long, Integer>();
        List<Long> jobIds = new ArrayList<Long>();
        jobIds.add((long) -1);
        int totalDefaultQuotedCostCount = 0;
        BigDecimal buyTotal = BigDecimal.ZERO;
        BigDecimal sellTotal = BigDecimal.ZERO;
        BigDecimal profitTotal = BigDecimal.ZERO;
        BigDecimal markupTotal = BigDecimal.ZERO;
        BigDecimal marginTotal = BigDecimal.ZERO;
        Integer totalPendingAmountCount = 0;

        Map<String, String> workgroupPrefs = preferenceService.findGroupPrefs(projectDTO.getOwnerWorkgroupId());
        boolean buyItemizedTaxAndShipping = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, workgroupPrefs);
        boolean sellItemizedTaxAndShipping = false;
        if (projectDTO.getClientWorkgroupId() != null) {
            workgroupPrefs = preferenceService.findGroupPrefs(projectDTO.getClientWorkgroupId());
            sellItemizedTaxAndShipping = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, workgroupPrefs);
        }

        if (originalOrderList != null) {
            for (OrderDTO orderDTO : originalOrderList) {
                Integer pendingAmountCount = 0;
                // skip if not accepted or pending
                if (!(isAccepted(orderDTO.getStateId()) || isPending(orderDTO.getOrderVersionId(), orderDTO.getStateId()))) {
                    continue;
                }
                // skip if order is pending and includePending is FALSE
                if (!filterIncludePending && isPending(orderDTO.getOrderVersionId(), orderDTO.getStateId())) {
                    continue;
                } else if (filterIncludePending && isPending(orderDTO.getOrderVersionId(), orderDTO.getStateId())) {
                    pendingAmountCount ++;
                }

                boolean isBuyOrder = isUserBuyer(orderDTO.getBuyerWorkgroupId(), projectDTO.getOwnerWorkgroupId())
                        && !hasOfflineBuyer(projectDTO.isClientNotOnNoosh(), orderDTO.getBuyerWorkgroupId(),
                        orderDTO.getSupplierWorkgroupId(), orderDTO.getBuClientId());

                List<OrderItem> orderItems = orderItemRepository.findByOrderVersionId(orderDTO.getOrderVersionId());
                BigDecimal totalPrice = BigDecimal.ZERO;

                OrderVersionDTO aggregatedOrderDetailDTO = aggregatedOrderService.getSimpleAggregatedOrder(
                        orderDTO.getOrderId(), null);

                List<OrderDTO> allChangeOrders = null;

                if (filterIncludePending) {
                    allChangeOrders = orderMyBatisMapper.getChangeOrderListByParent(projectId,
                            Arrays.asList(orderDTO.getOrderId()), false, Arrays.asList((long)-1));
                }

                for (OrderItem orderItem : orderItems) {
                    totalPrice = totalPrice.add(orderItem.getValue());
                    PcJob job = orderItem.getPcJob();
                    // filter to include only jobs that matches the jobStation filter option
                    if ( !filterIncludeArchived && job.getOcObjectStateId() == ObjectStateID.JOB_SPEC_USER_STATE_ARCHIVED) {
                        continue;
                    }

                    jobSet.add(job);
                    jobIds.add(job.getPcJobId());
                    Map resultMap = getTotalAmount(job.getPcJobId(), filterIncludePending, aggregatedOrderDetailDTO,
                            allChangeOrders, pendingAmountCount, (buyItemizedTaxAndShipping || sellItemizedTaxAndShipping));
                    BigDecimal amount = (BigDecimal)resultMap.get("totalAmount");
                    BigDecimal taxAmount = (BigDecimal)resultMap.get("totalTaxAmount");
                    BigDecimal shippingAmount = (BigDecimal)resultMap.get("totalShippingAmount");
                    pendingAmountCount = (Integer) resultMap.get("pendingAmountCount");
                    if (isBuyOrder) {
//                        List<OrderDTO> oList = buyMap.get(job.getPcJobId());
//                        if (oList == null)
//                            oList = new ArrayList<OrderDTO>();
//                        oList.add(orderDTO);
//                        buyMap.put(job.getPcJobId(), oList);

                        BigDecimal total = buyMap.get(job.getPcJobId());
                        BigDecimal tax = buyTaxMap.get(job.getPcJobId());
                        BigDecimal shipping = buyShippingMap.get(job.getPcJobId());
                        Integer pendingTotal = buyPendingMap.get(job.getPcJobId());
                        if (total == null) {
                            buyMap.put(job.getPcJobId(), amount);
                        } else {
                            buyMap.put(job.getPcJobId(), amount.add(total));
                        }
                        if (pendingTotal == null) {
                            buyPendingMap.put(job.getPcJobId(), pendingAmountCount);
                        } else {
                            buyPendingMap.put(job.getPcJobId(), pendingTotal + pendingAmountCount);
                        }
                        if (tax == null) {
                            buyTaxMap.put(job.getPcJobId(), taxAmount);
                        } else {
                            buyTaxMap.put(job.getPcJobId(), taxAmount.add(tax));
                        }
                        if (shipping == null) {
                            buyShippingMap.put(job.getPcJobId(), shippingAmount);
                        } else {
                            buyShippingMap.put(job.getPcJobId(), shippingAmount.add(shipping));
                        }
                    } else {
//                        List<OrderDTO> oList = sellMap.get(job.getPcJobId());
//                        if (oList == null)
//                            oList = new ArrayList<OrderDTO>();
//                        oList.add(orderDTO);
//                        sellMap.put(job.getPcJobId(), oList);
                        BigDecimal total = sellMap.get(job.getPcJobId());
                        BigDecimal tax = sellMap.get(job.getPcJobId());
                        BigDecimal shipping = sellMap.get(job.getPcJobId());
                        Integer pendingTotal = sellPendingMap.get(job.getPcJobId());
                        if (total == null) {
                            sellMap.put(job.getPcJobId(), amount);
                        } else {
                            sellMap.put(job.getPcJobId(), amount.add(total));
                        }
                        if (pendingTotal == null) {
                            sellPendingMap.put(job.getPcJobId(), pendingAmountCount);
                        } else {
                            sellPendingMap.put(job.getPcJobId(), pendingTotal + pendingAmountCount);
                        }
                        if (tax == null) {
                            sellTaxMap.put(job.getPcJobId(), taxAmount);
                        } else {
                            sellTaxMap.put(job.getPcJobId(), taxAmount.add(tax));
                        }
                        if (shipping == null) {
                            sellShippingMap.put(job.getPcJobId(), shippingAmount);
                        } else {
                            sellShippingMap.put(job.getPcJobId(), shippingAmount.add(shipping));
                        }
                    }
                    totalPendingAmountCount += pendingAmountCount;
                }
            }
            List<PcJob> jobs = jobSet.stream().sorted(Comparator.comparing(j -> ((PcJob)j ).getSpecReference().getSpecName()).thenComparing(j -> ((PcJob)j ).getPcJobId())).collect(Collectors.toList());
            // find all quotes for the given jobs
            // sort into job->quoteItem map
            List<Long> quoteStateIds = new ArrayList<Long>();
            quoteStateIds.add(ObjectStateID.QUOTE_ACCEPTED);
            quoteStateIds.add(ObjectStateID.QUOTE_CLOSED);
            List<QuoteItem> quoteItems = quoteItemRepository.findByJobIdsAndStateIds(jobIds, quoteStateIds);
            List<OrderDetailMarkupDTO> orderDetailMarkupDTOs = new ArrayList<OrderDetailMarkupDTO>();
            if (jobs != null && jobs.size() > 0) {
                for (PcJob job : jobs) {
                    int defaultQuotedCostCount = 0;
                    OrderDetailMarkupDTO orderDetailMarkupDTO = new OrderDetailMarkupDTO();

                    orderDetailMarkupDTO.setSpecName(job.getSpecReference().getSpecName());
                    orderDetailMarkupDTO.setJobState(i18NUtils.getObjectStateMessage(job.getOcObjectStateId()));
                    if (job.getOcObjectStateId() != null) {
                        ObjectState objectState = objectStateRepository.findById(job.getOcObjectStateId()).orElse(null);
                        orderDetailMarkupDTO.setJobStateStrId(objectState == null ? null : objectState.getDescriptionStrId().toString());
                    }

                    orderDetailMarkupDTO.setIconString(job.getSpecReference().getSpecType().getIcon());
                    if (quoteItems != null) {
                        for (QuoteItem quoteItem : quoteItems) {
                            if (quoteItem.getJobId() != null && quoteItem.getJobId().longValue() == job.getPcJobId()) {
                                if (buyMap.get(job.getPcJobId()) == null && defaultToQuotedCost) {
                                    QuotePrice quotePrice = getAggregatedQuotePrice(quoteItem.getChosenQuotePrice());

                                    if (quotePrice != null) {
                                        buyMap.put(job.getPcJobId(), quotePrice.getPreMarkup());
                                    }
                                    defaultQuotedCostCount ++;
                                    totalDefaultQuotedCostCount += defaultQuotedCostCount;
                                }
                                QuoteItemDTO quoteItemDTO = new QuoteItemDTO();
                                quoteItemDTO.setId(quoteItem.getId());
                                QuoteItemPriceDTO quoteItemPriceDTO = new QuoteItemPriceDTO();
                                quoteItemPriceDTO.setMarkupFixed(quoteItem.getChosenQuotePrice().getMarkupFixed());
                                quoteItemPriceDTO.setMarkupPercent(quoteItem.getChosenQuotePrice().getMarkupPercent());
                                quoteItemDTO.setChosenQuotePrice(quoteItemPriceDTO);
                                orderDetailMarkupDTO.setQuoteItem(quoteItemDTO);
                                orderDetailMarkupDTO.setDefaultQuotedCostCount(defaultQuotedCostCount);
                            }
                        }
                    }

                    BigDecimal sellAmount = sellMap.containsKey(job.getPcJobId()) ? sellMap.get(job.getPcJobId()) : null;
                    BigDecimal buyAmount = buyMap.containsKey(job.getPcJobId()) ? buyMap.get(job.getPcJobId()) : null;
                    BigDecimal buyTaxAmount = buyTaxMap.containsKey(job.getPcJobId()) ? buyTaxMap.get(job.getPcJobId()) : null;
                    BigDecimal sellTaxAmount = sellTaxMap.containsKey(job.getPcJobId()) ? sellTaxMap.get(job.getPcJobId()) : null;
                    BigDecimal buyShippingAmount = buyShippingMap.containsKey(job.getPcJobId()) ? buyShippingMap.get(job.getPcJobId()) : null;
                    BigDecimal sellShippingAmount = sellShippingMap.containsKey(job.getPcJobId()) ? sellShippingMap.get(job.getPcJobId()) : null;
                    BigDecimal profitAmount = sellAmount != null && buyAmount != null ? sellAmount.subtract(buyAmount)
                            : (buyAmount != null && sellAmount == null ? (BigDecimal.ZERO).subtract(buyAmount)
                                : (buyAmount == null && sellAmount != null ? sellAmount.subtract(BigDecimal.ZERO) : BigDecimal.ZERO));
                    BigDecimal marginAmount = sellAmount != null && sellAmount.compareTo(BigDecimal.ZERO) != 0 ? profitAmount.divide(sellAmount, 5, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                    BigDecimal markupAmount = buyAmount != null && buyAmount.compareTo(BigDecimal.ZERO) != 0 ? profitAmount.divide(buyAmount, 5, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                    if (buyAmount != null && buyAmount.compareTo(BigDecimal.ZERO) != 0) {
                        orderDetailMarkupDTO.setEffectiveMarkup(markupAmount);
                    }

                    if (sellAmount != null && sellAmount.compareTo(BigDecimal.ZERO) != 0) {
                        orderDetailMarkupDTO.setMarginAmount(marginAmount);
                    }

                    orderDetailMarkupDTO.setBuyAmount(buyAmount);
                    orderDetailMarkupDTO.setSellAmount(sellAmount);
                    orderDetailMarkupDTO.setBuyTaxAmount(buyTaxAmount);
                    orderDetailMarkupDTO.setBuyShippingAmount(buyShippingAmount);
                    orderDetailMarkupDTO.setSellTaxAmount(sellTaxAmount);
                    orderDetailMarkupDTO.setSellShippingAmount(sellShippingAmount);
                    orderDetailMarkupDTO.setProfitAmount(profitAmount);
                    orderDetailMarkupDTO.setJobId(job.getPcJobId());
                    orderDetailMarkupDTO.setPendingBuyAmountCount(buyPendingMap.containsKey(job.getPcJobId()) ? buyPendingMap.get(job.getPcJobId()) : 0);
                    orderDetailMarkupDTO.setPendingSellAmountCount(sellPendingMap.containsKey(job.getPcJobId()) ? sellPendingMap.get(job.getPcJobId()) : 0);

                    buyTotal = buyAmount != null ? buyTotal.add(buyAmount) : buyTotal.add(BigDecimal.ZERO);
                    sellTotal = sellAmount != null ? sellTotal.add(sellAmount) : sellTotal.add(BigDecimal.ZERO);
                    profitTotal = profitTotal.add(profitAmount);


                    orderDetailMarkupDTOs.add(orderDetailMarkupDTO);
                }
            }
            orderDetailAnalysisDTO.setDetails(orderDetailMarkupDTOs);
        }
        orderDetailAnalysisDTO.setTotalDefaultQuotedCostCount(totalDefaultQuotedCostCount);
        orderDetailAnalysisDTO.setTotalPendingAmountCount(totalPendingAmountCount);
        orderDetailAnalysisDTO.setBuyTotal(buyTotal);
        orderDetailAnalysisDTO.setSellTotal(sellTotal);
        orderDetailAnalysisDTO.setProfitTotal(profitTotal);
        markupTotal = buyTotal.compareTo(BigDecimal.ZERO) != 0 ? profitTotal.divide(buyTotal, 5, BigDecimal.ROUND_HALF_UP) : null;
        marginTotal = sellTotal.compareTo(BigDecimal.ZERO) != 0 ? profitTotal.divide(sellTotal, 5, BigDecimal.ROUND_HALF_UP) : null;
        orderDetailAnalysisDTO.setMarkupTotal(markupTotal);
        orderDetailAnalysisDTO.setMarginTotal(marginTotal);

        return orderDetailAnalysisDTO;

    }

    public Map getTotalAmount(Long jobId, boolean includePendingChangeOrders, OrderVersionDTO aggregatedOrderDetailDTO,
                                 List<OrderDTO> allChangeOrders, Integer pendingAmountCount, boolean itemizedTaxAndShippingEnabled) {
        Map resultMap = new HashMap();
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalTaxAmount = BigDecimal.ZERO;
        BigDecimal totalShippingAmount = BigDecimal.ZERO;
        OrderItemDTO aggreggatedOrderItem = aggregatedOrderDetailDTO.getOrderItemDTOs().stream()
                .filter(o -> o.getJobId().longValue() == jobId).findFirst().orElse(null);
        totalAmount = totalAmount.add(aggreggatedOrderItem.getValue());
        if (itemizedTaxAndShippingEnabled) {
            totalTaxAmount = totalTaxAmount.add(aggreggatedOrderItem.getTax() != null ? aggreggatedOrderItem.getTax() : BigDecimal.ZERO);
            totalShippingAmount = totalShippingAmount.add(aggreggatedOrderItem.getShipping() != null ? aggreggatedOrderItem.getShipping() : BigDecimal.ZERO);
        }

        // if include pending, go through the pending
        // add the pending amount to the total amount
        if (includePendingChangeOrders) {
//                    List<OrderDTO> allChangeOrders = orderMyBatisMapper.getChangeOrderListByParent(projectId, Arrays.asList(order.getOrderId()), Arrays.asList((long)-1));
            if (allChangeOrders != null && allChangeOrders.size() > 0) {
                for (OrderDTO changeOrder : allChangeOrders) {
                    if (isPending(changeOrder.getOrderVersionId(), changeOrder.getStateId())) {
                        OrderItem orderItem = orderItemRepository.findByJobIdAndOrderVersionId(jobId, changeOrder.getOrderVersionId());
                        if (orderItem != null) {
                            totalAmount = totalAmount.add(orderItem.getValue());
                            pendingAmountCount = pendingAmountCount + 1;
                            if (itemizedTaxAndShippingEnabled) {
                                totalTaxAmount = totalTaxAmount.add(orderItem.getTax() != null ? orderItem.getTax() : BigDecimal.ZERO);
                                totalShippingAmount = totalShippingAmount.add(orderItem.getShipping() != null ? orderItem.getShipping() : BigDecimal.ZERO);
                            }
                        }
                    }
                }
            }
        } // if include pending

        resultMap.put("totalAmount", totalAmount);
        resultMap.put("totalTaxAmount", totalTaxAmount);
        resultMap.put("totalShippingAmount", totalShippingAmount);
        resultMap.put("pendingAmountCount", pendingAmountCount);

        return resultMap;
    }

    @Transactional(readOnly = true)
    public QuotePrice getAggregatedQuotePrice(QuotePrice quotePrice) {
        QuotePrice thisQuotePrice = quotePrice != null
                && quotePrice.getParentQuotePriceId() != null ?
        quotePriceRepository.findById(quotePrice.getParentQuotePriceId()).orElse(null): quotePrice;

        if (thisQuotePrice == null) return thisQuotePrice;

        List<QuotePrice> includedChildren = quotePriceRepository.findByRootQuotePriceIdAndIsVisibleToBuyer(thisQuotePrice.getId(), true);

        int childrenCount = includedChildren != null ? includedChildren.size() : 0;
        if  (childrenCount == 0)
            return thisQuotePrice;

        // all the children are already in asc order
        // aggregrated from the first child to the last
        // sum all pricings, latest quantity override previous
        QuotePrice aggrBean = new QuotePrice();

        for (int cInd=0; cInd < childrenCount; cInd++) {
            QuotePrice child = includedChildren.get(cInd);
            // add child's prices to the aggr
            if (child == null) continue;
            BigDecimal preMarkup = child.getPreMarkup() != null ? child.getPreMarkup().add(aggrBean.getPreMarkup() == null ? BigDecimal.ZERO : aggrBean.getPreMarkup()) : BigDecimal.ZERO;
            BigDecimal exPreMarkup = child.getExPreMarkup() != null ? child.getExPreMarkup().add(aggrBean.getExPreMarkup() == null ? BigDecimal.ZERO : aggrBean.getExPreMarkup()) : BigDecimal.ZERO;
            BigDecimal markupFixed = child.getMarkupFixed() != null ? child.getMarkupFixed().add(aggrBean.getMarkupFixed() == null ? BigDecimal.ZERO : aggrBean.getMarkupFixed()) : BigDecimal.ZERO;
            BigDecimal exMarkupFixed = child.getExMarkupFixed() != null ? child.getExMarkupFixed().add(aggrBean.getExMarkupFixed() == null ? BigDecimal.ZERO : aggrBean.getExMarkupFixed()) : BigDecimal.ZERO;
            BigDecimal price = child.getPrice() != null ? child.getPrice().add(aggrBean.getPrice() == null ? BigDecimal.ZERO : aggrBean.getPrice()) : BigDecimal.ZERO;
            BigDecimal exPrice = child.getExPrice() != null ? child.getExPrice().add(aggrBean.getExPrice() == null ? BigDecimal.ZERO : aggrBean.getExPrice()) : BigDecimal.ZERO;
            double markupPercent = 0;
            double profit = price.subtract(preMarkup).subtract(markupFixed).doubleValue();
            // compute only if divident and divisor are not zero
            if (Math.round(preMarkup.doubleValue()) != 0 && Math.round(profit) != 0) {
                markupPercent = price.subtract(preMarkup).subtract(markupFixed).divide(preMarkup, 5, BigDecimal.ROUND_HALF_UP).doubleValue() * 100;
            }
            BigDecimal tax = child.getTax() != null ? child.getTax().add(aggrBean.getTax() == null ? BigDecimal.ZERO : aggrBean.getTax()) : BigDecimal.ZERO;
            BigDecimal exTax = child.getExTax() != null ? child.getExTax().add(aggrBean.getExTax() == null ? BigDecimal.ZERO : aggrBean.getExTax()) : BigDecimal.ZERO;
            BigDecimal shipping = child.getShipping() != null ? child.getShipping().add(aggrBean.getShipping() == null ? BigDecimal.ZERO : aggrBean.getShipping()) : BigDecimal.ZERO;
            BigDecimal exShipping = child.getExShipping() != null ? child.getExShipping().add(aggrBean.getExShipping() == null ? BigDecimal.ZERO : aggrBean.getExShipping()) : BigDecimal.ZERO;
            // set all attributes - including custom attr
            aggrBean.setCustomPropertyId(child.getCustomPropertyId());
            // set the aggr prices
            aggrBean.setPreMarkup(preMarkup);
            aggrBean.setPrice(price);
            aggrBean.setMarkupFixed(markupFixed);
            aggrBean.setMarkupPercent(markupPercent);
            aggrBean.setTax(tax);
            aggrBean.setShipping(shipping);
            aggrBean.setExPreMarkup(exPreMarkup);
            aggrBean.setExPrice(exPrice);
            aggrBean.setExMarkupFixed(exMarkupFixed);
            aggrBean.setExTax(exTax);
            aggrBean.setExShipping(exShipping);
        }
        return aggrBean;
    }

    public OrderMarkupAnalysisFilterVO getFilter(long workgroupId, long userId, long projectId) {
        if (!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, workgroupId)) {
            throw new NoPreferenceException("Your workgroup is not turned on Preference!");
        }
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);
        OrderMarkupAnalysisFilter filter = new OrderMarkupAnalysisFilter();

        // includePending
        String includePendingPref = preferenceService.getString(
                OrderMarkupAnalysisFilter.ORDER_MARKUP_ANALYSIS_FILTER_PREF_PREFIX
                        + OrderMarkupAnalysisFilter.CONTROLNAME_INCLUDING_PENDING, prefs);
        if (includePendingPref != null && includePendingPref.trim().length() > 0) {
            filter.setIncludePending(Integer.valueOf(includePendingPref));
        }

        String includeArchivedPref = preferenceService.getString(
                OrderMarkupAnalysisFilter.ORDER_MARKUP_ANALYSIS_FILTER_PREF_PREFIX
                        + OrderMarkupAnalysisFilter.CONTROLNAME_INCLUDING_ARCHIVED_JOBS, prefs);
        if (includeArchivedPref != null && includeArchivedPref.length() > 0) {
            filter.setIncludeArchived(Integer.valueOf(includeArchivedPref));
        }

        String defaultToQuotedCostPref = preferenceService.getString(
                OrderMarkupAnalysisFilter.ORDER_MARKUP_ANALYSIS_FILTER_PREF_PREFIX
                        + OrderMarkupAnalysisFilter.CONTROLNAME_DEFAULT_TO_QUOTED_COST, prefs);
        if (defaultToQuotedCostPref != null && defaultToQuotedCostPref.length() > 0) {
            filter.setDefaultToQuotedCost(Integer.valueOf(defaultToQuotedCostPref));
        }

        OrderMarkupAnalysisFilterVO vo = new OrderMarkupAnalysisFilterVO();
        vo.setFilter(filter);
        String actionSellChangeOrdersLink = NooshOneUrlUtil.composeMarkupAnalysisGoButtonLinkToEnterprise(projectId, 1);
        String actionBuyChangeOrdersLink = NooshOneUrlUtil.composeMarkupAnalysisGoButtonLinkToEnterprise(projectId, 2);
        vo.setActionSellChangeOrdersLink(actionSellChangeOrdersLink);
        vo.setActionBuyChangeOrdersLink(actionBuyChangeOrdersLink);
        vo.setBuyOrderListLink(NooshOneUrlUtil.composeListOrdersToEnterprise(projectId, (long) 0));
        vo.setSellOrderListLink(NooshOneUrlUtil.composeListOrdersToEnterprise(projectId, (long) 1));

        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        Map<String, String> workgroupPrefs = preferenceService.findGroupPrefs(projectDTO.getOwnerWorkgroupId());
        boolean buyItemizedTaxAndShipping = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, workgroupPrefs);
        vo.setBuyItemizedTaxAndShipping(buyItemizedTaxAndShipping);


        if (projectDTO.getClientWorkgroupId() != null) {
            workgroupPrefs = preferenceService.findGroupPrefs(projectDTO.getClientWorkgroupId());
            boolean sellItemizedTaxAndShipping = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, workgroupPrefs);
            vo.setSellItemizedTaxAndShipping(sellItemizedTaxAndShipping);
        }

        return vo;
    }

    public void updateFilter(Long workgroupId, Long userId, OrderMarkupAnalysisFilter filter) throws Exception {
        Map<String, String> prefs = new HashMap<>();
        // save preference
        prefs.put(OrderMarkupAnalysisFilter.ORDER_MARKUP_ANALYSIS_FILTER_PREF_PREFIX
                        + OrderMarkupAnalysisFilter.CONTROLNAME_INCLUDING_PENDING,
                (filter.getIncludePending() == null) ? ""
                        : Integer.toString(filter.getIncludePending()));
        prefs.put(OrderMarkupAnalysisFilter.ORDER_MARKUP_ANALYSIS_FILTER_PREF_PREFIX
                        + OrderMarkupAnalysisFilter.CONTROLNAME_INCLUDING_ARCHIVED_JOBS,
                (filter.getIncludeArchived() == null) ? ""
                        : Integer.toString(filter.getIncludeArchived()));
        prefs.put(OrderMarkupAnalysisFilter.ORDER_MARKUP_ANALYSIS_FILTER_PREF_PREFIX
                        + OrderMarkupAnalysisFilter.CONTROLNAME_DEFAULT_TO_QUOTED_COST,
                filter.getDefaultToQuotedCost() == null ? "" : Integer.toString(filter.getDefaultToQuotedCost()));

        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    public HSSFWorkbook exportOrderDetail(Long projectId, Long orderId, Long currentUserId, Long currentWorkgroupId, String locale) {

        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        AggregatedOrderVersionDTO aggregatedOrderDTO = aggregatedOrderService.findAggregatedOrderDTO(orderId, projectId, currentWorkgroupId, currentUserId);
        // get currency
        Workgroup workgroup = workgroupRepository.findById(currentWorkgroupId).orElse(null);
        OrderVersionDTO originalOrder = aggregatedOrderDTO.getOriginalOrder();
        Workgroup buyerWorkgroup = workgroupRepository.findById(originalOrder.getBuyerWorkgroupId()).orElse(null);
        boolean isPubworx = "pubworx".equals(buyerWorkgroup.getPortal());
        String currencySymbol = Money.getCurrencySymbol(workgroup.getCurrency().getCurrency());
        int decimalPlaces = workgroup.getDecimalPlaces() != null ? workgroup.getDecimalPlaces().intValue() : 2;

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet();
        sheet.autoSizeColumn(1);

        AccountUser user = accountUserRepository.findById(currentUserId).orElse(null);
        String timeZoneCode = user.getPerson().getTimeZone().getTimezoneCode();

        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(projectDTO.getPortalWorkgroupId());

        addDataToExcel(projectDTO, aggregatedOrderDTO, workbook, sheet, currencySymbol, decimalPlaces, groupPrefs, isPubworx, timeZoneCode, locale);

        return workbook;
    }

    private void addDataToExcel(ProjectDTO projectDTO, AggregatedOrderVersionDTO aggregatedOrderDTO, HSSFWorkbook workbook,
                                HSSFSheet sheet, String currencySymbol, int decimalPlaces, Map<String, String> groupPrefs,
                                boolean isPubworx, String timeZoneCode, String locale) {

        int i = 0;
        HSSFFont font = workbook.createFont();
        font.setBold(true);
        HSSFCellStyle cellStyle1 = workbook.createCellStyle();
        cellStyle1.setFont(font);
        cellStyle1.setAlignment(HorizontalAlignment.LEFT);

        HSSFCellStyle cellStyle2 = workbook.createCellStyle();
        cellStyle2.setAlignment(HorizontalAlignment.LEFT);

        HSSFRow row;
        HSSFCell cell;

        // Project Name
        row = sheet.createRow(i);
        getCellValue(row, cellStyle1, "Project Name", 0);
        getCellValue(row, cellStyle2, projectDTO.getName(), 1);
        // Project Number
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Project Number", 0);
        getCellValue(row, cellStyle2, projectDTO.getProjectNumber(), 1);
        // Project ID
        Long projectId;
        if (projectDTO.getMasterProjectId() != null) {
            projectId = projectDTO.getMasterProjectId();
        } else {
            projectId = projectDTO.getId();
        }
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Project ID", 0);
        getCellValue(row, cellStyle2, projectId, 1);
        i = i + 2;

        OrderVersionDTO orderVersionDTO = aggregatedOrderDTO.getOriginalOrder();
        // Order Name
        row = sheet.createRow(i);
        getCellValue(row, cellStyle1, "Order Name", 0);
        getCellValue(row, cellStyle2, orderVersionDTO.getTitle(), 1);
        // Supplier Tracking
        getCellValue(row, cellStyle1, "Supplier Tracking", 2);
        getCellValue(row, cellStyle2, orderVersionDTO.getSupplierReference(), 3);

        // Order ID
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Order ID", 0);
        getCellValue(row, cellStyle2, orderVersionDTO.getOrderId(), 1);
        // Payment Method
        getCellValue(row, cellStyle1, "Payment Method", 2);
        getCellValue(row, cellStyle2, i18NUtils.getMessage(orderVersionDTO.getPaymentMethod().getDescriptionStrId(), locale), 3);

        // Payment Ref. #
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Payment Ref. #", 0);
        getCellValue(row, cellStyle2, orderVersionDTO.getPaymentReference(), 1);
        // Supplier Selection Reason
        getCellValue(row, cellStyle1, "Supplier Selection Reason", 2);
        if (orderVersionDTO.isUserBuyer() && !projectDTO.isClientProject()) {
            cell = row.createCell(3);
            if (orderVersionDTO.getReason() != null) {
                if (orderVersionDTO.getReason().isOther()) {
                    cell.setCellValue(orderVersionDTO.getReasonOther());
                } else {
                    if (orderVersionDTO.getReason().getNameStrId() != null && orderVersionDTO.getReason().getNameStrId() > 0) {
                        cell.setCellValue(i18NUtils.getMessage(orderVersionDTO.getReason().getNameStrId(), locale));
                    } else if (orderVersionDTO.getReason().getNameStr() != null) {
                        cell.setCellValue(orderVersionDTO.getReason().getNameStr());
                    }
                }
            }
        }

        // Status
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Status", 0);
        getCellValue(row, cellStyle2, i18NUtils.getObjectStateMessage(orderVersionDTO.getOrderState().getObjectStateId(), locale), 1);
        // Allowed Overs
        boolean closeOrderNegotiation = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION, groupPrefs);
        getCellValue(row, cellStyle1, "Allowed Overs", 2);
        if (closeOrderNegotiation) {
            getCellValue(row, cellStyle2, orderVersionDTO.getOversPercent() != null ? orderVersionDTO.getOversPercent() + "%" : "", 3);
        }
        // Order Creation Date
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Order Creation Date", 0);
        getCellValue(row, cellStyle2, getNonNullDate(orderVersionDTO.getCreateDate(), timeZoneCode, locale, "MM/dd/yyyy hh:mm a"), 1);
        // Allowed Unders
        getCellValue(row, cellStyle1, "Allowed Unders", 2);
        if (closeOrderNegotiation) {
            getCellValue(row, cellStyle2, orderVersionDTO.getUndersPercent() != null ? orderVersionDTO.getUndersPercent() + "%" : "", 3);
        }
        // Order Completion Date
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Order Completion Date", 0);
        getCellValue(row, cellStyle2, getNonNullDate(orderVersionDTO.getCompletionDate(), timeZoneCode, locale, "MM/dd/yyyy hh:mm a"), 1);

        List<PropertyAttributeDTO> propertyAttributes = new ArrayList<>();
        if (orderVersionDTO.getCustomPropertyId() != null) {
            propertyAttributes = propertyMyBatisMapper.findBillToByPropertyId(orderVersionDTO.getCustomPropertyId());
        }
        // Bill To
        if (isPubworx) {
            getCellValue(row, cellStyle1, "Bill To", 2);
            getCellValue(row, cellStyle2, getStringValue(propertyAttributes, "O_BILL_TO_str"), 3);
        }
        // Buyer Workgroup
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Buyer Workgroup", 0);
        getCellValue(row, cellStyle2, orderVersionDTO.getBuyerWorkgroup() != null ? orderVersionDTO.getBuyerWorkgroup().getName() : "", 1);

        // Bill To - line1
        if (isPubworx) {
            getCellValue(row, cellStyle2, getStringValue(propertyAttributes, "O_BILL_TO_LINE1_str"), 3);
        }
        // Buyer Person
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Buyer Person", 0);
        getCellValue(row, cellStyle2, orderVersionDTO.getBuyer() != null ? orderVersionDTO.getBuyer().getFullName() : "", 1);

        // Bill To - line2
        if (isPubworx) {
            getCellValue(row, cellStyle2, getStringValue(propertyAttributes, "O_BILL_TO_LINE2_str"), 3);
        }
        // Supplier Workgroup
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Supplier Workgroup", 0);
        getCellValue(row, cellStyle2, orderVersionDTO.getSupplierWorkgroup() != null ? orderVersionDTO.getSupplierWorkgroup().getName() : "", 1);
        // Bill To - line 3
        if (isPubworx) {
            getCellValue(row, cellStyle2, getStringValue(propertyAttributes, "O_BILL_TO_LINE3_str"), 3);
        }

        // Supplier Person
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Supplier Person", 0);
        getCellValue(row, cellStyle2, orderVersionDTO.getSupplier() != null ? orderVersionDTO.getSupplier().getFullName() : "", 1);
        // Bill To - city, state,postal
        if (isPubworx) {
            getCellValue(row, cellStyle2, getStringValue(propertyAttributes, "O_BILL_TO_CITY_str") + ", " +
                    getStringValue(propertyAttributes, "O_BILL_TO_STATE_str") + " " +
                    getStringValue(propertyAttributes, "O_BILL_TO_POSTAL_str"), 3);
        }

        // Order Grand Total
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Order Grand Total", 0);
        getCellValue(row, cellStyle2, getNonNullString(aggregatedOrderDTO.getAggregatedOrder().getGrandTotal(), currencySymbol), 1);
        // Bill To - country
        if (isPubworx) {
            getCellValue(row, cellStyle2, getStringValue(propertyAttributes, "O_BILL_TO_COUNTRY_str"), 3);
        }

        // Comments
        row = sheet.createRow(++i);
        getCellValue(row, cellStyle1, "Comments", 0);
        getCellValue(row, cellStyle2, orderVersionDTO.getComments(), 1);

        if (isPubworx) {
            row = sheet.createRow(++i);
            getCellValue(row, cellStyle1, "Terms & Conditions", 0);
            getCellValue(row, cellStyle2, "", 1);

            AcTerms buyerTerms = orderVersionDTO.getBuyerTermsId() != null ?
                    acTermsRepository.findById(orderVersionDTO.getBuyerTermsId()).orElse(null) : null;
            row = sheet.createRow(++i);
            getCellValue(row, cellStyle1, "Purchase Terms and Conditions", 0);
            getCellValue(row, cellStyle2, buyerTerms != null ? buyerTerms.getText() : "", 1);

            AcTerms supplierTerms = orderVersionDTO.getSupplierWorkgroupId() != null
                    && orderVersionDTO.getSupplierTermsId() != null
                    ? acTermsRepository.findById(orderVersionDTO.getSupplierTermsId()).orElse(null) : null;
            row = sheet.createRow(++i);
            getCellValue(row, cellStyle1, "Sales Terms and Conditions", 0);
            getCellValue(row, cellStyle2, supplierTerms != null ? supplierTerms.getText() : "", 1);
        }

        // user custom field
        if (orderVersionDTO.getCustomPropertyId() != null) {
            Map<String, String> userCustomFieldMap = getUserCustomField(orderVersionDTO, projectId, CustomFieldClassID.ORDER, orderVersionDTO.getCustomPropertyId());
            for (Map.Entry<String, String> entry : userCustomFieldMap.entrySet()) {
                row = sheet.createRow(++i);
                getCellValue(row, cellStyle1, entry.getKey(), 0);
                getCellValue(row, cellStyle2, entry.getValue(), 1);
            }
        }

        HSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // Spec info
        i = i + 2;
        int j = 0;
        row = sheet.createRow(i);
        getCellValue(row, cellStyle, "Spec Name", j);
        getCellValue(row, cellStyle, "Spec ID", ++j);
        getCellValue(row, cellStyle, "Spec Type", ++j);
        getCellValue(row, cellStyle, "Job ID", ++j);
        getCellValue(row, cellStyle, "Completion Date", ++j);
        getCellValue(row, cellStyle, "Quantity", ++j);
        getCellValue(row, cellStyle, "Price", ++j);
        List<OrderItemDTO> orderItemDTOList = aggregatedOrderDTO.getAggregatedOrder().getOrderItemDTOs();
        // order item custom field
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            if (orderItemDTO.getCustomPropertyId() != null) {
                Map<String, String> userCustomFieldMap = getUserCustomField(orderVersionDTO, projectId, CustomFieldClassID.ORDER_ITEM, orderItemDTO.getCustomPropertyId());
                for (Map.Entry<String, String> entry : userCustomFieldMap.entrySet()) {
                    getCellValue(row, cellStyle, entry.getKey(), ++j);
                }
                break;
            }
        }
        getCellValue(row, cellStyle, "Item Price", ++j);

        // shipment
        cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        getCellValue(row, cellStyle, "Shipment Recipient", ++j);
        getCellValue(row, cellStyle, "Location", ++j);
        getCellValue(row, cellStyle, "Request Type", ++j);
        getCellValue(row, cellStyle, "Qty Requested", ++j);
        getCellValue(row, cellStyle, "Date Due", ++j);
        getCellValue(row, cellStyle, "Qty Shipped", ++j);
        getCellValue(row, cellStyle, "Date Shipped", ++j);
        getCellValue(row, cellStyle, "Shipping Method", ++j);
        getCellValue(row, cellStyle, "Shipping Carrier", ++j);
        getCellValue(row, cellStyle, "Tracking Number", ++j);
        getCellValue(row, cellStyle, "Shipping Cost", ++j);
        getCellValue(row, cellStyle, "Qty Received", ++j);
        getCellValue(row, cellStyle, "Date Received", ++j);

        // spec property
        Set<String> columnNameWithValueSet = new HashSet<>();
        Set<String> columnNameNoValueSet = new HashSet<>();
        Map<Long, Map<String, Object>> propertyMap = new HashMap<>();
        for (OrderItemDTO orderItemDTO : orderItemDTOList) {
            Spec spec = specRepository.findById(orderItemDTO.getSpecId()).orElse(null);
            List<PropertyAttributeDTO> specPropertys = propertyMyBatisMapper.findSpecPropertyByPropertyId(spec.getPrPropertyId());
            Map<String, Object> paramMap = new HashMap<>();
            // sort spec fields alphabetically and remove the label fields
            for (PropertyAttributeDTO property : specPropertys) {
                Object object = getDataSetValue(property);
                if (property.getParamName() != null && object != null
                        && !property.getParamName().endsWith("_LABEL")) {
                    columnNameWithValueSet.add(property.getParamName());
                    paramMap.put(property.getParamName(), getDataSetValue(property));
                } else if (property.getParamName() != null && object == null
                        && !property.getParamName().endsWith("_LABEL")) {
                    paramMap.put(property.getParamName(), getDataSetValue(property));
                    columnNameNoValueSet.add(property.getParamName());
                }
            }
            propertyMap.put(spec.getId(), paramMap);
        }
        List<String> columnNameWithValueList = new ArrayList<String>(columnNameWithValueSet);
        List<String> columnNameNoValueList = new ArrayList<String>(columnNameNoValueSet);
        // sort alphabetically
        Collections.sort(columnNameWithValueList, new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                return s1.compareToIgnoreCase(s2);
            }
        });

        // sort alphabetically
        Collections.sort(columnNameNoValueList, new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                return s1.compareToIgnoreCase(s2);
            }
        });
        List<String> columnNames = new ArrayList<>();
        columnNames.addAll(columnNameWithValueList);
        columnNames.addAll(columnNameNoValueList);
        cellStyle = workbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        for (String columnName : columnNames) {
            cell = row.createCell(++j);
            cell.setCellValue(columnName);
            cell.setCellStyle(cellStyle);
        }

        // cell style of spec value
        cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);

        int rowNum = i;
        for (int k = 0; k < orderItemDTOList.size(); k++) {
            OrderItemDTO orderItemDTO = orderItemDTOList.get(k);

            rowNum++;
            row = sheet.createRow(rowNum);
            OrderItem orderItem = orderItemRepository.findById(orderItemDTO.getId()).orElse(null);
            Spec spec = orderItem.getSpec();

            int x = 0;
            // Spec Name
            getCellValue(row, cellStyle, spec.getSpecName(), x++);
            // Spec ID
            getCellValue(row, cellStyle, spec.getId(), x++);
            // Spec Type
            getCellValue(row, cellStyle, spec.getSpecType().getLabelStr(), x++);
            // Job ID
            getCellValue(row, cellStyle, orderItemDTO.getJobId(), x++);
            // Completion Date
            getCellValue(row, cellStyle, getNonNullDate(orderItemDTO.getCompletionDate(), timeZoneCode, locale, "MM/dd/yyyy hh:mm a"), x++);
            // Quantity
            getCellValue(row, cellStyle, orderItemDTO.getQuantity() != null ? orderItemDTO.getQuantity().toString() : "", x++);
            // Price
            // Default order items, but if isConvertible is true, then use spec default one
            UofmDTO defaultUofmDTO = uofmMapper.toDTO(uofmRepository.findDefaultBySpecTypeId(orderItemDTO.getSpec().getSpSpecTypeId()));
            if (defaultUofmDTO.getIsConvertible()) {
                orderItemDTO.setUofm(defaultUofmDTO);
            }
            getCellValue(row, cellStyle, getNonNullString(orderItemDTO.getPricePerUnits(defaultUofmDTO.getDefault()), currencySymbol, decimalPlaces) + " per " + orderItemDTO.getUofm().getDescription(), x++);

            // order item custom field
            if (orderItemDTO.getCustomPropertyId() != null) {
                Map<String, String> userCustomFieldMap = getUserCustomField(orderVersionDTO, projectId, CustomFieldClassID.ORDER_ITEM, orderItemDTO.getCustomPropertyId());
                for (Map.Entry<String, String> entry : userCustomFieldMap.entrySet()) {
                    getCellValue(row, cellStyle, entry.getValue(), x++);
                }
            }

            // Item Price
            getCellValue(row, cellStyle, getNonNullString(orderItemDTO.getValue(), currencySymbol, decimalPlaces), x++);

            // spec property
            int y = x+13;
            Map<String, Object> paramMap = propertyMap.get(spec.getId());
            for (String columnName : columnNames) {
                getCellValue(row, cellStyle, paramMap.get(columnName) !=null ? paramMap.get(columnName).toString() : "", y++);
            }

            // Shipment
            List<Request> requests = orderItem.getShipment().getRequests();
            for (int m = 0; m < requests.size(); m++) {
                int requestCellNum = x;
                if (m != 0) {
                    rowNum++;
                    row = sheet.createRow(rowNum);
                }

                Request request = requests.get(m);
                Address address = addressRepository.findById(request.getAddressId()).orElse(null);
                // Shipment Recipient
                getCellValue(row, cellStyle, request.getFullName(), requestCellNum++);
                // Location
                getCellValue(row, cellStyle, getLocation(address, locale).toString(), requestCellNum++);
                // Request Type
                getCellValue(row, cellStyle, i18NUtils.getMessage(request.getRequestType().getNameStrId(), locale), requestCellNum++);
                // Qty Requested
                getCellValue(row, cellStyle, request.getQuantity(), requestCellNum++);
                // Date Due
                getCellValue(row, cellStyle, getNonNullDate(request.getDeliveryDate(), timeZoneCode, locale, "MM/dd/yyyy"), requestCellNum++);
                // Qty Shipped
                getCellValue(row, cellStyle, Double.valueOf(request.getTotalShippedQty()).longValue(), requestCellNum++);

                List<Delivery> deliveries = request.getDeliveries();
                for (int n = 0; n < deliveries.size(); n++) {
                    int deliveryCellNum = requestCellNum;
                    if (n != 0) {
                        rowNum++;
                    }
                    Delivery delivery = deliveries.get(n);
                    // Date Shipped
                    getCellValue(row, cellStyle, getNonNullDate(delivery.getShippedDate(), timeZoneCode, locale, "MM/dd/yyyy"), deliveryCellNum++);

                    // Shipping Method
                    Method method = request.getMethod();
                    String shippingMethod = "";
                    if (method != null && method.getNameStrId() != null && method.getNameStrId() > 0) {
                        shippingMethod = i18NUtils.getMessage(method.getNameStrId(), locale);
                    } else if(method != null && method.getNameStr() != null) {
                        shippingMethod = method.getNameStr();
                    }
                    getCellValue(row, cellStyle, shippingMethod, deliveryCellNum++);

                    // Shipping Carrier
                    Carrier carrier = request.getCarrier();
                    StringBuilder shippingCarrier = new StringBuilder();
                    if (carrier != null && carrier.getNameStrId() != null && carrier.getNameStrId() >0) {
                        shippingCarrier.append(i18NUtils.getMessage(carrier.getNameStrId(), locale));

                    } else if(carrier != null && carrier.getNameStr() != null) {
                        shippingCarrier.append(carrier.getNameStr());
                    }
                    if (request.getCarrierOther() != null) {
                        shippingCarrier.append(" - "+request.getCarrierOther());
                    }
                    shippingCarrier.append(" / "+ shippingMethod);
                    if (request.getMethodOther() != null) {
                        shippingCarrier.append(" - "+request.getMethodOther());
                    }
                    getCellValue(row, cellStyle, shippingCarrier.toString(), deliveryCellNum++);

                    // Tracking Number
                    getCellValue(row, cellStyle, delivery.getTrackingnumber(), deliveryCellNum++);
                    // Shipping Cost
                    getCellValue(row, cellStyle, getNonNullString(delivery.getCost(), currencySymbol), deliveryCellNum++);
                    // Qty Received
                    getCellValue(row, cellStyle, delivery.getReceivedQuantity() != null ? delivery.getReceivedQuantity().longValue(): "", deliveryCellNum++);
                    // Date Received
                    getCellValue(row, cellStyle, getNonNullDate(delivery.getReceivedDate() , timeZoneCode, locale, "MM/dd/yyyy"), deliveryCellNum++);
                }
            }
        }

        // Set column width adaptive
        for (int z = 0; z < j; z++) {
            sheet.setColumnWidth(z,sheet.getColumnWidth(z)*12/10);
        }
    }

    public Map<String, String> getUserCustomField(OrderVersionDTO orderVersionDTO, Long projectId, Long customFieldClassId, Long customPropertyId) {
        ProjectDTO buyerProject = orderService.findBuyerProject(orderVersionDTO, projectId);
        List<UserFieldDTO> fields = customFieldService.getFieldsByWorkgroupId(orderVersionDTO.getBuyerWorkgroupId(), customFieldClassId, false);
        Property property = propertyRepository.findById(customPropertyId).get();
        Set<PropertyAttribute> propertyAttributeSet = property.getPropertyAttributeSet();
        Map<String, String> userCustomFieldMap = new HashMap();
        if (fields != null && fields.size() > 0) {
            for (UserFieldDTO field : fields) {
                String value = null;
                for (PropertyAttribute att : propertyAttributeSet) {
                    String customFieldParamName = att.getPropertyParam().getParamName();
                    if (customFieldParamName.equals(field.getParamName())) {
                        // pick a value from string/number/date
                        value = att.getStringValue();
                        if (att.getNumberValue() != null) {
                            value = att.getNumberValue().toString();
                        }
                        if (att.getDateValue() != null) {
                            value = att.getDateValue().toString();
                        }
                        break;
                    }
                }
                userCustomFieldMap.put(field.getLabel(), value);
            }
        }
        return userCustomFieldMap;
    }

    public void getCellValue(HSSFRow row, HSSFCellStyle cellStyle, Object value, int num) {
        HSSFCell cell = row.createCell(num);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(value != null ? value.toString() : "");
    }

    public String getStringValue(List<PropertyAttributeDTO> propertyAttributes, String paramName) {
        for (PropertyAttributeDTO propertyAttribute : propertyAttributes) {
            if (paramName.equals(propertyAttribute.getParamName())) {
                return propertyAttribute.getStringValue();
            }
        }
        return "";
    }

    private String getNonNullString(BigDecimal value, String currencySymbol) {
        return value != null ? currencySymbol + value.setScale(2, RoundingMode.HALF_UP) : null;
    }

    private String getNonNullString(BigDecimal value, String currencySymbol, int newScale) {
        return value != null ? currencySymbol + value.setScale(newScale, RoundingMode.HALF_UP) : null;
    }

    private String getNonNullDate(LocalDateTime value, String timeZoneCode, String locale, String formant) {
        return value != null ? DateUtil.getDateStr(DateUtil.convertTimeZone(value, timeZoneCode, locale), formant) : "";
    }

    private StringBuilder getLocation(Address address, String locale) {
        StringBuilder location = new StringBuilder();
        if (address != null) {
            if (address.getLine1() != null) {
                location.append(address.getLine1() + " ");
            }
            if (address.getLine2() != null) {
                location.append(address.getLine2() + " ");
            }
            if (address.getLine3() != null) {
                location.append(address.getLine3() + ".");
            }
            if (address.getCity() != null) {
                location.append(address.getCity() + ",");
            }
            if (address.getState() != null) {
                location.append(address.getState() + " ");
            }
            if (address.getPostal() != null) {
                location.append(address.getPostal() + " ");
            }
            location.append(i18NUtils.getMessage(address.getCountry().getNameStrId(), locale));
        }
        return location;
    }

    public static Object getDataSetValue(PropertyAttributeDTO propertyAttributeDTO) {
        switch (propertyAttributeDTO.getPrDataTypeId() != null ? propertyAttributeDTO.getPrDataTypeId().intValue() : 0) {
            case (int) DataTypeID.STRING:
                return propertyAttributeDTO.getStringValue();
            case (int) DataTypeID.LONG:
                return propertyAttributeDTO.getNumberValue();
            case (int) DataTypeID.DOUBLE:
                return propertyAttributeDTO.getNumberValue();
            case (int) DataTypeID.DATE:
                return propertyAttributeDTO.getDateValue();
            case (int) DataTypeID.BOOLEAN:
                return propertyAttributeDTO.getNumberValue();
            default:
                return propertyAttributeDTO.getStringValue();
        }
    }

    public void updatePriceFilter(Long workgroupId, Long userId, List<Long> filter) throws Exception {
        Map<String, String> prefs = new HashMap<>();
        if (filter != null) {
            String priceFilters = "";
            for (Long id : filter) {
                priceFilters += (id + ",");
            }
            prefs.put(REACT_ORDER_LISTING_PRICE_FILTER, priceFilters.substring(0, priceFilters.length() - 1));
        } else {
            prefs.put(REACT_ORDER_LISTING_PRICE_FILTER, "-1");
        }

        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    public OrderListPriceFilter getPriceFilter(long workgroupId, long userId) throws Exception {
        OrderListPriceFilter orderListPriceFilter = new OrderListPriceFilter();
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);
        List<Long> filter = new ArrayList<>();
        String priceFilters = preferenceService.getString(REACT_ORDER_LISTING_PRICE_FILTER, prefs, "-1");
        if (priceFilters != null && priceFilters.trim().length() > 0) {
            String[] ids = priceFilters.split(",");
            for (int i = 0; i < ids.length; i ++) {
                filter.add(Long.parseLong(ids[i]));
            }
        }
        orderListPriceFilter.setPriceFilter(filter);
        return orderListPriceFilter;
    }

}
