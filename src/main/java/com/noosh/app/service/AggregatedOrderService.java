package com.noosh.app.service;

import com.noosh.app.commons.constant.CostCenterItemName;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.OrderTypeID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.costcenter.*;
import com.noosh.app.commons.dto.order.*;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.rfe.RfeSupplierDTO;
import com.noosh.app.commons.dto.shipment.ShRequestDTO;
import com.noosh.app.commons.dto.shipment.ShipmentDTO;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.entity.order.Order;
import com.noosh.app.commons.entity.order.OrderState;
import com.noosh.app.commons.entity.order.OrderVersion;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.commons.entity.shipment.Shipment;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.terms.AcTerms;
import com.noosh.app.commons.entity.uofm.Uofm;
import com.noosh.app.commons.vo.breakout.BreakoutVO;
import com.noosh.app.commons.vo.order.OrderGeneralInfoVO;
import com.noosh.app.commons.vo.order.OrderItemVO;
import com.noosh.app.commons.vo.uofm.UofmVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.feign.SpecOpenFeignClient;
import com.noosh.app.feign.WorkgroupOpenFeignClient;
import com.noosh.app.mapper.OrderMapper;
import com.noosh.app.mapper.OrderItemMapper;
import com.noosh.app.mapper.OrderStateMapper;
import com.noosh.app.mapper.OrderVersionMapper;
import com.noosh.app.mapper.shipment.ShRequestMapper;
import com.noosh.app.mapper.terms.AcTermsMapper;
import com.noosh.app.mapper.uofm.UofmMapper;
import com.noosh.app.repository.estimate.EstimateItemPriceRepository;
import com.noosh.app.repository.jpa.order.OrderRepository;
import com.noosh.app.repository.jpa.order.OrderStateRepository;
import com.noosh.app.repository.jpa.order.OrderVersionRepository;
import com.noosh.app.repository.jpa.spec.SpecRepository;
import com.noosh.app.repository.shipment.ShipmentRepository;
import com.noosh.app.repository.terms.AcTermsRepository;
import com.noosh.app.repository.uofm.UofmRepository;
import com.noosh.app.service.comparator.AcceptDateComparator;
import com.noosh.app.service.costcenter.CostCenterService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.permission.ordering.*;
import com.noosh.app.service.rfe.RfeService;
import com.noosh.app.service.util.AggregatedCostCenterComparator;
import com.noosh.app.service.util.DateUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import jakarta.annotation.PostConstruct;
import com.noosh.app.service.vat.VatService;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * User: Neal Shan
 * Date: 5/22/2018
 */
@Service
public class AggregatedOrderService {

    @Inject
    private OrderService orderService;
    @Inject
    private AcTermsRepository acTermsRepository;
    @Inject
    private AcTermsMapper acTermsMapper;

    @Inject
    private OrderMapper orderMapper;
    @Inject
    private CopyOrderPermission copyOrderPermission;
    @Inject
    private CancelOrderPermission cancelOrderPermission;
    @Inject
    private CostCenterAllocationPermission costCenterAllocationPermission;
    @Inject
    private CreateChangeOrderPermission createChangeOrderPermission;
    @Inject
    private EditSupplierRefPermission editSupplierRefPermission;
    @Inject
    private EditShipmentPermission editShipmentPermission;
    @Inject
    private ViewShipmentPermission viewShipmentPermission;
    @Inject
    private ViewSupplierRatingPermission viewSupplierRatingPermission;
    @Inject
    private ViewSourcingPermission viewSourcingPermission;
    @Inject
    private CompleteOrderPermission completeOrderPermission;
    @Inject
    private OrderRepository orderRepository;
    @Inject
    private OrderStateRepository orderStateRepository;
    @Inject
    private OrderVersionRepository orderVersionRepository;
    @Inject
    private ShipmentRepository shipmentRepository;
    @Inject
    private SpecRepository specRepository;
    @Inject
    private PermissionService permissionService;
    @Inject
    private EstimateItemPriceRepository estimateItemPriceRepository;
    @Inject
    private RfeService rfeService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private OrderItemMapper orderItemMapper;
    @Inject
    private OrderStateMapper orderStateMapper;
    @Inject
    private OrderVersionMapper orderVersionMapper;
    @Inject
    private UofmRepository uofmRepository;
    @Inject
    private UofmMapper uofmMapper;
    @Inject
    private ShRequestMapper requestMapper;
    @Inject
    private SpecOpenFeignClient specOpenFeignClient;
    @Inject
    private WorkgroupOpenFeignClient workgroupOpenFeignClient;
    @Inject
    private CostCenterService costCenterService;
    @Inject
    private VatService vatService;
    @Inject
    private ProjectService projectService;

    private final List<String> noNeedCompareList = new ArrayList<>();

    @PostConstruct
    public void init() {
        noNeedCompareList.add("CCA_QTY_num");
    }

    public AggregatedOrderVersionDTO findAggregatedOrderDTO(Long orderId, Long projectId, Long currentWorkgroupId, Long currentUserId) {
        // original
        AggregatedOrderVersionDTO aggregatedOrderVersionDTO = new AggregatedOrderVersionDTO();
        OrderVersionDTO originalOrder = orderService.findOrderVersionById(orderId, projectId, currentUserId, currentWorkgroupId);
        aggregatedOrderVersionDTO.setOriginalOrder(originalOrder);

        // aggregated
        OrderVersionDTO initAggregatedOrder = SerializationUtils.clone(originalOrder);
        aggregatedOrderVersionDTO.setAggregatedOrder(getAggregatedOrder(initAggregatedOrder, null, currentWorkgroupId, currentUserId));

        return aggregatedOrderVersionDTO;
    }

    public AggregatedOrderVersionDTO findSimpleAggregatedOrderDTO(Long orderId, Long currentWorkgroupId, Long currentUserId) {
        // original
        AggregatedOrderVersionDTO aggregatedOrderVersionDTO = new AggregatedOrderVersionDTO();
        OrderVersionDTO originalOrder = orderService.findSimpleOrderById(orderId);
        aggregatedOrderVersionDTO.setOriginalOrder(originalOrder);

        // aggregated
        OrderVersionDTO initAggregatedOrder = SerializationUtils.clone(originalOrder);
        aggregatedOrderVersionDTO.setAggregatedOrder(getAggregatedOrder(initAggregatedOrder, null, currentWorkgroupId, currentUserId));

        return aggregatedOrderVersionDTO;
    }

    public AggregatedOrderDetailDTO findAggregatedOrderDetail(Long orderId, Long projectId, Long currentWorkgroupId, Long currentUserId, String orderType, Boolean printView) {
        AggregatedOrderDetailDTO aggregatedOrderDetailDTO = findAggregatedOrderDetailById(orderId, projectId, currentWorkgroupId, currentUserId);
        findOrderButtons(orderId, projectId, currentWorkgroupId, currentUserId, aggregatedOrderDetailDTO, orderType);
        if (printView) {
            findSmartFormInfo(aggregatedOrderDetailDTO, projectId); // find smart form label, value
            findShipmentInfo(aggregatedOrderDetailDTO); // find shipment request information
            OrderVersionDTO originalOrder = orderService.findSimpleOrderById(aggregatedOrderDetailDTO.getOrderVersionDTO().getOriginalOrder().getOrderId());
            OrderVersionDTO[] changeOrders = findSimpleChangeOrders(originalOrder);
            BigDecimal pendingGrandTotal = getTotalPendingOrderGrandTotal(changeOrders);
            boolean hasPendingOrder = pendingGrandTotal != null;
            AggregatedOrderVersionDTO aggregatedOrderVersionDTO = aggregatedOrderDetailDTO.getOrderVersionDTO();
            aggregatedOrderVersionDTO.setHasPendingChangeOrder(hasPendingOrder);
            aggregatedOrderVersionDTO.setPendingChangeGrandTotal(hasPendingOrder ? pendingGrandTotal : BigDecimal.ZERO);
            aggregatedOrderVersionDTO.setPendingChangeGrandTotalCurrencyId(aggregatedOrderVersionDTO.getAggregatedOrder().getTaxCurrencyId());
            if (originalOrder.getRate() != null && originalOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrder.getExCurrencyId() != null) {
                BigDecimal exPendingGrandTotal = getExTotalPendingOrderGrandTotal(changeOrders);
                aggregatedOrderVersionDTO.setExPendingChangeGrandTotal(hasPendingOrder ? exPendingGrandTotal : BigDecimal.ZERO);
            }
        }
        return aggregatedOrderDetailDTO;
    }

    public void findShipmentInfo(AggregatedOrderDetailDTO orderDetailDTO) {
        List<Long> jobIdList = new ArrayList<>();
        Map<Long, ShipmentDTO> shipmentDTOMap = new HashMap<>();
        orderDetailDTO.getOrderVersionDTO().getAggregatedOrder().getOrderItemDTOs().forEach(orderItemDTO -> {
            jobIdList.add(orderItemDTO.getJobId());
            ShipmentDTO shipmentDTO = orderItemDTO.getShipment();
            if (shipmentDTO != null) {
                shipmentDTOMap.put(shipmentDTO.getShShipmentId(), shipmentDTO);
            }
        });
        List<Shipment> shipmentList = shipmentRepository.findByPcJobIdIn(jobIdList);
        shipmentList.forEach(shipment -> {
            List<ShRequestDTO> requestDTOList = new ArrayList<>();
            List<Long> requestCustomIdList = new ArrayList<>();
            shipment.getRequests().forEach(request -> {
                requestDTOList.add(requestMapper.toDTO(request));
                if (request.getPropertyId() != null) {
                    requestCustomIdList.add(request.getPropertyId());
                }
            });
            if (!requestDTOList.isEmpty()) {
                if (!requestCustomIdList.isEmpty()) {
                    Map<Long, Map<String, Object>> customAttributesMap = workgroupOpenFeignClient.findCustomAttribute(requestCustomIdList);
                    requestDTOList.forEach(requestDTO -> {
                        if (requestDTO.getPropertyId() != null) {
                            requestDTO.setCustomAttributes(customAttributesMap.get(requestDTO.getPropertyId()));
                        }
                    });
                }
                shipmentDTOMap.get(shipment.getShShipmentId()).setRequests(requestDTOList);
            }
        });
    }


    public void findSmartFormInfo(AggregatedOrderDetailDTO orderDetailDTO, Long projectId) {
        Map<Long, Map<String, Object>> smartFormSpecIdValueMap = specOpenFeignClient.findSmartFormSpecsByProjectId(projectId);
        if (smartFormSpecIdValueMap != null) {
            orderDetailDTO.getOrderVersionDTO().getAggregatedOrder().getOrderItemDTOs().forEach(orderItemDTO -> {
                orderItemDTO.getSpec().setSmartFormValues(smartFormSpecIdValueMap.get(orderItemDTO.getSpecId()));
            });
        }
    }


    public AggregatedOrderDetailDTO findAggregatedOrderDetailById(Long orderId, Long projectId, Long currentWorkgroupId, Long currentUserId) {
        AggregatedOrderDetailDTO aggregatedOrderDetailDTO = new AggregatedOrderDetailDTO();

        // original
        AggregatedOrderVersionDTO aggregatedOrderVersionDTO = new AggregatedOrderVersionDTO();
        OrderVersionDTO originalOrder = orderService.findOrderVersionById(orderId, projectId, currentUserId, currentWorkgroupId);
        aggregatedOrderVersionDTO.setOriginalOrder(originalOrder);

        // aggregated
        OrderVersionDTO initAggregatedOrder = SerializationUtils.clone(originalOrder);
        aggregatedOrderVersionDTO.setAggregatedOrder(getAggregatedOrder(initAggregatedOrder, null, currentWorkgroupId, currentUserId));
        //paper flow
        ProjectDTO parentProject = projectService.findProjectById(projectId);
        boolean isPaperFlow = originalOrder.getBuyerProject().getIsPaperFlow() && !originalOrder.isOutsourcingSellOrder(parentProject);
        if (isPaperFlow) {
            orderService.findExternalItems(aggregatedOrderVersionDTO.getAggregatedOrder());
        }

        aggregatedOrderDetailDTO.setOrderVersionDTO(aggregatedOrderVersionDTO);
        return aggregatedOrderDetailDTO;
    }


    private void findOrderButtons(Long orderId, Long projectId, Long currentWorkgroupId, Long currentUserId,
                                  AggregatedOrderDetailDTO orderDetailDTO, String orderType) {

        OrderVersionDTO orderVersionDTO = orderDetailDTO.getOrderVersionDTO().getOriginalOrder();

        // copy button
        if (copyOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                && (!orderVersionDTO.isDraft()) && (!orderVersionDTO.isPaperOrder())) {
            String copyButton = NooshOneUrlUtil.composeReorderOrderLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
            orderDetailDTO.setReorderButton(copyButton);
        }

        // cancel button
        if (cancelOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)) {
            String cancelButton = NooshOneUrlUtil.composeCancelOrderLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
            orderDetailDTO.setCancelButton(cancelButton);
        }

        // create Change Order Button
        if (createChangeOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                && (!orderVersionDTO.isDraft()) && (!orderVersionDTO.isPaperOrder())) {
            String createChangeOrderButton = NooshOneUrlUtil.composeCreateChangeOrderLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
            orderDetailDTO.setCreateChangeOrderButton(createChangeOrderButton);
        }

        // edit supplier ref Button
        if (editSupplierRefPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                && (!orderVersionDTO.isDraft())) {
            String backUrl = "/noosh/procurement/ordering/order/viewOrder?orderId=" + orderId + "&objectId="
                    + projectId + "&objectClassId=1000000&renderSpecs=false";
            String editSupplierRefButton = NooshOneUrlUtil.composeUpdateSupplierTrackingLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion(), backUrl, orderType);
            orderDetailDTO.setEditSupplierRefButton(editSupplierRefButton);
        }

        // cost Center Allocation Button
        if (costCenterAllocationPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)) {
            String costCenterAllocation = NooshOneUrlUtil.composeAggregatedCostCenterAllocationLinkToEnterprise(projectId,
                    orderId, orderVersionDTO.getVersion());
            orderDetailDTO.setCostCenterAllocationButton(costCenterAllocation);
        }

        // create edit Shipments Button
        List<Long> jobIds = new ArrayList<Long>();
        if (orderVersionDTO.getOrderItemDTOs() != null && orderVersionDTO.getOrderItemDTOs().size() > 0) {
            orderVersionDTO.getOrderItemDTOs().stream().forEach(o -> jobIds.add(o.getJobId()));
            List<Shipment> shipments = shipmentRepository.findByPcJobIdIn(jobIds);
            if (editShipmentPermission.check(null, currentWorkgroupId, currentUserId, projectId) && (!orderVersionDTO.isPaperOrder())
                    && viewShipmentPermission.check(null, currentWorkgroupId, currentUserId, projectId)
                    && orderVersionDTO.isAccepted() && (!orderVersionDTO.isPaperOrder())) {
                List<String> shipmentIds = new ArrayList<String>();
                shipments.stream().forEach(s -> shipmentIds.add("" + s.getShShipmentId()));
                String backUrl = "/noosh/procurement/ordering/order/viewOrder?orderId=" + orderId + "&objectId="
                        + projectId + "&objectClassId=1000000&renderSpecs=false&aggregate=true";
                String editShipment = NooshOneUrlUtil.composeEditShipmentLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), backUrl, shipmentIds);
                orderDetailDTO.setEditShipmentButton(editShipment);
            }
        }

        // create supplier rating Button
        if (viewSupplierRatingPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId) && (!orderVersionDTO.isDraft())) {
            String supplierRating = NooshOneUrlUtil.composeSupplierRatingLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion());
            orderDetailDTO.setSupplierRatingButton(supplierRating);
        }

        // create supplier sourcing Button
        if (viewSourcingPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId) && (!orderVersionDTO.isDraft())) {
            String sourcingButton = NooshOneUrlUtil.composeSourcingLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion());
            orderDetailDTO.setSourcingStrategiesButton(sourcingButton);
        }

        // create closing order Button
        if (completeOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId) && (!orderVersionDTO.isDraft())
                && (!orderDetailDTO.getOrderVersionDTO().getAggregatedOrder().isCompletedAndHasAcceptedClosingChangeOrder())
                && (!orderService.hasClosedChangeOrder(orderId, orderVersionDTO.getParent().getOwnerWorkgroupId()))) {
            String completeButton = NooshOneUrlUtil.composeCompleteOrderLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion(), orderType);
            orderDetailDTO.setCompleteButton(completeButton);
        }

        // dismiss supplier button
        if(permissionService.checkAll(PermissionID.DISMISS_SUPPLIER, currentWorkgroupId, currentUserId, projectId)){
            String dismissSupplierButton = null;
            // check if current user is supplier
            if (!orderVersionDTO.isUserSupplier()) {
                // determine whether there is an estimate related to the order
                List<Long> estimateItemPriceIds = new ArrayList<Long>();
                List<OrderItemDTO> orderItems = orderVersionDTO.getOrderItemDTOs();
                if (orderVersionDTO.getOrderItemDTOs() != null && orderVersionDTO.getOrderItemDTOs().size() > 0) {
                    for (int i = 0; i < orderItems.size(); i++) {
                        if (orderItems.get(i).getEstimateItemPriceId() != null && orderItems.get(i).getEstimateItemPriceId() > 0) {
                            estimateItemPriceIds.add(orderItems.get(i).getEstimateItemPriceId());
                        }
                    }
                }
                // if there's no estimate related to this order, hide the button and exit
                if (estimateItemPriceIds.size() > 0) {
                    // find the RFE the the estimate. If none can be found, hide the button and exit
                    List<EstimateItemPrice> itemPrices = estimateItemPriceRepository.findByIdIn(estimateItemPriceIds);
                    if (itemPrices != null && itemPrices.size() > 0) {
                        // check whether there are still active suppliers for this RFE. If not, hide the button and exit
                        long rfeId = itemPrices.get(0).getEstimateItem().getRfeItem().getRfeId();

                        dismissSupplierButton = NooshOneUrlUtil.composedismissSupplierButtonLinkToEnterprise(projectId, rfeId);

                        if(!permissionService.checkAll(PermissionID.VIEW_RFE, currentWorkgroupId, currentUserId, projectId)) { // the current user may not have access to RFE
                            dismissSupplierButton = null;
                        }

                        List<RfeSupplierDTO> dismissableGroups = rfeService.findDismissableSupplier(rfeId, projectId);
                        if (dismissableGroups == null || dismissableGroups.size() == 0) {
                            dismissSupplierButton = null;
                        }

                    }

                }

            }
            orderDetailDTO.setDismissButton(dismissSupplierButton);
        }

        AggregatedOrderVersionDTO aggregatedOrderVersionDTO = orderDetailDTO.getOrderVersionDTO();
        OrderVersionDTO originalOrder = aggregatedOrderVersionDTO.getOriginalOrder();
        OrderVersionDTO aggregatedOrder = aggregatedOrderVersionDTO.getAggregatedOrder();

        // initiate a carry over change order to the Buy/Sell order
        String carryOverChangeOrderButton = NooshOneUrlUtil.composeCarryOverChangeOrderButtonChangeOrderLinkToEnterprise(projectId,
                aggregatedOrder.getId(), orderId);
        ProjectDTO parent = originalOrder.getParent();
        if (originalOrder.isPending()
                && (parent.isOutsourcerProject() || (parent.isBrokerProject() && parent.isClientOnNoosh()))) {
            // buy orders button is only for outsourcers with create order permission.
            if (!createChangeOrderPermission.check(projectId, currentUserId, currentWorkgroupId)) {
                carryOverChangeOrderButton = null;
            }

            int itemCount = originalOrder.getOrderItemDTOs() != null ? originalOrder.getOrderItemDTOs().size() : 0;
            if (itemCount == 0) {
                carryOverChangeOrderButton = null;
            }
        } else {
            carryOverChangeOrderButton = null;
        }
        orderDetailDTO.setCarryOverChangeOrderButton(carryOverChangeOrderButton);

    }

    public int getAcceptedChangeOrderCount(OrderVersionDTO originalOrder, Long currentWorkgroupId, Long currentUserId) {
        OrderVersionDTO[] changeOrders = findChangeOrders(originalOrder, currentWorkgroupId, currentUserId);
        if (changeOrders == null || changeOrders.length == 0)
            return 0;

        int count=0;
        for (int i=0; i < changeOrders.length; i++) {
            if (changeOrders[i].isAccepted())
                count++;
        }
        return count;
    }

    public OrderVersionDTO getAggregatedOrder(OrderVersionDTO originalOrder, LocalDateTime maxAcceptedDate, Long currentWorkgroupId, Long currentUserId) {
        return getAggregatedOrder(originalOrder, maxAcceptedDate, currentWorkgroupId, currentUserId, false);
    }

    public OrderVersionDTO getAggregatedOrder(OrderVersionDTO originalOrder, LocalDateTime maxAcceptedDate, Long currentWorkgroupId, Long currentUserId, boolean isNeedCostcenter) {

        // no reason to continue if there are no change orders

        // there are too many unnecessary queries for aggregated calculations.
        //OrderVersionDTO[] changeOrders = findChangeOrders(originalOrder, currentWorkgroupId, currentUserId);
        OrderVersionDTO[] changeOrders = findSimpleChangeOrders(originalOrder);

        // Get all accepted change orders and sort them in ascending order by acceptance date
        // By doing this, the data in the LAST change order accepted will take precendence over
        // the original order and any other change order that has been accepted.
        List<OrderVersionDTO> changeOrderList = new ArrayList<OrderVersionDTO>();
        LocalDateTime lastAcceptDateTime = originalOrder.getAcceptDate();
        for (int i = 0; i < changeOrders.length; i++) {
            boolean includeInAggr = getIncludeInAggr(originalOrder, maxAcceptedDate, changeOrders[i]);

            if (includeInAggr) {
                changeOrderList.add(changeOrders[i]);
                lastAcceptDateTime = changeOrders[i].getAcceptDate();
            }
        }
        OrderVersionDTO[] sortedChangeOrders = (OrderVersionDTO[]) changeOrderList.toArray(new OrderVersionDTO[changeOrderList.size()]);
        Arrays.sort(sortedChangeOrders, new AcceptDateComparator());

        boolean isCompletedAndHasAcceptedClosingChangeOrder = originalOrder.isCompleted() && hasAcceptedClosingChangeOrder(sortedChangeOrders);

        originalOrder.setCompletedAndHasAcceptedClosingChangeOrder(isCompletedAndHasAcceptedClosingChangeOrder);

        BigDecimal tax = originalOrder.getTax() == null ? BigDecimal.ZERO : originalOrder.getTax();
        BigDecimal shipping = originalOrder.getShipping() == null ? BigDecimal.ZERO : originalOrder.getShipping();
        BigDecimal orderItemDiscountSurchargeTotal = originalOrder.getOrderItemDiscountSurchargeTotal() == null ? BigDecimal.ZERO : originalOrder.getOrderItemDiscountSurchargeTotal();
        BigDecimal discountOrSurchargeTotal = originalOrder.getDors() == null ? BigDecimal.ZERO : originalOrder.getDors();
        BigDecimal discountOrSurchargeGrandTotal = originalOrder.getDiscountOrSurchargeTotal() == null ? BigDecimal.ZERO : originalOrder.getDiscountOrSurchargeTotal();
        BigDecimal totalValue = BigDecimal.ZERO;
        // dual currency
        boolean isDualCurrency = originalOrder.getRate() != null && originalOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrder.getExCurrencyId() != null;
        BigDecimal exTax = originalOrder.getExTax() == null ? BigDecimal.ZERO : originalOrder.getExTax();
        BigDecimal exShipping = originalOrder.getExShipping() == null ? BigDecimal.ZERO : originalOrder.getExShipping();
        BigDecimal exOrderItemDiscountSurchargeTotal = originalOrder.getExOrderItemDiscountSurchargeTotal() == null ? BigDecimal.ZERO : originalOrder.getExOrderItemDiscountSurchargeTotal();
        BigDecimal exDiscountOrSurchargeTotal = originalOrder.getExDors() == null ? BigDecimal.ZERO : originalOrder.getExDors();
        BigDecimal exDiscountOrSurchargeGrandTotal = originalOrder.getExDiscountOrSurchargeTotal() == null ? BigDecimal.ZERO : originalOrder.getExDiscountOrSurchargeTotal();
        BigDecimal exTotalValue = BigDecimal.ZERO;

        List<OrderItemDTO> allChangeOrderItems = new ArrayList<OrderItemDTO>();

        List<OrderCostCenterDTO> orderCostCenterDTOs = new ArrayList<OrderCostCenterDTO>();
        if (isNeedCostcenter) {
            List<OrderCostCenterDTO> originalOrderCostCenters = costCenterService.buildCostCenterList(
                    orderItemMapper.toEntityList(originalOrder.getOrderItemDTOs()), originalOrder, originalOrder.getParent().getId(), null);
            if (originalOrderCostCenters != null && originalOrderCostCenters.size() > 0) {
                orderCostCenterDTOs.addAll(originalOrderCostCenters);
            }
        }

        for (int i = sortedChangeOrders.length - 1; i >= 0; i--) {
            if (sortedChangeOrders[i].getOrderItemDTOs() != null && sortedChangeOrders[i].getOrderItemDTOs().size() > 0) {
                allChangeOrderItems.addAll(sortedChangeOrders[i].getOrderItemDTOs());
            }
            tax = tax.add(sortedChangeOrders[i].getTax() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getTax());
            shipping = shipping.add(sortedChangeOrders[i].getShipping() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getShipping());
            orderItemDiscountSurchargeTotal = orderItemDiscountSurchargeTotal.add(sortedChangeOrders[i].getOrderItemDiscountSurchargeTotal() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getOrderItemDiscountSurchargeTotal());
            discountOrSurchargeTotal = discountOrSurchargeTotal.add(sortedChangeOrders[i].getDors() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getDors());
            discountOrSurchargeGrandTotal = discountOrSurchargeGrandTotal.add(sortedChangeOrders[i].getDiscountOrSurchargeTotal() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getDiscountOrSurchargeTotal());
            if (isDualCurrency) {
                exTax = exTax.add(sortedChangeOrders[i].getExTax() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getExTax());
                exShipping = exShipping.add(sortedChangeOrders[i].getExShipping() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getExShipping());
                exOrderItemDiscountSurchargeTotal = exOrderItemDiscountSurchargeTotal.add(sortedChangeOrders[i].getExOrderItemDiscountSurchargeTotal() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getExOrderItemDiscountSurchargeTotal());
                exDiscountOrSurchargeTotal = exDiscountOrSurchargeTotal.add(sortedChangeOrders[i].getExDors() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getExDors());
                exDiscountOrSurchargeGrandTotal = exDiscountOrSurchargeGrandTotal.add(sortedChangeOrders[i].getExDiscountOrSurchargeTotal() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getExDiscountOrSurchargeTotal());
            }
            originalOrder.setComments(sortedChangeOrders[i].getComments());
            Long orderCustomPropertyId = sortedChangeOrders[i].getCustomPropertyId();
            if (orderCustomPropertyId != null && orderCustomPropertyId > 0) {
                originalOrder.setCustomPropertyId(orderCustomPropertyId);
            }
            if (isNeedCostcenter) {
                List<OrderCostCenterDTO> changeOrderCostCenters = costCenterService.buildCostCenterList(
                        orderItemMapper.toEntityList(sortedChangeOrders[i].getOrderItemDTOs()), sortedChangeOrders[i], originalOrder.getParent().getId(), null);
                if (changeOrderCostCenters != null && changeOrderCostCenters.size() > 0) {
                    orderCostCenterDTOs.addAll(changeOrderCostCenters);
                }
            }
        }
        originalOrder.setTax(tax);
        originalOrder.setShipping(shipping);
        if(isCompletedAndHasAcceptedClosingChangeOrder) {
            originalOrder.setOrderItemDiscountSurchargeTotal(orderItemDiscountSurchargeTotal);
            originalOrder.setDiscountOrSurcharge(discountOrSurchargeTotal);
            originalOrder.setDiscountOrSurchargeTotal(discountOrSurchargeGrandTotal);
        }
        if (isDualCurrency) {
            originalOrder.setExTax(exTax);
            originalOrder.setExShipping(exShipping);
            if(isCompletedAndHasAcceptedClosingChangeOrder) {
                originalOrder.setExOrderItemDiscountSurchargeTotal(exOrderItemDiscountSurchargeTotal);
                originalOrder.setExDiscountOrSurcharge(exDiscountOrSurchargeTotal);
                originalOrder.setExDiscountOrSurchargeTotal(exDiscountOrSurchargeGrandTotal);
            }
        }
        if (isNeedCostcenter) {
            originalOrder.setCostCenters(orderCostCenterDTOs);
        }

        // loop through all the orderitems in the list and aggregate them
        OrderItemDTO[] aggregatedOrderItems = originalOrder.getOrderItemDTOs().toArray(new OrderItemDTO[originalOrder.getOrderItemDTOs().size()]);
        for (int i = 0; i < allChangeOrderItems.size(); i++) {
            OrderItemDTO thisItem = (OrderItemDTO) allChangeOrderItems.get(i);
            OrderItemDTO aggregatedOrderItem = aggregatedOrderItems[thisItem.getItemIndex() - 1];
            aggregatedOrderItem.setId(thisItem.getId());
            aggregatedOrderItem.setComments(thisItem.getComments());
            aggregatedOrderItem.setSpec(thisItem.getSpec());
            aggregatedOrderItem.setSpecId(thisItem.getSpecId());
            aggregatedOrderItem.setSpecNodeId(thisItem.getSpecNodeId());
            aggregatedOrderItem.setJobId(thisItem.getJobId());
            aggregatedOrderItem.setUofmId(thisItem.getUofmId());
            aggregatedOrderItem.setUofm(thisItem.getUofm());
            Long itemCustomPropertyId = thisItem.getCustomPropertyId();
            if (itemCustomPropertyId != null && itemCustomPropertyId > 0) {
                aggregatedOrderItem.setCustomPropertyId(itemCustomPropertyId);
            }

            BigDecimal value = aggregatedOrderItem.getValue() == null ? BigDecimal.ZERO : aggregatedOrderItem.getValue();
            BigDecimal itemTax = aggregatedOrderItem.getTax() == null ? BigDecimal.ZERO : aggregatedOrderItem.getTax();
            BigDecimal itemShipping = aggregatedOrderItem.getShipping() == null ? BigDecimal.ZERO : aggregatedOrderItem.getShipping();
            BigDecimal discountOrSurcharge = aggregatedOrderItem.getDors() == null ? BigDecimal.ZERO : aggregatedOrderItem.getDors();

            BigDecimal exValue = aggregatedOrderItem.getExValue() == null ? BigDecimal.ZERO : aggregatedOrderItem.getExValue();
            BigDecimal exItemTax = aggregatedOrderItem.getExTax() == null ? BigDecimal.ZERO : aggregatedOrderItem.getExTax();
            BigDecimal exItemShipping = aggregatedOrderItem.getExShipping() == null ? BigDecimal.ZERO : aggregatedOrderItem.getExShipping();
            BigDecimal exDiscountOrSurcharge = aggregatedOrderItem.getExDors() == null ? BigDecimal.ZERO : aggregatedOrderItem.getExDors();

            aggregatedOrderItem.setBreakouts(thisItem.getBreakouts());
            aggregatedOrderItem.setQuantity(thisItem.getQuantity());
            aggregatedOrderItem.setValue(value.add(thisItem.getValue() == null ? BigDecimal.ZERO : thisItem.getValue()));
            aggregatedOrderItem.setCompletionDate(thisItem.getCompletionDate());
            aggregatedOrderItem.setTax(itemTax.add(thisItem.getTax() == null ? BigDecimal.ZERO : thisItem.getTax()));
            aggregatedOrderItem.setShipping(itemShipping.add(thisItem.getShipping() == null ? BigDecimal.ZERO : thisItem.getShipping()));
            if(isCompletedAndHasAcceptedClosingChangeOrder) {
                aggregatedOrderItem.setDors(discountOrSurcharge.add(thisItem.getDors() == null ? BigDecimal.ZERO : thisItem.getDors()));
            }
            if (isDualCurrency) {
                aggregatedOrderItem.setExValue(exValue.add(thisItem.getExValue() == null ? BigDecimal.ZERO : thisItem.getExValue()));
                aggregatedOrderItem.setExTax(exItemTax.add(thisItem.getExTax() == null ? BigDecimal.ZERO : thisItem.getExTax()));
                aggregatedOrderItem.setExShipping(exItemShipping.add(thisItem.getExShipping() == null ? BigDecimal.ZERO : thisItem.getExShipping()));
                if(isCompletedAndHasAcceptedClosingChangeOrder) {
                    aggregatedOrderItem.setExDors(exDiscountOrSurcharge.add(thisItem.getExDors() == null ? BigDecimal.ZERO : thisItem.getExDors()));
                }
            }
        }

        for (int i = 0 ; i < aggregatedOrderItems.length; i++) {
            if (aggregatedOrderItems[i].getValue() != null) {
                totalValue = totalValue.add(aggregatedOrderItems[i].getValue());
            }
            if (isDualCurrency && aggregatedOrderItems[i].getExValue() != null) {
                exTotalValue = exTotalValue.add(aggregatedOrderItems[i].getExValue());
            }
        }

        originalOrder.setOrderItemDTOs(Arrays.asList(aggregatedOrderItems));
        originalOrder.setOrderItemSubtotal(totalValue);
        originalOrder.setGrandTotal(totalValue.add(tax).add(shipping));
        if (isDualCurrency) {
            originalOrder.setExOrderItemSubtotal(exTotalValue);
            originalOrder.setExGrandTotal(exTotalValue.add(exTax).add(exShipping));
        }
        originalOrder.setLatestAcceptDate(lastAcceptDateTime);
        return originalOrder;
    }

    private boolean getIncludeInAggr(OrderVersionDTO originalOrder, LocalDateTime maxAcceptedDate, OrderVersionDTO changeOrder) {
        boolean includeInAggr = false;
        long changeOrderStateId = changeOrder.getOrderState().getObjectStateId();

        // if main order is accepted and this change order is accepted, include the change order in the aggregation
        if (originalOrder.isAccepted() && changeOrder.isAccepted()) {
            includeInAggr = true;

            // if main order is cancelled and this change order is cancelled, include the change order in the aggregation
        } else if (originalOrder.getOrderState().getObjectStateId() == ObjectStateID.ORDER_CANCELLED
                && changeOrderStateId == ObjectStateID.ORDER_CANCELLED) {
            includeInAggr = true;
        }

        // if the change-order was accepted after a certain threshold, exclude it from the aggregation
        if (maxAcceptedDate != null && changeOrder.isAccepted() &&
                (changeOrder.getAcceptDate().isAfter(maxAcceptedDate) ||
                        changeOrder.getAcceptDate().isEqual(maxAcceptedDate))) {
            includeInAggr = false;
        }
        return includeInAggr;
    }

    private OrderVersionDTO[] findChangeOrders(OrderVersionDTO originalOrder, Long currentWorkgroupId, Long currentUserId) {
        List<Long> changeOrderIds = orderRepository.findChangeOrderIds(originalOrder.getOrderId());
        OrderVersionDTO[] changeOrders = new OrderVersionDTO[changeOrderIds.size()];
        long projectId = originalOrder.getParent().getId();
        for (int i = 0; i < changeOrderIds.size(); i++) {
            changeOrders[i] = orderService.findOrderVersionById(changeOrderIds.get(i), projectId, currentUserId, currentWorkgroupId);
        }

        return changeOrders;
    }

    private OrderVersionDTO[] findSimpleChangeOrders(OrderVersionDTO originalOrder) {
        List<Long> changeOrderIds = orderRepository.findChangeOrderIds(originalOrder.getOrderId());
        OrderVersionDTO[] changeOrders = new OrderVersionDTO[changeOrderIds.size()];
        for (int i = 0; i < changeOrderIds.size(); i++) {
            changeOrders[i] = orderService.findSimpleOrderById(changeOrderIds.get(i));
        }

        return changeOrders;
    }

    private boolean hasAcceptedClosingChangeOrder(OrderVersionDTO[] changeOrders) {
        if (changeOrders != null) {
            for (int i = 0; i < changeOrders.length; i++) {
                if (changeOrders[i].isClosingChangeOrder() &&
                        (changeOrders[i].isPending() || changeOrders[i].isAccepted())) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean hasPendingClosingChangeOrder(OrderVersionDTO originalOrder) {
        OrderVersionDTO[] changeOrders = findSimpleChangeOrders(originalOrder);
        if (changeOrders != null) {
            for (int i = 0; i < changeOrders.length; i++) {
                if (changeOrders[i].isClosingChangeOrder() && changeOrders[i].isPending()) {
                    return true;
                }
            }
        }
        return false;
    }

    @Transactional(readOnly = true)
    public OrderVersionDTO getSimpleAggregatedOrder(Long originalOrderId, LocalDateTime maxAcceptedDate) {
        OrderVersionDTO originalOrder = orderService.findSimpleOrderById(originalOrderId);
        // no reason to continue if there are no change orders
        OrderVersionDTO[] changeOrders = findSimpleChangeOrders(originalOrder);

        // Get all accepted change orders and sort them in ascending order by acceptance date
        // By doing this, the data in the LAST change order accepted will take precendence over
        // the original order and any other change order that has been accepted.
        List<OrderVersionDTO> changeOrderList = new ArrayList<OrderVersionDTO>();
        for (int i = 0; i < changeOrders.length; i++) {
            boolean includeInAggr = getIncludeInAggr(originalOrder, maxAcceptedDate, changeOrders[i]);

            if (includeInAggr) {
                changeOrderList.add(changeOrders[i]);
            }
        }
        OrderVersionDTO[] sortedChangeOrders = (OrderVersionDTO[]) changeOrderList.toArray(new OrderVersionDTO[changeOrderList.size()]);
        Arrays.sort(sortedChangeOrders, new AcceptDateComparator());

        boolean isCompletedAndHasAcceptedClosingChangeOrder = originalOrder.isCompleted() && hasAcceptedClosingChangeOrder(sortedChangeOrders);

        originalOrder.setCompletedAndHasAcceptedClosingChangeOrder(isCompletedAndHasAcceptedClosingChangeOrder);

        BigDecimal tax = originalOrder.getTax() == null ? BigDecimal.ZERO : originalOrder.getTax();
        BigDecimal shipping = originalOrder.getShipping() == null ? BigDecimal.ZERO : originalOrder.getShipping();
        BigDecimal orderItemDiscountSurchargeTotal = originalOrder.getOrderItemDiscountSurchargeTotal() == null ? BigDecimal.ZERO : originalOrder.getOrderItemDiscountSurchargeTotal();
        BigDecimal discountOrSurchargeTotal = originalOrder.getDors() == null ? BigDecimal.ZERO : originalOrder.getDors();
        BigDecimal discountOrSurchargeGrandTotal = originalOrder.getDiscountOrSurchargeTotal() == null ? BigDecimal.ZERO : originalOrder.getDiscountOrSurchargeTotal();
        List<OrderItemDTO> allChangeOrderItems = new ArrayList<OrderItemDTO>();
        BigDecimal totalValue = BigDecimal.ZERO;
        for (int i = sortedChangeOrders.length - 1; i >= 0; i--) {
            if (sortedChangeOrders[i].getOrderItemDTOs() != null && sortedChangeOrders[i].getOrderItemDTOs().size() > 0) {
                allChangeOrderItems.addAll(sortedChangeOrders[i].getOrderItemDTOs());
            }
            tax = tax.add(sortedChangeOrders[i].getTax() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getTax());
            shipping = shipping.add(sortedChangeOrders[i].getShipping() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getShipping());
            orderItemDiscountSurchargeTotal = orderItemDiscountSurchargeTotal.add(sortedChangeOrders[i].getOrderItemDiscountSurchargeTotal() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getOrderItemDiscountSurchargeTotal());
            discountOrSurchargeTotal = discountOrSurchargeTotal.add(sortedChangeOrders[i].getDors() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getDors());
            discountOrSurchargeGrandTotal = discountOrSurchargeGrandTotal.add(sortedChangeOrders[i].getDiscountOrSurchargeTotal() == null ? BigDecimal.ZERO : sortedChangeOrders[i].getDiscountOrSurchargeTotal());
            originalOrder.setComments(sortedChangeOrders[i].getComments());
        }
        originalOrder.setTax(tax);
        originalOrder.setShipping(shipping);
        if(isCompletedAndHasAcceptedClosingChangeOrder) {
            originalOrder.setOrderItemDiscountSurchargeTotal(orderItemDiscountSurchargeTotal);
            originalOrder.setDiscountOrSurcharge(discountOrSurchargeTotal);
            originalOrder.setDiscountOrSurchargeTotal(discountOrSurchargeGrandTotal);
        }

        // loop through all the orderitems in the list and aggregate them
        OrderItemDTO[] aggregatedOrderItems = originalOrder.getOrderItemDTOs().toArray(new OrderItemDTO[originalOrder.getOrderItemDTOs().size()]);
        for (int i = 0; i < allChangeOrderItems.size(); i++) {
            OrderItemDTO thisItem = (OrderItemDTO) allChangeOrderItems.get(i);
            OrderItemDTO aggregatedOrderItem = aggregatedOrderItems[thisItem.getItemIndex() - 1];

            aggregatedOrderItem.setComments(thisItem.getComments());
            aggregatedOrderItem.setSpec(thisItem.getSpec());
            aggregatedOrderItem.setJobId(thisItem.getJobId());

            BigDecimal value = aggregatedOrderItem.getValue() == null ? BigDecimal.ZERO : aggregatedOrderItem.getValue();
            BigDecimal itemTax = aggregatedOrderItem.getTax() == null ? BigDecimal.ZERO : aggregatedOrderItem.getTax();
            BigDecimal itemShipping = aggregatedOrderItem.getShipping() == null ? BigDecimal.ZERO : aggregatedOrderItem.getShipping();
            BigDecimal discountOrSurcharge = aggregatedOrderItem.getDors() == null ? BigDecimal.ZERO : aggregatedOrderItem.getDors();

            aggregatedOrderItem.setBreakouts(thisItem.getBreakouts());
            aggregatedOrderItem.setQuantity(thisItem.getQuantity());
            aggregatedOrderItem.setValue(value.add(thisItem.getValue() == null ? BigDecimal.ZERO : thisItem.getValue()));
            aggregatedOrderItem.setCompletionDate(thisItem.getCompletionDate());
            aggregatedOrderItem.setTax(itemTax.add(thisItem.getTax() == null ? BigDecimal.ZERO : thisItem.getTax()));
            aggregatedOrderItem.setShipping(itemShipping.add(thisItem.getShipping() == null ? BigDecimal.ZERO : thisItem.getShipping()));
            if(isCompletedAndHasAcceptedClosingChangeOrder) {
                aggregatedOrderItem.setDors(discountOrSurcharge.add(thisItem.getDors() == null ? BigDecimal.ZERO : thisItem.getDors()));
            }
        }

        for (int i = 0 ; i < aggregatedOrderItems.length; i++) {
            totalValue = totalValue.add(aggregatedOrderItems[i].getValue());
        }

        originalOrder.setOrderItemDTOs(Arrays.asList(aggregatedOrderItems));
        originalOrder.setOrderItemSubtotal(totalValue);
        originalOrder.setGrandTotal(totalValue.add(tax).add(shipping));
        return originalOrder;
    }

    @Transactional(readOnly = true)
    public BigDecimal getTotalPendingOrderGrandTotal(OrderVersionDTO[] changeOrders) {
        BigDecimal pendingTotal = BigDecimal.ZERO;
        boolean hasPendingOrder = false;
        for (OrderVersionDTO changeOrder : changeOrders) {
            boolean isPending = changeOrder.isPending() || changeOrder.isPendingSubmission();
            if (isPending) {
                hasPendingOrder = true;
                pendingTotal = pendingTotal.add(changeOrder.getOrderItemTotal());
                pendingTotal = pendingTotal.add(changeOrder.getTax());
                pendingTotal = pendingTotal.add(changeOrder.getShipping());
            }
        }
        return hasPendingOrder ? pendingTotal : null;
    }

    @Transactional(readOnly = true)
    public BigDecimal getExTotalPendingOrderGrandTotal(OrderVersionDTO[] changeOrders) {
        BigDecimal exPendingTotal = BigDecimal.ZERO;
        boolean hasPendingOrder = false;
        for (OrderVersionDTO changeOrder : changeOrders) {
            boolean isPending = changeOrder.isPending() || changeOrder.isPendingSubmission();
            if (isPending) {
                hasPendingOrder = true;
                exPendingTotal = exPendingTotal.add(changeOrder.getExOrderItemTotal());
                exPendingTotal = exPendingTotal.add(changeOrder.getExTax() == null ? BigDecimal.ZERO : changeOrder.getExTax());
                exPendingTotal = exPendingTotal.add(changeOrder.getExShipping() == null ? BigDecimal.ZERO : changeOrder.getExShipping());
            }
        }
        return hasPendingOrder ? exPendingTotal : null;
    }

    public List<SpecOrderItemDTO> findAggregatedOrderItemBySpec(List<Long> specIds) {
        List<SpecOrderItemDTO> specOrderItemDTOs = new ArrayList<SpecOrderItemDTO>();
        if (specIds != null && specIds.size() > 0) {
            for (Long specId : specIds) {
                Spec currentSpec = specRepository.findById(specId).orElse(null);
                if (currentSpec == null) continue;
                    if (currentSpec.getSpecReference() != null && currentSpec.getSpecReference().getProperty() != null) {
                        Property property = currentSpec.getSpecReference().getProperty();
                        boolean isAdded = false;
                        SpecOrderItemDTO specOrderItemDTO = new SpecOrderItemDTO();
                        specOrderItemDTO.setSpecId(specId);
                        if (property != null && property.getPropertyAttributeSet() != null) {
                            long projectId = (long) -1;
                            long orderId = (long) -1;
                            for (PropertyAttribute propertyAttribute : property.getPropertyAttributeSet()) {
                                if ("lastOrderItemUnitPrice_money".equalsIgnoreCase(propertyAttribute.getPropertyParam().getParamName())
                                        && (!ObjectUtils.isEmpty(propertyAttribute.getNumberValue()))) {
                                    specOrderItemDTO.setLastUnitPrice(propertyAttribute.getNumberValue());
                                    isAdded = true;
                                } else if ("lastOrderItemUnitPrice_moneyCurrencyId".equalsIgnoreCase(propertyAttribute.getPropertyParam().getParamName())
                                        && (!ObjectUtils.isEmpty(propertyAttribute.getNumberValue()))) {
                                    specOrderItemDTO.setLastOrderCurrencyId(propertyAttribute.getNumberValue().longValue());
                                    isAdded = true;
                                } else if ("lastOrderItemQuantity_num".equalsIgnoreCase(propertyAttribute.getPropertyParam().getParamName())
                                        && (!ObjectUtils.isEmpty(propertyAttribute.getNumberValue()))) {
                                    specOrderItemDTO.setLastOrderQty(propertyAttribute.getNumberValue());
                                    isAdded = true;
                                } else if ("lastOrderAcceptedDate_date".equalsIgnoreCase(propertyAttribute.getPropertyParam().getParamName())
                                        && (!ObjectUtils.isEmpty(propertyAttribute.getDateValue()))) {
                                    specOrderItemDTO.setLastOrderDate(propertyAttribute.getDateValue());
                                    isAdded = true;
                                } else if ("lastOrderName_str".equalsIgnoreCase(propertyAttribute.getPropertyParam().getParamName())
                                        && (!ObjectUtils.isEmpty(propertyAttribute.getStringValue()))) {
                                    specOrderItemDTO.setLastOrderName(propertyAttribute.getStringValue());
                                    isAdded = true;
                                } else if ("lastOrderProjectId_num".equalsIgnoreCase(propertyAttribute.getPropertyParam().getParamName())
                                        && (!ObjectUtils.isEmpty(propertyAttribute.getNumberValue()))) {
                                    projectId = propertyAttribute.getNumberValue().longValue();
                                    isAdded = true;
                                } else if ("lastOrderId_num".equalsIgnoreCase(propertyAttribute.getPropertyParam().getParamName())
                                        && (!ObjectUtils.isEmpty(propertyAttribute.getNumberValue()))) {
                                    orderId = propertyAttribute.getNumberValue().longValue();
                                    isAdded = true;
                                }
                            }
                            if (orderId > 0 && projectId > 0) {
                                specOrderItemDTO.setLastOrderExternalLink(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(projectId, orderId));
                            }
                        }
                        if (isAdded) {
                            specOrderItemDTOs.add(specOrderItemDTO);
                        }

                    }
            }
        }
        return specOrderItemDTOs;
    }

    public AggregatedCostCenterSummaryDTO getAggregatedCostCenter(Long orderId, Long projectId, Long workgroupId, Long userId) {
        AggregatedCostCenterSummaryDTO aggregatedCostCenterSummaryDTO = new AggregatedCostCenterSummaryDTO();
        aggregatedCostCenterSummaryDTO.setSummary(getAggregatedCostCenterForOrder(orderId, projectId, workgroupId, userId));
        List<Order> changeOrders = orderRepository.findByParentOrderIdOrderByIdAsc(orderId);
        if (changeOrders != null && changeOrders.size() > 0) {
            Order originalOrder = changeOrders.get(0);
            OrderVersion originalOrderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(originalOrder.getId(), true);
            OrderVersionDTO originalOrderVersionDTO = orderVersionMapper.toDTO(originalOrderVersion);
            List<OrderCostCenterDetailDTO> orderCostCenterDetailDTOs = new ArrayList<OrderCostCenterDetailDTO>();
            int i = 0;
            Map<Long, Double> quantityMap = new HashMap<>();
            for (Order order : changeOrders) {
                List<OrderCostCenterDTO> orderCostCenterDTOs = new ArrayList<>();
                if (i == 0) {
                    OrderCostCenterDetailDTO orderCostCenterDetailDTO = costCenterService.getOrderCostCenter(originalOrderVersionDTO, workgroupId, projectId, userId, false);
                    orderCostCenterDetailDTOs.add(orderCostCenterDetailDTO);
                    orderCostCenterDTOs = orderCostCenterDetailDTO.getOrderCostCenters();
                } else {
                    OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(order.getId(), true);
                    OrderVersionDTO orderVersionDTO = orderVersionMapper.toDTO(orderVersion);
                    if (orderVersionDTO.getOrderState() == null) {
                        OrderState currentOrderState = orderStateRepository.findByOrderIdAndIsCurrent(orderVersionDTO.getOrderId(), true);
                        orderVersionDTO.setOrderState(orderStateMapper.toDTO(currentOrderState));
                    }
                    if (orderVersionDTO.isDraft() && orderVersionDTO.getCreateUserId().longValue() != userId.longValue()) {
                        continue;
                    }
                    OrderCostCenterDetailDTO orderCostCenterDetailDTO = costCenterService.getOrderCostCenter(orderVersionDTO, workgroupId, projectId, userId, false);
                    orderCostCenterDetailDTOs.add(orderCostCenterDetailDTO);
                    orderCostCenterDTOs = orderCostCenterDetailDTO.getOrderCostCenters();
                }
                //Reset the quantity for change order
                if (orderCostCenterDTOs != null && orderCostCenterDTOs.size() > 0) {
                    for (OrderCostCenterDTO orderCostCenterDTO : orderCostCenterDTOs) {
                        if (i > 0) {
                            if (orderCostCenterDTO.getSpecId() != null
                                    && quantityMap.containsKey(orderCostCenterDTO.getSpecId())
                                    && quantityMap.get(orderCostCenterDTO.getSpecId()) != null) {
                                double baseQuantity = quantityMap.get(orderCostCenterDTO.getSpecId()).doubleValue();
                                double costcenterQuantity = orderCostCenterDTO.getQuantity();
                                quantityMap.put(orderCostCenterDTO.getSpecId(), costcenterQuantity);
                                orderCostCenterDTO.setQuantity(costcenterQuantity - baseQuantity);
                            }
                        } else if (orderCostCenterDTO.getSpecId() != null) {
                            quantityMap.put(orderCostCenterDTO.getSpecId(), orderCostCenterDTO.getQuantity());
                        }
                    }
                }
                i++;
            }
            aggregatedCostCenterSummaryDTO.setDetails(orderCostCenterDetailDTOs);
        }
        return aggregatedCostCenterSummaryDTO;
    }

    public OrderCostCenterDetailDTO getAggregatedCostCenterForProjectDeskoid(Long orderId, Long projectId, Long workgroupId, Long userId) {
        List<Order> orders = orderRepository.findByParentOrderIdOrderByIdAsc(orderId);
        
        // original order
        Order originalOrder = orders.get(0);
        OrderVersion originalOrderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(originalOrder.getId(), true);
        OrderVersionDTO originalOrderVersionDTO = orderVersionMapper.toDTO(originalOrderVersion);
        OrderCostCenterDetailDTO originalOrderCostCenterDetailDTO = costCenterService.getOrderCostCenter(originalOrderVersionDTO, workgroupId, projectId, userId, false);

        OrderVersionDTO[] changeOrders = findChangeOrders(originalOrderVersionDTO, workgroupId, userId);
       
        // change orders
        for (int i = 0; i < changeOrders.length; i++) {
            OrderVersionDTO changeOrderVersionDTO = changeOrders[i];
            boolean includeInAggr = getIncludeInAggr(originalOrderVersionDTO, null, changeOrders[i]);
            if (includeInAggr) {
                OrderCostCenterDetailDTO changeOrderCostCenterDetailDTO = costCenterService.getOrderCostCenter(changeOrderVersionDTO, workgroupId, projectId, userId, false);

                // append change order's cost center to original
                appendChangeToOriginal(changeOrderCostCenterDetailDTO, originalOrderCostCenterDetailDTO);
            }

        }

        // recalculate percent for order item cost center
        if (originalOrderCostCenterDetailDTO.getOrderCostCenters() != null && originalOrderCostCenterDetailDTO.getOrderCostCenters().size() > 0) {
            for (OrderCostCenterDTO originalOrderCostCenterDTO : originalOrderCostCenterDetailDTO.getOrderCostCenters()) {
                if (originalOrderCostCenterDTO.getCostCenters() != null && originalOrderCostCenterDTO.getCostCenters().size() > 0) {
                    originalOrderCostCenterDTO.setCostCenters(aggregateCostCenterAllocation(originalOrderCostCenterDTO.getCostCenters()));
                }

                if (originalOrderCostCenterDTO.getOrderItemTax() != null) {
                    originalOrderCostCenterDTO.getOrderItemTax().setCostCenters(aggregateCostCenterAllocation(originalOrderCostCenterDTO.getOrderItemTax().getCostCenters()));
                }
                if (originalOrderCostCenterDTO.getOrderItemShipping() != null) {
                    originalOrderCostCenterDTO.getOrderItemShipping().setCostCenters(aggregateCostCenterAllocation(originalOrderCostCenterDTO.getOrderItemShipping().getCostCenters()));
                }

            }
        }

        return originalOrderCostCenterDetailDTO;
    }

    private void appendChangeToOriginal(OrderCostCenterDetailDTO changeOrderCostCenterDetailDTO,
                                        OrderCostCenterDetailDTO originalOrderCostCenterDetailDTO) {
        // order
        // -- item, item tax, item shipping
        Map<Long, OrderCostCenterDTO> changeOrderItemSpecToCostCenterMap = new HashMap<>();
        for (OrderCostCenterDTO changeOrderItemCostCenterDTO : changeOrderCostCenterDetailDTO.getOrderCostCenters()) {
            if (changeOrderItemCostCenterDTO.getSpecId() != null) {
                changeOrderItemSpecToCostCenterMap.put(changeOrderItemCostCenterDTO.getSpecId(), changeOrderItemCostCenterDTO);
            }
        }

        for (OrderCostCenterDTO originalOrderItemCostCenterDTO : originalOrderCostCenterDetailDTO.getOrderCostCenters()) {
            if (originalOrderItemCostCenterDTO.getSpecId() != null) {
                OrderCostCenterDTO changeOrderItemCostCenterDTO = changeOrderItemSpecToCostCenterMap.get(originalOrderItemCostCenterDTO.getSpecId());

                if (changeOrderItemCostCenterDTO != null) {
                    originalOrderItemCostCenterDTO.setItemValue(originalOrderItemCostCenterDTO.getItemValue() + changeOrderItemCostCenterDTO.getItemValue());
                    if (originalOrderItemCostCenterDTO.getCostCenters() == null) {
                        originalOrderItemCostCenterDTO.setCostCenters(new ArrayList<>());
                    }
                    if (changeOrderItemCostCenterDTO.getCostCenters() != null) {
                        originalOrderItemCostCenterDTO.getCostCenters().addAll(changeOrderItemCostCenterDTO.getCostCenters());
                    }

                    if (originalOrderItemCostCenterDTO.getOrderItemTax() != null) {
                        originalOrderItemCostCenterDTO.getOrderItemTax().setItemValue(originalOrderItemCostCenterDTO.getOrderItemTax().getItemValue() + changeOrderItemCostCenterDTO.getOrderItemTax().getItemValue());
                        if (originalOrderItemCostCenterDTO.getOrderItemTax().getCostCenters() == null) {
                            originalOrderItemCostCenterDTO.getOrderItemTax().setCostCenters(new ArrayList<>());
                        }
                        if (changeOrderItemCostCenterDTO.getOrderItemTax().getCostCenters() != null) {
                            originalOrderItemCostCenterDTO.getOrderItemTax().getCostCenters().addAll(changeOrderItemCostCenterDTO.getOrderItemTax().getCostCenters());
                        }

                    }
                    if (originalOrderItemCostCenterDTO.getOrderItemShipping() != null) {
                        originalOrderItemCostCenterDTO.getOrderItemShipping().setItemValue(originalOrderItemCostCenterDTO.getOrderItemShipping().getItemValue() + changeOrderItemCostCenterDTO.getOrderItemShipping().getItemValue());
                        if (originalOrderItemCostCenterDTO.getOrderItemShipping().getCostCenters() == null) {
                            originalOrderItemCostCenterDTO.getOrderItemShipping().setCostCenters(new ArrayList<>());
                        }
                        if (changeOrderItemCostCenterDTO.getOrderItemShipping().getCostCenters() != null) {
                            originalOrderItemCostCenterDTO.getOrderItemShipping().getCostCenters().addAll(changeOrderItemCostCenterDTO.getOrderItemShipping().getCostCenters());
                        }

                    }
                }

            }
        }

        // order tax & shipping
        Map<String, OrderCostCenterDTO> changeOrderTaxAndShippingMap = new HashMap<>();

        for (OrderCostCenterDTO changeOrderCostCenterDTO : changeOrderCostCenterDetailDTO.getOrderCostCenters()) {
            if (changeOrderCostCenterDTO.getSpecId() == null) {
                changeOrderTaxAndShippingMap.put(changeOrderCostCenterDTO.getItemName(), changeOrderCostCenterDTO);
            }
        }

        Map<String, OrderCostCenterDTO> orderTaxAndShippingMap = new HashMap<>();
        for (OrderCostCenterDTO originalOrderCostCenterDTO : originalOrderCostCenterDetailDTO.getOrderCostCenters()) {
            if (originalOrderCostCenterDTO.getSpecId() == null) {
                orderTaxAndShippingMap.put(originalOrderCostCenterDTO.getItemName(), originalOrderCostCenterDTO);
            }
        }

        OrderCostCenterDTO orderTaxCostCenterDTO = orderTaxAndShippingMap.get("TAX");
        OrderCostCenterDTO changeOrderTaxCostCenterDTO = changeOrderTaxAndShippingMap.get("TAX");
        if (orderTaxCostCenterDTO != null && changeOrderTaxCostCenterDTO != null) {
            orderTaxCostCenterDTO.setItemValue(orderTaxCostCenterDTO.getItemValue() + changeOrderTaxCostCenterDTO.getItemValue());
            if (orderTaxCostCenterDTO.getCostCenters() == null) {
                orderTaxCostCenterDTO.setCostCenters(new ArrayList<>());
            }
            if (changeOrderTaxCostCenterDTO.getCostCenters() != null) {
                orderTaxCostCenterDTO.getCostCenters().addAll(changeOrderTaxCostCenterDTO.getCostCenters());
            }
        }

        OrderCostCenterDTO orderShippingCostCenterDTO = orderTaxAndShippingMap.get("SHIPPING");
        OrderCostCenterDTO changeOrderShippingCostCenterDTO = changeOrderTaxAndShippingMap.get("SHIPPING");
        if (orderShippingCostCenterDTO != null && changeOrderShippingCostCenterDTO != null) {
            orderShippingCostCenterDTO.setItemValue(orderShippingCostCenterDTO.getItemValue() + changeOrderShippingCostCenterDTO.getItemValue());
            if (orderShippingCostCenterDTO.getCostCenters() == null) {
                orderShippingCostCenterDTO.setCostCenters(new ArrayList<>());
            }
            if (changeOrderShippingCostCenterDTO.getCostCenters() != null) {
                orderShippingCostCenterDTO.getCostCenters().addAll(changeOrderShippingCostCenterDTO.getCostCenters());
            }
        }
        
    }

    public OrderCostCenterDetailDTO getAggregatedCostCenterForOrder(Long orderId, Long projectId, Long workgroupId, Long userId) {
        OrderVersionDTO initAggregatedOrder = orderService.findOrderVersionById(orderId, projectId, userId, workgroupId);
        OrderVersionDTO aggregatedOrderDTO = getAggregatedOrder(initAggregatedOrder, null, workgroupId, userId, true);

        OrderCostCenterDetailDTO aggregatedOrderCostCenterDetailDTO = new OrderCostCenterDetailDTO();
        List<OrderCostCenterDTO> costCenterDTOs = aggregatedOrderDTO.getCostCenters();

        aggregatedOrderCostCenterDetailDTO.setOrderId(orderId);
        aggregatedOrderCostCenterDetailDTO.setOrderName(aggregatedOrderDTO.getOrderTitle());
        aggregatedOrderCostCenterDetailDTO.setOrderExternalLink(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(
                projectId, orderId));
        aggregatedOrderCostCenterDetailDTO.setEditCostCenterExternalLink(NooshOneUrlUtil.composeEditAggregatedCenterAllocationLinkToEnterprise(
                projectId, aggregatedOrderDTO.getOrderId(), aggregatedOrderDTO.getOrderId(), aggregatedOrderDTO.getVersion()));

        if (costCenterDTOs != null && costCenterDTOs.size() > 0) {
            OrderCostCenterDTO itemTotal = new OrderCostCenterDTO();
            OrderCostCenterDTO tax = new OrderCostCenterDTO();
            OrderCostCenterDTO shipping = new OrderCostCenterDTO();

            itemTotal.setItemValue(aggregatedOrderDTO.getOrderItemSubtotal() != null ? aggregatedOrderDTO.getOrderItemSubtotal().doubleValue() : 0.0);
            tax.setItemValue(aggregatedOrderDTO.getTax() != null ? aggregatedOrderDTO.getTax().doubleValue() : 0.0);
            shipping.setItemValue(aggregatedOrderDTO.getShipping() != null ? aggregatedOrderDTO.getShipping().doubleValue() : 0.0);

            for (OrderCostCenterDTO orderCostCenterDTO : costCenterDTOs) {
                if (orderCostCenterDTO.getCostCenters() != null && orderCostCenterDTO.getCostCenters().size() > 0) {
                    if (orderCostCenterDTO.getItemName().equalsIgnoreCase(CostCenterItemName.TAX)) {
                        if (tax.getCostCenters() == null) {
                            tax.setCostCenters(new ArrayList<CostCenterAllocationDTO>());
                        }
                        tax.getCostCenters().addAll(orderCostCenterDTO.getCostCenters());
                    } else if (orderCostCenterDTO.getItemName().equalsIgnoreCase(CostCenterItemName.SHIPPING)) {
                        if (shipping.getCostCenters() == null) {
                            shipping.setCostCenters(new ArrayList<CostCenterAllocationDTO>());
                        }
                        shipping.getCostCenters().addAll(orderCostCenterDTO.getCostCenters());
                    } else {
                        if (itemTotal.getCostCenters() == null) {
                            itemTotal.setCostCenters(new ArrayList<CostCenterAllocationDTO>());
                        }
                        itemTotal.getCostCenters().addAll(orderCostCenterDTO.getCostCenters());
                    }
                }
            }
            AggregatedCostCenterComparator aggregatedCostCenterComparator = new AggregatedCostCenterComparator();
            if (itemTotal.getCostCenters() != null && itemTotal.getCostCenters().size() > 0) {
                List<CostCenterAllocationDTO> itemsCostCenters = itemTotal.getCostCenters();
                for (CostCenterAllocationDTO costCenterAllocationDTO : itemsCostCenters) {
                    List<PropertyAttributeDTO> customs = costCenterAllocationDTO.getCustomFields();
                    if (customs != null && customs.size() > 0) {
                        for (PropertyAttributeDTO dto :customs) {
                            if (noNeedCompareList.contains(dto.getParamName())) {
                                dto.setIsIgnoreCompare(true);
                            }
                        }
                    }
                }
                itemTotal.setCostCenters(aggregateCostCenterAllocation(itemTotal.getCostCenters()));
                itemTotal.getCostCenters().sort(aggregatedCostCenterComparator);
            }
            //If order item ammount is 0, then use project level, if project is null, then show 0
            if (new BigDecimal(itemTotal.getItemValue()).compareTo(BigDecimal.ZERO) == 0) {
                ProjectCostCenterDetailDTO projectCostCenterDetailDTO = costCenterService.getCostCenterForProject(projectId, workgroupId, userId);
                if (projectCostCenterDetailDTO.getCostCenters() != null && projectCostCenterDetailDTO.getCostCenters().size() > 0) {
                    itemTotal.setCostCenters(projectCostCenterDetailDTO.getCostCenters());
                    itemTotal.getCostCenters().sort(aggregatedCostCenterComparator);
                }
            }
            if (tax.getCostCenters() != null && tax.getCostCenters().size() > 0) {
                tax.setCostCenters(aggregateCostCenterAllocation(tax.getCostCenters()));
                tax.getCostCenters().sort(aggregatedCostCenterComparator);
            }
            if (shipping.getCostCenters() != null && shipping.getCostCenters().size() > 0) {
                shipping.setCostCenters(aggregateCostCenterAllocation(shipping.getCostCenters()));
                shipping.getCostCenters().sort(aggregatedCostCenterComparator);
            }
            List<OrderCostCenterDTO> orderCostCenterDTOs = new ArrayList<OrderCostCenterDTO>();
            itemTotal.setItemName(CostCenterItemName.ITEMS_TOTAL);
            tax.setItemName(CostCenterItemName.TAX);
            shipping.setItemName(CostCenterItemName.SHIPPING);
            orderCostCenterDTOs.add(itemTotal);
            orderCostCenterDTOs.add(tax);
            orderCostCenterDTOs.add(shipping);
//            for (OrderCostCenterDTO orderCostCenterDTO : orderCostCenterDTOs) {
//                if (orderCostCenterDTO.getCostCenters() != null && orderCostCenterDTO.getCostCenters().size() > 0) {
//                    for (CostCenterAllocationDTO costCenterAllocationDTO : orderCostCenterDTO.getCostCenters()) {
//                        costCenterAllocationDTO.setPercent(this.aggregateAllocationsPercent(
//                                orderCostCenterDTO.getItemValue(), costCenterAllocationDTO.getAmount()));
//                    }
//                }
//
//            }
            aggregatedOrderCostCenterDetailDTO.setOrderCostCenters(orderCostCenterDTOs);
        }

        if (!costCenterAllocationPermission.check(initAggregatedOrder, workgroupId, userId, projectId)) {
            throw new NoPermissionException("You don't have permission to view cost center for this order");
        }
        if (permissionService.checkAll(PermissionID.EDIT_ORDER_COST_CENTER, workgroupId, userId, projectId)) {
            aggregatedOrderCostCenterDetailDTO.setCanEditCostCenter(true);
        }

        if (permissionService.checkAll(PermissionID.ADD_ORDER_COST_CENTER, workgroupId, userId, projectId)) {
            aggregatedOrderCostCenterDetailDTO.setCanAddCostCenter(true);
        }
        return aggregatedOrderCostCenterDetailDTO;

    }

    public Double aggregateAllocationsPercent(Double total, Double amount) {
//        if (total != null && ccaList.size() > 0) {
//            double totalAmount = 0;
//            double percent = 0;
//            for (int i = 0; i < ccaList.size(); i++) {
//                CostCenterAllocationDTO cca = (CostCenterAllocationDTO)ccaList.get(i);
//                double thisAmount = cca.getAmount() != null ? cca.getAmount().doubleValue() : 0.0;
//                totalAmount += (thisAmount * cca.getPercent()) / 100;
//                percent = cca.getPercent();
//            }
//            //To avoid NaN Error(NKB142761), we should keep an eye on it why the total value is 0.
//            if(total > 0){
//                percent = (totalAmount * 100) / total ;
//            }
//            return percent;
//        } else {
//            return 0.0;
//        }
        if (total != null && total > 0 && amount != null) {
            return (amount * 100) / total;
        }
        return 0.0;
    }

    /*
  This value related how much should be paid, so it should be with high precision
 */
    public Double computePercentAmount(Double total, Double percent)
    {
        if (total != null ) {
            //double amount = ( total.getAmount() * this.getPercent() ) / 100;
            BigDecimal amount = (new BigDecimal(Double.toString(total.doubleValue())))
                    .multiply(new BigDecimal(Double.toString(percent))).multiply(new BigDecimal(Double.toString(0.01)));
            return amount.doubleValue();
        }

        return 0.0;
    }

    public List<CostCenterAllocationDTO> aggregateCostCenterAllocation(List<CostCenterAllocationDTO> dtos) {
        AggregatedCostCenterComparator aggregatedCostCenterComparator = new AggregatedCostCenterComparator();
        List<CostCenterAllocationDTO> aggregatedList = new ArrayList<CostCenterAllocationDTO>();
        List<CostCenterAllocationDTO> duplicate = new ArrayList<CostCenterAllocationDTO>();
        Double totalAmount = 0.0;
        if (dtos != null && dtos.size() > 0) {
            for (int i = 0; i < dtos.size(); i++) {
                for (int j = i + 1; j < dtos.size(); j++) {
                    if (aggregatedCostCenterComparator.compare(dtos.get(i), dtos.get(j)) == 0) {
                        if (dtos.get(j).getAmount() != null && dtos.get(i).getAmount() != null) {
                            dtos.get(i).setAmount(new BigDecimal(dtos.get(i).getAmount()).setScale(2, RoundingMode.HALF_UP).doubleValue()
                                    + new BigDecimal(dtos.get(j).getAmount()).setScale(2, RoundingMode.HALF_UP).doubleValue());
                        }
                        duplicate.add(dtos.get(j));
                    }
                }
                if (!duplicate.contains(dtos.get(i))) {
                    aggregatedList.add(dtos.get(i));
                    if (dtos.get(i).getAmount() != null) {
                        totalAmount += dtos.get(i).getAmount();
                    }
                }
            }

            for (CostCenterAllocationDTO costCenterAllocationDTO : aggregatedList) {
                costCenterAllocationDTO.setPercent(aggregateAllocationsPercent(totalAmount, costCenterAllocationDTO.getAmount()));
            }
        }
        return aggregatedList;
    }

    public AggregateOrderAmountDTO getAggregateOrderAmount(Long changeOrderId, Long projectId, Long currentWorkgroupId, Long currentUserId) {
        // Find the original order
        if (changeOrderId == null) {
            throw new IllegalArgumentException("Please pass the valid the order id!");
        }
        OrderVersionDTO changeOrder = orderService.findOrderVersionById(changeOrderId, projectId, currentUserId, currentWorkgroupId);
        if (changeOrder == null) {
            throw new IllegalArgumentException("Can't find the change order!");
        }
        // aggregated
        long originalOrderId = changeOrder.getOrder() != null && changeOrder.getOrder().getParentOrderId() != null
                ? changeOrder.getOrder().getParentOrderId() : (long) -1;
        OrderVersionDTO initAggregatedOrder = orderService.findOrderVersionById(originalOrderId, projectId, currentUserId, currentWorkgroupId);
        boolean isDualCurrency = initAggregatedOrder.getRate() != null && initAggregatedOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && initAggregatedOrder.getExCurrencyId() != null;
        OrderVersionDTO aggregateOrder = getAggregatedOrder(initAggregatedOrder, changeOrder.getAcceptDate() != null
                ? changeOrder.getAcceptDate().plusSeconds(1) : null, currentWorkgroupId, currentUserId);
        BigDecimal shipping = aggregateOrder.getShipping();
        BigDecimal tax = aggregateOrder.getTax();
        BigDecimal subTotal = aggregateOrder.getOrderItemSubtotal();
        BigDecimal grandTotal = aggregateOrder.getGrandTotal();
        if (changeOrder.isPending() || changeOrder.isDraft()) {
            if (changeOrder.getShipping() != null) {
                shipping = shipping.add(changeOrder.getShipping());
            }
            if (changeOrder.getTax() != null) {
                tax = tax.add(changeOrder.getTax());
            }
            subTotal = subTotal.add(changeOrder.getSubTotal());
            grandTotal = grandTotal.add(changeOrder.getGrandTotal());
        }

        AggregateOrderAmountDTO aggregateOrderAmountDTO = new AggregateOrderAmountDTO();
        aggregateOrderAmountDTO.setGrandTotal(grandTotal);
        aggregateOrderAmountDTO.setShipping(shipping);
        aggregateOrderAmountDTO.setTax(tax);
        aggregateOrderAmountDTO.setSubTotal(subTotal);
        aggregateOrderAmountDTO.setIsDualCurrency(isDualCurrency);
        //dual currency
        if (isDualCurrency) {
            BigDecimal exShipping = aggregateOrder.getExShipping() != null ? aggregateOrder.getExShipping() : BigDecimal.ZERO;
            BigDecimal exTax = aggregateOrder.getExTax() != null ? aggregateOrder.getExTax() : BigDecimal.ZERO;
            BigDecimal exSubTotal = aggregateOrder.getExOrderItemSubtotal();
            BigDecimal exGrandTotal = aggregateOrder.getExGrandTotal();
            if (changeOrder.isPending() || changeOrder.isDraft()) {
                if (changeOrder.getExShipping() != null) {
                    exShipping = exShipping.add(changeOrder.getExShipping());
                }
                if (changeOrder.getExTax() != null) {
                    exTax = exTax.add(changeOrder.getExTax());
                }
                exSubTotal = exSubTotal.add(changeOrder.getExSubTotal());
                exGrandTotal = exGrandTotal.add(changeOrder.getExGrandTotal());
            }
            aggregateOrderAmountDTO.setExGrandTotal(exGrandTotal);
            aggregateOrderAmountDTO.setExShipping(exShipping);
            aggregateOrderAmountDTO.setExTax(exTax);
            aggregateOrderAmountDTO.setExSubTotal(exSubTotal);
        }

        return aggregateOrderAmountDTO;
    }

    public OrderGeneralInfoVO getReorderDetail(Long orderId, Long projectId, Long currentWorkgroupId, Long currentUserId) throws Exception{
        OrderGeneralInfoVO orderGeneralInfoVO = new OrderGeneralInfoVO();
        OrderVersionDTO orderVersionDTO = orderService.findOrderVersionById(orderId, projectId, currentUserId, currentWorkgroupId, true);
        if (!copyOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)){
            throw new NoPermissionException("Your don't have permission to copy order!");
        }

        Long originalOrderId = orderVersionDTO.getOrderId();
        //retrive order and orderItems
        List<Long> changeOrderIds = orderRepository.findChangeOrderIds(originalOrderId);
        if (changeOrderIds != null && changeOrderIds.size() > 0) {
            orderVersionDTO = getAggregatedOrder(orderVersionDTO, null, currentWorkgroupId, currentUserId);
        }
        if (orderVersionDTO != null) {
            orderMapper.toOrderGeneralInfoVO(orderVersionDTO, orderGeneralInfoVO, true);
        }

        orderGeneralInfoVO.setOrderId(-1L);
        orderGeneralInfoVO.setReference(null);
        orderGeneralInfoVO.setTitle(null);
        orderGeneralInfoVO.setOrderTypeId(OrderTypeID.QUICK_ORDER);
        orderGeneralInfoVO.setIsSensitive(orderVersionDTO.getIsSensitive() == null ? Boolean.TRUE : orderVersionDTO.getIsSensitive());

        LocalDateTime completionDate = orderVersionDTO.getCompletionDate();
        if (completionDate != null && !DateUtil.isValidCompletionDate(completionDate)) {
            completionDate = null;
        }
        orderGeneralInfoVO.setCompletionDate(completionDate);

        Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(orderVersionDTO.getBuyerWorkgroupId());
        String percentStr = preferenceService.getString(PreferenceID.PC_TAX_PERCENTAGE, buyerPrefs,"0");
        double taxPercent = Double.parseDouble(percentStr);
        orderGeneralInfoVO.setTaxPercent(taxPercent);
        boolean itemizedTaxAndShippingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, buyerPrefs);

        //reset order items spec?
        List<OrderItemVO> orderItemVOS = orderGeneralInfoVO.getOrderItems();
        for (OrderItemVO orderItemVO : orderItemVOS) {
            orderItemVO.setId(-1L);
            orderItemVO.setEstimateItemPriceId(-1L);
            List<BreakoutVO> breakoutVOS = orderItemVO.getBreakouts();
            if (breakoutVOS != null && breakoutVOS.size() > 0) {
                for (BreakoutVO breakoutVO : breakoutVOS) {
                    breakoutVO.setId(-1L);
                }
            }

            //uofms
            List<Uofm> uofms = uofmRepository.findBySpecTypeId(orderItemVO.getSpec().getSpSpecTypeId());
            if (uofms != null && uofms.size() > 0) {
                List<UofmVO> uofmVOS = uofms.stream().map(uofmMapper::toVO).collect(Collectors.toList());
                if (uofmVOS != null && uofmVOS.size() > 0) {
                    orderItemVO.setUofms(uofmVOS);
                }
            }
            orderItemVO.setCompletionDate(completionDate);
            if (itemizedTaxAndShippingEnabled) {
                orderItemVO.setTaxPercent(BigDecimal.valueOf(taxPercent));
            }
            //clean the paper details and logics details
            orderItemVO.setPaperDetails(Collections.emptyList());
            orderItemVO.setLogisticsDetails(Collections.emptyMap());
            orderItemVO.setExternalItems(Collections.emptyList());
        }

        orderGeneralInfoVO.setContractPricingEnabled(preferenceService.check(PreferenceID.WORKGROUP_OPTION_CONTRACT_PRICING, buyerPrefs));
        boolean isDatePrefsEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT_DATE_PREFERENCES, buyerPrefs);
        boolean isComplDateRequired = !isDatePrefsEnabled || preferenceService.check(PreferenceID.PS_ORDER_COMPLETION_DATE_REQUIRED, buyerPrefs);
        orderGeneralInfoVO.setIsCompletionDateRequired(isComplDateRequired);

        boolean isEnableComplexVAT = orderVersionDTO.getIsEnableComplexVAT();
        if (isEnableComplexVAT) {
            orderGeneralInfoVO.setVatsInfo(vatService.findVATList(buyerPrefs));
        }

        return orderGeneralInfoVO;
    }
}