package com.noosh.app.service.proposal;

import com.itextpdf.layout.properties.TextAlignment;
import com.noosh.app.commons.dto.proposal.ProposalDTO;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.draw.SolidLine;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.*;
import com.itextpdf.layout.properties.UnitValue;
import com.noosh.app.commons.dto.proposal.ProposalItemDTO;
import com.noosh.app.commons.dto.proposal.SpecSummaryFieldValueDTO;
import com.noosh.app.commons.dto.proposal.WorkgroupLogoDTO;
import com.noosh.app.commons.dto.quote.QuotePriceDTO;
import com.noosh.app.commons.dto.spec.ShipmentWidgetDTO;
import com.noosh.app.commons.entity.logo.Resource;
import com.noosh.app.repository.jpa.logo.ResourceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;


import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Locale;

/**
 * <AUTHOR> Shan
 * @since 4/25/2024
 */
@Service
public class NativePdfService {


    @Autowired
    private ResourceRepository resourceRepository;

    public ByteArrayOutputStream createPDF(ProposalDTO proposal, String locale) throws IOException {

        PdfFont fontRobotoRegular = PdfFontFactory.createFont("font/Roboto/Roboto-Regular.ttf");
        PdfFont fontRobotoBold = PdfFontFactory.createFont("font/Roboto/Roboto-Bold.ttf");
        PdfFont fontRobotoMedium = PdfFontFactory.createFont("font/Roboto/Roboto-Medium.ttf");

        try (OutputStream out = new ByteArrayOutputStream()) {
            PdfWriter writer = new PdfWriter(out);
            PdfDocument pdfDoc = new PdfDocument(writer);
            pdfDoc.addEventHandler(PdfDocumentEvent.END_PAGE, new FooterEventHandler(fontRobotoRegular));

            Document document = new Document(pdfDoc, PageSize.A4);
            document.setMargins(40, 40, 80, 40);

            quotationLabel(document, fontRobotoRegular, locale);
            toLabel(document, fontRobotoBold, locale);
            toClient(document, proposal, fontRobotoRegular);
            logoAndOutsourcer(pdfDoc, document, proposal, fontRobotoRegular);

            summary(document, proposal, fontRobotoRegular, fontRobotoMedium, locale);

            if (proposal.getQuoteComments() != null) {
                Paragraph quoteCommentsParagraph = new Paragraph(proposal.getQuoteComments());
                quoteCommentsParagraph.setFontSize(8).setFont(fontRobotoRegular).setFontColor(convertCssColorToRgb("#212121")).setMarginTop(20);
                document.add(quoteCommentsParagraph);
            }

            SolidLine solidLine = new SolidLine(1);
            solidLine.setColor(convertCssColorToRgb("#F1F1F1"));
            SolidBorder solidBorder = new SolidBorder(convertCssColorToRgb("#F1F1F1"), 1);

            for (ProposalItemDTO item : proposal.getProposalItems()) {
                addSpec(document, solidLine, solidBorder, proposal, item, fontRobotoRegular, fontRobotoMedium, locale);
            }
            document.add(new LineSeparator(solidLine).setMarginTop(10).setMarginBottom(10));

            addTotal(proposal, fontRobotoRegular, fontRobotoBold, document, solidLine, solidBorder, locale);

            addTerms(proposal, fontRobotoRegular, fontRobotoBold, document, locale);

            document.close();

            return (ByteArrayOutputStream) out;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private void addTerms(ProposalDTO proposal, PdfFont fontRobotoRegular, PdfFont fontRobotoBold, Document document, String locale) {
        if (proposal.getTermTexts() != null) {
            Paragraph termsParagraph = new Paragraph(PdfI18nData.getLabel("Terms & Conditions", locale))
                    .setFontSize(8)
                    .setFontColor(convertCssColorToRgb("#757575"))
                    .setFont(fontRobotoBold)
                    .setMarginTop(10);
            Paragraph termsDetailParagraph = new Paragraph(proposal.getTermTexts())
                    .setFontSize(7)
                    .setFontColor(convertCssColorToRgb("#212121"))
                    .setFont(fontRobotoRegular);
            document.add(termsParagraph);
            document.add(termsDetailParagraph);
        }
    }

    private void addTotal(ProposalDTO proposal, PdfFont fontRobotoRegular, PdfFont fontRobotoBold, Document document, SolidLine solidLine, SolidBorder solidBorder, String locale) {
        Paragraph pricesParagraph = new Paragraph()
                .setWidth(150)
                .setFixedLeading(6)
                .setMarginLeft(385);
        pricesParagraph
                .add(new Paragraph(PdfI18nData.getLabel("Total", locale))
                        .setFont(fontRobotoBold)
                        .setFontSize(10)
                        .setWidth(60))
                .add(new Paragraph(getFormattedPrice(proposal, proposal.getTotalPrice()))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(70))
                .add(new Paragraph().setWidth(130).setHeight(1).setBorderBottom(solidBorder))
                .add(new Paragraph(proposal.getTaxLabelString())
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(60))
                .add(new Paragraph(getFormattedPrice(proposal, proposal.getTax()))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(70))
                .add(new Paragraph().setWidth(130).setHeight(1).setBorderBottom(solidBorder))
                .add(new Paragraph(PdfI18nData.getLabel("Shipping", locale))
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(60))
                .add(new Paragraph(getFormattedPrice(proposal, proposal.getShipping()))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(70))
                .add(new Paragraph().setWidth(130).setHeight(1).setBorderBottom(solidBorder))
                .add(new Paragraph(PdfI18nData.getLabel("Grand Total", locale))
                        .setFont(fontRobotoBold)
                        .setFontSize(10)
                        .setWidth(60))
                .add(new Paragraph(getFormattedPrice(proposal, proposal.getGrandTotal()))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(70))
                .add(new LineSeparator(solidLine))
                .add(new Paragraph().setWidth(130).setHeight(1).setBorderBottom(solidBorder));

        document.add(pricesParagraph);
    }

    private void summary(Document document, ProposalDTO proposal, PdfFont fontRobotoRegular, PdfFont fontRobotoMedium, String locale) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM dd, yyyy");
        Paragraph summaryParagraph = new Paragraph().setWidth(350).setMarginTop(5).setMarginBottom(5);
        summaryParagraph.add(summaryLabel(PdfI18nData.getLabel("Quotation ID", locale), fontRobotoMedium));
        summaryParagraph.add(summaryValue(proposal.getProjectNumber(), fontRobotoRegular));
        summaryParagraph.add(summaryLabel(PdfI18nData.getLabel("Project Name", locale), fontRobotoMedium));
        summaryParagraph.add(summaryValue(proposal.getProjectName(), fontRobotoRegular));
        summaryParagraph.add(summaryLabel(PdfI18nData.getLabel("Prepared Date", locale), fontRobotoMedium));
        summaryParagraph.add(summaryValue(proposal.getPrepareDate() == null ? "" : formatter.format(proposal.getPrepareDate()), fontRobotoRegular));
        summaryParagraph.add(summaryLabel(PdfI18nData.getLabel("Expiration Date", locale), fontRobotoMedium));
        summaryParagraph.add(summaryValue(proposal.getExpirationDate() == null ? "" : formatter.format(proposal.getExpirationDate()), fontRobotoRegular));
        summaryParagraph.add(summaryLabel(PdfI18nData.getLabel("Presented By", locale), fontRobotoMedium));
        summaryParagraph.add(summaryValue(proposal.getProjectCustomAttributes() == null ? "" : String.valueOf(proposal.getProjectCustomAttributes().getOrDefault("SALES_PERSON_str", "")), fontRobotoRegular));
        document.add(summaryParagraph);
    }

    private void addSpec(Document document, SolidLine solidLine, SolidBorder solidBorder,
                         ProposalDTO proposal, ProposalItemDTO item, PdfFont fontRobotoRegular, PdfFont fontRobotoMedium, String locale) {
        addSpecTitle(document, item, fontRobotoMedium, locale);
        addSpecSummary(document, solidLine, item, fontRobotoRegular, fontRobotoMedium, locale);
        addSpecShipTo(document, solidLine, proposal, item, fontRobotoRegular, fontRobotoMedium, locale);
        addSpecPricingInfo(document, solidBorder, proposal, item, fontRobotoRegular, fontRobotoMedium, locale);
    }

    private void addSpecShipTo(Document document, SolidLine solidLine, ProposalDTO proposal, ProposalItemDTO item,
                               PdfFont fontRobotoRegular, PdfFont fontRobotoMedium, String locale) {
        if (item.getShipTos() != null && !item.getShipTos().isEmpty()) {
            Paragraph shipToLabelParagraph = new Paragraph()
                    .setWidth(95)
                    .add(new Paragraph(PdfI18nData.getLabel("Ship To", locale))
                            .setUnderline()
                            .setFontSize(10).setFont(fontRobotoMedium))
                    .setMarginLeft(9);

            Paragraph shipToParagraph = new Paragraph();
            for (ShipmentWidgetDTO shipTo : item.getShipTos()) {
                StringBuilder sb = new StringBuilder();
                sb.append(PdfI18nData.getLabel("Quantity", locale) + ": ");
                sb.append(shipTo.getQuantity() == null ? 0 : shipTo.getQuantity());
                sb.append('\n');

                if (shipTo.getFirstName() != null && !shipTo.getFirstName().isEmpty()) {
                    sb.append(shipTo.getFirstName());
                    sb.append(' ');
                }
                if (shipTo.getLastName() != null && !shipTo.getLastName().isEmpty()) {
                    sb.append(shipTo.getLastName());
                }
                if (shipTo.getFirstName() != null || shipTo.getLastName() != null) {
                    sb.append('\n');
                }

                if (shipTo.getCompanyName() != null && !shipTo.getCompanyName().isEmpty()) {
                    sb.append(shipTo.getCompanyName());
                    sb.append('\n');
                }

                if (shipTo.getLine1() != null && !shipTo.getLine1().isEmpty()) {
                    sb.append(shipTo.getLine1());
                    sb.append(' ');
                }
                if (shipTo.getLine2() != null && !shipTo.getLine2().isEmpty()) {
                    sb.append(shipTo.getLine2());
                    sb.append(' ');
                }
                if (shipTo.getLine3() != null && !shipTo.getLine3().isEmpty()) {
                    sb.append(shipTo.getLine3());
                }
                if (shipTo.getLine1() != null || shipTo.getLine2() != null || shipTo.getLine3() != null) {
                    sb.append(shipTo.getCompanyName());
                    sb.append('\n');
                }

                if (shipTo.getCity() != null && !shipTo.getCity().isEmpty()) {
                    sb.append(shipTo.getCity());
                    sb.append(',');
                    sb.append(' ');
                }
                if (shipTo.getState() != null && !shipTo.getState().isEmpty()) {
                    sb.append(shipTo.getState());
                    sb.append(' ');
                }
                if (shipTo.getPostal() != null && !shipTo.getPostal().isEmpty()) {
                    sb.append(shipTo.getPostal());
                }

                shipToParagraph
                        .add(new Paragraph(sb.toString())
                                .setFont(fontRobotoMedium)
                                .setFontSize(10));
            }

            UnitValue[] percentArray = UnitValue.createPointArray(new float[]{100, 250});
            Table table = new Table(percentArray);
            Cell cell1 = new Cell().add(shipToLabelParagraph).setBorder(Border.NO_BORDER);
            Cell cell2 = new Cell().add(shipToParagraph).setBorder(Border.NO_BORDER);
            table.addCell(cell1);
            table.addCell(cell2);
            table.setBorder(Border.NO_BORDER);
            document.add(table);

            document.add(new LineSeparator(solidLine).setMarginTop(10).setMarginBottom(10));
        }

    }

    private void addSpecPricingInfo(Document document, SolidBorder solidBorder, ProposalDTO proposal,
                                    ProposalItemDTO item, PdfFont fontRobotoRegular, PdfFont fontRobotoMedium, String locale) {
        Paragraph pricingLabelParagraph = new Paragraph()
                .setWidth(95)
                .add(new Paragraph(PdfI18nData.getLabel("Pricing\nInformation", locale))
                        .setUnderline()
                        .setFontSize(10).setFont(fontRobotoMedium))
                .setMarginLeft(9);

        int labelLen = 55;
        int valueLen = 70;
        int size = 0;
        for (QuotePriceDTO quotePrice : item.getQuotePrices()) {
            if (quotePrice.getIsVisibleToBuyer()) {
                size++;
            }
        }
        int width = labelLen + valueLen * size;
        Paragraph pricesParagraph = new Paragraph()
                .setWidth(width).setFixedLeading(6);
        pricesParagraph.add(new Paragraph(PdfI18nData.getLabel("Quantity", locale))
                .setFont(fontRobotoMedium)
                .setFontSize(10)
                .setWidth(labelLen));

        for (QuotePriceDTO quotePrice : item.getQuotePrices()) {
            if (quotePrice.getIsVisibleToBuyer()) {
                pricesParagraph.add(new Paragraph(String.valueOf(quotePrice.getQuantity()))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(valueLen));
            }
        }
        pricesParagraph.add(new Paragraph().setWidth(width).setHeight(1).setBorderBottom(solidBorder));

        pricesParagraph.add(new Paragraph(PdfI18nData.getLabel("Item Price", locale))
                .setFont(fontRobotoMedium)
                .setFontSize(10)
                .setWidth(labelLen));
        for (QuotePriceDTO quotePrice : item.getQuotePrices()) {
            if (quotePrice.getIsVisibleToBuyer()) {
                pricesParagraph.add(new Paragraph(getFormattedPrice(proposal, quotePrice.getPrice()))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(valueLen));
            }
        }
        pricesParagraph.add(new Paragraph().setWidth(width).setHeight(1).setBorderBottom(solidBorder));

        pricesParagraph.add(new Paragraph(PdfI18nData.getLabel("Unit Price", locale))
                .setFont(fontRobotoMedium)
                .setFontSize(10)
                .setWidth(labelLen));
        for (QuotePriceDTO quotePrice : item.getQuotePrices()) {
            if (quotePrice.getIsVisibleToBuyer()) {
                pricesParagraph.add(new Paragraph(getFormattedPrice(proposal, quotePrice.getUnitPrice()))
                        .setTextAlignment(TextAlignment.RIGHT)
                        .setFont(fontRobotoRegular)
                        .setFontSize(10)
                        .setWidth(valueLen));
            }
        }
        pricesParagraph.add(new Paragraph().setWidth(width).setHeight(1).setBorderBottom(solidBorder));


        UnitValue[] percentArray = UnitValue.createPointArray(new float[]{100, 150});
        Table table = new Table(percentArray);
        Cell cell1 = new Cell().add(pricingLabelParagraph).setBorder(Border.NO_BORDER);
        Cell cell2 = new Cell().add(pricesParagraph).setBorder(Border.NO_BORDER);
        table.addCell(cell1);
        table.addCell(cell2);
        table.setBorder(Border.NO_BORDER);
        document.add(table);
    }

    private void addSpecSummary(Document document, SolidLine solidLine, ProposalItemDTO item, PdfFont fontRobotoRegular, PdfFont fontRobotoMedium, String locale) {
        Paragraph specificLabelParagraph = new Paragraph(PdfI18nData.getLabel("Specification", locale))
                .setUnderline()
                .setFontSize(10).setFont(fontRobotoMedium)
                .setMarginLeft(10);
        document.add(specificLabelParagraph);

        Paragraph specificParagraph = new Paragraph()
                .setMarginLeft(10);
        for (SpecSummaryFieldValueDTO specSummaryFieldValue : item.getSpecSummaryFieldValues()) {
            specificParagraph
                    .add(new Paragraph(specSummaryFieldValue.getName())
                            .setFont(fontRobotoMedium)
                            .setFontSize(10)
                            .setWidth(100))
                    .add(new Paragraph(specSummaryFieldValue.getStringValue() == null ? "" : specSummaryFieldValue.getStringValue())
                            .setFont(fontRobotoRegular)
                            .setFontSize(10)
                            .setWidth(150));
        }
        document.add(specificParagraph);
        document.add(new LineSeparator(solidLine).setMarginTop(10).setMarginBottom(10));
    }

    private void addSpecTitle(Document document, ProposalItemDTO item, PdfFont fontRobotoMedium, String locale) {
        Paragraph titleParagraph = new Paragraph();
        titleParagraph.setPaddings(5, 0, 5, 10)
                .setMarginTop(20)
                .setBackgroundColor(convertCssColorToRgb("#F1F1F1"));
        titleParagraph.add(new Paragraph(item.getItemIndex() + ". " + item.getSpecName())
                .setFontSize(10).setFont(fontRobotoMedium).setWidth(270));
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.CANADA);
        titleParagraph.add(new Paragraph(PdfI18nData.getLabel("Completion date", locale) + ": " + (item.getCompletionDate() == null ? "" : dateFormat.format(item.getCompletionDate())))
                .setFontSize(10).setFont(fontRobotoMedium).setWidth(220).setTextAlignment(TextAlignment.RIGHT));
        document.add(titleParagraph);
    }

    private String getFormattedPrice(ProposalDTO proposal, BigDecimal price) {
        BigDecimal number = price == null ? BigDecimal.ZERO : price;
        BigDecimal rounded = number.setScale(2, RoundingMode.HALF_UP);
        return proposal.getCurrencySymbol() + rounded;
    }

    private void logoAndOutsourcer(PdfDocument pdfDoc, Document document, ProposalDTO proposal, PdfFont fontRobotoRegular) {
        Rectangle pageSize = pdfDoc.getFirstPage().getPageSize();
        float x = pageSize.getRight() - 130 - 50;
        float y = pageSize.getTop() - 60;

        if (proposal.isHasLogo()) {
            WorkgroupLogoDTO logo = proposal.getLogo();
            Resource resource = resourceRepository.findByPath(logo.getResourcePath());
            byte[] content = resource.getContent();
            Image image = new Image(ImageDataFactory.create(content));
            image.scaleAbsolute(130, 30);

            x = pageSize.getRight() - image.getImageScaledWidth() - 50;
            y = pageSize.getTop() - 60;
            image.setFixedPosition(x, y);
            document.add(image);
        } else {
            byte[] content = readImageFromJar("images/logo.png");
            if (content == null) {
                content = new byte[0];
            }
            Image image = new Image(ImageDataFactory.create(content));
            image.setHeight(30);

            x = pageSize.getRight() - image.getImageScaledWidth() - 50;
            y = pageSize.getTop() - 60;
            image.setFixedPosition(x, y);
            document.add(image);
        }

        Paragraph toOutsourcer = toOutsourcer(proposal, fontRobotoRegular);
        toOutsourcer.setFixedPosition(x, y - 50, 200);

        document.add(toOutsourcer);
    }

    private byte[] readImageFromJar(String imagePath) {
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(imagePath);
        byte[] imageBytes = null;
        if (inputStream != null) {
            try {
                imageBytes = inputStream.readAllBytes();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return imageBytes;
    }

    private void quotationLabel(Document document, PdfFont fontRobotoRegular, String locale) {
        document.add(new Paragraph(PdfI18nData.getLabel("Quotation", locale))
                .setFontSize(20)
                .setFont(fontRobotoRegular)
                .setFontColor(convertCssColorToRgb("#702BE8")));
    }

    private void toLabel(Document document, PdfFont fontRobotoBold, String locale) {
        document.add(new Paragraph(PdfI18nData.getLabel("To", locale))
                .setFontSize(10)
                .setFont(fontRobotoBold)
                .setFontColor(convertCssColorToRgb("#757575"))
                .setFixedLeading(16));
    }

    private Paragraph toOutsourcer(ProposalDTO proposal, PdfFont fontRobotoRegular) {
        StringBuilder sb = new StringBuilder();
        sb.append(proposal.getOutsourcerName());

        if (proposal.getOutsourcerAddress().getLine1() != null
                || proposal.getOutsourcerAddress().getLine2() != null
                || proposal.getOutsourcerAddress().getLine3() != null) {
            sb.append("\n");
            sb.append((proposal.getOutsourcerAddress().getLine1() == null ? "" : proposal.getOutsourcerAddress().getLine1() + " ")
                    + (proposal.getOutsourcerAddress().getLine2() == null ? "" : proposal.getOutsourcerAddress().getLine2() + " ")
                    + (proposal.getOutsourcerAddress().getLine3() == null ? "" : proposal.getOutsourcerAddress().getLine3()));
        }

        if (proposal.getOutsourcerAddress().getCountryName() != null
                || proposal.getOutsourcerAddress().getCity() != null
                || proposal.getOutsourcerAddress().getState() != null
                || proposal.getOutsourcerAddress().getPostal() != null) {
            sb.append("\n");
            sb.append((proposal.getOutsourcerAddress().getCity() == null ? "" : proposal.getOutsourcerAddress().getCity() + ", ")
                    + (proposal.getOutsourcerAddress().getState() == null ? "" : proposal.getOutsourcerAddress().getState() + " ")
                    + (proposal.getOutsourcerAddress().getPostal() == null ? "" : proposal.getOutsourcerAddress().getPostal() + " ")
                    + (proposal.getOutsourcerAddress().getCountryName() == null ? "" : proposal.getOutsourcerAddress().getCountryName()));
        }

        return new Paragraph(sb.toString())
                .setFontSize(8)
                .setFont(fontRobotoRegular)
                .setFontColor(convertCssColorToRgb("#757575"))
                .setFixedLeading(12);
    }

    private void toClient(Document document, ProposalDTO proposal, PdfFont fontRobotoRegular) {
        StringBuilder sb = new StringBuilder();
        sb.append(proposal.getClientName());

        if (proposal.getClientAddress().getLine1() != null ||
                proposal.getClientAddress().getLine2() != null ||
                proposal.getClientAddress().getLine3() != null) {
            sb.append("\n");
            sb.append((proposal.getClientAddress().getLine1() == null ? "" : proposal.getClientAddress().getLine1() + " ")
                    + (proposal.getClientAddress().getLine2() == null ? "" : proposal.getClientAddress().getLine2() + " ")
                    + (proposal.getClientAddress().getLine3() == null ? "" : proposal.getClientAddress().getLine3()));
        }

        if (proposal.getClientAddress().getCountryName() != null ||
                proposal.getClientAddress().getCity() != null ||
                proposal.getClientAddress().getState() != null ||
                proposal.getClientAddress().getPostal() != null) {
            sb.append("\n");
            sb.append((proposal.getClientAddress().getCity() == null ? "" : proposal.getClientAddress().getCity() + ", ")
                    + (proposal.getClientAddress().getState() == null ? "" : proposal.getClientAddress().getState() + " ")
                    + (proposal.getClientAddress().getPostal() == null ? "" : proposal.getClientAddress().getPostal() + " ")
                    + (proposal.getClientAddress().getCountryName() == null ? "" : proposal.getClientAddress().getCountryName()));
        }

        document.add(new Paragraph(sb.toString())
                .setFontSize(10)
                .setFont(fontRobotoRegular)
                .setFontColor(convertCssColorToRgb("#757575"))
                .setFixedLeading(14));
    }

    private Paragraph summaryLabel(String value, PdfFont fontRobotoMedium) {
        return new Paragraph(value)
                .setFontSize(10)
                .setFont(fontRobotoMedium)
                .setFontColor(convertCssColorToRgb("#757575"))
                .setFixedLeading(8)
                .setWidth(100);
    }

    private Paragraph summaryValue(String value, PdfFont fontRobotoRegular) {
        return new Paragraph(value)
                .setFontSize(10)
                .setFont(fontRobotoRegular)
                .setFontColor(convertCssColorToRgb("#212121"))
                .setFixedLeading(8)
                .setWidth(235);
    }

    private DeviceRgb convertCssColorToRgb(String cssColor) {
        int r = Integer.parseInt(cssColor.substring(1, 3), 16);
        int g = Integer.parseInt(cssColor.substring(3, 5), 16);
        int b = Integer.parseInt(cssColor.substring(5, 7), 16);
        return new DeviceRgb(r, g, b);
    }

}
