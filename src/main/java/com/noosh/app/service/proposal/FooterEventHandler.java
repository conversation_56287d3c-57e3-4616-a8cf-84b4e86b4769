package com.noosh.app.service.proposal;

import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;

import java.io.IOException;

public class FooterEventHandler implements IEventHandler {

    private PdfFont fontRobotoRegular;

    public FooterEventHandler(PdfFont fontRobotoRegular) {
        this.fontRobotoRegular = fontRobotoRegular;
    }

    @Override
    public void handleEvent(Event event) {
        PdfDocumentEvent docEvent = (PdfDocumentEvent) event;
        PdfPage page = docEvent.getPage();
        int pageNum = docEvent.getDocument().getPageNumber(page);
        int totalPages = docEvent.getDocument().getNumberOfPages();

        PdfCanvas canvas = new PdfCanvas(page);

        // Creates header text content
        canvas.beginText();

        canvas.setFontAndSize(fontRobotoRegular, 8);
        canvas.setFillColor(convertCssColorToRgb("#757575"));
        canvas.moveText(515, 50);
        canvas.showText(String.format("Page %d of %d", pageNum, totalPages));

        canvas.endText();
        canvas.stroke();

        canvas.release();
    }

    private static DeviceRgb convertCssColorToRgb(String cssColor) {
        int r = Integer.parseInt(cssColor.substring(1, 3), 16);
        int g = Integer.parseInt(cssColor.substring(3, 5), 16);
        int b = Integer.parseInt(cssColor.substring(5, 7), 16);
        return new DeviceRgb(r, g, b);
    }

}