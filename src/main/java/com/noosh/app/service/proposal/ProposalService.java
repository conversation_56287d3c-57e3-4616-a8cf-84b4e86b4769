package com.noosh.app.service.proposal;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.proposal.*;
import com.noosh.app.commons.dto.quote.QuotePriceDTO;
import com.noosh.app.commons.dto.spec.ShipmentWidgetDTO;
import com.noosh.app.commons.dto.spec.SpecItem;
import com.noosh.app.commons.dto.userfield.UserFieldReqDTO;
import com.noosh.app.commons.entity.breakout.Breakout;
import com.noosh.app.commons.entity.breakout.BreakoutType;
import com.noosh.app.commons.entity.currency.NCurrency;
import com.noosh.app.commons.entity.logo.Resource;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyParam;
import com.noosh.app.commons.entity.proposal.ProposalBranding;
import com.noosh.app.commons.entity.proposal.ProposalItem;
import com.noosh.app.commons.entity.proposal.ProposalSpecField;
import com.noosh.app.commons.entity.proposal.ProposalSpecSummaryItem;
import com.noosh.app.commons.entity.quote.Quote;
import com.noosh.app.commons.entity.quote.QuoteItem;
import com.noosh.app.commons.entity.quote.QuotePrice;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.term.Terms;
import com.noosh.app.commons.vo.userfield.UserFieldDefsWithValuesVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.feign.WorkgroupOpenFeignClient;
import com.noosh.app.mapper.breakout.BreakoutMapper;
import com.noosh.app.repository.jpa.breakout.BreakoutRepository;
import com.noosh.app.repository.jpa.currency.CurrencyRepository;
import com.noosh.app.repository.jpa.logo.ResourceRepository;
import com.noosh.app.repository.jpa.property.PropertyParamRepository;
import com.noosh.app.repository.jpa.property.PropertyRepository;
import com.noosh.app.repository.jpa.proposal.*;
import com.noosh.app.repository.jpa.quote.QuoteRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.jpa.spec.SpecRepository;
import com.noosh.app.repository.jpa.term.TermsRepository;
import com.noosh.app.repository.mybatis.category.CategoryMyBatisMapper;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.repository.mybatis.proposal.ProposalMyBatisMapper;
import com.noosh.app.service.apijob.WebClientService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.quote.QuoteItemService;
import com.noosh.app.service.quote.QuotePriceService;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.Money;
import com.noosh.app.service.util.PDFTemplateUtil;
import com.noosh.app.service.util.Util;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 12/14/2021
 */
@Service
public class ProposalService {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    private static final DateTimeFormatter REACT_DATE_TIME_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd'T'HH:mm:ss[.SSS][X]"); // support both 'yyyy-MM-dd'T'HH:mm:ss' and "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'" format
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.CANADA);

    @Autowired
    private ProposalMyBatisMapper proposalMyBatisMapper;
    @Autowired
    private ProposalItemRepository proposalItemRepository;
    @Autowired
    private SpecRepository specRepository;
    @Autowired
    private ResourceRepository resourceRepository;
    @Autowired
    private PropertyMyBatisMapper propertyMyBatisMapper;
    @Autowired
    private QuoteItemService quoteItemService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private BreakoutRepository breakoutRepository;
    @Autowired
    private QuoteRepository quoteRepository;
    @Autowired
    private CurrencyRepository currencyRepository;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private I18NUtils i18NUtils;
    @Autowired
    private BreakoutMapper breakoutMapper;
    @Autowired
    private TermsRepository termsRepository;
    @Autowired
    private CategoryMyBatisMapper categoryMyBatisMapper;
    @Autowired
    private QuotePriceService quotePriceService;
    @Autowired
    private ProposalItemService proposalItemService;
    @Autowired
    private NativePdfService nativePdfService;
    @Autowired
    private ProposalSpecFieldRepository proposalSpecFieldRepository;
    @Autowired
    private ProposalSpecSummaryRepository proposalSpecSummaryRepository;
    @Autowired
    private ProposalSpecSummaryItemRepository proposalSpecSummaryItemRepository;
    @Autowired
    private PropertyParamRepository propertyParamRepository;
    @Autowired
    private WorkgroupOpenFeignClient workgroupOpenFeignClient;
    @Autowired
    private PropertyRepository propertyRepository;
    @Autowired
    private ProposalBrandingRepository proposalBrandingRepository;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private WebClientService webClientService;

    @Value("${enterprise.internalDomain}")
    private String nooshApiEnterpriseHost;
    @Value("${enterprise.port}")
    private String nooshApiEnterprisePort;

    public ByteArrayOutputStream exportProposal(Long userId, Long workgroupId, Long projectId, Long proposalId, String template, String locale) throws IOException {
        ProposalDTO proposal = findProposalDTO(userId, workgroupId, projectId, proposalId);
        if (template.equalsIgnoreCase("proposal-hhg-native")) {
            return nativePdfService.createPDF(proposal, locale);
        } else {
            return PDFTemplateUtil.createPDF(proposal, template + ".ftl");
        }
    }

    public ProposalDTO findProposalDTO(Long userId, Long workgroupId, Long projectId, Long proposalId) {
        Workgroup workgroup = workgroupRepository.findById(workgroupId).get();
        boolean isHH = workgroup != null && workgroup.getPortal() != null &&
                (workgroup.getPortal().equals("hh-fedex") || workgroup.getPortal().equals("hh-sap"));

        ProjectDTO project = projectService.findProjectById(projectId);
        if (project == null) {
            throw new NotFoundException("Project " + projectId + " was not found");
        }

        List<Long> propertyIds = new ArrayList<>();
        propertyIds.add(project.getCustomPropertyId());
        Map<Long, Map<String, Object>> customAttributeMap = workgroupOpenFeignClient.findCustomAttributesByPropertyIds(propertyIds);
        project.setCustomAttributes(customAttributeMap.get(project.getCustomPropertyId()));

        ProjectDTO ownerProject = projectService.getOwnerProject(project);
        ProposalDTO proposal = proposalMyBatisMapper.findProposal(projectId, proposalId);
        if (proposal == null) {
            throw new NotFoundException("Proposal " + proposalId + " was not found");
        }

        proposal.setProjectName(project.getName());
        proposal.setProjectNumber(project.getProjectNumber());
        proposal.setProjectCustomAttributes(project.getCustomAttributes());

        Quote quote = quoteRepository.findById(proposal.getQuoteId()).orElse(null);

        UserFieldReqDTO quoteReqDTO = new UserFieldReqDTO();
        quoteReqDTO.setFieldClassId(CustomFieldClassID.QUOTE);
        quoteReqDTO.setWorkgroupId(project.getPortalWorkgroupId());
        List<Long> quotePropertyIds = new ArrayList<>();
        quotePropertyIds.add(quote.getCustomPropertyId());
        quoteReqDTO.setPropertyIds(quotePropertyIds);
        quoteReqDTO.setIsSupplierView(false);
        UserFieldDefsWithValuesVO quoteUserFieldDefsWithValuesVO = workgroupOpenFeignClient.findUserFieldDefsWithValues(quoteReqDTO);
        proposal.setQuoteCustomPropertyId(quote.getCustomPropertyId());
        proposal.setQuoteUserFields(quoteUserFieldDefsWithValuesVO);

        proposal.setQuoteComments(quote.getComments());

        if (proposal.getOutsourcerAddress() != null) {
            String countryName = i18NUtils.getMessage(proposal.getOutsourcerAddress().getCountryNameStrId());
            proposal.getOutsourcerAddress().setCountryName(countryName);
        }
        if (proposal.getClientAddress() != null) {
            String countryName = i18NUtils.getMessage(proposal.getClientAddress().getCountryNameStrId());
            proposal.getClientAddress().setCountryName(countryName);
        }

        if (proposal.getLogoId() != null && proposal.getLogoId() > 0) {
            proposal.setHasLog(true);
        } else {
            proposal.setHasLog(false);
        }

        proposal.setShowSupplierInfo(quote.isSupplierVisible());
        proposal.setIsCreatedAtMarginOn(quote.getIsCreatedAtMarginOn());

        List<ProposalItem> proposalItems = proposalItemRepository.findAllByProposalId(proposalId);
        if (proposalItems != null && proposalItems.size() > 0) {

            List<Long> proposalItemIds = new ArrayList<>();
            proposalItems.stream().forEach(proposalItem -> {
                proposalItemIds.add(proposalItem.getId());
            });
            // category
            List<CategoryDTO> categories = categoryMyBatisMapper.findCategoryByObjects(userId, workgroupId, proposalItemIds);
            // Proposal Aggregated
            boolean showAggregation = proposal.getIsAggregationEnable() && isAggregateable(categories, proposalItems);
            if (showAggregation) {
                categories = proposalAggregated(categories, proposalItems, proposal);
                proposal.setShowAggregation(showAggregation);
            }
            proposal.setCategories(categories);

            NCurrency currency = currencyRepository.findById(quote.getTaxCurrencyId()).orElse(null);
            String symbol = Money.getCurrencySymbol(currency.getCurrency());
            proposal.setCurrencySymbol(symbol);

            List<Long> quoteItemPropertyIds = new ArrayList<>();

            List<ProposalItemDTO> proposalItemDTOS = new ArrayList<>();
            BigDecimal totalPrice = BigDecimal.ZERO;
            String sustainabilityResult = null;
            JSONArray sustainabilityJsonArray = null;
            // proposal item
            for (ProposalItem proposalItem : proposalItems) {
                ProposalItemDTO proposalItemDTO = new ProposalItemDTO();
                proposalItemDTO.setItemIndex(proposalItem.getItemIndex());
                proposalItemDTO.setComments(proposalItem.getComments());

                ProposalSustainabilityDTO proposalSustainabilityDTO = new ProposalSustainabilityDTO();

                QuoteItem quoteItem = proposalItem.getQuoteItem();
                quoteItemPropertyIds.add(quoteItem.getCustomPropertyId());
                proposalItemDTO.setQuoteItemCustomPropertyId(quoteItem.getCustomPropertyId());
                proposalItemDTO.setCompletionDate(quoteItem.getCompletionDate());
                proposalItemDTO.setQuoteItemComments(quoteItem.getComments());

                List<ShipmentWidgetDTO> shipTos = proposalMyBatisMapper.findShipTos(quoteItem.getJobId());
                proposalItemDTO.setShipTos(shipTos);

                // specs
                Spec spec = specRepository.findById(quoteItem.getSpecId()).orElse(null);
                proposalItemDTO.setSpecName(spec.getSpecName());
                proposalSustainabilityDTO.setSpecName(spec.getSpecName());

                proposalItemDTO.setSpecSummaryFieldValues(prepareSpecSummary(proposalItem, spec.getPrPropertyId()));

                // category
                for(CategoryDTO category : categories) {
                    if (category.getObjectId() == proposalItem.getId().longValue()) {
                        proposalItemDTO.setCategoryName(category.getName());
                    }
                }

                // quantities
                List<QuotePriceDTO> quotePrices = new ArrayList<>();
                // aggr quote prices
                List<QuotePrice> aggrQuotePrices = quoteItemService.getQuotePricesForQuantity(spec, quoteItem, ownerProject, true, true, true);
                for (QuotePrice aggrQuotePrice : aggrQuotePrices) {
                    QuotePriceDTO quotePriceDTO = new QuotePriceDTO();
                    quotePriceDTO.setQuantity(aggrQuotePrice.getQuantity());
                    quotePriceDTO.setIsVisibleToBuyer(aggrQuotePrice.isVisibleToBuyer());
                    quotePriceDTO.setPrice(aggrQuotePrice.getPrice());
                    if (aggrQuotePrice.getQuantity() != null) {
                        quotePriceDTO.setUnitPrice(aggrQuotePrice.getPrice().divide(BigDecimal.valueOf(aggrQuotePrice.getQuantity()), 3, BigDecimal.ROUND_HALF_UP));
                    }
                    quotePriceDTO.setPreMarkup(aggrQuotePrice.getPreMarkup());
                    quotePriceDTO.setMarkupPercent(aggrQuotePrice.getMarkupPercent());
                    quotePriceDTO.setMarginPercent(aggrQuotePrice.getMarginPercent());
                    quotePriceDTO.setMarkupFixed(aggrQuotePrice.getMarkupFixed());
                    quotePriceDTO.setTax(aggrQuotePrice.getTax());
                    quotePriceDTO.setShipping(aggrQuotePrice.getShipping());
                    quotePriceDTO.setSupplierName(quoteItemService.findSupplierName(aggrQuotePrice, project.isOutsourcerProject()));

                    if (quoteItem.getChosenQuotePriceId() != null && quoteItem.getChosenQuotePriceId().longValue() == aggrQuotePrice.getId()) {
                        totalPrice = totalPrice.add(aggrQuotePrice.getPrice());
                        quotePriceDTO.setIsChoosePrice(true);

                        if (isHH) {
                            // find propertyId from proposal - quote - quote item - source - estiamte/order item - propertyId
                            Long sourcePropertyId = null;
                            if (aggrQuotePrice.getEstimateItemPriceId() != null) {
                                sourcePropertyId = proposalMyBatisMapper.findPropertyIdByEstimateItemPriceId(aggrQuotePrice.getEstimateItemPriceId());
                            } else if (aggrQuotePrice.getOrderItemId() != null) {
                                sourcePropertyId = proposalMyBatisMapper.findPropertyIdByOrderItemId(aggrQuotePrice.getOrderItemId());
                            }

                            BigDecimal material = null;
                            BigDecimal estimatedMaterial = null;
                            BigDecimal estimatedProduction = null;
                            BigDecimal logistics = null;
                            BigDecimal estimatedLogistics = null;

                            // load sustainability data from quote item source
                            if (sourcePropertyId != null) {
                                List<Long> sourcePropertyIds = new ArrayList<>();
                                sourcePropertyIds.add(sourcePropertyId);
                                Map<Long, Map<String, Object>> sourceCustomAttributeMap = workgroupOpenFeignClient.findCustomAttributesByPropertyIds(sourcePropertyIds);
                                Map<String, Object> sourceCustomAttributes = sourceCustomAttributeMap.get(sourcePropertyId);
                                if (sourceCustomAttributes != null) {
                                    // get actual HHG Material Carbon,
                                    // if no get estimate HHG Material Estimated Carbon(new)

                                    for(String key : sourceCustomAttributes.keySet()) {
                                        Object value = sourceCustomAttributes.get(key);
                                        if (value == null) {
                                            continue;
                                        }

                                        if (key.startsWith("NON_PAPER_CARBON")) {
                                            // HHG Material Carbon: NON_PAPER_CARBON1_num, NON_PAPER_CARBON2_num …
                                            if (material == null) {
                                                material = new BigDecimal(value.toString());
                                            } else {
                                                material = material.add(new BigDecimal(value.toString()));
                                            }
                                        } else if (key.equals("EST_MATERIAL_CO2_num")) {
                                            // HHG Material Estimated Carbon(new): EST_MATERIAL_CO2_num
                                            estimatedMaterial = new BigDecimal(value.toString());
                                        } else if (key.equals("LOG_WTW_CO2_EQU_num")) {
                                            // HHG Logistics Carbon: LOG_WTW_CO2_EQU_num
                                            logistics = new BigDecimal(value.toString()).multiply(BigDecimal.valueOf(1000));
                                        } else if (key.equals("EST_LOG_CO2_EQU_num")) {
                                            // HHG Logistics Estimated Carbon(new): EST_LOG_CO2_EQU_num
                                            estimatedLogistics = new BigDecimal(value.toString()).multiply(BigDecimal.valueOf(1000));
                                        } else if (key.equals("EST_PROD_CO2_num")) {
                                            // HHG Production Carbon: always use estimate
                                            // HHG Production Estimated Carbon(new): EST_PROD_CO2_num
                                            estimatedProduction = new BigDecimal(value.toString());
                                        }
                                    }

                                    proposalSustainabilityDTO.setMaterial(material);
                                    proposalSustainabilityDTO.setEstMaterial(estimatedMaterial);
                                    proposalSustainabilityDTO.setMaterialIsEstimated(material == null && estimatedMaterial != null);
                                    proposalSustainabilityDTO.setLogistics(logistics);
                                    proposalSustainabilityDTO.setEstLogistics(estimatedLogistics);
                                    proposalSustainabilityDTO.setLogisticsIsEstimated(logistics == null && estimatedLogistics != null);
                                    proposalSustainabilityDTO.setEstProduction(estimatedProduction);


                                }
                            }

                            // if no actual and estimated data, request api to get estimated value
                            if (material == null && logistics == null &&
                                    estimatedMaterial == null && estimatedProduction == null && estimatedLogistics == null) {
                                // estimatedMaterial  estimatedLogistics  estimatedProduction

                                // http://localhost:7777
//                            String host = "*************:7777";
                                String host = nooshApiEnterpriseHost;
                                if (!("80").equals(nooshApiEnterprisePort)) {
                                    host = nooshApiEnterpriseHost + ":" + nooshApiEnterprisePort;
                                }

                                try {
                                    if (sustainabilityResult == null) {
                                        sustainabilityResult = webClientService.createWebApiWithJsonBody(
                                                "http://",
                                                host,
                                                "/noosh/internal/api/projectGreen/getEstimatedCarbon?projectId=" + projectId + "&proposalId=" + proposalId + "&userId=" + userId,
                                                HttpMethod.GET,
                                                null,
                                                null
                                        ).block();
                                        if (sustainabilityResult != null && !sustainabilityResult.isEmpty() && !"null".equals(sustainabilityResult)) {
                                            sustainabilityJsonArray = new JSONArray(sustainabilityResult);
                                        }
                                    }

                                    if (sustainabilityJsonArray != null && sustainabilityJsonArray.length() > 0) {
                                        // [
                                        //   {
                                        //      "jobId":8743258,
                                        //      "EST_LOG_CO2_EQU_num":7.85765467E-5,
                                        //      "quoteItemId":2810329,
                                        //      "EST_MATERIAL_CO2_num":25.9110766,
                                        //      "EST_PROD_CO2_num":6.1190257491
                                        //   }
                                        //]
                                        for (int i = 0; i < sustainabilityJsonArray.length(); i++) {
                                            if (sustainabilityJsonArray.getJSONObject(i).optLong("quoteItemId", -1L) != quoteItem.getId()) {
                                                continue; // skip if quoteItemId not match
                                            }

                                            JSONObject jsonObject = sustainabilityJsonArray.getJSONObject(i);
                                            estimatedMaterial = jsonObject.optBigDecimal("EST_MATERIAL_CO2_num", null);
                                            estimatedLogistics = jsonObject.optBigDecimal("EST_LOG_CO2_EQU_num", null);
                                            if (estimatedLogistics != null) {
                                                estimatedLogistics = estimatedLogistics.multiply(BigDecimal.valueOf(1000));
                                            }
                                            estimatedProduction = jsonObject.optBigDecimal("EST_PROD_CO2_num", null);

                                        }
                                    }

                                } catch (Exception e) {
                                    logger.error("Error from sustainability api: ", e);
                                }

                                proposalSustainabilityDTO.setEstMaterial(estimatedMaterial);
                                proposalSustainabilityDTO.setMaterialIsEstimated(estimatedMaterial != null);
                                proposalSustainabilityDTO.setEstLogistics(estimatedLogistics);
                                proposalSustainabilityDTO.setLogisticsIsEstimated(estimatedLogistics != null);
                                proposalSustainabilityDTO.setEstProduction(estimatedProduction);
                            }

                            // reset material, logistics, production
                            proposalSustainabilityDTO.setMaterial(material != null ? material : estimatedMaterial);
                            proposalSustainabilityDTO.setLogistics(logistics != null ? logistics : estimatedLogistics);
                            if (logistics != null || estimatedLogistics != null) {
                                proposalSustainabilityDTO.setLogistics(logistics != null ? logistics : estimatedLogistics);
                            }
                            proposalSustainabilityDTO.setProduction(estimatedProduction);

                            // total and miles
                            if ((material != null || estimatedMaterial != null) &&
                                    (logistics != null || estimatedLogistics != null) &&
                                    (estimatedProduction != null)) {
                                proposalSustainabilityDTO.setTotal((material != null ? material : estimatedMaterial)
                                        .add(logistics != null ? logistics : estimatedLogistics)
                                        .add(estimatedProduction));
                                proposalSustainabilityDTO.setMiles(proposalSustainabilityDTO.getTotal().multiply(BigDecimal.valueOf(2.5)));
                            }

                            // comparison
                            if (proposalSustainabilityDTO.getTotal() != null &&
                                    estimatedMaterial != null && estimatedLogistics != null && estimatedProduction != null) {
                                BigDecimal estimatedTotal = estimatedMaterial.add(estimatedLogistics).add(estimatedProduction);
                                proposalSustainabilityDTO.setComparison(proposalSustainabilityDTO.getTotal()
                                        .subtract(estimatedTotal)
                                        .divide(estimatedTotal, 2, RoundingMode.HALF_UP));
                            }
                        }

                        proposalItemDTO.setProposalSustainability(proposalSustainabilityDTO);

                    }

                    // price breakout
                    if (proposal.isIncludePriceBreakouts()) {
                        List<Breakout> breakouts = breakoutRepository.findByObjectIdAndObjectClassId(aggrQuotePrice.getId(), ObjectClassID.QUOTE_PRICE);
                        if (breakouts.size() > 0) {
                            List<BreakoutDTO> breakoutList = new ArrayList<>();
                            breakouts.stream().forEach(breakout -> {
                                BreakoutDTO breakoutDTO = breakoutMapper.toDTO(breakout);
                                breakoutList.add(breakoutDTO);
                                if (breakout.getBreakoutType().getIsQuantity()) {
                                    quotePriceDTO.setHasSubQty(true);
                                }
                            });
                            quotePriceDTO.setBreakouts(breakoutList);
                        }
                    }
                    quotePrices.add(quotePriceDTO);
                }
                proposalItemDTO.setQuotePrices(quotePrices);
                proposalItemDTOS.add(proposalItemDTO);
            }
            proposal.setTotalPrice(totalPrice);
            proposal.setTaxLabelString(getTaxLabelString(quote.getBuyerWorkgroupId()));
            proposal.setTax(quote.getTax());
            proposal.setShipping(quote.getShipping());
            BigDecimal grandTotal = totalPrice.add(quote.getTax()).add(quote.getShipping());
            proposal.setGrandTotal(grandTotal);
            proposal.setProposalItems(proposalItemDTOS);

            UserFieldReqDTO quoteItemReqDTO = new UserFieldReqDTO();
            quoteItemReqDTO.setFieldClassId(CustomFieldClassID.QUOTE_ITEM);
            quoteItemReqDTO.setWorkgroupId(project.getPortalWorkgroupId());
            quoteItemReqDTO.setPropertyIds(quoteItemPropertyIds);
            quoteItemReqDTO.setIsSupplierView(false);
            UserFieldDefsWithValuesVO quoteItemsUserFieldDefsWithValuesVO = workgroupOpenFeignClient.findUserFieldDefsWithValues(quoteItemReqDTO);
            proposal.setQuoteItemsUserFields(quoteItemsUserFieldDefsWithValuesVO);
        }


        WorkgroupLogoDTO logo = proposal.getLogo();
        if (logo != null) {
            Resource resource = resourceRepository.findByPath(logo.getResourcePath());
            logo.setLogoImage(Base64.encodeBase64String(resource.getContent()));
//            byte[] content = resource.getContent();
//            try(FileImageOutputStream imageOutput = new FileImageOutputStream(new File(ResourceUtils.getURL("classpath:").getPath() + "/images/" + logo.getUserFilename()))) {
//                imageOutput.write(content, 0, content.length);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
        }

        // Terms and Conditions
        if (proposal.getTermsId() != null) {
            Terms terms = termsRepository.findById(proposal.getTermsId()).orElse(null);
            proposal.setTermTexts(terms.getText());
        }

        // current date
        SimpleDateFormat format = new SimpleDateFormat("MMMMM dd, yyyy");
        String currentDate = format.format(new Date());
        proposal.setCurrentDate(currentDate);

        // branding
        ProposalBranding branding = proposalBrandingRepository.findByProposalId(proposalId);
        if (branding != null) {
            proposal.setLogoPosition(branding.getLogoPosition());
            proposal.setBrandingColorsType(branding.getBrandingColorsType());
            proposal.setCustomizedColors(branding.getCustomizedColors());
        }

        return proposal;
    }

    private String getTaxLabelString(Long workgrouopId) {
        String labelStr = i18NUtils.getMessage(StringID.TAX_DEFAULT_LABEL_STR);
        Map<String, String> prefs = preferenceService.findGroupPrefs(workgrouopId, Arrays.asList(PreferenceID.PC_TAX_LABEL_STRING));
        if (prefs != null && (!prefs.isEmpty()) && prefs.containsKey(PreferenceID.PC_TAX_LABEL_STRING) && prefs.get(PreferenceID.PC_TAX_LABEL_STRING) != null) {
            labelStr = i18NUtils.getMessage(StringID.TAX_DEFAULT_LABEL_STR);
        }
        return labelStr;
    }

    public boolean isAggregateable(List<CategoryDTO> categories, List<ProposalItem> proposalItems) {
        if (categories == null || (categories != null && categories.size() == 0)) {
            return false;
        }

        for (CategoryDTO category : categories) {
            List<ProposalItem> items = getProposalItems(category, proposalItems);
            if (items != null && items.size() > 0) {
                List<QuotePrice> quotePrices = items.get(0).getQuoteItem().getQuotePrices();
                Set<Long> qtySet = new HashSet();
                for (QuotePrice quotePrice : quotePrices) {
                    if (quotePrice.isVisibleToBuyer()) {
                        // this is when order changed quantity
                        // not support yet
                        if (quotePrice.getQuantity().longValue() != quotePriceService.getAggregatedQuotePrice(quotePrice).getQuantity()) {
                            return false;
                        }
                        qtySet.add(quotePrice.getQuantity());
                    }
                }

                // make sure all items of this category have the same quantities
                for (ProposalItem item : items) {
                    Set mySet = new HashSet();
                    List<QuotePrice> myPrices = item.getQuoteItem().getQuotePrices();
                    for (QuotePrice myPrice : myPrices) {
                        if (myPrice.isVisibleToBuyer()) {
                            mySet.add(myPrice.getQuantity());
                        }
                    }
                    if (mySet.size() != qtySet.size()) {
                        return false;
                    }
                    if ( !qtySet.equals(mySet)) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    public List<ProposalItem> getProposalItems(CategoryDTO cat, List<ProposalItem> proposalItems) {
        List<ProposalItem> itemList = new ArrayList();
        if (proposalItems != null && proposalItems.size() > 0) {
            for (ProposalItem item : proposalItems) {
                if (item.getId() == cat.getObjectId().longValue()) {
                    itemList.add(item);
                }
            }
        }
        return itemList;
    }

    public List<CategoryDTO> proposalAggregated(List<CategoryDTO> categories, List<ProposalItem> proposalItems, ProposalDTO proposalDTO) {
        List<CategoryDTO> categoryDTOList = new ArrayList<>();
        List<BreakoutType> breakoutTypes = new ArrayList<>();
        for (CategoryDTO category : categories) {
            List<ProposalItem> items = getProposalItems(category, proposalItems);
            Map bkMap = new HashMap();
            // iterate through all items and compose the bk map so we have all breakouts
            for (ProposalItem item : items) {
                QuotePrice quotePrice = item.getQuoteItem().getChosenQuotePrice();
                List<Breakout> breakouts = breakoutRepository.findByObjectIdAndObjectClassId(quotePrice.getId(), ObjectClassID.QUOTE_PRICE);
                for (Breakout breakout : breakouts) {
                    BreakoutType bkType = breakout.getBreakoutType() ;
                    String bkTypeName = bkType.getName() ;
                    // add to the map if not already existed
                    if (bkMap.get(bkTypeName) == null) {
                        bkMap.put(bkTypeName, breakout);
                        breakoutTypes.add(bkType);
                    }
                }
            }
            // get the quantities
            if (items != null && items.size() > 0) {
                Set qtySet = new HashSet();
                List<QuotePrice> quotePrices = items.get(0).getQuoteItem().getQuotePrices();
                for (QuotePrice quotePrice : quotePrices) {
                    if (quotePrice.isVisibleToBuyer()) {
                        Long qty = quotePrice.getQuantity();
                        qtySet.add(qty);
                    }
                }
                List<Long> qtys = new ArrayList(qtySet);
                Collections.sort(qtys);
                category.setQtyList(qtys);
                // item price
                Map<String, List<BigDecimal>> priceMap = new HashMap<>();
                breakoutTypes.stream().forEach(breakoutType -> {
                    List<BigDecimal> priceList = new ArrayList<>();
                    qtys.stream().forEach(qty -> {
                        priceList.add(getVisibleAggregratedPrice(qty, breakoutType, items));
                    });
                    priceMap.put(breakoutType.getName(), priceList);
                });
                category.setPriceMap(priceMap);

                // price, tax, shipping
                List<BigDecimal> itemPrices = new ArrayList<>();
                List<BigDecimal> taxs = new ArrayList<>();
                List<BigDecimal> shippings = new ArrayList<>();
                List<BigDecimal> totalPrices = new ArrayList<>();
                qtys.stream().forEach(qty -> {
                    getVisibleAggregratedPrice(qty, items, itemPrices, taxs, shippings, totalPrices);
                });
                category.setItemPrices(itemPrices);
                category.setTaxs(taxs);
                category.setShippings(shippings);
                category.setTotalPrices(totalPrices);
            }

            category.setBreakoutMap(bkMap);
            categoryDTOList.add(category);
        }
        proposalDTO.setBreakoutTypes(breakoutTypes);
        return categoryDTOList;
    }


    public BigDecimal getVisibleAggregratedPrice(long qty, BreakoutType breakoutType, List<ProposalItem> items) {
        BigDecimal aggregatedPrice = BigDecimal.ZERO;
        for (ProposalItem item : items) {
            BigDecimal price = proposalItemService.getVisibleAggregatedPrice(qty, item.getQuoteItem(), breakoutType);
            aggregatedPrice = aggregatedPrice.add(price);
        }
        return aggregatedPrice;
    }

    public void getVisibleAggregratedPrice(long qty, List<ProposalItem> items, List<BigDecimal> itemPrices,
                                           List<BigDecimal> taxs, List<BigDecimal> shippings, List<BigDecimal> totalPrices) {
        BigDecimal aggregatedPrice = BigDecimal.ZERO;
        BigDecimal aggregatedtax = BigDecimal.ZERO;
        BigDecimal aggregatedShipping = BigDecimal.ZERO;
        BigDecimal aggregatedTotalPrice = BigDecimal.ZERO;
        for (ProposalItem item : items) {
            List<QuotePrice> priceBeans = proposalItemService.getQuotePriceBeanForQty(qty, item.getQuoteItem()) ;
            for (QuotePrice priceBean : priceBeans) {
                if (priceBean.isVisibleToBuyer()) {
                    aggregatedPrice = aggregatedPrice.add(priceBean.getPrice());
                    if (priceBean.getTax() != null) {
                        aggregatedtax = aggregatedtax.add(priceBean.getTax());
                    }
                    if (priceBean.getShipping() != null) {
                        aggregatedShipping = aggregatedShipping.add(priceBean.getShipping());
                    }
                    if (priceBean.getTotalSellPrice() != null) {
                        aggregatedTotalPrice = aggregatedTotalPrice.add(priceBean.getTotalSellPrice());
                    }
                }
            }
        }
        itemPrices.add(aggregatedPrice);
        taxs.add(aggregatedtax);
        shippings.add(aggregatedShipping);
        totalPrices.add(aggregatedTotalPrice);
    }

    private List<SpecSummaryFieldValueDTO> prepareSpecSummary(ProposalItem proposalItem, Long propertyId) {
        //1 spec item attributes
        List<Property> properties = propertyRepository.findByParentPropertyId(propertyId);
        List<Long> propertyIds = properties.stream().map(Property::getPrPropertyId).collect(Collectors.toList());
        Map<Long, Map<String, Object>> customAttributeMap = workgroupOpenFeignClient.findCustomAttributesByPropertyIds(propertyIds);
        List<SpecItem> specItems = new ArrayList<>();
        for (Property property : properties) {
            SpecItem specItem = new SpecItem();
            specItem.setPropertyId(property.getPrPropertyId());
            specItem.setPropertyTypeId(property.getPrPropertyTypeId());
            Map<String, Object> customAttributes = customAttributeMap.get(property.getPrPropertyId());
            customAttributes.forEach((k, v) -> {
                if (k.toLowerCase().endsWith("_date")) {
                    customAttributes.put(k, DATE_FORMAT.format(LocalDateTime.parse((String) v, REACT_DATE_TIME_FORMATTER)));
                }
            });
            specItem.setCustomAttributes(customAttributes);
            specItems.add(specItem);
        }

        // 2 find spec summary fields
        List<SpecSummaryFieldValueDTO> allSpecSummaryFieldValueDTOS = new ArrayList<>();
        List<ProposalSpecSummaryItem> proposalSpecSummaryItems = proposalSpecSummaryItemRepository.findByProposalSpecSummaryId(proposalItem.getProposalSpecSummaryId());
        for (ProposalSpecSummaryItem proposalSpecSummaryItem : proposalSpecSummaryItems) {
            for (SpecItem specItem : specItems) {
                if (specItem.getPropertyTypeId().equals(proposalSpecSummaryItem.getPropertyTypeId())) {
                    Map<String, Object> customAttributes = specItem.getCustomAttributes();
                    if (customAttributes == null || customAttributes.isEmpty()) {
                        continue;
                    }

                    List<ProposalSpecField> proposalSpecFields = proposalSpecFieldRepository.findByProposalSpecSummaryItemId(proposalSpecSummaryItem.getId());

                    Map listMap = new LinkedHashMap();
                    for (ProposalSpecField proposalSpecField : proposalSpecFields) {
                        int rowNumber = Integer.parseInt(Long.toString(proposalSpecField.getRowNumber()));
                        int columnNumber = Integer.parseInt(Long.toString(proposalSpecField.getColumnNumber()));

                        ArrayList rowList = (ArrayList) listMap.get(new Integer(rowNumber));
                        if (rowList == null) {
                            rowList = new ArrayList();
                            if (columnNumber > 0) {
                                rowList.add(null);
                            }
                        }
                        rowList.add(columnNumber, proposalSpecField);
                        listMap.put(new Integer(rowNumber), rowList);
                    }

                    List[] values = (ArrayList[]) listMap.values().toArray(new ArrayList[listMap.values().size()]);
                    ArrayList list = new ArrayList();
                    for (int v = 0; v < values.length; v++) {
                        ArrayList subList = (ArrayList) values[v];
                        ListIterator subListIter = subList.listIterator();
                        int i = subListIter.nextIndex();
                        while (subListIter.hasNext()) {
                            ProposalSpecField bean = (ProposalSpecField) subListIter.next();
                            if (bean == null && i > 1) {
                                subList.remove(bean);
                            }
                        }
                        list.add(values[v]);
                    }

                    LinkedHashMap rMap = new LinkedHashMap();
                    ListIterator iter = list.listIterator();
                    while (iter.hasNext()) {                    //row by row
                        int index = iter.nextIndex();
                        ArrayList rList = (ArrayList) iter.next();
                        List labelList = rList.subList(0, 1);           //currently we only allow 1 value in the label column
                        ListIterator labelListIterator = labelList.listIterator();
                        String labelString = "";
                        boolean labelContainsLabel = false;
                        boolean labelPresent = false;
                        while (labelListIterator.hasNext()) {
                            ProposalSpecField bean = (ProposalSpecField) labelListIterator.next();
                            String temp = "";
                            if (bean != null) {
                                labelPresent = true;
                                if (bean.getIsText()) {
                                    temp = getFormattedName(bean);
                                    labelContainsLabel = true;
                                }
                                else {
                                    PropertyParam propBean = propertyParamRepository.findById(bean.getPropertyParamId()).get();
                                    String paramName = propBean.getParamName();
                                    if (propBean.getPrDataTypeId() == DataTypeID.BOOLEAN) {  //check if I need special formatting for Date or Money
                                        String paramValue = String.valueOf(customAttributes.get(paramName));
                                        if (paramValue.equals("1")) {
                                            temp = "true";
                                        }
                                        else {
                                            temp = "false";
                                        }
                                    }
                                    else {
                                        if (propBean.getPrDataTypeId() == DataTypeID.LONG && paramName.lastIndexOf("CurrencyId") > -1) { //if it ends in CurrencyId, then I need to display the currency
                                            String paramValue = (String) customAttributes.get(paramName);
                                            String currency = Money.getCurrency(new Long(paramValue).longValue());
                                            temp = Money.getCurrencySymbolIso(currency);
                                        }
                                        else {
                                            temp = formatForWord(String.valueOf(customAttributes.get(paramName)));
                                        }
                                    }
                                }
                                if (temp.length() > 0)
                                    labelString = labelString.concat(temp);
                            }
                        }

                        List valueList = rList.subList(1, rList.size());     //list of ProposalSpecFields
                        ListIterator valueListIterator = valueList.listIterator();
                        String valueString = "";
                        boolean valueContainsLabel = false;
                        boolean valuePresent = false;
                        while (valueListIterator.hasNext()) {
                            ProposalSpecField bean = (ProposalSpecField) valueListIterator.next();
                            String temp = "";
                            if (bean != null) {
                                valuePresent = true;
                                if (bean.getIsText()) {
                                    temp = getFormattedName(bean);
                                    valueContainsLabel = true;
                                }
                                else {
                                    PropertyParam propBean = propertyParamRepository.findById(bean.getPropertyParamId()).get();
                                    String paramName = propBean.getParamName();
                                    if (propBean.getPrDataTypeId() == DataTypeID.BOOLEAN) {  //check if I need special formatting for Date or Money
                                        String paramValue = String.valueOf(customAttributes.get(paramName));
                                        if (paramValue.equals("1")) {
                                            temp = "true";
                                        }
                                        else {
                                            temp = "false";
                                        }
                                    }
                                    else if (propBean.getPrDataTypeId() == DataTypeID.LONG && paramName.lastIndexOf("CurrencyId") > -1) { //if it ends in CurrencyId, then I need to display the currency
                                        String paramValue = (String) customAttributes.get(paramName);
                                        String currency = Money.getCurrency(new Long(paramValue).longValue());
                                        temp = Money.getCurrencySymbolIso(currency);
                                    }
                                    else {
                                        temp = formatForWord(String.valueOf(customAttributes.get(paramName)));
                                    }
                                }
                                if (temp.length() > 0) {
                                    if (valueString.length() == 0) {
                                        valueString = valueString.concat(temp);
                                    } else {
                                        String space = " ";
                                        valueString = (valueString).concat(space).concat(temp);
                                    }
                                }
                            }
                        }

                        ArrayList rListFromMap = (ArrayList) rMap.get(new Integer(index));
                        if (rListFromMap == null) {
                            rListFromMap = new ArrayList();
                        }

                        //Label----------Value
                        //Text---Object or Object---Text    show Line if a Text or Object is on either Label or Value side, and they contain a value
                        if (labelPresent && valuePresent && labelString.length() > 0 && valueString.length() > 0) {
                            rListFromMap.add(labelString);
                            rListFromMap.add(valueString);
                            rMap.put(new Integer(index), rListFromMap);
                        }
                        //Text---(nothing)                  show Line if Text is on Label side and no other object present on Value side
                        else if (labelPresent && labelContainsLabel && labelString.length() > 0 && !valuePresent) {
                            rListFromMap.add(labelString);
                            rListFromMap.add("");
                            rMap.put(new Integer(index), rListFromMap);
                        }
                        //(nothing)---Text                  show Line if Text is on Value side and no other object present on Label side
                        else if (valuePresent && valueContainsLabel && valueString.length() > 0 && !labelPresent) {
                            rListFromMap.add("");
                            rListFromMap.add(valueString);
                            rMap.put(new Integer(index), rListFromMap);
                        }
                    }

                    List[] temp = (ArrayList[]) rMap.values().toArray(new ArrayList[rMap.values().size()]);
                    ArrayList newList = new ArrayList();
                    newList.addAll(Arrays.asList(temp));

                    List<SpecSummaryFieldValueDTO> specSummaryFieldValueDTOS = new ArrayList<>();
                    newList.forEach(row -> {
                        ArrayList rowList = (ArrayList) row;
                        SpecSummaryFieldValueDTO specSummaryFieldValueDTO = new SpecSummaryFieldValueDTO();
                        specSummaryFieldValueDTO.setName((String) rowList.get(0));
                        specSummaryFieldValueDTO.setStringValue((String) rowList.get(1));
                        specSummaryFieldValueDTOS.add(specSummaryFieldValueDTO);
                    });

                    allSpecSummaryFieldValueDTOS.addAll(specSummaryFieldValueDTOS);
                }
            }

        }

        return allSpecSummaryFieldValueDTOS;
    }

    private String getFormattedName(ProposalSpecField proposalSpecField) {
        if (proposalSpecField.getIsText()) {
            return proposalSpecField.getName();
        }
        else {
            PropertyParam paramBean = propertyParamRepository.findById(proposalSpecField.getPropertyParamId()).get();
            String name = paramBean.getParamName();
            return Util.toTitleCase(name, '_', ' ');
        }
    }

    private String formatForWord(String inputStr) {
        if (inputStr == null) {
            return "";
        }

        String source = inputStr;
        source = source.replaceAll("&amp;", "&");
        source = source.replaceAll("&quot;", "\"");
        source = source.replaceAll("&gt;", ">");
        source = source.replaceAll("&lt;", "<");
        return source;
    }

}
