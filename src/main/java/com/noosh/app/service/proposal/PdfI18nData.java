package com.noosh.app.service.proposal;

import java.util.HashMap;
import java.util.Map;

public class PdfI18nData {

    private static final Map<String, String> es_LA = new HashMap<>();

    static {
        es_LA.put("Terms & Conditions", "Términos y condiciones");
        es_LA.put("Total", "Total");
        es_LA.put("Shipping", "Envío");
        es_LA.put("Grand Total", "Gran total");
        es_LA.put("Quotation ID", "ID de cotización");
        es_LA.put("Project Name", "Nombre del proyecto");
        es_LA.put("Prepared Date", "Fecha preparada");
        es_LA.put("Expiration Date", "Fecha de caducidad");
        es_LA.put("Presented By", "Presentado por");
        es_LA.put("Ship To", "Envie a");
        es_LA.put("Quantity", "Cantidad");
        es_LA.put("Pricing\nInformation", "Información\nde precio");
        es_LA.put("Item Price", "Precio del articulo");
        es_LA.put("Unit Price", "Precio unitario");
        es_LA.put("Specification", "Especificación");
        es_LA.put("Completion date", "Fecha de Terminación");
        es_LA.put("Quotation", "Cotización");
        es_LA.put("To", "A");

    }

    public static String getLabel(String key, String locale) {
        if ("es_LA".equals(locale) || "es_MX".equals(locale)) {
            return es_LA.get(key);
        } else {
            return key;
        }
    }

}
