package com.noosh.app.service.proposal;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.property.PropertyTypeDTO;
import com.noosh.app.commons.dto.proposal.CategoryDTO;
import com.noosh.app.commons.dto.proposal.ProposalTemplateDTO;
import com.noosh.app.commons.dto.proposal.SpecSummaryDTO;
import com.noosh.app.commons.dto.proposal.WorkgroupLogoDTO;
import com.noosh.app.commons.dto.spec.SpecTypeDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.address.Address;
import com.noosh.app.commons.entity.address.Country;
import com.noosh.app.commons.entity.category.Category;
import com.noosh.app.commons.entity.category.CategoryClass;
import com.noosh.app.commons.entity.category.CategoryObject;
import com.noosh.app.commons.entity.logo.Resource;
import com.noosh.app.commons.entity.logo.WorkgroupLogo;
import com.noosh.app.commons.entity.proposal.ProposalSpecSummary;
import com.noosh.app.commons.entity.proposal.ProposalSpecSummaryItem;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.proposal.ProposalTemplate;
import com.noosh.app.commons.entity.spec.SpecType;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.category.CategoryBodyVO;
import com.noosh.app.commons.vo.category.CategoryListVO;
import com.noosh.app.commons.vo.category.CategoryMoveVO;
import com.noosh.app.commons.vo.category.CategoryVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.logo.*;
import com.noosh.app.commons.vo.proposal.*;
import com.noosh.app.commons.vo.proposal.ProposalTemplateListVO;
import com.noosh.app.commons.vo.proposal.ProposalTemplateVO;
import com.noosh.app.commons.vo.proposal.ProposalTemplateBodyVO;
import com.noosh.app.commons.vo.proposal.ProposalTemplateDetailVO;
import com.noosh.app.commons.vo.spec.SpecTypeVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.repository.jpa.account.AccountUserRepository;
import com.noosh.app.repository.jpa.address.AddressRepository;
import com.noosh.app.repository.jpa.category.CategoryClassRepository;
import com.noosh.app.repository.jpa.category.CategoryRepository;
import com.noosh.app.repository.jpa.country.CountryRepository;
import com.noosh.app.repository.jpa.logo.ResourceRepository;
import com.noosh.app.repository.jpa.logo.WorkgroupLogoRepository;
import com.noosh.app.repository.jpa.proposal.ProposalSpecSummaryItemRepository;
import com.noosh.app.repository.jpa.proposal.ProposalSpecSummaryRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.jpa.proposal.ProposalTemplateRepository;
import com.noosh.app.repository.jpa.spec.SpecTypeRepository;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.repository.mybatis.proposal.ProposalMyBatisMapper;
import com.noosh.app.repository.mybatis.spec.SpecTypeMyBatisMapper;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.FileUtil;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 10/31/2021
 */
@Service
@Transactional
public class AdminProposalService {

    public static final long MAX_LOGO_SIZE = 10*1024;  //10 KB
    public static final String RESOURCE_ROOT = "/logos/";

    @Autowired
    private ProposalMyBatisMapper proposalMyBatisMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private CategoryRepository categoryRepository;
    @Autowired
    private CategoryClassRepository categoryClassRepository;
    @Autowired
    private WorkgroupLogoRepository workgroupLogoRepository;
    @Autowired
    private I18NUtils i18NUtils;
    @Autowired
    private AccountUserRepository accountUserRepository;
    @Autowired
    private ProposalTemplateRepository proposalTemplateRepository;
    @Autowired
    private AddressRepository addressRepository;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private ProposalSpecSummaryRepository proposalSpecSummaryRepository;
    @Autowired
    private SpecTypeRepository specTypeRepository;
    @Autowired
    private PropertyMyBatisMapper propertyMyBatisMapper;
    @Autowired
    private ProposalSpecSummaryItemRepository proposalSpecSummaryItemRepository;
    @Autowired
    private SpecTypeMyBatisMapper specTypeMyBatisMapper;
    @Autowired
    private ResourceRepository resourceRepository;
    @Autowired
    private CountryRepository countryRepository;

    /**
     * category list
     * @param userId
     * @param workgroupId
     * @param page
     * @return
     */
    public CategoryListVO categoriesList(Long userId, Long workgroupId, PageVO page) {
        CategoryListVO categoryListVO = new CategoryListVO();
        // Proposal Categories
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        List<CategoryDTO> categoryList = proposalMyBatisMapper.findCategories(userId, workgroupId);
        page.setTotal(pageInfo.getTotal());

        boolean hasPermission = permissionService.checkAll(PermissionID.MANAGE_PROPOSAL_ADMIN, workgroupId, userId, -1L);

        List<CategoryVO> categorys = new ArrayList<>();
        categoryList.stream().forEach(categoryDTO -> {
            CategoryVO categoryVO = new CategoryVO();
            categoryVO.setCategoryId(categoryDTO.getCategoryId());
            categoryVO.setName(categoryDTO.getName());
            categoryVO.setDescription(categoryDTO.getDescription());
            if (hasPermission) {
                categoryVO.setUpCategoryExternalLink(NooshOneUrlUtil.composeCategoryMoveUpLinkToEnterprise(categoryDTO.getCategoryId()));
                categoryVO.setDownCategoryExternalLink(NooshOneUrlUtil.composeCategoryMoveDownLinkToEnterprise(categoryDTO.getCategoryId()));
                categoryVO.setEditCategoryExternalLink(NooshOneUrlUtil.composeEditCategoryLinkToEnterprise(categoryDTO.getCategoryId()));
                categoryVO.setDeleteCategoryExternalLink(NooshOneUrlUtil.composeDeleteCategoryLinkToEnterprise(categoryDTO.getCategoryId()));
            }
            categorys.add(categoryVO);
        });

        categoryListVO.setCategorys(categorys);
        if (hasPermission) {
            categoryListVO.setCreateCategoryExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.EDIT_CATEGORY));
        }

        return categoryListVO;
    }

    /**
     * Move up or move down category
     * @param userId
     * @param workgroupId
     * @param categoryMoveVO
     */
    public void moveCategory(Long userId, Long workgroupId, CategoryMoveVO categoryMoveVO) {

        if (categoryMoveVO.getCategoryId() == null) {
            throw new UnexpectedException("category id cannot be null");
        } else {
            Category category = categoryRepository.findById(categoryMoveVO.getCategoryId()).orElse(null);
            if (category == null) {
                throw new NotFoundException("category is not found");
            }
        }

        List<CategoryDTO> categories = proposalMyBatisMapper.findCategories(userId, workgroupId);
        List<Long> categoryClassIds = new ArrayList<>();
        categories.forEach(category -> {
            categoryClassIds.add(category.getCategoryClassId());
        });

        // category class
        List<CategoryClass> categoryClassList = categoryClassRepository.findAllById(categoryClassIds);
        Map<Long, List<CategoryClass>> categoryClassMap = new HashMap<>();
        categoryClassList.forEach(categoryClass -> {
            List<CategoryClass> categoryClasses = categoryClassMap.get(categoryClass.getCategoryId());
            if (categoryClasses == null) {
                categoryClasses = new ArrayList<>();
            }
            if (!categoryClasses.contains(categoryClass)) {
                categoryClasses.add(categoryClass);
            }
            categoryClassMap.put(categoryClass.getCategoryId(), categoryClasses);
        });

        if (!categories.isEmpty()) {
            CategoryDTO from = null;
            CategoryDTO to = null;

            //find the from & to
            for (int i=0; i < categories.size(); i++) {
                if (categories.get(i).getCategoryId().equals(categoryMoveVO.getCategoryId())) {
                    from = categories.get(i);
                    if (categoryMoveVO.getIsMoveDown()) {
                        to = categories.get(i+1);
                    } else {
                        to = categories.get(i-1);
                    }
                    break;
                }
            }
            if (from != null && to != null) {
                CategoryClass fromClass = categoryClassMap.get(from.getCategoryId()).get(0);
                CategoryClass toClass = categoryClassMap.get(to.getCategoryId()).get(0);
                //update the ordering
                long fromPos = fromClass.getOrdinalNumber();
                long toPos = toClass.getOrdinalNumber();

                fromClass.setOrdinalNumber(toPos);
                toClass.setOrdinalNumber(fromPos);
                List<CategoryClass> classes = new ArrayList<>();
                classes.add(fromClass);
                classes.add(toClass);
                categoryClassRepository.saveAllAndFlush(classes);
            }
        }

    }

    /**
     * edit or create category
     * @param userId
     * @param workgroupId
     * @param categoryBodyVO
     */
    public void editCategory(Long userId, Long workgroupId, CategoryBodyVO categoryBodyVO) {

        // create category
        if (categoryBodyVO.getCategoryId() == null) {
            Category category = new Category();
            category.setWorkgroupId(workgroupId);
            category.setName(categoryBodyVO.getName());
            category.setDescription(categoryBodyVO.getDescription());
            category.setIsDisabled(false);
            category.setCreateUserId(userId);
            category.setModDate(null);
            category = categoryRepository.saveAndFlush(category);

            // get all classIds that is already in the system.
            List<CategoryClass> categoryClasses = categoryClassRepository.findAllByCategoryId(category.getId());
            List<Long> existingClassIds = new ArrayList();
            for (CategoryClass categoryClass : categoryClasses) {
                existingClassIds.add(categoryClass.getId());
            }

            List<Long> objectClassIds = new ArrayList<>();
            objectClassIds.add(ObjectClassID.PROCUREMENT);
            objectClassIds.add(ObjectClassID.PROPOSAL_ITEM);
            // if already in system, don't add again
            for (Long objectClassId : objectClassIds) {
                boolean alreadyExisted = false;
                for (Long existingClassId : existingClassIds) {
                    if (existingClassId == objectClassId.longValue()) {
                        alreadyExisted = true;
                        break;
                    }
                }
                if (!alreadyExisted) {
                    // if not already in the sytem, persist & update
                    CategoryClass categoryClass = new CategoryClass();
                    categoryClass.setCategoryId(category.getId());
                    categoryClass.setObjectClassId(objectClassId);
                    categoryClass.setCreateUserId(userId);
                    categoryClass.setModUserId(userId);
                    CategoryClass categoryClassEntity = categoryClassRepository.saveAndFlush(categoryClass);
                    //create category & assign its id as ordinal
                    //1. quick
                    //2. easy db migration
                    categoryClassEntity.setOrdinalNumber(categoryClassEntity.getId());
                    categoryClassRepository.saveAndFlush(categoryClassEntity);

                    existingClassIds.add(categoryClassEntity.getId());
                }
            }
        } else {
            // edit category
            Category category = categoryRepository.findById(categoryBodyVO.getCategoryId()).orElse(null);
            if (category == null) {
                throw new NotFoundException("Category not found");
            }
            if (categoryBodyVO.getName() != null) {
                category.setName(categoryBodyVO.getName());
            }
            if (categoryBodyVO.getDescription() != null) {
                category.setDescription(categoryBodyVO.getDescription());
            }
            category.setModUserId(userId);
            category.setModDate(LocalDateTime.now());
            categoryRepository.save(category);
        }
    }

    /**
     * Delete category
     * @param categoryId
     */
    public void deleteCategory(Long categoryId) {
        Category category = categoryRepository.findById(categoryId).orElse(null);
        if (category == null) {
            throw new NotFoundException("Category not found");
        }

        // find all object-association
        // if there's any association and if !bRemoveAll
        // just disable the category
        List<CategoryObject> categoryObjects = category.getCategoryObjects();
        if (categoryObjects != null && categoryObjects.size() > 0) {
            category.setIsDisabled(true);
            categoryRepository.save(category);
            return;
        }

        // remove all class-associations in CategoryClass:
        List<CategoryClass> categoryClasses = category.getCategoryClasses();
        categoryClassRepository.deleteAll(categoryClasses);

        // remove the category itself
        categoryRepository.delete(category);
    }

    /**
     * Get workgroup logo list
     * @param userId
     * @param workgroupId
     * @param page
     * @return
     */
    public WorkgroupLogoListVO logosList(Long userId, Long workgroupId, PageVO page) {
        WorkgroupLogoListVO logoListVO = new WorkgroupLogoListVO();
        // workgroup logo
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        List<WorkgroupLogoDTO> workgroupLogoList = proposalMyBatisMapper.findLogos(workgroupId);
        page.setTotal(pageInfo.getTotal());

        List<String> logoPaths = new ArrayList<>();
        workgroupLogoList.forEach(logoDTO -> {
            logoPaths.add(logoDTO.getResourcePath());
        });

        List<Resource> resources = resourceRepository.findAllByPathIn(logoPaths);
        Map<String, String> contextMap = new HashMap<>();
        resources.forEach(resource ->{
            contextMap.put(resource.getPath(),  Base64.encodeBase64String(resource.getContent()));
        });

        boolean hasPermission = permissionService.checkAll(PermissionID.MANAGE_PROPOSAL_ADMIN, workgroupId, userId, -1L);

        List<WorkgroupLogoVO> logos = new ArrayList<>();
        workgroupLogoList.stream().forEach(logoDTO -> {
            WorkgroupLogoVO logoVO = new WorkgroupLogoVO();
            logoVO.setLogoId(logoDTO.getLogoId());
            logoVO.setLogoImage(contextMap.get(logoDTO.getResourcePath()));
            logoVO.setName(logoDTO.getName());
            logoVO.setWidth(logoDTO.getWidth());
            logoVO.setHeight(logoDTO.getHeight());
            logoVO.setCreatedFullName(logoDTO.getCreatedFullName());
            logoVO.setCreatedDate(logoDTO.getCreatedDate());
            logoVO.setUpdatedFullName(logoDTO.getUpdatedFullName());
            logoVO.setLastUpdatedDate(logoDTO.getLastUpdatedDate());
            logoVO.setDetailsLogoExternalLink(NooshOneUrlUtil.composeDetailsLogoLinkToEnterprise(logoDTO.getLogoId()));
            if (hasPermission) {
                logoVO.setEditLogoExternalLink(NooshOneUrlUtil.composeEditLogoLinkToEnterprise(logoDTO.getLogoId()));
                logoVO.setDeleteLogoExternalLink(NooshOneUrlUtil.composeDeleteLogoLinkToEnterprise(logoDTO.getLogoId()));
            }
            logos.add(logoVO);
        });

        logoListVO.setLogos(logos);
        if (hasPermission) {
            logoListVO.setCreateLogoExternalLink(NooshOneUrlUtil.composeCreateLogoLinkToEnterprise());
        }

        return logoListVO;
    }

    /**
     * View workgroup logo detail
     * @param logoId
     * @return
     */
    public LogoDetailVO viewLogo(Long logoId) {
        WorkgroupLogo logo = workgroupLogoRepository.findById(logoId).orElse(null);
        if (logo == null) {
            throw new NotFoundException("Logo not found");
        }
        LogoDetailVO detailVO = new LogoDetailVO();
        detailVO.setName(logo.getName());
        detailVO.setWidth(logo.getWidth());
        detailVO.setHeight(logo.getHeight());
        AccountUser createUser = accountUserRepository.findById(logo.getCreateUserId()).orElse(null);
        detailVO.setCreatedBy(createUser.getPerson().getFullName());
        detailVO.setCreatedOn(logo.getCreateDate());

        AccountUser modUser = accountUserRepository.findById(logo.getModUserId()).orElse(null);
        detailVO.setLastUpdatedBy(modUser.getPerson().getFullName());
        detailVO.setLastUpdatedOn(logo.getModDate());

        return detailVO;
    }

    public byte[] viewLogoImage(Long logoId) {
        WorkgroupLogo logo = workgroupLogoRepository.findById(logoId).orElse(null);
        if (logo == null) {
            throw new NotFoundException("Logo not found");
        }
        Resource resource = resourceRepository.findByPath(logo.getResourcePath());
        return resource.getContent();
    }

    /**
     * Create or edit workgroup logo
     * @param userId
     * @param workgroupId
     * @param logoId
     * @param logoName
     * @param logoFile
     */
    public LogoVO editLogo(Long userId, Long workgroupId, Long logoId, String logoName, MultipartFile logoFile) throws IOException {
        if (logoName == null) {
            throw new IllegalArgumentException("Logo name must be not empty.");
        }
        if (logoName.length() > 128) {
            throw new IllegalArgumentException("Logo name must be less than 128 characters.");
        }

        LogoVO vo = new LogoVO();
        WorkgroupLogo logo;
        if (logoId != null && logoId > 0) {
            logo = workgroupLogoRepository.findById(logoId).orElse(null);
            logo.setName(logoName);
            logo.setModUserId(userId);
            logo.setModDate(LocalDateTime.now());

            praseFile(logo, logoFile);
            // Saves a resource to the database storing a descriptor record in
            saveResource(userId, logo.getResourcePath(), logoFile.getInputStream(), logo.getByteCount());

            WorkgroupLogo save = workgroupLogoRepository.save(logo);
            vo.setLogoId(save.getId());
            vo.setName(save.getName());
            return vo;
        } else {
            logo = new WorkgroupLogo();
            logo.setWorkgroupId(workgroupId);
            logo.setIsActive(true);
            logo.setCreateUserId(userId);
            logo.setModDate(null);
            logo.setName(logoName);

            praseFile(logo, logoFile);
            WorkgroupLogo workgroupLogo = workgroupLogoRepository.save(logo);
            workgroupLogo.setResourcePath(RESOURCE_ROOT+workgroupLogo.getId());

            // Saves a resource to the database storing a descriptor record in
            saveResource(userId, workgroupLogo.getResourcePath(), logoFile.getInputStream(), logo.getByteCount());
            WorkgroupLogo save = workgroupLogoRepository.save(workgroupLogo);
            vo.setLogoId(save.getId());
            vo.setName(save.getName());
            return vo;
        }
    }

    private void praseFile(WorkgroupLogo logo, MultipartFile logoFile) throws IOException {
        // logo file uploading
        File file = FileUtil.inputStreamToFile(logoFile);
        if ((file != null) && (file.length() > 0)) {
            String userFilename = logoFile.getOriginalFilename();
            if (userFilename == null) {
                throw new IllegalArgumentException("File name must be not empty.");
            }
            if (userFilename.length() > 256) {
                throw new IllegalArgumentException("File name must be less than 256 characters.");
            }
            InputStream inputStream = logoFile.getInputStream();
            ImageInputStream iis = ImageIO.createImageInputStream(inputStream);
            Iterator readers = ImageIO.getImageReaders(iis);
            if (readers.hasNext()) {
                ImageReader reader = (ImageReader) readers.next();
                reader.setInput(iis, true);
                int imageIndex = 0;
                int width = reader.getWidth(imageIndex);
                int height = reader.getHeight(imageIndex);

                logo.setWidth(width);
                logo.setHeight(height);
                logo.setUserFileName(userFilename);
                logo.setByteCount(file.length());

                if (file.length() > MAX_LOGO_SIZE) {
                    throw new IllegalArgumentException("The size of the logo file must be less than 10 KB.");
                }
            }
        } else {
            throw new IllegalArgumentException("Unsupported image type.");
        }
    }

    /**
     * Saves a resource to the database storing a descriptor record in
     * NF_RESOURCE.
     *
     * @param userId
     * @param resourcePath
     * @param input
     * @return The number of bytes saved.
     * @throws NullPointerException If path or input are null.
     */
    public void saveResource(Long userId, String resourcePath, InputStream input, long contentSize) throws IOException {
        if (input == null) {
            throw new UnexpectedException("Image cannot is null");
        }
        // insert or update the NF_RESOURCE descriptor record
        Resource resource = resourceRepository.findByPath(resourcePath);
        if (resource == null) {
            resource = new Resource();
            resource.setPath(resourcePath);
            resource.setContent(input.readAllBytes());
            resource.setContentSize(contentSize);
            resource.setCreateUserId(userId);
            resource.setModDate(null);
        } else {
            resource.setContent(input.readAllBytes());
            resource.setContentSize(contentSize);
            resource.setModUserId(userId);
            resource.setModDate(LocalDateTime.now());
        }
        resourceRepository.save(resource);
    }

    /**
     * Delete workgroup logo
     * @param logoId
     */
    public void deleteLogo(Long logoId) {
        WorkgroupLogo workgroupLogo = workgroupLogoRepository.findById(logoId).orElse(null);
        if (workgroupLogo == null) {
            throw new NotFoundException("Logo not found");
        }
        workgroupLogo.setIsActive(false);
        workgroupLogo.setModDate(LocalDateTime.now());
        workgroupLogo.setModUserId(JwtUtil.getUserId());
        workgroupLogoRepository.save(workgroupLogo);
    }

    /**
     * Get proposal template list
     * @param userId
     * @param workgroupId
     * @param page
     * @return
     */
    public ProposalTemplateListVO templatesList(Long userId, Long workgroupId, PageVO page) {
        ProposalTemplateListVO templateListVO = new ProposalTemplateListVO();
        // Proposal Templates
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        List<ProposalTemplateDTO> proposalTemplateList = proposalMyBatisMapper.findTemplates(workgroupId);
        page.setTotal(pageInfo.getTotal());

        boolean hasPermission = permissionService.checkAll(PermissionID.MANAGE_PROPOSAL_ADMIN, workgroupId, userId, -1L);

        List<ProposalTemplateVO> templates = new ArrayList<>();
        proposalTemplateList.stream().forEach(templateDTO -> {
            ProposalTemplateVO templateVO = new ProposalTemplateVO();
            templateVO.setTemplateId(templateDTO.getTemplateId());
            templateVO.setIsDefault(templateDTO.getIsDefault());
            templateVO.setName(templateDTO.getName());
            templateVO.setCreatedFullName(templateDTO.getCreatedFullName());
            templateVO.setCreatedDate(templateDTO.getCreatedDate());
            templateVO.setUpdatedFullName(templateDTO.getUpdatedFullName());
            templateVO.setLastUpdatedDate(templateDTO.getLastUpdatedDate());
            templateVO.setDetailsTemplateExternalLink(NooshOneUrlUtil.composeDetailsTemplateLinkToEnterprise(templateDTO.getTemplateId()));
            if (hasPermission) {
                templateVO.setEditTemplateExternalLink(NooshOneUrlUtil.composeEditTemplateLinkToEnterprise(templateDTO.getTemplateId()));
                templateVO.setDeleteTemplateExternalLink(NooshOneUrlUtil.composeDeleteTemplateLinkToEnterprise(templateDTO.getTemplateId()));
            }
            templates.add(templateVO);
        });
        templateListVO.setTemplates(templates);
        if (hasPermission) {
            templateListVO.setCreateLogoExternalLink(NooshOneUrlUtil.composeCreateTemplateLinkToEnterprise());
        }
        templateListVO.setBackExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.WORKGROUP_HOME));
        return templateListVO;
    }

    /**
     * View proposal template detail
     * @param templateId
     * @param locale
     * @return
     */
    public ProposalTemplateDetailVO viewTemplate(Long templateId, String locale, Long workgroupId) {
        ProposalTemplate template = proposalTemplateRepository.findById(templateId).orElse(null);
        if (template == null) {
            throw new NotFoundException("Proposal template not found");
        }

        ProposalTemplateDetailVO detailVO = new ProposalTemplateDetailVO();
        detailVO.setTemplateName(template.getName());

        WorkgroupLogo workgroupLogo = template.getWorkgroupLogo();
        if (workgroupLogo != null) {
            detailVO.setLogoName(workgroupLogo.getName());
            detailVO.setDetailsLogoExternalLink(NooshOneUrlUtil.composeDetailsLogoLinkToEnterprise(workgroupLogo.getId()));
        }

        detailVO.setLogoId(template.getLogoId());
        detailVO.setLogoPosition(template.getLogoPosition());
        detailVO.setBrandingColorsType(template.getBrandingColorsType());
        detailVO.setCustomizedColors(template.getCustomizedColors());
        detailVO.setCompanyAddressId(template.getCompanyAddressId());

        detailVO.setCompanyName(template.getCompanyName());
        Address address = addressRepository.findById(template.getCompanyAddressId()).orElse(null);
        detailVO.setLine1(address.getLine1());
        detailVO.setLine2(address.getLine2());
        detailVO.setLine3(address.getLine3());
        detailVO.setCity(address.getCity());
        detailVO.setState(address.getState());
        detailVO.setPostal(address.getPostal());
        detailVO.setCountryId(address.getCountry() != null ?address.getCountry().getId() : null);
        detailVO.setCountryStrId(address.getCountry() != null ?address.getCountry().getNameStrId() : null);
        detailVO.setCompanyPhone(template.getCompanyPhone());
        detailVO.setCompanyFax(template.getCompanyFax());
        detailVO.setCompanyUrl(template.getCompanyUrl());
        detailVO.setIncludePageNumber(template.isIncludePageNumber());
        detailVO.setIsLandscape(template.isLandscape());
        detailVO.setIncludeCoverPage(template.isIncludeCoverPage());
        detailVO.setIncludeCoverLetter(template.isIncludeCoverLetter());
        if (template.isIncludeCoverLetter()) {
            detailVO.setIntroText(template.getIntroText());
            detailVO.setConclusionText(template.getConclusionText());
            detailVO.setClosingText(template.getClosingText());
        }
        detailVO.setIncludeTermsAndConditions(template.isIncludeTermsConditions());
        detailVO.setIncludeProposalNote(template.isIncludeProposalNote());
        if (template.isIncludeProposalNote()) {
            detailVO.setProposalNote(template.getProposalNote());

        }
        detailVO.setIncludeSignaturePage(template.isIncludeSignaturePage());
        detailVO.setEnableSpecSummaryReg(template.isUseSpecSummary() != null && template.isUseSpecSummary());
        if (template.isUseSpecSummary() != null && template.isUseSpecSummary()) {
            detailVO.setUseSpecSummary(template.isUseSpecSummary());
            detailVO.setIsSpecSummaryCompact(template.isSpecSummaryCompact());
            detailVO.setUseSpecSummaryBorder(template.isUseSpecSummaryBorder());
            detailVO.setSpecSummaryColumns(template.getSpecSummaryNumColumns());
        }
        detailVO.setQuantityLayout(template.getLayout());
        detailVO.setIncludePriceBreakouts(template.isIncludePriceBreakouts());
        detailVO.setIsMarkupVisible(template.isMarkupVisible());
        detailVO.setIsSeparateSpecPricing(template.isSeparateSpecPricing());
        detailVO.setIsActive(template.getIsActive());
        detailVO.setIsDefault(template.getIsDefault());
        detailVO.setSumUpAllQuotedQuantity1(template.isSumUpAllQuotedQuantity1());
        return detailVO;
    }

    public ProposalTemplateOptionVO templateOptions(Long workgroupId) {
        ProposalTemplateOptionVO vo = new ProposalTemplateOptionVO();
        vo.setSpecSummaryRegEnabled(preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROPOSAL_SPEC_SUMMARY_REG, workgroupId));
        // address
        Workgroup workgroup = workgroupRepository.findById(workgroupId).orElse(new Workgroup());
        if (workgroup != null) {
            vo.setCompanyName(workgroup.getName());
        }
        Address address = workgroup.getWorkgroupAddress().getAddress();
        if (workgroup.getWorkgroupAddress() != null && address != null) {
            vo.setLine1(address.getLine1());
            vo.setLine2(address.getLine2());
            vo.setLine3(address.getLine3());
            vo.setCity(address.getCity());
            vo.setState(address.getState());
            vo.setPostalCode(address.getPostal());
            vo.setCountryStrId(address.getCountry().getNameStrId());
            vo.setCountryId(address.getCountry().getId());
        }

        List<WorkgroupLogo> logos = workgroupLogoRepository.findAllByWorkgroupIdAndIsActive(workgroupId, true);
        List<DropdownVO<Long>> logoOptions = new ArrayList<>();
        logos.forEach(logo -> {
            logoOptions.add(new DropdownVO<>(logo.getId(), logo.getName()));
        });

        List<DropdownVO<Long>> countryOptions = new ArrayList<>();
        List<Country> countries = countryRepository.findAll();
        countries.forEach(country -> {
            countryOptions.add(new DropdownVO<>(country.getId(), country.getNameStrId()!=null ? country.getNameStrId().toString() :  null));
        });

        List<DropdownVO<String>> layoutOptions = new ArrayList<>();
        layoutOptions.add(new DropdownVO<>("vertical", "vertical"));
        layoutOptions.add(new DropdownVO<>("horizontal", "horizontal"));

        vo.setLogos(logoOptions);
        vo.setCountrys(countryOptions);
        vo.setLayouts(layoutOptions);
        return vo;
    }

    /**
     * Create or edit proposal template
     * @param userId
     * @param workgroupId
     * @param bodyVO
     */
    public void editTemplate(Long userId, Long workgroupId, ProposalTemplateBodyVO bodyVO) {
        if (bodyVO.getIsDefault()) {
            // mark other default templates as non-default
            List<ProposalTemplate> proposalTemplates = proposalTemplateRepository.findByWorkgroupIdAndIsDefault(workgroupId, true);
            if (proposalTemplates != null && !proposalTemplates.isEmpty()) {
                proposalTemplates.forEach(template -> {
                    template.setIsDefault(false);
                    template.setModUserId(userId);
                    template.setModDate(LocalDateTime.now());
                    proposalTemplateRepository.save(template);
                });
            }
        }

        ProposalTemplate template = new ProposalTemplate();
        Address address;
        if (bodyVO.getTemplateId() != null) {
            template = proposalTemplateRepository.findById(bodyVO.getTemplateId()).orElse(null);
            address = addressRepository.findById(bodyVO.getCompanyAddressId()).orElse(null);
            address.setLine1(bodyVO.getLine1());
            address.setLine2(bodyVO.getLine2());
            address.setLine3(bodyVO.getLine3());
            address.setCity(bodyVO.getCity());
            address.setState(bodyVO.getState());
            address.setPostal(bodyVO.getPostal());
            address.setCountryId(bodyVO.getCountryId());
            address.setModUserId(userId);
            address.setModDate(LocalDateTime.now());

            template.setModUserId(userId);
            template.setModDate(LocalDateTime.now());
        } else {
            Workgroup workgroup = workgroupRepository.findById(workgroupId).orElse(null);
            template.setWorkgroupId(workgroupId);
            template.setCompanyName(workgroup.getCompany().getName());

            address = new Address();
            address.setLine1(bodyVO.getLine1());
            address.setLine2(bodyVO.getLine2());
            address.setLine3(bodyVO.getLine3());
            address.setCity(bodyVO.getCity());
            address.setState(bodyVO.getState());
            address.setPostal(bodyVO.getPostal());
            address.setCountryId(bodyVO.getCountryId());
            address.setCreateUserId(userId);
            address.setModDate(null);


            template.setMarkupVisible(false);
            template.setIsActive(true);

            //default some of these template options to true to mimic options on previous proposals
            template.setIncludeCoverPage(true);
            template.setIncludeCoverLetter(true);
            template.setUseSpecSummary(preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROPOSAL_SPEC_SUMMARY_REG, workgroupId));
            template.setSpecSummaryCompact(false);
            template.setIncludeTermsConditions(true);
            template.setIncludeProposalNote(false);
            template.setIncludeSignaturePage(true);
            template.setLayout("vertical");
            template.setIncludePriceBreakouts(true);
            template.setIncludePageNumber(false);
            template.setLandscape(false);
            template.setUseSpecSummaryBorder(true);
            template.setSpecSummaryNumColumns(1);
            template.setCreateUserId(userId);
            template.setModDate(null);
        }
        Address saveAddress = addressRepository.save(address);

        template.setName(bodyVO.getTemplateName());
        template.setIsActive(bodyVO.getIsActive());
        template.setIsDefault(bodyVO.getIsDefault());
        template.setLogoId(bodyVO.getLogoId());
        template.setLogoPosition(bodyVO.getLogoPosition());
        template.setBrandingColorsType(bodyVO.getBrandingColorsType());
        template.setCustomizedColors(bodyVO.getCustomizedColors());
        template.setCompanyName(bodyVO.getCompanyName());
        template.setCompanyAddressId(saveAddress.getId());
        template.setCompanyPhone(bodyVO.getCompanyPhone());
        template.setCompanyFax(bodyVO.getCompanyFax());
        template.setCompanyUrl(bodyVO.getCompanyUrl());
        template.setIncludePageNumber(bodyVO.isIncludePageNumber());
        template.setLandscape(bodyVO.getIsLandscape());
        template.setIncludeCoverPage(bodyVO.isIncludeCoverPage());
        template.setIncludeCoverLetter(bodyVO.isIncludeCoverLetter());
        template.setIntroText(bodyVO.getIntroText());
        template.setConclusionText(bodyVO.getConclusionText());
        template.setClosingText(bodyVO.getClosingText());
        template.setIncludeTermsConditions(bodyVO.isIncludeTermsAndConditions());
        template.setIncludeProposalNote(bodyVO.isIncludeProposalNote());
        template.setProposalNote(bodyVO.getProposalNote());
        template.setIncludeSignaturePage(bodyVO.isIncludeSignaturePage());
        template.setUseSpecSummary(bodyVO.isUseSpecSummary());
        template.setSpecSummaryCompact(bodyVO.getIsSpecSummaryCompact());
        template.setUseSpecSummaryBorder(bodyVO.isUseSpecSummaryBorder());
        template.setSpecSummaryNumColumns(bodyVO.getSpecSummaryColumns());
        template.setLayout(bodyVO.getQuantityLayout());
        template.setIncludePriceBreakouts(bodyVO.isIncludePriceBreakouts());
        template.setMarkupVisible(bodyVO.getIsMarkupVisible());
        template.setSeparateSpecPricing(bodyVO.getIsSeparateSpecPricing());
        template.setSumUpAllQuotedQuantity1(bodyVO.isSumUpAllQuotedQuantity1());
        proposalTemplateRepository.save(template);
    }

    /**
     * Delete proposal template
     * @param templateId
     */
    public void deleteTemplate(Long templateId) {
        ProposalTemplate template = proposalTemplateRepository.findById(templateId).orElse(null);
        if (template == null) {
            throw new NotFoundException("Proposal template not found");
        }
        Address address = addressRepository.findById(template.getCompanyAddressId()).orElse(null);
        if (template == null) {
            throw new NotFoundException("Proposal template address not found");
        }
        proposalTemplateRepository.delete(template);
        addressRepository.delete(address);
    }

    /**
     * Get spec summary list
     * @param userId
     * @param workgroupId
     * @param page
     * @return
     */
    public SpecSummaryListVO specSummaryList(Long userId, Long workgroupId, PageVO page) {
        SpecSummaryListVO specSummaryListVO = new SpecSummaryListVO();

        specSummaryListVO.setEnableSpecSummaryReg(preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROPOSAL_SPEC_SUMMARY_REG, workgroupId));
        // Proposal Templates
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        List<SpecSummaryDTO> specSummaryDTOList = proposalMyBatisMapper.findSpecSummary(workgroupId);
        page.setTotal(pageInfo.getTotal());

        boolean hasPermission = permissionService.checkAll(PermissionID.MANAGE_PROPOSAL_ADMIN, workgroupId, userId, -1L);

        List<SpecSummaryVO> specSummaryList = new ArrayList<>();
        specSummaryDTOList.stream().forEach(specSummaryDTO -> {
            SpecSummaryVO specSummaryVO = new SpecSummaryVO();
            specSummaryVO.setSpecSummaryId(specSummaryDTO.getSpecSummaryId());
            specSummaryVO.setName(specSummaryDTO.getName());
            specSummaryVO.setIconHref(specSummaryDTO.getIconHref());
            specSummaryVO.setSpecTypeStr(specSummaryDTO.getSpecTypeLabelStr());
            specSummaryVO.setSpecTypeStrId(specSummaryDTO.getSpecTypeLabelStrId() != null && specSummaryDTO.getSpecTypeLabelStrId() > 0
                    ? specSummaryDTO.getSpecTypeLabelStrId().toString() : null);
            specSummaryVO.setSpecType(getSpecTypeLabel(specSummaryDTO));
            specSummaryVO.setIsDefault(specSummaryDTO.getIsDefault());
            if (hasPermission) {
                specSummaryVO.setEditSpecSummaryExternalLink(NooshOneUrlUtil.composeEditSpecSummaryLinkToEnterprise(specSummaryDTO.getSpecSummaryId(), specSummaryDTO.getSpecTypeId()));
                specSummaryVO.setDeleteSpecSummaryExternalLink(NooshOneUrlUtil.composeDeleteSpecSummaryLinkToEnterprise(specSummaryDTO.getSpecSummaryId(), specSummaryDTO.getSpecTypeId()));
            }
            specSummaryList.add(specSummaryVO);
        });
        specSummaryListVO.setSpecSummaryList(specSummaryList);
        if (hasPermission) {
            specSummaryListVO.setCreateLogoExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.CREATE_SPEC_SUMMARY));
        }
        return specSummaryListVO;
    }

    private String getSpecTypeLabel(SpecSummaryDTO specSummaryDTO) {
        if (specSummaryDTO.getSpecTypeLabelStrId() != null) {
            return i18NUtils.getMessage(specSummaryDTO.getSpecTypeLabelStrId());
        } else if(specSummaryDTO.getSpecTypeLabelStr() != null) {
            return specSummaryDTO.getSpecTypeLabelStr();
        } else {
            return null;
        }
    }

    /**
     * Get spec type list
     * @param workgroupId
     * @param locale
     * @return
     */
    public List<SpecTypeVO> specTypes(Long workgroupId, String locale) {
        List<SpecTypeVO> vos = new ArrayList<>();

        List<SpecTypeDTO> specTypes = specTypeMyBatisMapper.findSpecTypes(workgroupId);
        specTypes.forEach(dto -> {
            SpecTypeVO vo = new SpecTypeVO();
            vo.setSpecTypeId(dto.getId());
            if (dto.getLabelStrId() != null && dto.getLabelStrId() != -1) {
                vo.setSpecTypeNameStrId(dto.getLabelStrId());
            } else {
                vo.setSpecTypeName(dto.getLabelStr());
            }
            vo.setDesciption(dto.getDescription());
            vo.setTemplateName(dto.getTemplateBaseName());
            vos.add(vo);
        });

        return vos;
    }

    /**
     * Create or edit spec summary
     * @param userId
     * @param workgroupId
     * @param bodyVO
     */
    public void editSpecSummary(Long userId, Long workgroupId, SpecSummaryBodyVO bodyVO) {
        // create proposal spec summary
        if(bodyVO.getSpecSummaryId() == null) {
            if (bodyVO.getIsDefault()) {
                SpecSummaryDTO defaultSpecSummary = proposalMyBatisMapper.findDefaultSpecSummary(bodyVO.getSpecTypeId(), workgroupId);
                if (defaultSpecSummary != null) {
                    throw new UnexpectedException("There already exists a default spec summary for this spec type.");
                }
            }
            SpecType specType = specTypeRepository.findById(bodyVO.getSpecTypeId()).orElse(null);
            if (specType == null) {
                throw new NotFoundException(i18NUtils.getMessage(StringID.SPEC_SUMMARY_TYPE_REQUIRED));
            }
            ProposalSpecSummary specSummary = new ProposalSpecSummary();
            specSummary.setSpecTypeId(bodyVO.getSpecTypeId());
            specSummary.setWorkgroupId(workgroupId);
            specSummary.setPropertyTypeId(specType.getPrPropertyTypeId());
            specSummary.setName(bodyVO.getName());
            specSummary.setDefault(bodyVO.getIsDefault());
            specSummary.setCreateUserId(userId);
            specSummary.setModDate(null);
            ProposalSpecSummary summary = proposalSpecSummaryRepository.saveAndFlush(specSummary);

            List<PropertyTypeDTO> propertyTypes = propertyMyBatisMapper.findTree(specType.getPrPropertyTypeId());
            propertyTypes.forEach(propertyType -> {
                ProposalSpecSummaryItem item = new ProposalSpecSummaryItem();
                item.setProposalSpecSummaryId(summary.getId());
                item.setPropertyTypeId(propertyType.getPropertyTypeId());
                item.setCreateUserId(userId);
                proposalSpecSummaryItemRepository.save(item);
            });
        } else {
            if(bodyVO.getName() == null) {
                throw new UnexpectedException("The spec summary name is required.");
            }
            ProposalSpecSummary specSummary = proposalSpecSummaryRepository.findById(bodyVO.getSpecSummaryId()).orElse(null);
            if (bodyVO.getIsDefault()) {
                SpecSummaryDTO defaultSpecSummary = proposalMyBatisMapper.findDefaultSpecSummary(specSummary.getSpecTypeId(), specSummary.getWorkgroupId());
                if (defaultSpecSummary != null && defaultSpecSummary.getSpecSummaryId() != bodyVO.getSpecSummaryId().longValue()) {
                    throw new UnexpectedException("There already exists a default spec summary for this spec type.");
                }
            }
            specSummary.setName(bodyVO.getName());
            specSummary.setDefault(bodyVO.getIsDefault());
            specSummary.setModUserId(userId);
            specSummary.setModDate(LocalDateTime.now());
            proposalSpecSummaryRepository.save(specSummary);
        }
    }

    /**
     * Delete spec summary
     * @param specSummaryId
     */
    public void deleteSpecSummary(Long specSummaryId) {
        ProposalSpecSummary specSummary = proposalSpecSummaryRepository.findById(specSummaryId).orElse(null);
        if (specSummary == null) {
            throw new NotFoundException("Proposal spec summary not found");
        }
        specSummary.setIsDisabled(1L);
        specSummary.setDefault(false);
        proposalSpecSummaryRepository.save(specSummary);
    }

}
