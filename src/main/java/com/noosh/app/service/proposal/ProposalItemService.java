package com.noosh.app.service.proposal;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.entity.breakout.Breakout;
import com.noosh.app.commons.entity.breakout.BreakoutType;
import com.noosh.app.commons.entity.quote.QuoteItem;
import com.noosh.app.commons.entity.quote.QuotePrice;
import com.noosh.app.repository.jpa.breakout.BreakoutRepository;
import com.noosh.app.repository.jpa.quote.QuotePriceRepository;
import com.noosh.app.service.quote.QuotePriceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 4/1/2022
 */
@Service
@Transactional(readOnly = true)
public class ProposalItemService {

    @Autowired
    private QuotePriceService quotePriceService;
    @Autowired
    private BreakoutRepository breakoutRepository;
    @Autowired
    private QuotePriceRepository quotePriceRepository;

    public BigDecimal getVisibleAggregatedPrice(long quantity, QuoteItem quoteItem, BreakoutType breakoutType) {
        List<QuotePrice> pBeans = getQuotePriceBeanForQty(quantity, quoteItem) ;
        BigDecimal price = BigDecimal.ZERO;
        for (QuotePrice pBean : pBeans) {
            if (pBean.isVisibleToBuyer() ) {
                QuotePrice quotePrice = quotePriceService.getAggregatedQuotePrice(pBean);
                Breakout breakout = getBreakoutByType(quotePrice, breakoutType);
                if (breakout != null) {
                    price = breakout.getPrice() != null ? price.add(breakout.getPrice()) : price;
                }

                if (pBean.getParentQuotePriceId() != null) {
                    List<QuotePrice> childrens = quotePriceRepository.findAllByParentQuotePriceId(pBean.getParentQuotePriceId());
                    if (childrens != null && childrens.size() > 0) {
                        for (QuotePrice children : childrens) {
                            Breakout childBkBean = getBreakoutByType(children, breakoutType);
                            if (childBkBean != null) {
                                price = childBkBean.getPrice() != null ? price.add(childBkBean.getPrice()) : price;
                            }
                        }
                    }
                }
            }
        }
        return price;
    }

    public List<QuotePrice> getQuotePriceBeanForQty(long quantity, QuoteItem quoteItem) {
        // convert to 100th in long format for better comparison
        long lQty = (long) (quantity * 100 + 0.5);
        List<QuotePrice> priceBeans = quoteItem.getQuotePrices() ;
        List pList = new ArrayList();
        if (priceBeans != null && priceBeans.size() > 0) {
            for (QuotePrice priceBean : priceBeans) {
                // convert to 100th in long format for integer comparison
                long myQty = (long) (priceBean.getQuantity() * 100 + 0.5) ;
                if (myQty == lQty) {
                    pList.add(priceBean);
                }
            }
        }
        return pList;
    }

    public Breakout getBreakoutByType(QuotePrice quotePrice, BreakoutType bt) {
        List<Breakout> breakouts = breakoutRepository.findByObjectIdAndObjectClassId(quotePrice.getId(), ObjectClassID.QUOTE_PRICE);
        for (Breakout breakout : breakouts) {
            if (breakout.getBreakoutTypeId().longValue() == bt.getId()) {
                return breakout;
            }
        }
        return null;
    }

}
