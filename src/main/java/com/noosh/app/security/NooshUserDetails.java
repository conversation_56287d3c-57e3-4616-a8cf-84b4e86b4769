package com.noosh.app.security;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

/**
 * @Author: <PERSON><PERSON>yu Hu
 * @Date: 12/28/2015
 */
public class NooshUserDetails extends User {

    private static final long serialVersionUID = -2877941941914774692L;

    private long personId;

    private long accountUserId;

    private long workGroupId;

    private long workGroupTypeId;

    private String timezone;

    private String locale;

    private String currency;

    private String loginAs;
    
    private String portalName;

    private String timeZoneCode;

    private String firstName;

    private String lastName;

    private Long localeId;

    private Long impersonateUserId;

    public NooshUserDetails(String username, String password, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, true, true, true, true, authorities);
    }

    public NooshUserDetails(String username, String password, boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public long getPersonId() {
        return personId;
    }

    public void setPersonId(long personId) {
        this.personId = personId;
    }

    public long getAccountUserId() {
        return accountUserId;
    }

    public void setAccountUserId(long accountUserId) {
        this.accountUserId = accountUserId;
    }

    public long getWorkGroupId() {
        return workGroupId;
    }

    public void setWorkGroupId(long workGroupId) {
        this.workGroupId = workGroupId;
    }

    public long getWorkGroupTypeId() {
        return workGroupTypeId;
    }

    public void setWorkGroupTypeId(long workGroupTypeId) {
        this.workGroupTypeId = workGroupTypeId;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getLoginAs() {
        return loginAs;
    }

    public void setLoginAs(String loginAs) {
        this.loginAs = loginAs;
    }

    public String getPortalName() {
    	if(null==portalName||("").equals(portalName.trim())){
    		return "default";
    	}
		return portalName;
	}

	public void setPortalName(String portalName) {
		this.portalName = portalName;
	}

	public String getTimeZoneCode() {
        return timeZoneCode;
    }

    public void setTimeZoneCode(String timeZoneCode) {
        this.timeZoneCode = timeZoneCode;
    }

    public Long getLocaleId() {
        return localeId;
    }

    public void setLocaleId(Long localeId) {
        this.localeId = localeId;
    }

    public Long getImpersonateUserId() {
        return impersonateUserId;
    }

    public void setImpersonateUserId(Long impersonateUserId) {
        this.impersonateUserId = impersonateUserId;
    }
}
