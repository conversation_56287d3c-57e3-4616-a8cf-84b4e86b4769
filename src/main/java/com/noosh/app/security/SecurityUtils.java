package com.noosh.app.security;

import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * Utility class for Spring Security.
 */
public final class SecurityUtils {

    private SecurityUtils() {
    }

    public static NooshUserDetails getNooshUserDetails() {
        NooshUserDetails nooshUserDetails = null;
        SecurityContext securityContext = SecurityContextHolder.getContext();
        if (securityContext.getAuthentication() != null) {
            if (securityContext.getAuthentication().getPrincipal() != null && securityContext.getAuthentication().getPrincipal() instanceof NooshUserDetails) {
                nooshUserDetails = (NooshUserDetails) securityContext.getAuthentication().getPrincipal();
            } else if (securityContext.getAuthentication().getDetails() != null && securityContext.getAuthentication().getDetails() instanceof NooshUserDetails) {
                nooshUserDetails = (NooshUserDetails) securityContext.getAuthentication().getDetails();
            }
        }
        return nooshUserDetails;
    }

    public static long getCurrentAccountUserId() {
        long accountUserId = -1;
        NooshUserDetails nooshUserDetails = getNooshUserDetails();
        if (nooshUserDetails != null) {
            accountUserId = nooshUserDetails.getAccountUserId();
        }
        return accountUserId;
    }

    public static long getCurrentUserWorkGroupId() {
        long workGroupId = -1;
        NooshUserDetails nooshUserDetails = getNooshUserDetails();
        if (nooshUserDetails != null) {
            workGroupId = nooshUserDetails.getWorkGroupId();
        }
        return workGroupId;
    }

    public static String getUserTimezoneCode() {
        String timezoneCode = "GMT";
        NooshUserDetails nooshUserDetails = getNooshUserDetails();
        if (nooshUserDetails != null) {
            timezoneCode = nooshUserDetails.getTimeZoneCode();
        }
        return timezoneCode;
    }

    public static String getUserDisplayName() {
        NooshUserDetails nooshUserDetails = getNooshUserDetails();
        if (nooshUserDetails != null) {
            return nooshUserDetails.getFirstName() + " " + nooshUserDetails.getLastName();
        }
        return nooshUserDetails.getUsername();
    }

}
