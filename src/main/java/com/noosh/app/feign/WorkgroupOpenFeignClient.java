package com.noosh.app.feign;

import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 6/9/2023
 */
@FeignClient(name = "workgroupresource")
public interface WorkgroupOpenFeignClient {

    @PostMapping(value = "/api/customAttribute/findCustomAttributesByPropertyIds")
    Map<Long, Map<String, Object>> findCustomAttribute(@RequestBody List<Long> propertyIds);

    @GetMapping(value = "/api/dualCurrency/getSupplierDualCurrency")
    Double getSupplierDualCurrency(@RequestParam("groupId") Long groupId,
                                   @RequestParam("buWorkgroupId") Long buWorkgroupId,
                                   @RequestParam("currencyId") Long currencyId);

    @GetMapping(value = "/api/dualCurrency/getSupplierDualCurrencyByTargetGroupId")
    Double getSupplierDualCurrencyByTargetGroupId(@RequestParam("groupId") Long groupId,
                                                  @RequestParam("targetGroupId") Long targetGroupId);
}
