package com.noosh.app.feign;

import com.noosh.app.commons.vo.account.SupplierFlagVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 9/25/19
 */
@FeignClient(name = "accountresource")
public interface AccountOpenFeignClient {
    @RequestMapping(method = RequestMethod.GET, value = "/api/accountUser/{userId}")
    String getUserById(@PathVariable("userId") Long userId);

    @RequestMapping(method = RequestMethod.GET, value = "/api/flag/supplier")
    SupplierFlagVO getSupplierFlagVO(
            @RequestParam(value = "isRealTimeQuery", required = true, defaultValue = "false") Boolean isRealTimeQuery,
            @RequestParam(value = "objectId", required = true) Long objectId,
            @RequestParam(value = "objectClassId", required = true) Long objectClassId,
            @RequestParam(value = "ownerWorkgroupId", required = true) Long ownerWorkgroupId,
            @RequestParam(value = "supplierWorkgroupId", required = true) Long supplierWorkgroupId);

    @RequestMapping(method = RequestMethod.GET, value = "/api/preferences/group")
    Map<String, String> getPreferencesForGroup(
            @RequestParam(value = "workgroupId") Long workgroupId,
            @RequestParam(value = "preferenceIds", required = false) List<String> preferenceIds);

    @RequestMapping(method = RequestMethod.GET, value = "/api/preferences/user")
    Map<String, String> getPreferencesForUser(
            @RequestParam(value = "workgroupId") Long workgroupId,
            @RequestParam(value = "userId") Long userId,
            @RequestParam(value = "preferenceIds", required = false) List<String> preferenceIds);

    @RequestMapping(method = RequestMethod.GET, value = "/api/preference/check/group")
    String getPreferenceForGroup(
            @RequestParam(value = "preferenceId") String preferenceId,
            @RequestParam(value = "workgroupId") Long workgroupId);

    @RequestMapping(method = RequestMethod.GET, value = "/api/preference/check/user")
    String getPreferenceForUser(
            @RequestParam(value = "preferenceId") String preferenceId,
            @RequestParam(value = "workgroupId") Long workgroupId,
            @RequestParam(value = "userId") Long userId);

    @RequestMapping(method = RequestMethod.GET, value = "/api/permission/check")
    Boolean checkPermission(
            @RequestParam(value = "permissionId") Long permissionId,
            @RequestParam(value = "workgroupId") Long workgroupId,
            @RequestParam(value = "userId") Long userId,
            @RequestParam(value = "projectId") Long projectId);

    @RequestMapping(method = RequestMethod.GET, value = "/api/permission/check/bulk")
    Map<String, Boolean> getPermissionMap(
            @RequestParam(value = "permissionIds") List<Long> permissionIds,
            @RequestParam(value = "workgroupId") Long workgroupId,
            @RequestParam(value = "userId") Long userId,
            @RequestParam(value = "projectIds") List<Long> projectIds);

    @PostMapping(value = "/api/preference/update/user")
    void updatePreferencesForUser(
            @RequestParam(value = "workgroupId") Long workgroupId,
            @RequestParam(value = "userId") Long userId,
            @RequestBody Map<String, String> preferences);

    @GetMapping(value = "/api/permission/check/workgroup/cannot")
    Boolean checkWorkgroupLevelCannotPrivilege(
            @RequestParam(value = "permissionId") Long permissionId,
            @RequestParam(value = "workgroupId") Long workgroupId,
            @RequestParam(value = "userId") Long userId);

    @GetMapping(value = "/api/permission/checkForAdminTeam")
    Boolean checkForAdminTeam(
            @RequestParam(value = "permissionId") Long permissionId,
            @RequestParam(value = "objectId") Long objectId,
            @RequestParam(value = "objectClassId") Long objectClassId,
            @RequestParam(value = "userId") Long userId);

    @GetMapping(value = "/api/permission/bulkCheckForAdminTeam")
    Map<Long, Boolean> bulkCheckForAdminTeam(
            @RequestParam(value = "permissionIds") List<Long> permissionIds,
            @RequestParam(value = "objectId") Long objectId,
            @RequestParam(value = "objectClassId") Long objectClassId,
            @RequestParam(value = "userId") Long userId);
}
