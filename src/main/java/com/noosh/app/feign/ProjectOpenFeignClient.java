package com.noosh.app.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * User: leilaz
 * Date: 7/9/20
 */
@FeignClient(name = "projectresource")
public interface ProjectOpenFeignClient {
    @Deprecated
    @RequestMapping(method = RequestMethod.GET, value = "/team/accessProject")
    String accessProject(@RequestParam("projectId") Long projectId);

    @RequestMapping(method = RequestMethod.GET, value = "/collaboration/object/{objectId}/objectClass/{objectClassId}")
    String getContainableById(@PathVariable(value = "objectId") Long objectId, @PathVariable(value = "objectClassId") Long objectClassId);

    @RequestMapping(method = RequestMethod.GET, value = "/collaboration/object/{objectId}/objectClass/{objectClassId}/parent/{parentId}/parentClass/{parentClassId}")
    String getContainableByParentId(@PathVariable(value = "objectId") Long objectId, @PathVariable(value = "objectClassId") Long objectClassId,
                                    @PathVariable(value="parentId") Long parentId, @PathVariable(value="parentClassId") Long parentClassId);

    @RequestMapping(method = RequestMethod.GET, value = "/feign/project")
    String getProjectById(@RequestParam(value = "projectId") Long projectId);

    @GetMapping(value = "/feign/team/canAccessProject")
    Boolean canAccessProject(@RequestParam("projectId") Long projectId);

    @GetMapping("/feign/project/getProjectTreeIds")
    public List<Long> getProjectTreeIds(
            @RequestParam("projectId") Long projectId,
            @RequestParam("userId") Long userId,
            @RequestParam("workgroupId") Long workgroupId,
            @RequestParam("allowWorkgroupProjects") Boolean allowWorkgroupProjects
    );
}
