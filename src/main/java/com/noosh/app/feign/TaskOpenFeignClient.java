package com.noosh.app.feign;

import com.noosh.app.commons.dto.task.TaskUpdateDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * User: leilaz
 * Date: 9/29/19
 */
@FeignClient(name = "taskresource")
public interface TaskOpenFeignClient {
    @RequestMapping(method = RequestMethod.GET, value = "/task/type/{taskTypeId}/object/{objectId}/objectClass/{objectClassId}")
    String getTaskByType(@PathVariable(value = "taskTypeId") Long taskTypeId,
                         @PathVariable(value = "objectClassId") Long objectClassId, @PathVariable(value = "objectId") Long objectId);

    @RequestMapping(value = "/task/update/user/{userId}", method = RequestMethod.PUT)
    String updateTask(@RequestBody TaskUpdateDTO taskUpdateDTO, @PathVariable(value = "userId") Long userId);

}
