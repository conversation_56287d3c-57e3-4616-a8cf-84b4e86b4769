package com.noosh.app.feign;

import io.micrometer.core.annotation.Timed;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 9/26/19
 */
@FeignClient(name = "specresource")
public interface SpecOpenFeignClient {
    @RequestMapping(method = RequestMethod.GET, value = "/spec")
    String getSpecListByIds(@RequestParam(value = "id") List<Long> specIds, @RequestParam(value = "projectId") Long projectId);

    @GetMapping(value = "/feign/spec/smartForms")
    @Timed
    Map<Long, Map<String, Object>> findSmartFormSpecsByProjectId(@RequestParam(value = "projectId") Long projectId);
}
