package com.noosh.app.feign;

import com.noosh.app.commons.dto.tracking.TrackingDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * User: leilaz
 * Date: 9/27/19
 */
@FeignClient(name = "trackingresource")
public interface TrackingOpenFeignClient {
    @RequestMapping(method = RequestMethod.POST, value = "/tracking/create")
    String createTracking(@RequestBody TrackingDTO trackingDTO);
}
