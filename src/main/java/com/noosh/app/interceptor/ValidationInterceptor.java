package com.noosh.app.interceptor;

import com.noosh.app.commons.entity.security.Locale;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.repository.jpa.security.LocaleRepository;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Common Http Request Validation
 * 1. invalid locale
 * <AUTHOR>
 * @since 0.0.1
 */
@Component
public class ValidationInterceptor implements AsyncHandlerInterceptor {

    private List<String> localeCodes = new ArrayList<>();

    @Autowired
    private LocaleRepository localeRepository;

    @PostConstruct
    public void init() {
        List<Locale> locales = localeRepository.findAll();
        localeCodes = locales.stream()
                .map(Locale::getLocaleCode)
                .collect(Collectors.toList());
    }


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // check locale
        String locale = request.getParameter("locale");
        if (StringUtils.isNotEmpty(locale)) {
            if(!localeCodes.contains(locale)) {
                throw new NotFoundException(locale + " not found");
            }
        }

        return true;
    }
}
