package com.noosh.app.exception;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.util.StdDateFormat;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;

import java.io.IOException;

public class JSONAccessDeniedHandler implements AccessDeniedHandler {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) throws IOException {
        response.setContentType("application/json;charset=UTF-8");

        if (accessDeniedException instanceof org.springframework.security.web.csrf.CsrfException) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
        } else {
            response.setStatus(HttpStatus.FORBIDDEN.value());
        }

        if (logger.isDebugEnabled()) {
            logger.debug("Access denied: {}", accessDeniedException.getMessage());
        }

        String requestUri = request.getRequestURI() != null ? request.getRequestURI() : "Unknown";

        ErrorDto error = new ErrorDto(response.getStatus(),
                response.getStatus() == HttpStatus.UNAUTHORIZED.value() ? HttpStatus.UNAUTHORIZED.getReasonPhrase() : HttpStatus.FORBIDDEN.getReasonPhrase(),
                accessDeniedException.getLocalizedMessage(),
                requestUri);

        ObjectMapper mapper = new ObjectMapper();
        mapper.setDateFormat(new StdDateFormat().withColonInTimeZone(false));
        mapper.writeValue(response.getWriter(), error);
    }
}