package com.noosh.app.exception;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Date;

/*
 * <AUTHOR>
 * @since 1/24/2020
 * This class tries really hard to mimic the default Spring error response
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ErrorDto {
    private Date timestamp;
    @JsonInclude(JsonInclude.Include.NON_DEFAULT) //default being 0
    private int status;
    private String error;
    private String message;
    private String path;

    public ErrorDto(int status, String error, String message, String path) {
        this.timestamp = Date.from(ZonedDateTime.now(ZoneOffset.UTC).toInstant());
        this.status = status;
        this.error = error;
        this.message = message;
        this.path = path;
    }

    public ErrorDto(String message) {
        this.message = message;
    }

    public Date getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
}
