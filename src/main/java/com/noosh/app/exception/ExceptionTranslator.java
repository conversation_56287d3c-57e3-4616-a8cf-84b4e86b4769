package com.noosh.app.exception;

import com.noosh.app.commons.vo.ErrorVO;
import feign.FeignException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
@RestControllerAdvice
public class ExceptionTranslator {

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleMissingServletRequestParameterException(MissingServletRequestParameterException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleIllegalArgumentException(IllegalArgumentException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(NotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    public ErrorVO handleNotFoundException(NotFoundException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(NoPreferenceException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ResponseBody
    public ErrorVO handleNoPreferenceException(NoPreferenceException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(NoPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ResponseBody
    public ErrorVO handleNoPermissionException(NoPermissionException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(FeignException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleFeignException(FeignException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(FeignInvokeBusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleFeignInvokeBusinessException(FeignInvokeBusinessException ex) {
        return new ErrorVO(ex.getMessage());
    }
}
