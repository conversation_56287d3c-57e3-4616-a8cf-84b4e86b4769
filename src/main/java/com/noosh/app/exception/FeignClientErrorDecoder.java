package com.noosh.app.exception;

import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @Author: <PERSON>henyu Hu
 * @Date: 7/18/2022
 */

@Configuration
public class FeignClientErrorDecoder implements ErrorDecoder {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String BUSINESS_ERROR_MESSAGE = "msg";
    public static final String DEFAULT_MESSAGE = "message";
    public static final String DEFAULT_ERROR_MESSAGE = "error";

    @Override
    public Exception decode(String s, Response response) {
        int status = response.status();

        String responseBody;
        String errorMessage;
        try {
            responseBody = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
        } catch (IOException e) {
            e.printStackTrace();
            return new UnexpectedException(e.getMessage());
        }
        JSONObject jsonResponse = new JSONObject(responseBody);
        if (jsonResponse.has(BUSINESS_ERROR_MESSAGE) && jsonResponse.get(BUSINESS_ERROR_MESSAGE) != null) {
            errorMessage = jsonResponse.getString(BUSINESS_ERROR_MESSAGE);
        } else if (jsonResponse.has(DEFAULT_MESSAGE) || jsonResponse.has(DEFAULT_ERROR_MESSAGE)) {
            String error = jsonResponse.has(DEFAULT_ERROR_MESSAGE) && jsonResponse.get(DEFAULT_ERROR_MESSAGE) != null ?
                    jsonResponse.getString(DEFAULT_ERROR_MESSAGE) : "";
            String msg = jsonResponse.has(DEFAULT_MESSAGE) && jsonResponse.get(DEFAULT_MESSAGE) != null ?
                    jsonResponse.getString(DEFAULT_MESSAGE) : "";
            errorMessage = error + " - " + msg;
        } else {
            errorMessage = responseBody;
        }
        errorMessage += String.format(" (Feign Request: [%d] [%s] %s)", status,
                response.request().httpMethod().name(), response.request().url());
        logger.error(errorMessage);

        if (status == HttpStatus.FORBIDDEN.value()) {
            return new ForbiddenException(errorMessage);
        } else if (status == HttpStatus.NOT_FOUND.value()) {
            return new NotFoundException(errorMessage);
        } else {
            return new FeignInvokeBusinessException(errorMessage);
        }
    }
}
