package com.noosh.app.mapper.terms;

import com.noosh.app.commons.dto.terms.AcTermsDTO;
import com.noosh.app.commons.entity.terms.AcTerms;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * User: leilaz
 * Date: 10/12/17
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AcTermsMapper {
    public AcTermsDTO toDTO (AcTerms acTerms);
}
