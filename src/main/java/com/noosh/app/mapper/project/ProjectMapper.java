package com.noosh.app.mapper.project;

import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.project.Project;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class ProjectMapper {

    public ProjectDTO toDTO(Project project) {
        return new ProjectDTO(project);
    }

}
