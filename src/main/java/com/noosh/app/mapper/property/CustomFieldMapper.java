package com.noosh.app.mapper.property;

import com.noosh.app.commons.constant.CustomFieldControlID;
import com.noosh.app.commons.dto.custom.CustomFieldControlDTO;
import com.noosh.app.commons.dto.custom.CustomFieldDTO;
import com.noosh.app.commons.dto.customfield.UserFieldDTO;
import com.noosh.app.commons.entity.property.CustomField;
import com.noosh.app.commons.vo.custom.CustomFieldVO;
import com.noosh.app.service.util.Util;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

/**
 * User: leilaz
 * Date: 11/29/17
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class CustomFieldMapper {
    @Inject
    private CustomFieldControlMapper customFieldControlMapper;

    @Mapping(source = "customFieldControl", target = "customFieldControl", ignore = true)
    public abstract CustomFieldDTO toDTOWithoutControl(CustomField customField);

    public CustomFieldDTO toDTO(CustomField customField) {
        CustomFieldDTO customFieldDTO = toDTOWithoutControl(customField);
        if (customField != null && customField.getCustomFieldControl() != null) {
            CustomFieldControlDTO customFieldControlDTO = customFieldControlMapper.toDTO(customField.getCustomFieldControl());
            customFieldDTO.setCustomFieldControl(customFieldControlDTO);
        }
        return customFieldDTO;
    }

    public List<CustomFieldVO> toCustomFieldVOs(List<UserFieldDTO> customFieldDTOs) {
        List<CustomFieldVO> customFieldVOs = new ArrayList<CustomFieldVO>();
        if (customFieldDTOs != null && customFieldDTOs.size() > 0) {
            for (UserFieldDTO customFieldDTO : customFieldDTOs) {
                CustomFieldVO customFieldVO = new CustomFieldVO();
                customFieldVO.setId(customFieldDTO.getParamId());
                customFieldVO.setLabel(customFieldDTO.getLabel());
                customFieldVO.setParamName(customFieldDTO.getParamName());
                customFieldVO.setIsRequired(customFieldDTO.getIsRequired());
                customFieldVO.setCustomFieldControlId(customFieldDTO.getCustomFieldControlId());
                if (customFieldDTO.getFieldValues() != null) {
                    String[] elements = Util.split(customFieldDTO.getFieldValues(), "\r\n");
                    customFieldVO.setFieldValues(elements);
                } else {
                    customFieldVO.setFieldValues(new String[]{});
                }
                if (customFieldDTO.getAttributes() != null) {
                    StringTokenizer attrs = new StringTokenizer(customFieldDTO.getAttributes(), "= ");
                    while (attrs.hasMoreTokens()) {
                        String key = attrs.nextToken("= ");
                        String value = attrs.nextToken();
                        if ("size".equalsIgnoreCase(key)) {
                            customFieldVO.setSize(value);
                        } else if ("maxlength".equalsIgnoreCase(key)) {
                            customFieldVO.setMaxLength(value);
                        } else if ("rows".equalsIgnoreCase(key)) {
                            customFieldVO.setRows(value);
                        } else if ("cols".equalsIgnoreCase(key)) {
                            customFieldVO.setCols(value);
                        }
                    }
                }
                if (customFieldDTO.getAttributes() == null && null != customFieldDTO.getCustomFieldControlId()) {
                    switch (Integer.parseInt(customFieldDTO.getCustomFieldControlId().toString())) {
                        case (int) CustomFieldControlID.INT:
                            customFieldVO.setSize("25");
                            customFieldVO.setMaxLength("18");
                            break;
                        case (int)CustomFieldControlID.NUMBER:
                            customFieldVO.setSize("25");
                            customFieldVO.setMaxLength("18");
                            break;
                        case (int)CustomFieldControlID.TEXT:
                            customFieldVO.setSize("25");
                            customFieldVO.setMaxLength("25");
                            break;
                        case (int)CustomFieldControlID.DATE:
                            customFieldVO.setSize("9");
                            customFieldVO.setMaxLength("25");
                            break;
                        case (int)CustomFieldControlID.MONEY:
                            customFieldVO.setSize("9");
                            customFieldVO.setMaxLength("25");
                            break;
                        case (int)CustomFieldControlID.DATETIME:
                            customFieldVO.setSize("9");
                            customFieldVO.setMaxLength("25");
                            break;
                    }
                }

                customFieldVOs.add(customFieldVO);
            }
        }
        return customFieldVOs;
    }

}
