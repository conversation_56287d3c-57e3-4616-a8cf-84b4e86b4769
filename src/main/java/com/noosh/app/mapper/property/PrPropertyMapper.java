package com.noosh.app.mapper.property;

import com.noosh.app.commons.dto.property.PropertyDTO;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import jakarta.inject.Inject;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 10/11/17
 */
@Mapper(componentModel = "spring", uses = {})
public abstract class PrPropertyMapper {
    @Inject
    PropertyAttributeMapper propertyAttributeMapper;
    @Mapping(source = "propertyAttributeSet", ignore = true, target = "propertyAttributeSet")
    public abstract PropertyDTO toDTOWithoutAttribute(Property property);

    public PropertyDTO toDTO(Property property) {
        PropertyDTO propertyDTO = toDTOWithoutAttribute(property);
        if (property != null) {
            Set<PropertyAttribute> propertyAttributes = property.getPropertyAttributeSet();
            if (propertyAttributes != null && propertyAttributes.size() > 0) {
                propertyDTO.setPropertyAttributeSet(propertyAttributes.stream().map(propertyAttributeMapper::toDTO).collect(Collectors.toSet()));
            }
        }
        return propertyDTO;
    }
}
