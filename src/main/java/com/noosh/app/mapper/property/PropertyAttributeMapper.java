package com.noosh.app.mapper.property;

import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * User: leilaz
 * Date: 10/15/17
 */
@Mapper(componentModel = "spring", uses = {})
public abstract class PropertyAttributeMapper {
    public PropertyAttributeDTO toDTO(PropertyAttribute propertyAttribute) {
        return new PropertyAttributeDTO(propertyAttribute.getId(),
                propertyAttribute.getNumberValue(), propertyAttribute.getDateValue(), propertyAttribute.getStringValue(),
                propertyAttribute.getPropertyParam().getPrDataTypeId(), propertyAttribute.getPropertyParam().getParamName());
    }

    @Mapping(target = "value", ignore = true)
    public abstract PropertyAttributeVO toVO(PropertyAttributeDTO dto);

    public abstract List<PropertyAttributeVO> toVOs(List<PropertyAttributeDTO> dtos);

    public List<PropertyAttributeDTO> getPropertyAttributeDTOs(Property property) {
        if (property == null) {
            return null;
        }
        Set<PropertyAttribute> propertyAttributes = property.getPropertyAttributeSet();
        List<PropertyAttributeDTO> propertyAttributeDTOs = new ArrayList<PropertyAttributeDTO>();
        if (propertyAttributes != null && !propertyAttributes.isEmpty()) {
            for (PropertyAttribute attribute : propertyAttributes) {
                PropertyAttributeDTO propertyAttributeDTO = new PropertyAttributeDTO(
                        attribute.getId(), attribute.getNumberValue(), attribute.getDateValue(),
                        attribute.getStringValue(), attribute.getPropertyParam().getPrDataTypeId(), attribute.getPropertyParam().getParamName());
                propertyAttributeDTO.setParamId(attribute.getPrPropertyParamId());
                propertyAttributeDTO.setPrPropertyId(attribute.getPrPropertyId());
                propertyAttributeDTOs.add(propertyAttributeDTO);
            }
        }
        return propertyAttributeDTOs;
    }

    public PropertyAttributeDTO getPaperSelection(Property property) {
        if (property == null) {
            return null;
        }
        List<PropertyAttributeDTO> propertyAttributeDTOs = getPropertyAttributeDTOs(property);
        //find the propertyAttributeDTO in propertyAttributeDTOs with the paramName = PAPER_SELECTION and there is only one
        PropertyAttributeDTO paperSelection = null;
        boolean isPaperExist = false;
        for (PropertyAttributeDTO propertyAttributeDTO : propertyAttributeDTOs) {
            if (propertyAttributeDTO.getParamName().equals("IS_PAPER_EXIST") && "1".equals(propertyAttributeDTO.getStringValue())) {
                isPaperExist = true;
            }
            if (propertyAttributeDTO.getParamName().equals("PAPER_SELECTION")) {
                paperSelection = propertyAttributeDTO;
            }
        }
        if (isPaperExist && paperSelection != null) {
            return paperSelection;
        }
        return null;
    }
}
