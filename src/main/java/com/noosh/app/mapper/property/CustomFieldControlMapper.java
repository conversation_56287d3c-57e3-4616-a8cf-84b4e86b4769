package com.noosh.app.mapper.property;

import com.noosh.app.commons.dto.custom.CustomFieldControlDTO;
import com.noosh.app.commons.dto.custom.CustomFieldTypeDTO;
import com.noosh.app.commons.entity.property.CustomFieldControl;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * User: leilaz
 * Date: 11/29/17
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class CustomFieldControlMapper {
    @Mapping(source = "customFieldType", target = "customFieldType", ignore = true)
    public abstract CustomFieldControlDTO toDTOWithOutType(CustomFieldControl customFieldControl);

    public CustomFieldControlDTO toDTO(CustomFieldControl customFieldControl) {
        CustomFieldControlDTO customFieldControlDTO = toDTOWithOutType(customFieldControl);
        if (customFieldControl.getCustomFieldType() != null) {
            CustomFieldTypeDTO customFieldTypeDTO = new CustomFieldTypeDTO();
            customFieldTypeDTO.setId(customFieldControl.getCustomFieldType().getId());
            customFieldTypeDTO.setConstantToken(customFieldControl.getCustomFieldType().getConstantToken());
            customFieldControlDTO.setCustomFieldType(customFieldTypeDTO);
        }
        return customFieldControlDTO;
    }
}
