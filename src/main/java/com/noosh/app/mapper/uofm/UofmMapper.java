package com.noosh.app.mapper.uofm;

import com.noosh.app.commons.dto.shipment.UofmDTO;
import com.noosh.app.commons.entity.uofm.Uofm;
import com.noosh.app.commons.vo.uofm.UofmVO;
import com.noosh.app.service.util.I18NUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 10/11/17
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class UofmMapper {
    @Inject
    private I18NUtils i18NUtils;

    public abstract UofmDTO toDTOWithOutDesc(Uofm uofm);

    public abstract UofmVO toBaseVO(Uofm uofm);

    public UofmDTO toDTO(Uofm uofm) {
        UofmDTO uofmDTO = toDTOWithOutDesc(uofm);
        if (uofmDTO != null) {
            uofmDTO.setPluralDescription(uofm.getPluralDescStrId() != null ? i18NUtils.getMessage(uofm.getPluralDescStrId()) : null);
            uofmDTO.setDescription(uofm.getDescriptionStrId() != null ? i18NUtils.getMessage(uofm.getDescriptionStrId()) : null);
            uofmDTO.setIsConvertible(uofm.getRelativeToBuUofmId() != null);
        }
        return uofmDTO;
    }

    public UofmVO toVO(Uofm uofm){
        UofmVO uofmVO = toBaseVO(uofm);
        if (uofmVO != null) {
            uofmVO.setIsConvertible(uofm.getRelativeToBuUofmId() != null);
        }
        return uofmVO;
    }

    public abstract UofmVO toVO(UofmDTO uofmDTO);
}
