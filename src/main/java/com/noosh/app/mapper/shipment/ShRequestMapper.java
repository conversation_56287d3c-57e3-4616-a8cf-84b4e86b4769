package com.noosh.app.mapper.shipment;

import com.noosh.app.commons.dto.security.AddressDTO;
import com.noosh.app.commons.dto.shipment.CarrierDTO;
import com.noosh.app.commons.dto.shipment.MethodDTO;
import com.noosh.app.commons.dto.shipment.RequestTypeDTO;
import com.noosh.app.commons.dto.shipment.ShRequestDTO;
import com.noosh.app.commons.entity.shipment.Request;
import com.noosh.app.commons.vo.address.AddressVO;
import com.noosh.app.commons.vo.shipment.ShRequestVO;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {})
public abstract class ShRequestMapper {

    public abstract ShRequestDTO toDTO(Request request);

    public ShRequestVO toVO(ShRequestDTO request) {
        if (request == null) {
            return null;
        }
        ShRequestVO requestVO = new ShRequestVO();
        requestVO.setPhone(request.getPhone());
        requestVO.setCompany(request.getCompany());
        requestVO.setDeliveryDate(request.getDeliveryDate());
        requestVO.setQuantity(request.getQuantity());
        requestVO.setInstruction(request.getInstruction());
        requestVO.setEmail(request.getEmail());
        requestVO.setFirstName(request.getFirstName());
        requestVO.setLastName(request.getLastName());
        requestVO.setMiddleName(request.getMiddleName());
        requestVO.setDescription(request.getDescription());
        requestVO.setOrdinalNumber(request.getOrdinalNumber());
        requestVO.setUseSpecPackaging(request.getUseSpecPackaging());
        requestVO.setWorkgroupName(request.getWorkgroupName());

        if (StringUtils.isNotEmpty(request.getCarrierOther())) {
            requestVO.setCarrierStr(request.getCarrierOther());
        } else if (request.getCarrier() != null) {
            CarrierDTO carrierDTO = request.getCarrier();
            if (carrierDTO.getNameStrId() != null && carrierDTO.getNameStrId() > 0) {
                requestVO.setCarrierStrId(carrierDTO.getNameStrId());
            } else {
                requestVO.setCarrierStr(carrierDTO.getNameStr());
            }
        }

        if (StringUtils.isNotEmpty(request.getMethodOther())) {
            requestVO.setShipMethodStr(request.getMethodOther());
        } else if (request.getMethod() != null) {
            MethodDTO methodDTO = request.getMethod();
            if (methodDTO.getNameStrId() != null && methodDTO.getNameStrId() > 0) {
                requestVO.setShipMethodStrId(methodDTO.getNameStrId());
            } else {
                requestVO.setShipMethodStr(methodDTO.getNameStr());
            }
        }

        RequestTypeDTO requestTypeDTO = request.getRequestType();
        if (requestTypeDTO != null) {
            if (requestTypeDTO.getNameStrId() != null && requestTypeDTO.getNameStrId() > 0) {
                requestVO.setRequestTypeStrId(requestTypeDTO.getNameStrId());
            }
            else {
                requestVO.setRequestTypeStr(requestTypeDTO.getNameStr());
            }
        }

        AddressDTO addressDTO = request.getAddress();
        if (addressDTO != null) {
            AddressVO addressVO = new AddressVO();
            addressVO.setCity(addressDTO.getCity());
            addressVO.setLine1(addressDTO.getLine1());
            addressVO.setLine2(addressDTO.getLine2());
            addressVO.setLine3(addressDTO.getLine3());
            addressVO.setPostal(addressDTO.getPostal());
            addressVO.setState(addressDTO.getState());
            addressVO.setCountryStrId(addressDTO.getCountry().getNameStrId());
            requestVO.setAddress(addressVO);
        }

        if (request.getCustomAttributes() != null) {
            requestVO.setCustomAttributes(request.getCustomAttributes());
        }
        return requestVO;
    }
}
