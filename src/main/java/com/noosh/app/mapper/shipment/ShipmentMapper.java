package com.noosh.app.mapper.shipment;

import com.noosh.app.commons.dto.shipment.ShRequestDTO;
import com.noosh.app.commons.dto.shipment.ShipmentDTO;
import com.noosh.app.commons.entity.shipment.Shipment;
import com.noosh.app.commons.vo.shipment.ShRequestVO;
import com.noosh.app.commons.vo.shipment.ShipmentVO;
import jakarta.inject.Inject;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.List;

/**
 * User: leilaz
 * Date: 10/11/17
 */
@Mapper(componentModel = "spring", uses = {})
public abstract class ShipmentMapper {

    @Inject
    ShRequestMapper requestMapper;

    @Mapping(target = "job", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @Mapping(target = "requests", ignore = true)
    public abstract ShipmentDTO toDTOWithoutJob(Shipment shipment);

    public ShipmentDTO toDTO(Shipment shipment) {
        ShipmentDTO shipmentDTO = toDTOWithoutJob(shipment);
        shipmentDTO.setTotalReceivedQty(shipment.getTotalReceivedQty());
        shipmentDTO.setTotalReqQty(shipment.getTotalReqQty());
        shipmentDTO.setTotalShippedQty(shipment.getTotalShippedQty());
        return shipmentDTO;
    }

    public ShipmentVO toVO(ShipmentDTO shipmentDTO) {
        ShipmentVO shipmentVO = new ShipmentVO();
        shipmentVO.setId(shipmentDTO.getShShipmentId());
        shipmentVO.setShipmentDeliveredQty(shipmentDTO.getTotalShippedQty());
        shipmentVO.setShipmentRecievedQty(shipmentDTO.getTotalReceivedQty());
        shipmentVO.setTotalReqQty(shipmentDTO.getTotalReqQty());

        if (shipmentDTO.getRequests() != null) {
            List<ShRequestVO> requestVOList = new ArrayList<>();
            for (ShRequestDTO requestDTO : shipmentDTO.getRequests()) {
                requestVOList.add(requestMapper.toVO(requestDTO));
            }
            shipmentVO.setRequests(requestVOList);
        }
        return shipmentVO;
    }

    public ShipmentVO toVO(Shipment shipment) {
        ShipmentVO shipmentVO = new ShipmentVO();
        shipmentVO.setId(shipment.getShShipmentId());
        shipmentVO.setShipmentDeliveredQty(shipment.getTotalShippedQty());
        shipmentVO.setShipmentRecievedQty(shipment.getTotalReceivedQty());
        shipmentVO.setTotalReqQty(shipment.getTotalReqQty());
        return shipmentVO;
    }
}
