package com.noosh.app.mapper.security;

import com.noosh.app.commons.dto.security.ObjectStateDTO;
import com.noosh.app.commons.entity.security.ObjectState;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * User: lukez
 * Date: 6/29/22
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class ObjectStateMapper {
    public abstract ObjectStateDTO toDTO(ObjectState objectState);

}
