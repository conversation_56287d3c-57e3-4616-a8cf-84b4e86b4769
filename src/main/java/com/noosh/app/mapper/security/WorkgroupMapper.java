package com.noosh.app.mapper.security;

import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.entity.security.Workgroup;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class WorkgroupMapper {
    public abstract WorkgroupDTO toDTO(Workgroup workgroup);
}
