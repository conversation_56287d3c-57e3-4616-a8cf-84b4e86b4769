package com.noosh.app.mapper.component;

import com.noosh.app.commons.constant.ReasonID;
import com.noosh.app.commons.constant.StringID;
import com.noosh.app.commons.dto.customfield.UserFieldDTO;
import com.noosh.app.commons.dto.order.PaymentMethodDTO;
import com.noosh.app.commons.dto.reason.WorkgroupReasonDTO;
import com.noosh.app.commons.dto.shipment.UofmDTO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.service.util.I18NUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 7/31/20
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class DropdownMapper {
    @Inject
    private I18NUtils i18NUtils;

    @Mapping(source = "name", target = "label")
    @Mapping(target = "labelStrId", expression = "java(paymentMethodDTO.getDescriptionStrId() != null ? paymentMethodDTO.getDescriptionStrId().toString() : null)")
    public abstract DropdownVO<Long> toPaymentDropdown(PaymentMethodDTO paymentMethodDTO);

    public List<DropdownVO<Long>>  toPaymentDropdowns(List<PaymentMethodDTO> paymentMethodDTOs) {
        if (paymentMethodDTOs != null && paymentMethodDTOs.size() > 0) {
            List<DropdownVO<Long>> dropdownVOs = new ArrayList<DropdownVO<Long>>();
            for (PaymentMethodDTO paymentMethodDTO : paymentMethodDTOs) {
                dropdownVOs.add(toPaymentDropdown(paymentMethodDTO));
            }
            return dropdownVOs;
        }
        return null;
    }

    public List<DropdownVO<Long>>  toSelectionReasons(List<WorkgroupReasonDTO> workgroupReasonDTOs) {
        List<DropdownVO<Long>> dropdownVOs = new ArrayList<DropdownVO<Long>>();
        DropdownVO<Long> dropdownVOBlank = new DropdownVO<Long>();
        dropdownVOBlank.setId((long) -1);
        dropdownVOBlank.setLabel("");
        dropdownVOs.add(dropdownVOBlank);
        if (workgroupReasonDTOs != null && workgroupReasonDTOs.size() > 0) {

            for (WorkgroupReasonDTO workgroupReasonDTO : workgroupReasonDTOs) {
                DropdownVO<Long> dropdownVO = new DropdownVO<Long>();
                dropdownVO.setId(workgroupReasonDTO.getReasonId());
                dropdownVO.setLabel(workgroupReasonDTO.getPcReason().getNameStr() != null
                        ? workgroupReasonDTO.getPcReason().getNameStr() : workgroupReasonDTO.getPcReason().getReasonName());
                dropdownVO.setLabelStrId(workgroupReasonDTO.getPcReason().getNameStrId() != null ? workgroupReasonDTO.getPcReason().getNameStrId().toString() : null);
                dropdownVO.setLabelStr(workgroupReasonDTO.getPcReason().getNameStr());
                dropdownVOs.add(dropdownVO);
            }

            dropdownVOs = dropdownVOs.stream().sorted(Comparator.comparing(DropdownVO :: getLabel)).collect(Collectors.toList());
        }
        DropdownVO<Long> dropdownVO = new DropdownVO<Long>();
        dropdownVO.setId(ReasonID.REASON_DESC_OTHER);
        dropdownVO.setLabel(i18NUtils.getMessage(StringID.REASON_DESC_OTHER));
        dropdownVOs.add(dropdownVO);

        return dropdownVOs;
    }

    public List<DropdownVO<Long>>  toCustomFields(List<UserFieldDTO> customFieldDTOs) {
        if (customFieldDTOs != null && customFieldDTOs.size() > 0) {
            List<DropdownVO<Long>> dropdownVOs = new ArrayList<DropdownVO<Long>>();
            for (UserFieldDTO customFieldDTO : customFieldDTOs) {
                DropdownVO<Long> dropdownVO = new DropdownVO<Long>();
                dropdownVO.setId(customFieldDTO.getParamId());
                dropdownVO.setLabel(customFieldDTO.getLabel());
                dropdownVOs.add(dropdownVO);
            }
            return dropdownVOs;
        }
        return null;
    }

    public List<DropdownVO<Long>> toBudgetTypeDropdowns(List<UserFieldDTO> customFieldDTOs) {
        List<DropdownVO<Long>> dropdownVOs = new ArrayList<DropdownVO<Long>>();
        DropdownVO<Long> unClassifiedDropdownVO = new DropdownVO<Long>();
        unClassifiedDropdownVO.setId(-1L);
        unClassifiedDropdownVO.setLabel("--Unclassified--");
        unClassifiedDropdownVO.setLabelStrId(String.valueOf(StringID.UNCLASSIFIED));
        dropdownVOs.add(unClassifiedDropdownVO);
        if (customFieldDTOs != null && customFieldDTOs.size() > 0) {
            for (UserFieldDTO customFieldDTO : customFieldDTOs) {
                if (customFieldDTO.getIncludeInTotal()) {
                    DropdownVO<Long> dropdownVO = new DropdownVO<Long>();
                    dropdownVO.setId(customFieldDTO.getId());
                    dropdownVO.setLabel(customFieldDTO.getLabel());
                    dropdownVOs.add(dropdownVO);
                }
            }
        }
        return dropdownVOs;
    }

    public List<DropdownVO<Long>> toUofmDropdowns(List<UofmDTO> uofmDTOS) {
        if (uofmDTOS != null && uofmDTOS.size() > 0) {
            List<DropdownVO<Long>> dropdownVOs = new ArrayList<DropdownVO<Long>>();
            for (UofmDTO uofmDTO : uofmDTOS) {
                DropdownVO<Long> dropdownVO = new DropdownVO<Long>();
                dropdownVO.setId(uofmDTO.getConversionFactor());
                dropdownVO.setLabelStr(uofmDTO.getPluralDescStrId().toString());
                dropdownVOs.add(dropdownVO);
            }
            return dropdownVOs;
        }
        return null;
    }

}
