package com.noosh.app.mapper.routing;

import com.noosh.app.commons.dto.routing.RoutingRecipientDTO;
import com.noosh.app.commons.entity.routing.RoutingRecipient;
import com.noosh.app.mapper.account.AccountUserMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;

/**
 * User: lukez
 * Date: 6/29/22
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class RoutingRecipientMapper {
    @Inject
    private AccountUserMapper accountUserMapper;

    @Mapping(source = "toUser", ignore = true, target = "toUser")
    public abstract RoutingRecipientDTO toBaseDTO(RoutingRecipient routingRecipient);

    public RoutingRecipientDTO toDTO(RoutingRecipient routingRecipient) {
        RoutingRecipientDTO routingRecipientDTO = toBaseDTO(routingRecipient);
        routingRecipientDTO.setToUser(accountUserMapper.toDTO(routingRecipient.getToUser()));
        return routingRecipientDTO;
    }

}
