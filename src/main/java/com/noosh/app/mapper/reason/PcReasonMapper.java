package com.noosh.app.mapper.reason;

import com.noosh.app.commons.dto.reason.PcReasonDTO;
import com.noosh.app.commons.entity.order.PcReason;
import com.noosh.app.service.util.I18NUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 8/4/20
 */
@Mapper(componentModel = "spring", uses = {})
public abstract class PcReasonMapper {
    @Inject
    I18NUtils i18NUtils;

    @Mapping(target = "reasonName", expression = "java(pcReason.getNameStrId() != null ? i18NUtils.getMessage(pcReason.getNameStrId()) : null)")
    public abstract PcReasonDTO toReasonDTO(PcReason pcReason);
}
