package com.noosh.app.mapper.reason;

import com.noosh.app.commons.dto.reason.WorkgroupReasonDTO;
import com.noosh.app.commons.entity.reason.WorkgroupReason;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;

/**
 * User: leilaz
 * Date: 8/4/20
 */
@Mapper(componentModel = "spring", uses = {})
public abstract class WorkgroupReasonMapper {
    @Inject
    private PcReasonMapper pcReasonMapper;

    @Mapping(source = "pcReason", target = "pcReason", ignore = true)
    public abstract WorkgroupReasonDTO toDTONoReason(WorkgroupReason workgroupReason);

    public WorkgroupReasonDTO toDTO(WorkgroupReason workgroupReason) {
        WorkgroupReasonDTO workgroupReasonDTO = toDTONoReason(workgroupReason);
        workgroupReasonDTO.setPcReason(pcReasonMapper.toReasonDTO(workgroupReason.getPcReason()));
        return workgroupReasonDTO;
    }

    public List<WorkgroupReasonDTO> toDTOs(List<WorkgroupReason> workgroupReasons) {
        if (workgroupReasons != null && workgroupReasons.size() > 0) {
            List<WorkgroupReasonDTO> workgroupReasonDTOs = new ArrayList<WorkgroupReasonDTO>();
            for (WorkgroupReason workgroupReason : workgroupReasons) {
                WorkgroupReasonDTO dto = toDTO(workgroupReason);
                workgroupReasonDTOs.add(dto);
            }
            return workgroupReasonDTOs;
        }
        return null;
    }
}
