package com.noosh.app.mapper.spec;

import com.noosh.app.commons.dto.spec.SpecReferenceDTO;
import com.noosh.app.commons.entity.spec.SpecReference;
import org.mapstruct.Mapper;

/**
 * User: leilaz
 * Date: 1/11/21
 */
@Mapper(componentModel = "spring", uses = {})
public interface SpecReferenceMapper {
    public SpecReferenceDTO toDTO(SpecReference specReference);
    public SpecReference toEntity(SpecReferenceDTO specReferenceDTO);
}
