package com.noosh.app.mapper.spec;

import com.noosh.app.commons.dto.spec.SpecTypeDTO;
import com.noosh.app.commons.entity.spec.SpecType;
import com.noosh.app.commons.vo.spec.SpecTypeVO;
import com.noosh.app.service.util.I18NUtils;
import org.mapstruct.Mapper;

import jakarta.inject.Inject;

/**
 * User: <PERSON>
 * Date: 5/6/2018
 */
@Mapper(componentModel = "spring", uses = {})
public abstract class SpecTypeMapper {

    @Inject
    private I18NUtils i18NUtils;

    public abstract SpecTypeDTO toDTO(SpecType specType);

    public abstract SpecTypeVO toVO(SpecTypeDTO specTypeDTO);

}
