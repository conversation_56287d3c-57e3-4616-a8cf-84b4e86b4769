package com.noosh.app.mapper;

import com.noosh.app.commons.dto.routing.RoutingRecipientDTO;
import com.noosh.app.commons.dto.routing.RoutingSlipDTO;
import com.noosh.app.commons.entity.routing.RoutingSlip;
import com.noosh.app.commons.vo.order.OrderGeneralInfoVO;
import com.noosh.app.commons.vo.order.RoutingSlipVO;
import com.noosh.app.commons.vo.order.SlipRecipientVO;
import com.noosh.app.mapper.routing.RoutingRecipientMapper;
import com.noosh.app.service.util.I18NUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 12/24/17
 */

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class RoutingSlipMapper {
    @Inject
    private I18NUtils i18NUtils;
    @Inject
    private RoutingRecipientMapper routingRecipientMapper;

    @Mapping(source = "routingRecipients", ignore = true, target = "routingRecipients")
    public abstract RoutingSlipDTO toBaseDTO(RoutingSlip routingSlip);

    public RoutingSlipDTO toDTO(RoutingSlip routingSlip) {
        RoutingSlipDTO routingSlipDTO = toBaseDTO(routingSlip);
        if (routingSlip != null) {
            List<RoutingRecipientDTO> routingRecipientDTOS = routingSlip.getRoutingRecipients().stream().map(routingRecipientMapper::toDTO).collect(Collectors.toList());
            routingSlipDTO.setRoutingRecipients(routingRecipientDTOS);
        }
        return routingSlipDTO;
    }

    RoutingSlipVO toVO(RoutingSlipDTO routingSlipDTO, Map approveMap, OrderGeneralInfoVO orderGeneralInfoVO) {
        if (routingSlipDTO != null) {
            RoutingSlipVO routingSlipVO = new RoutingSlipVO();
            routingSlipVO.setName(routingSlipDTO.getSlipName());
            routingSlipVO.setId(routingSlipDTO.getId());
            routingSlipVO.setRoutingStatus(showRoutingStatus(routingSlipDTO));
            routingSlipVO.setRoutingStatusStrId(showRoutingStatusStrId(routingSlipDTO));
            routingSlipVO.setIsShowRoutingSection(orderGeneralInfoVO.getIsUserBuyer() && (!orderGeneralInfoVO.getIsRetracted()));

            List<SlipRecipientVO> recipientVOs = new ArrayList<>();
            if (routingSlipDTO.getRoutingRecipients() != null && routingSlipDTO.getRoutingRecipients().size() > 0) {
                for (RoutingRecipientDTO recipientDTO : routingSlipDTO.getRoutingRecipients()) {
                    SlipRecipientVO recipientVO = new SlipRecipientVO();
                    recipientVO.setId(recipientDTO.getId());
                    recipientVO.setName(recipientDTO.getToUser().getFullName());
                    recipientVO.setStatus(showRecipientStatus(orderGeneralInfoVO.getShowPendingApproval(), recipientDTO));
                    recipientVO.setStatusStrId(showRecipientStatusStrId(orderGeneralInfoVO.getShowPendingApproval(), recipientDTO));
                    recipientVO.setOrderType(orderGeneralInfoVO.getShowPendingApproval() ?
                            (orderGeneralInfoVO.getIsCloseChangeOrder() ? "Closing Order" : (orderGeneralInfoVO.getIsChangeOrder() ? "Change Order" : "")) : "");
                    if (!orderGeneralInfoVO.getShowPendingApproval()) {
                        if (((routingSlipDTO.getIsAllAtOnce() == null || routingSlipDTO.getIsAllAtOnce() == 0) && (recipientDTO.getId() == routingSlipDTO.getCurrentRecipientId().longValue()))
                                || (routingSlipDTO.getIsAllAtOnce() != null && routingSlipDTO.getIsAllAtOnce() == 1)) {
                            recipientVO.setIsFontBold(true);
                        } else {
                            recipientVO.setIsFontBold(false);
                        }
                    } else {
                        recipientVO.setIsFontBold(recipientDTO.getResponse() != 0 && recipientDTO.getResponse() != 2);
                    }
                    if (orderGeneralInfoVO.getShowPendingApproval() && recipientDTO.getResponse() != null && recipientDTO.getResponse() == 1) {
                        continue;
                    }
                    if (!(orderGeneralInfoVO.getShowPendingApproval() && ((routingSlipDTO.getIsAllAtOnce() != null && routingSlipDTO.getIsAllAtOnce() == 1)
                            || (routingSlipDTO.getIsSequentialApprovals() != null && routingSlipDTO.getIsSequentialApprovals() == 1)))) {
                        if (routingSlipDTO.getIsAllAtOnce() == null || routingSlipDTO.getIsAllAtOnce() == 0) {
                            if (recipientDTO.getId().longValue() == routingSlipDTO.getCurrentRecipientId()) {
                                recipientVOs.add(recipientVO);
                                break;
                            }
                        }
                    }
                    recipientVOs.add(recipientVO);
                }
            }

            routingSlipVO.setRecipients(recipientVOs);
            routingSlipVO.setApproveMap(approveMap);
            return routingSlipVO;
        }
        return null;
    }

    private String showRoutingStatusStrId(RoutingSlipDTO routingSlip) {
        String status = "5409";
        if (routingSlip.getIsSequentialApprovals() != null && routingSlip.getIsSequentialApprovals().shortValue() == 1) {
            if (routingSlip.getHasAllRecipientsApproved()) {
                status = "5323";
            } else if (routingSlip.getDisapprovedCount() != 0) {
                status = "5324";
            }
        } else {
            if (routingSlip.getApprovedCount() != 0) {
                status = "5323";
            } else if (routingSlip.getApprovedWithChangesCount() != 0) {
                status = "5325";
            } else if (routingSlip.getDisapprovedCount() != 0) {
                status = "5324";
            }
        }
        return status;
    }

    private String showRecipientStatusStrId(boolean showPendingApproval, RoutingRecipientDTO recipient) {
        String status = "";
        if (!showPendingApproval) {
            switch (recipient.getResponse().intValue()) {
                case 1:
                    status = "5323";
                    break;
                case 2:
                    status = "5325";
                    break;
                case 0:
                    status = "5324";
                    break;
                default:
                    status = "5409";
            }
        } else {
            switch (recipient.getResponse().intValue()) {
                case 2:
                    status = "5325";
                    break;
                case 0:
                    status = "5324";
                    break;
                default:
                    status = "5409";
            }
        }
        return status;
    }

    private String showRoutingStatus(RoutingSlipDTO routingSlip) {
        String status = i18NUtils.getMessage("5409");
        if (routingSlip.getIsSequentialApprovals() != null && routingSlip.getIsSequentialApprovals().shortValue() == 1) {
            if (routingSlip.getHasAllRecipientsApproved()) {
                status = i18NUtils.getMessage("5323");
            } else if (routingSlip.getDisapprovedCount() != 0) {
                status = i18NUtils.getMessage("5324");
            }
        } else {
            if (routingSlip.getApprovedCount() != 0) {
                status = i18NUtils.getMessage("5323");
            } else if (routingSlip.getApprovedWithChangesCount() != 0) {
                status = i18NUtils.getMessage("5325");
            } else if (routingSlip.getDisapprovedCount() != 0) {
                status = i18NUtils.getMessage("5324");
            }
        }
        return status;
    }

    private String showRecipientStatus(boolean showPendingApproval, RoutingRecipientDTO recipient) {
        String status = "";
        if (!showPendingApproval) {
            switch (recipient.getResponse().intValue()) {
                case 1:
                    status = i18NUtils.getMessage("5323");
                    break;
                case 2:
                    status = i18NUtils.getMessage("5325");
                    break;
                case 0:
                    status = i18NUtils.getMessage("5324");
                    break;
                default:
                    status = i18NUtils.getMessage("5409");
            }
        } else {
            switch (recipient.getResponse().intValue()) {
                case 2:
                    status = i18NUtils.getMessage("5325");
                    break;
                case 0:
                    status = i18NUtils.getMessage("5324");
                    break;
                default:
                    status = i18NUtils.getMessage("5409");
            }
        }
        return status;
    }
}
