package com.noosh.app.mapper;

import com.noosh.app.commons.dto.costcenter.AggregatedCostCenterSummaryDTO;
import com.noosh.app.commons.dto.costcenter.AggregatedOrderCostCenterDetailDTO;
import com.noosh.app.commons.dto.order.*;
import com.noosh.app.commons.vo.costcenter.AggregatedCostCenterSummaryVO;
import com.noosh.app.commons.vo.costcenter.AggregatedOrderCostCenterDetailVO;
import com.noosh.app.commons.vo.order.AggregateOrderAmountVO;
import com.noosh.app.commons.vo.order.AggregatedOrderButtonVO;
import com.noosh.app.commons.vo.order.AggregatedOrderDetailVO;
import com.noosh.app.commons.vo.order.SpecOrderItemVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;

import java.math.BigDecimal;
import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class AggregatedOrderMapper {
    @Inject
    private OrderMapper orderMapper;

    public abstract AggregatedOrderButtonVO toAggregatedOrderButtonVO(AggregatedOrderDetailDTO orderDetailDTO);

    public abstract AggregateOrderAmountVO toAggregateOrderAmountVO(AggregateOrderAmountDTO dto);

    public AggregatedOrderDetailVO toAggregatedDetailVO(AggregatedOrderDetailDTO aggregatedOrderDetailDTO) throws Exception {
        AggregatedOrderVersionDTO aggregatedOrderVersionDTO = aggregatedOrderDetailDTO.getOrderVersionDTO();
        // 1. map aggregated original order Dto to vo
        AggregatedOrderDetailVO aggregatedOrderDetailVO = new AggregatedOrderDetailVO();
        OrderVersionDTO originalOrder = aggregatedOrderVersionDTO.getOriginalOrder();
        orderMapper.toOrderGeneralInfoVO(originalOrder, aggregatedOrderDetailVO);

        // 2. map aggregated aggregated order dto to parent vo
        OrderVersionDTO aggregatedOrder = aggregatedOrderVersionDTO.getAggregatedOrder();
        AggregatedOrderDetailVO parentOrderDetail = new AggregatedOrderDetailVO();
        orderMapper.toOrderGeneralInfoVO(aggregatedOrder, parentOrderDetail);
        aggregatedOrderDetailVO.setParentOrderItems(parentOrderDetail.getOrderItems());

        aggregatedOrderDetailVO.setParentItemizedTaxAndShippingEnabled(parentOrderDetail.isItemizedTaxAndShippingEnabled());
        aggregatedOrderDetailVO.setParentOrderNumber(parentOrderDetail.getOrderId().toString());
        aggregatedOrderDetailVO.setParentOrderReference(parentOrderDetail.getReference());
        aggregatedOrderDetailVO.setParentOversPercent(parentOrderDetail.getOversPercent());
        aggregatedOrderDetailVO.setParentUndersPercent(parentOrderDetail.getUndersPercent());
        Long valueCurrencyId = originalOrder.getValueCurrencyId();
        aggregatedOrderDetailVO.setMiscCost(originalOrder.getMiscCost() != null
                ? originalOrder.getMiscCost() : BigDecimal.ZERO);
        aggregatedOrderDetailVO.setMiscCostCurrencyId(originalOrder.getMiscCostCurrencyId() != null ? originalOrder.getMiscCostCurrencyId() : valueCurrencyId);
        aggregatedOrderDetailVO.setEnableCOReason(aggregatedOrderVersionDTO.getEnableCOReason());
        aggregatedOrderDetailVO.setOrderItemDiscountSurchargeTotal(originalOrder.getOrderItemDiscountSurchargeTotal() != null
                ? originalOrder.getOrderItemDiscountSurchargeTotal() : BigDecimal.ZERO);
        aggregatedOrderDetailVO.setOrderItemDiscountSurchargeTotalCurrencyId(valueCurrencyId);
        aggregatedOrderDetailVO.setDiscountOrSurchargeTotal(originalOrder.getDiscountOrSurchargeTotal() != null
                ? originalOrder.getDiscountOrSurchargeTotal() : BigDecimal.ZERO);
        aggregatedOrderDetailVO.setDiscountOrSurchargeTotalCurrencyId(valueCurrencyId);
        aggregatedOrderDetailVO.setRequiredApprovalInvoice(originalOrder.isRequiredApprovalInvoice());
        aggregatedOrderDetailVO.setAutoAcceptFinalInvoice(originalOrder.isAutoAcceptFinalInvoice());
        aggregatedOrderDetailVO.setInvoiceSentOnClosedOrder(originalOrder.isInvoiceSentOnClosedOrder());
        aggregatedOrderDetailVO.setParentCompletedAcceptedClosingChangeOrder(parentOrderDetail.getCompletedAndHasAcceptedClosingChangeOrder());
        aggregatedOrderDetailVO.setParentAccepted(parentOrderDetail.getIsAccepted());
        aggregatedOrderDetailVO.setParentTax(parentOrderDetail.getTax());
        aggregatedOrderDetailVO.setParentTaxCurrencyId(parentOrderDetail.getTaxCurrencyId());
        aggregatedOrderDetailVO.setParentShipping(parentOrderDetail.getShipping());
        aggregatedOrderDetailVO.setParentShippingCurrencyId(parentOrderDetail.getShippingCurrencyId());
        aggregatedOrderDetailVO.setParentOrderItemDiscountSurchargeTotal(parentOrderDetail.getOrderItemDiscountSurchargeTotal());
        aggregatedOrderDetailVO.setParentOrderItemDiscountSurchargeTotalCurrencyId(valueCurrencyId);
        aggregatedOrderDetailVO.setParentDiscountOrSurchargeTotal(parentOrderDetail.getDiscountOrSurchargeTotal());
        aggregatedOrderDetailVO.setParentDiscountOrSurchargeTotalCurrencyId(valueCurrencyId);
        aggregatedOrderDetailVO.setParentDiscountOrSurcharge(parentOrderDetail.getDiscountOrSurcharge());
        aggregatedOrderDetailVO.setParentDiscountOrSurchargeCurrencyId(parentOrderDetail.getDiscountOrSurchargeCurrencyId());
        aggregatedOrderDetailVO.setParentValueCurrency(parentOrderDetail.getValueCurrency());
        aggregatedOrderDetailVO.setParentSubTotal(aggregatedOrder.getOrderItemSubtotal());
        aggregatedOrderDetailVO.setParentSubTotalCurrencyId(valueCurrencyId);
        BigDecimal tax = parentOrderDetail.getTax() != null ? parentOrderDetail.getTax() : BigDecimal.ZERO;
        BigDecimal shipping = parentOrderDetail.getShipping() != null ? parentOrderDetail.getShipping() : BigDecimal.ZERO;
        aggregatedOrderDetailVO.setParentGrandTotal(aggregatedOrder.getOrderItemSubtotal().add(tax).add(shipping));
        aggregatedOrderDetailVO.setParentGrandTotalCurrencyId(valueCurrencyId);
        aggregatedOrderDetailVO.setHasPendingChangeOrder(aggregatedOrderVersionDTO.getHasPendingChangeOrder());
        aggregatedOrderDetailVO.setPendingChangeGrandTotal(aggregatedOrderVersionDTO.getPendingChangeGrandTotal());
        aggregatedOrderDetailVO.setPendingChangeGrandTotalCurrencyId(aggregatedOrderVersionDTO.getPendingChangeGrandTotalCurrencyId());
        if (originalOrder.getRate() != null && originalOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrder.getExCurrencyId() != null) {
            Long exValueCurrencyId = originalOrder.getExCurrencyId();
            aggregatedOrderDetailVO.setExMiscCost(originalOrder.getExMiscCost() != null
                    ? originalOrder.getExMiscCost() : BigDecimal.ZERO);
            aggregatedOrderDetailVO.setExMiscCostCurrencyId(originalOrder.getExMiscCostCurrencyId() != null ? originalOrder.getExMiscCostCurrencyId() : exValueCurrencyId);
            aggregatedOrderDetailVO.setExOrderItemDiscountSurchargeTotal(originalOrder.getExOrderItemDiscountSurchargeTotal() != null
                    ? originalOrder.getExOrderItemDiscountSurchargeTotal() : BigDecimal.ZERO);
            aggregatedOrderDetailVO.setExOrderItemDiscountSurchargeTotalCurrencyId(exValueCurrencyId);
            aggregatedOrderDetailVO.setExDiscountOrSurchargeTotal(originalOrder.getExDiscountOrSurchargeTotal() != null
                    ? originalOrder.getExDiscountOrSurchargeTotal() : BigDecimal.ZERO);
            aggregatedOrderDetailVO.setExDiscountOrSurchargeTotalCurrencyId(exValueCurrencyId);
            aggregatedOrderDetailVO.setParentExTax(parentOrderDetail.getExTax());
            aggregatedOrderDetailVO.setParentExTaxCurrencyId(parentOrderDetail.getExTaxCurrencyId());
            aggregatedOrderDetailVO.setParentExShipping(parentOrderDetail.getExShipping());
            aggregatedOrderDetailVO.setParentExShippingCurrencyId(parentOrderDetail.getExShippingCurrencyId());
            aggregatedOrderDetailVO.setParentExOrderItemDiscountSurchargeTotal(parentOrderDetail.getExOrderItemDiscountSurchargeTotal());
            aggregatedOrderDetailVO.setParentExOrderItemDiscountSurchargeTotalCurrencyId(exValueCurrencyId);
            aggregatedOrderDetailVO.setParentExDiscountOrSurchargeTotal(parentOrderDetail.getExDiscountOrSurchargeTotal());
            aggregatedOrderDetailVO.setParentExDiscountOrSurchargeTotalCurrencyId(exValueCurrencyId);
            aggregatedOrderDetailVO.setParentExDiscountOrSurcharge(parentOrderDetail.getExDiscountOrSurcharge());
            aggregatedOrderDetailVO.setParentExDiscountOrSurchargeCurrencyId(parentOrderDetail.getExDiscountOrSurchargeCurrencyId());
            aggregatedOrderDetailVO.setParentExSubTotal(aggregatedOrder.getExOrderItemSubtotal());
            aggregatedOrderDetailVO.setParentExSubTotalCurrencyId(exValueCurrencyId);
            BigDecimal exTax = parentOrderDetail.getExTax() != null ? parentOrderDetail.getExTax() : BigDecimal.ZERO;
            BigDecimal exShipping = parentOrderDetail.getExShipping() != null ? parentOrderDetail.getExShipping() : BigDecimal.ZERO;
            aggregatedOrderDetailVO.setParentExGrandTotal(aggregatedOrder.getExOrderItemSubtotal().add(exTax).add(exShipping));
            aggregatedOrderDetailVO.setParentExGrandTotalCurrencyId(exValueCurrencyId);
            aggregatedOrderDetailVO.setExPendingChangeGrandTotal(aggregatedOrderVersionDTO.getExPendingChangeGrandTotal());
            aggregatedOrderDetailVO.setExPendingChangeGrandTotalCurrencyId(exValueCurrencyId);
        }

        return aggregatedOrderDetailVO;
    }

    public abstract List<SpecOrderItemVO> toSpecOrderItemVOs (List<SpecOrderItemDTO> orderItemDTOs);

    public abstract AggregatedOrderCostCenterDetailVO toOrderCostCenterDetailVO(AggregatedOrderCostCenterDetailDTO orderCostCenterDetailDTO);

    public abstract AggregatedCostCenterSummaryVO toOrderCostCenterSummaryVO(AggregatedCostCenterSummaryDTO aggregatedCostCenterSummaryDTO);

}
