package com.noosh.app.mapper.rating;

import com.noosh.app.commons.entity.order.Order;
import com.noosh.app.commons.entity.order.OrderItem;
import com.noosh.app.commons.entity.rating.SrQuestion;
import com.noosh.app.commons.entity.rating.SrRating;
import com.noosh.app.commons.entity.rating.SrRatingItem;
import com.noosh.app.commons.entity.rating.SrSection;
import com.noosh.app.commons.vo.rating.*;
import com.noosh.app.commons.vo.spec.SpecVO;
import com.noosh.app.repository.jpa.order.OrderRepository;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 1/20/19
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class SupplierRatingMapper {
    @Inject
    private I18NUtils i18NUtils;
    @Inject
    private OrderRepository orderRepository;

    public abstract RatingQuestionVO toQuestionVO(SrQuestion question);

    public SupplierRatingVO toSupplierRating(SrRating rating) {
        SupplierRatingVO supplierRatingVO =new SupplierRatingVO();
        supplierRatingVO.setId(rating.getId());
        supplierRatingVO.setAverageGrade(rating.getAverageGrade());
        supplierRatingVO.setRatingComment(rating.getComments());
        supplierRatingVO.setGranularity(rating.getGranularity().intValue());
        supplierRatingVO.setNaAllowed(rating.getNaAllowed() != null && rating.getNaAllowed().shortValue() == (short) 1);
        supplierRatingVO.setCompletionDate(rating.getCompleteDate());

        if (rating.getSrRatingItemList() != null && rating.getSrRatingItemList().size() > 0) {
            List<SrRatingItem> ratingItems = rating.getSrRatingItemList();
            List<SupplierRatingItemVO> ratingItemVOs = new ArrayList<SupplierRatingItemVO>();
            for (SrRatingItem item : ratingItems)
            {
                SupplierRatingItemVO supplierRatingItemVO = new SupplierRatingItemVO();
                supplierRatingItemVO.setId(item.getId());
                supplierRatingItemVO.setComments(item.getComments());
                supplierRatingItemVO.setGradeWeight(item.getGrade() > 0 ? item.getGrade() * item.getSrQuestion().getWeight() / 100 : item.getGrade());
                supplierRatingItemVO.setGrade(item.getGrade());
                supplierRatingItemVO.setOrdinal(item.getSrQuestion().getOrdinal());
                supplierRatingItemVO.setPoint(item.getGrade() > 0 ? (rating.getGranularity() == 5 ? item.getGrade() / 25 + 1 : item.getGrade() / 50 + 1) : (item.getGrade() == 0 ? 1 : item.getGrade()));
                supplierRatingItemVO.setSectionId(item.getSrQuestion().getSectionId());
                supplierRatingItemVO.setQuestionId(item.getSrQuestion().getId());
                supplierRatingItemVO.setText(item.getSrQuestion().getText());
                supplierRatingItemVO.setWeight(item.getSrQuestion().getWeight());
                ratingItemVOs.add(supplierRatingItemVO);
            };
            supplierRatingVO.setRatingItem(ratingItemVOs);
        }
        return supplierRatingVO;
    }

    public SectionVO toSectionVO(SrSection section) {
        SectionVO sectionVO = new SectionVO();
        sectionVO.setId(section.getId());
        sectionVO.setTitle(section.getTitle());
        sectionVO.setDisplayOrder(section.getDisplayOrder());
        sectionVO.setStr(i18NUtils.getMessage(section.getStrId()));
        sectionVO.setStrId(section.getStrId());
        return sectionVO;
    }

    public RateByOrderItemVO toRateByOrderItemVO(OrderItem orderItem, List<SrRating> srRatings,
                                                 Long projectId, Long orderId, SpecVO specVO, boolean isChangeOrder) {
        RateByOrderItemVO rateByOrderItemVO = new RateByOrderItemVO();
        rateByOrderItemVO.setOrderItemId(orderItem.getId());
        rateByOrderItemVO.setRating(srRatings.stream()
                .map(this::toSupplierRating).collect(Collectors.toList()));
        String back = NooshOneUrlUtil.composeViewOrderLinkToEnterprise(projectId, orderId);
        if (isChangeOrder) {
            Order changeOrder = orderRepository.findById(orderId).orElse(null);
            if (changeOrder != null) {
                back = NooshOneUrlUtil.composeViewChangeOrderLinkToEnterprise(projectId, changeOrder.getParentOrderId(), orderId);
            }
        }
        specVO.setSpecLink(specVO.getSpecLink() == null ? NooshOneUrlUtil.composeViewSpecLinkForOrderItemToEnterprise(
                orderItem.getSpecNodeId(), specVO.getId(), projectId, back) : specVO.getSpecLink());
        rateByOrderItemVO.setSpec(specVO);
        return rateByOrderItemVO;
    }
}
