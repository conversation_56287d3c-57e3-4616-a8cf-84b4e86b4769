package com.noosh.app.mapper.breakout;

import com.noosh.app.commons.dto.breakout.BreakoutTypeDTO;
import com.noosh.app.commons.entity.breakout.BreakoutType;
import com.noosh.app.commons.vo.breakout.BreakoutTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * User: leilaz
 * Date: 10/11/17
 */
@Mapper(componentModel = "spring", uses = {})
public abstract class BreakoutTypeMapper {

    public abstract BreakoutTypeDTO toDTO(BreakoutType breakoutType);

    @Mapping(source = "descendents", target = "descendents", ignore = true)
    public abstract BreakoutTypeVO toVO(BreakoutTypeDTO breakoutTypeDTO);

    public BreakoutTypeVO toVOWithDescendents(BreakoutTypeDTO breakoutTypeDTO) {
        BreakoutTypeVO breakoutTypeVO = toVO(breakoutTypeDTO);
        List<BreakoutTypeDTO> descendents = breakoutTypeDTO.getDescendents();
        if (descendents != null && descendents.size() > 0) {
            for (BreakoutTypeDTO descendent : descendents ) {
                breakoutTypeVO.addToDescendents(toVOWithDescendents(descendent));
            }
        }
        return breakoutTypeVO;
    }
}
