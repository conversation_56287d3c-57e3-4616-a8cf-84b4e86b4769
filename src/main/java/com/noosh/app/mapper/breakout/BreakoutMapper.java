package com.noosh.app.mapper.breakout;

import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.entity.breakout.Breakout;
import com.noosh.app.commons.vo.breakout.BreakoutVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;

/**
 * User: leilaz
 * Date: 10/11/17
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class BreakoutMapper {
    @Inject
    private BreakoutTypeMapper breakoutTypeMapper;

    public BreakoutDTO toDTO(Breakout breakout) {
        BreakoutDTO breakoutDTO = toDTOWithoutBreakoutType(breakout);
        breakoutDTO.setBreakoutType(breakoutTypeMapper.toDTO(breakout.getBreakoutType()));
        return breakoutDTO;
    }

    public BreakoutDTO toDTO(Breakout breakout, List<Breakout> breakouts, int level) {
        BreakoutDTO breakoutDTO = toDTOWithoutBreakoutType(breakout);
        breakoutDTO.setLevel(level);
        breakoutDTO.setBreakoutType(breakoutTypeMapper.toDTO(breakout.getBreakoutType()));
        if (breakouts != null && breakouts.size() > 0) {
            for (Breakout breakout1 : breakouts) {
                if (breakout1.getBreakoutType().getParentTypeId() != null &&
                        (breakout1.getBreakoutType().getParentTypeId().longValue() == breakout.getBreakoutTypeId())) {
                    if (breakoutDTO.getDescendents() == null) {
                        List<BreakoutDTO> breakoutDTOs = new ArrayList<>();
                        breakoutDTO.setDescendents(breakoutDTOs);
                    }
                    breakoutDTO.getDescendents().add(toDTO(breakout1, breakouts, level + 1));
                }
            }
        }
        return breakoutDTO;
    }

    @Mapping(source = "breakoutType", target = "breakoutType", ignore = true)
    public abstract BreakoutDTO toDTOWithoutBreakoutType(Breakout breakout);

    @Mapping(source = "breakoutType", target = "breakoutType", ignore = true)
    @Mapping(source = "descendents", target = "descendents", ignore = true)
    public abstract BreakoutVO toVOWithoutBreakoutType(BreakoutDTO breakoutDTO);

    public BreakoutVO toVO(BreakoutDTO breakoutDTO) {
        BreakoutVO breakoutVO = toVOWithoutBreakoutType(breakoutDTO);
        breakoutVO.setBreakoutType(breakoutTypeMapper.toVO(breakoutDTO.getBreakoutType()));
        if (breakoutDTO.getDescendents() != null && breakoutDTO.getDescendents().size() > 0) {
            List<BreakoutVO> breakoutVOs = new ArrayList<BreakoutVO>();
            for (BreakoutDTO dto : breakoutDTO.getDescendents()) {
                breakoutVOs.add(toVO(dto));
            }
            breakoutVO.setDescendents(breakoutVOs);
        }
        return breakoutVO;
    }

}
