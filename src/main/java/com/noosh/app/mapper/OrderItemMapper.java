package com.noosh.app.mapper;

import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.order.OrderItemDTO;
import com.noosh.app.commons.dto.spec.SpecTypeDTO;
import com.noosh.app.commons.entity.order.Order;
import com.noosh.app.commons.entity.order.OrderItem;
import com.noosh.app.commons.vo.breakout.BreakoutVO;
import com.noosh.app.commons.vo.order.OrderItemVO;
import com.noosh.app.mapper.breakout.BreakoutMapper;
import com.noosh.app.mapper.property.PropertyAttributeMapper;
import com.noosh.app.mapper.shipment.ShipmentMapper;
import com.noosh.app.mapper.spec.SpecMapper;
import com.noosh.app.mapper.spec.SpecTypeMapper;
import com.noosh.app.mapper.uofm.UofmMapper;
import com.noosh.app.repository.jpa.order.OrderRepository;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;

/**
 * User: leilaz
 * Date: 10/11/17
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class OrderItemMapper {

    private static long REASON_OTHER = 1000008;

    @Inject
    ShipmentMapper shipmentMapper;
    @Inject
    UofmMapper uofmMapper;
    @Inject
    SpecMapper specMapper;
    @Inject
    private SpecTypeMapper specTypeMapper;
    @Inject
    private BreakoutMapper breakoutMapper;
    @Inject
    private PropertyAttributeMapper propertyAttributeMapper;
    @Inject
    private I18NUtils i18NUtils;
    @Inject
    private OrderRepository orderRepository;

    @Mapping(source = "shipment", ignore = true, target = "shipment")
    @Mapping(source = "uofm", ignore = true, target = "uofm")
    @Mapping(source = "spec", ignore = true, target = "spec")
    @Mapping(source = "defaultCurrency.currency", target = "valueCurrency")
    @Mapping(source = "reason", target = "reason", ignore = true)
    public abstract OrderItemDTO toDTOWithoutSUPS(OrderItem orderItem);

    @Mapping(source = "shipment", ignore = true, target = "shipment")
    @Mapping(source = "uofm", ignore = true, target = "uofm")

    @Mapping(source = "reason", target = "reason", ignore = true)
    @Mapping(source = "spec", ignore = true, target = "spec")
    public abstract OrderItem toEntityWithoutSUPS(OrderItemDTO orderItemDTO);

    public abstract OrderItemDTO toCopy(OrderItemDTO orderItemDTO);

    public List<OrderItem> toEntityList(List<OrderItemDTO> orderItemDTOs) {
        if (orderItemDTOs != null && orderItemDTOs.size() > 0) {
            List<OrderItem> orderItems = new ArrayList<OrderItem>();
            for (OrderItemDTO orderItemDTO : orderItemDTOs) {
                OrderItem orderItem = toEntityWithoutSUPS(orderItemDTO);
                orderItem.setSpec(specMapper.convertToEntity(orderItemDTO.getSpec()));
                orderItems.add(orderItem);
            }
            return orderItems;
        }
        return null;
    }

    public OrderItemDTO toDTO(OrderItem orderItem) {
        OrderItemDTO orderItemDTO = toDTOWithoutSUPS(orderItem);
        orderItemDTO.setShipment(shipmentMapper.toDTO(orderItem.getShipment()));
        orderItemDTO.setUofm(uofmMapper.toDTO(orderItem.getUofm()));
//        orderItemDTO.setProperty(propertyMapper.toDTO(orderItem.getProperty()));
        orderItemDTO.setSpec(specMapper.convertToSpecDTO(orderItem.getSpec()));
        String reason = null;
        if (orderItem.getReason() != null) {
            if (orderItem.getReason().getNameStr() != null) {
                reason = orderItem.getReason().getNameStr();
                orderItemDTO.setReasonStr(orderItem.getReason().getNameStr());
            } else if (orderItem.getReason().getNameStrId() != null && orderItem.getReason().getId().longValue() != REASON_OTHER) {
                reason = i18NUtils.getMessage(orderItem.getReason().getNameStrId());
                orderItemDTO.setReasonStrId(orderItem.getReason().getNameStrId().toString());
            }
        }
        orderItemDTO.setReason(reason);
        return orderItemDTO;
    }

    public OrderItemVO toVO(OrderItemDTO orderItemDTO, Long projectId, boolean isChangeOrder, boolean canManageShipment, boolean isBuyerProject) {
        return toVO(orderItemDTO, projectId, isChangeOrder, canManageShipment, isBuyerProject, false);
    }

    public OrderItemVO toVO(OrderItemDTO orderItemDTO, Long projectId, boolean isChangeOrder, boolean canManageShipment, boolean isBuyerProject, boolean isDualCurrency) {
        OrderItemVO itemVO = new OrderItemVO();
        itemVO.setId(orderItemDTO.getId());
        itemVO.setComments(orderItemDTO.getComments());
        itemVO.setAdditionalPrice(orderItemDTO.getAddPrice());
        itemVO.setAdditionalPriceCurrencyId(orderItemDTO.getAddPriceCurrencyId());
        itemVO.setIncludeAUP(orderItemDTO.getSpec().getIncludeAup());
        itemVO.setIsSpecChanged(orderItemDTO.getIsSpecChanged() != null ? orderItemDTO.getIsSpecChanged().booleanValue() : false);
        itemVO.setShipping(orderItemDTO.getShipping());
        itemVO.setShippingCurrencyId(orderItemDTO.getShippingCurrencyId());
        itemVO.setSubTotal(orderItemDTO.getSubTotal());
        itemVO.setSubTotalCurrencyId(orderItemDTO.getValueCurrencyId());
        itemVO.setTax(orderItemDTO.getTax());
        itemVO.setTaxCurrencyId(orderItemDTO.getTaxCurrencyId());
        itemVO.setUnitPrice(orderItemDTO.getPricePerUnits(orderItemDTO.getDefaultUofm() == null ? orderItemDTO.getUofm().getDefault() : orderItemDTO.getDefaultUofm().getDefault()));
        itemVO.setUnitPriceCurrencyId(orderItemDTO.getValueCurrencyId());
        itemVO.setValue(orderItemDTO.getValue());
        itemVO.setValueCurrencyId(orderItemDTO.getValueCurrencyId());
        itemVO.setCompletionDate(orderItemDTO.getCompletionDate());
        itemVO.setQuantity(orderItemDTO.getQuantity());
        itemVO.setValueCurrency(orderItemDTO.getValueCurrency());
        //dual currency
        if (isDualCurrency) {
            itemVO.setExAdditionalPrice(orderItemDTO.getExAddPrice());
            itemVO.setExAdditionalPriceCurrencyId(orderItemDTO.getExAddPriceCurrencyId());
            itemVO.setExShipping(orderItemDTO.getExShipping());
            itemVO.setExShippingCurrencyId(orderItemDTO.getExShippingCurrencyId());
            itemVO.setExSubTotal(orderItemDTO.getExSubTotal());
            itemVO.setExSubTotalCurrencyId(orderItemDTO.getExValueCurrencyId());
            itemVO.setExTax(orderItemDTO.getExTax());
            itemVO.setExTaxCurrencyId(orderItemDTO.getExTaxCurrencyId());
            itemVO.setExUnitPrice(orderItemDTO.getExPricePerUnits(orderItemDTO.getDefaultUofm() == null ? orderItemDTO.getUofm().getDefault() : orderItemDTO.getDefaultUofm().getDefault()));
            itemVO.setExUnitPriceCurrencyId(orderItemDTO.getExValueCurrencyId());
            itemVO.setExValue(orderItemDTO.getExValue());
            itemVO.setExValueCurrencyId(orderItemDTO.getExValueCurrencyId());
            itemVO.setExDiscountOrSurcharge(orderItemDTO.getExDors());
            itemVO.setExDiscountOrSurchargeCurrencyId(orderItemDTO.getExDorsCurrencyId());
        }

        itemVO.setJobId(orderItemDTO.getJobId());
        itemVO.setCustomPropertyId(orderItemDTO.getCustomPropertyId());
        itemVO.setReason(orderItemDTO.getReason());
        itemVO.setReasonOther(orderItemDTO.getReasonOther());
        itemVO.setReasonStrId(orderItemDTO.getReasonStrId());
        itemVO.setReasonStr(orderItemDTO.getReasonStr());
        itemVO.setDiscountOrSurcharge(orderItemDTO.getDors());
        itemVO.setDiscountOrSurchargeCurrencyId(orderItemDTO.getDorsCurrencyId());
		itemVO.setEstimateItemPriceId(orderItemDTO.getEstimateItemPriceId());
        if (orderItemDTO.getId() != null && orderItemDTO.getId() > 0 && canManageShipment && isBuyerProject) {
            itemVO.setEditShipmentButton(NooshOneUrlUtil.composeEditOrderItemShipmentLinkToEnterprise(projectId, orderItemDTO.getShipment().getShShipmentId()));
        }

        itemVO.setShipment(shipmentMapper.toVO(orderItemDTO.getShipment()));
        String back = NooshOneUrlUtil.composeViewOrderLinkToEnterprise(projectId, orderItemDTO.getOrderId());
        if (isChangeOrder) {
            Order changeOrder = orderRepository.findById(orderItemDTO.getOrderId()).orElse(null);
            if (changeOrder != null) {
                back = NooshOneUrlUtil.composeViewChangeOrderLinkToEnterprise(projectId, changeOrder.getParentOrderId(), orderItemDTO.getOrderId());
            }
        }

        itemVO.setSpec(specMapper.toVOForOrderItem(orderItemDTO.getSpecNodeId(), orderItemDTO.getSpec(), projectId, back));
        itemVO.getSpec().setSpecType(specTypeMapper.toVO(orderItemDTO.getSpec().getSpecType()));
        itemVO.setSpecNodeId(orderItemDTO.getSpecNodeId());
        itemVO.setUofm(uofmMapper.toVO(orderItemDTO.getUofm()));
        SpecTypeDTO specTypeDTO = orderItemDTO.getSpec().getSpecType();
        itemVO.setTimeMaterials((specTypeDTO.getIsTimeMaterials() != null && specTypeDTO.getIsTimeMaterials() == (short)1) ? Boolean.TRUE : Boolean.FALSE);
        itemVO.setBreakoutRatesStrId(orderItemDTO.getUofm().getBreakoutRatesStrId());
        itemVO.setBreakoutUnitsStrId(orderItemDTO.getUofm().getBreakoutUnitsStrId());
        itemVO.setAllowBreakouts(orderItemDTO.getAllowBreakouts());
        itemVO.setTotalFromBreakouts(orderItemDTO.getTotalFromBreakouts());
        itemVO.setIsOverridingBreakouts(orderItemDTO.getIsOverridingBreakouts());
        itemVO.setEstimateItemPriceId(orderItemDTO.getEstimateItemPriceId());
        itemVO.setBreakoutTypeId(orderItemDTO.getBreakoutTypeId());
        itemVO.setVatCode(orderItemDTO.getVatCode());
        itemVO.setVatRate(orderItemDTO.getVatRate());
        List<BreakoutVO> breakoutVOs = new ArrayList<>();
        if (orderItemDTO.getBreakouts() != null) {
            for (BreakoutDTO breakoutDTO : orderItemDTO.getBreakouts()) {
                BreakoutVO breakoutVO = breakoutMapper.toVO(breakoutDTO);
                breakoutVOs.add(breakoutVO);
            }
            itemVO.setBreakouts(breakoutVOs);
        }

        return itemVO;
    }
}
