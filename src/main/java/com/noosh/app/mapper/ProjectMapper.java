package com.noosh.app.mapper;

import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.project.Project;
import com.noosh.app.commons.vo.project.ProjectVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 10/5/15
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class ProjectMapper {

    public ProjectDTO toDTO(Project project) {
        ProjectDTO projectDTO = new ProjectDTO(project);

        return projectDTO;
    }

    public abstract Project toEntity(ProjectDTO projectDTO);

    public ProjectVO toVO(ProjectDTO projectDTO){
        ProjectVO projectVO = new ProjectVO();
        projectVO.setId(projectDTO.getId());
        projectVO.setName(projectDTO.getTitle());
        //projectVO.setIsBuyerProject(projectDTO.isBuyerProject());
        //projectVO.setIsClientProject(projectDTO.isClientProject());
        projectVO.setClientWorkgroup(projectDTO.getClientAccount());
        projectVO.setClientWorkgroupId(projectDTO.getClientWorkgroupId());
        projectVO.setIsClientNotOnNoosh(projectDTO.isClientNotOnNoosh());
        return projectVO;
    }

}
