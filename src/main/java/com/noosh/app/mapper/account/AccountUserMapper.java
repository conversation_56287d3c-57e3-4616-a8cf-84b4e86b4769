package com.noosh.app.mapper.account;

import com.noosh.app.commons.dto.account.AccountUserDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.person.Person;
import com.noosh.app.commons.vo.account.AccountUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class AccountUserMapper {

    public abstract AccountUserDTO toBaseDTO(AccountUser accountUser);
    public abstract AccountUserVO toVO(AccountUserDTO accountUserDTO);

    public AccountUserDTO toDTO(AccountUser accountUser) {
        AccountUserDTO accountUserDTO = toBaseDTO(accountUser);
        if (accountUserDTO != null) {
            Person person = accountUser.getPerson();
            accountUserDTO.setDefaultEmail(person.getDefaultEmail().getEmail());
            accountUserDTO.setFirstName(person.getFirstName());
            accountUserDTO.setMiddleName(person.getMiddleName());
            accountUserDTO.setLastName(person.getLastName());
            accountUserDTO.setProfileImg(person.getProfileImg());
        }
        return accountUserDTO;
    }

}
