package com.noosh.app.mapper.account;

import com.noosh.app.commons.dto.address.AddressDTO;
import com.noosh.app.commons.entity.account.Address;
import com.noosh.app.commons.vo.address.AddressVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AddressMapper {
    @Mapping(target = "countryStrId", expression = "java(address.getCountry() != null ? address.getCountry().getNameStrId(): null)")
    public abstract AddressVO toVO(Address address);

    public abstract List<AddressVO> toVOs(List<AddressDTO> dtos);
}
