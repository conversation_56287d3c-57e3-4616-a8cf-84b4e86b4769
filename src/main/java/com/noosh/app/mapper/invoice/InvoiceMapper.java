package com.noosh.app.mapper.invoice;

import com.noosh.app.commons.constant.StringID;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.property.PropertyDTO;
import com.noosh.app.commons.entity.invoice.Invoice;
import com.noosh.app.commons.vo.invoice.InvoiceVO;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import com.noosh.app.mapper.property.PrPropertyMapper;
import com.noosh.app.service.util.I18NUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class InvoiceMapper {
    @Inject
    PrPropertyMapper propertyMapper;
    @Inject
    private I18NUtils i18NUtils;

    @Mapping(source = "property", ignore = true, target = "customAttr")
    public abstract InvoiceDTO toDTOWithoutProperty(Invoice invoice);

    public InvoiceDTO toDTO(Invoice invoice) {
        InvoiceDTO invoiceDTO = toDTOWithoutProperty(invoice);
        if (invoice != null) {
            PropertyDTO propertyDTO = propertyMapper.toDTO(invoice.getProperty());
            if (propertyDTO != null) {
                invoiceDTO.setCustomAttr(propertyDTO.getPropertyAttributeSet());

            }
        }

        return invoiceDTO;
    }

    public static final Object[][] INVOICE_NON_BILLABLE_REASONS =
            {
                    {"1",  new Long(StringID.INVOICE_NON_BILLABLE_REASON_PREPAYMENT)},
                    {"2",  new Long(StringID.INVOICE_NON_BILLABLE_REASON_CHANGE_ORDERS)},
                    {"99", new Long(StringID.INVOICE_NON_BILLABLE_REASON_OTHER)}
            };

    @Mapping(target = "nonBillableReason", ignore = true,  source = "nonBillableReasonId")
    public abstract InvoiceVO toVONoReason(InvoiceDTO dto);

    public InvoiceVO toVO(InvoiceDTO dto) {
        InvoiceVO vo = toVONoReason(dto);
        if (vo != null) {
            if (dto.getNonBillableReasonId() != null) {
                for (int i = 0; i < INVOICE_NON_BILLABLE_REASONS.length; i++) {
                    Object[] reason = INVOICE_NON_BILLABLE_REASONS[i];
                    if (((String)reason[0]).equals("" + dto.getNonBillableReasonId())) {
                        vo.setNonBillableReason(i18NUtils.getMessage(((Long)reason[1])));
                        vo.setNonBillableReasonStrId(reason[1].toString());
                        break;
                    }
                }
            }
            if (dto.getCustomAttr() != null && dto.getCustomAttr().size() > 0) {
                List<PropertyAttributeVO> attributeVOs = new ArrayList<PropertyAttributeVO>();
                for (PropertyAttributeDTO propertyAttributeDTO : dto.getCustomAttr()) {
                    PropertyAttributeVO propertyAttributeVO = new PropertyAttributeVO(propertyAttributeDTO.getPrPropertyAttributeId(),
                            propertyAttributeDTO.getValue(), propertyAttributeDTO.getParamName(), propertyAttributeDTO.getParamId());
                    attributeVOs.add(propertyAttributeVO);
                }
                vo.setCustomAttr(attributeVOs);
            }
        }
        return vo;
    }

}
