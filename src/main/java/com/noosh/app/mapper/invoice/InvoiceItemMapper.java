package com.noosh.app.mapper.invoice;

import com.noosh.app.commons.dto.invoice.InvoiceItemDTO;
import com.noosh.app.commons.entity.invoice.InvoiceItem;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @auther mario
 * @date 7/29/2020
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class InvoiceItemMapper {

    public abstract InvoiceItemDTO toDTO(InvoiceItem invoiceItem);

}
