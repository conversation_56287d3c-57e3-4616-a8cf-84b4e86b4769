package com.noosh.app.mapper;

import com.noosh.app.commons.dto.order.OrderStateDTO;
import com.noosh.app.commons.entity.order.OrderState;
import com.noosh.app.mapper.account.AccountUserMapper;
import com.noosh.app.mapper.security.ObjectStateMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;

/**
 * User: lukez
 * Date: 6/29/22
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class OrderStateMapper  {

    @Inject
    AccountUserMapper accountUserMapper;
    @Inject
    ObjectStateMapper objectStateMapper;

    @Mapping(source = "creator", ignore = true, target = "creator")
    @Mapping(source = "objectState", ignore = true, target = "objectState")
    public abstract OrderStateDTO toBaseDTO(OrderState orderState);

    public OrderStateDTO toDTO(OrderState orderState) {
        OrderStateDTO orderStateDTO = toBaseDTO(orderState);
        orderStateDTO.setCreator(accountUserMapper.toDTO(orderState.getCreator()));
        orderStateDTO.setObjectState(objectStateMapper.toDTO(orderState.getObjectState()));
        return orderStateDTO;
    }
}
