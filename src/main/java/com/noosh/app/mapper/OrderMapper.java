package com.noosh.app.mapper;

import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.account.AccountUserDTO;
import com.noosh.app.commons.dto.costcenter.InvoiceOrderCostCenterDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDetailDTO;
import com.noosh.app.commons.dto.costcenter.ProjectCostCenterDetailDTO;
import com.noosh.app.commons.dto.order.OrderDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.order.OrderDetailDTO;
import com.noosh.app.commons.dto.order.ChangeOrderDetailDTO;
import com.noosh.app.commons.dto.order.ChangeOrderVersionDTO;
import com.noosh.app.commons.dto.order.OrderStateDTO;
import com.noosh.app.commons.dto.order.OrderItemDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.terms.AcTermsDTO;
import com.noosh.app.commons.entity.account.Address;
import com.noosh.app.commons.entity.collaboration.SyContainable;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.entity.order.OrBillingRecipient;
import com.noosh.app.commons.entity.order.Order;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.security.ServiceProvider;
import com.noosh.app.commons.entity.security.WorkgroupAddress;
import com.noosh.app.commons.entity.security.WorkgroupSd;
import com.noosh.app.commons.vo.account.AccountUserVO;
import com.noosh.app.commons.vo.address.AddressVO;
import com.noosh.app.commons.vo.breakout.BreakoutVO;
import com.noosh.app.commons.vo.costcenter.*;
import com.noosh.app.commons.vo.order.*;
import com.noosh.app.commons.vo.paper.PaperDetailVO;
import com.noosh.app.commons.vo.project.ProjectVO;
import com.noosh.app.commons.vo.spec.SpecVO;
import com.noosh.app.commons.vo.workgroup.WorkgroupVO;
import com.noosh.app.feign.WebOpenFeignClient;
import com.noosh.app.mapper.account.AddressMapper;
import com.noosh.app.mapper.externalItem.ExternalItemMapper;
import com.noosh.app.mapper.invoice.InvoiceMapper;
import com.noosh.app.mapper.property.PropertyAttributeMapper;
import com.noosh.app.repository.billing.BillingRecipientRepository;
import com.noosh.app.repository.collaboration.CollaborationRepository;
import com.noosh.app.repository.estimate.EstimateItemPriceRepository;
import com.noosh.app.repository.jpa.property.PropertyRepository;
import com.noosh.app.repository.jpa.security.ServiceProviderRepository;
import com.noosh.app.repository.jpa.security.WorkgroupAddressRepository;
import com.noosh.app.repository.jpa.security.WorkgroupSdRepository;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.repository.shipment.AddressRepository;
import com.noosh.app.service.account.AccountService;
import com.noosh.app.service.account.SupplierWorkgroupService;
import com.noosh.app.service.customfield.CustomFieldService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.rating.RatingService;
import com.noosh.app.service.util.OrderUtil;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;

import java.math.BigDecimal;
import java.util.*;

import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class OrderMapper {
    @Inject
    private I18NUtils i18NUtils;
    @Inject
    private ExternalItemMapper externalItemMapper;
    @Inject
    private OrderItemMapper orderItemMapper;
    @Inject
    private RoutingSlipMapper routingSlipMapper;
    @Inject
    private InvoiceMapper invoiceMapper;
    @Inject
    private ProjectService projectService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private SupplierWorkgroupService supplierWorkgroupService;
    @Inject
    private CollaborationRepository collaborationRepository;
    @Inject
    private ServiceProviderRepository serviceProviderRepository;
    @Inject
    private AccountService accountService;
    @Inject
    private PropertyMyBatisMapper propertyMyBatisMapper;
    @Inject
    private EstimateItemPriceRepository estimateItemPriceRepository;
    @Inject
    private RatingService ratingService;
    @Inject
    private WorkgroupSdRepository workgroupSdRepository;
    @Inject
    private WorkgroupAddressRepository workgroupAddressRepository;
    @Inject
    private BillingRecipientRepository billingRecipientRepository;
    @Inject
    private CustomFieldService customFieldService;
    @Inject
    private AddressRepository addressRepository;
    @Inject
    private AddressMapper addressMapper;
    @Inject
    private WebOpenFeignClient webOpenFeignClient;
    @Inject
    private PropertyRepository propertyRepository;
    @Inject
    private PropertyAttributeMapper propertyAttributeMapper;

    @Mapping(source = "id", target = "orderId")
    public abstract OrderDTO toDTO(Order order);

    public abstract OrderButtonVO toGeneralTermsAndButtonVO(OrderDetailDTO orderDetailDTO);

    public abstract ChangeOrderButtonVO toChangeButtonVO(ChangeOrderDetailDTO changeOrderDetailDTO);

    public abstract List<OrderCostCenterVO> toOrderCostCenterVO(List<OrderCostCenterDTO> orderCostCenterDTOs);

    public abstract OrderCostCenterDetailVO toOrderCostCenterDetailVO(OrderCostCenterDetailDTO orderCostCenterDetailDTO);

    public abstract List<InvoiceCostCenterVO> toInvoiceCostCenterDetailVO(List<InvoiceOrderCostCenterDTO> invoiceOrderCostCenterDTOs);

    public abstract ProjectCostCenterDetailVO toProjectCostCenterDetailVO(ProjectCostCenterDetailDTO projectCostCenterDetailDTO);

    public TermsVO toTermsVO(AcTermsDTO acTermsDTO) {
        if (acTermsDTO != null) {
            TermsVO termsVO = new TermsVO();
            termsVO.setId(acTermsDTO.getId());
            termsVO.setText(acTermsDTO.getText());
            return termsVO;
        }

        return null;
    }

    public <T extends OrderGeneralInfoVO> T  toOrderGeneralInfoVO(OrderVersionDTO orderVersionDTO, T orderDetailVO) throws Exception {
        return toOrderGeneralInfoVO(orderVersionDTO, orderDetailVO, false);
    }

    public <T extends OrderGeneralInfoVO> T  toOrderGeneralInfoVO(OrderVersionDTO orderVersionDTO, T orderDetailVO, boolean isEdit) throws Exception {
        boolean isDualCurrency = orderVersionDTO.getRate() != null && orderVersionDTO.getRate().compareTo(BigDecimal.ZERO) > 0 && orderVersionDTO.getExCurrencyId() != null;
        OrderStateDTO orderState = orderVersionDTO.getOrderState();
        orderDetailVO.setOrderId(orderVersionDTO.getOrderId());
        orderDetailVO.setOrderVersionId(orderVersionDTO.getId());
        orderDetailVO.setOrderTypeId(orderVersionDTO.getOrderTypeId());
        orderDetailVO.setVersionNumber(orderVersionDTO.getVersion());
        orderDetailVO.setTitle(orderVersionDTO.getTitle() == null ? orderVersionDTO.getReference() : orderVersionDTO.getTitle());
        orderDetailVO.setCompletionDate(orderVersionDTO.getCompletionDate());
        orderDetailVO.setReference(orderVersionDTO.getReference());
        orderDetailVO.setIsPaperOrder(orderVersionDTO.getOrderClassificationId() != null && orderVersionDTO.getOrderClassificationId() == OrderClassificationID.PAPER);
        orderDetailVO.setSupplierReference(orderVersionDTO.getSupplierReference());
        orderDetailVO.setItemizedTaxAndShippingEnabled(orderVersionDTO.getItemized());
        orderDetailVO.setTaxLabelString(orderVersionDTO.getTaxLabelString());
        orderDetailVO.setPcAllowBreakouts(orderVersionDTO.getPcAllowBreakouts());
        Long valueCurrencyId = orderVersionDTO.getValueCurrencyId();
        orderDetailVO.setSubTotal(orderVersionDTO.getSubTotal() != null ? orderVersionDTO.getSubTotal() : BigDecimal.ZERO);
        orderDetailVO.setSubTotalCurrencyId(valueCurrencyId);
        orderDetailVO.setGrandTotal(orderVersionDTO.getGrandTotal() != null ? orderVersionDTO.getGrandTotal() : BigDecimal.ZERO);
        orderDetailVO.setGrandTotalCurrencyId(valueCurrencyId);
        orderDetailVO.setTax(orderVersionDTO.getTax());
        orderDetailVO.setTaxCurrencyId(orderVersionDTO.getTaxCurrencyId());
        orderDetailVO.setShipping(orderVersionDTO.getShipping());
        orderDetailVO.setShippingCurrencyId(orderVersionDTO.getShippingCurrencyId());
        orderDetailVO.setIsDualCurrency(isDualCurrency);
        if (isDualCurrency) {
            orderDetailVO.setRate(orderVersionDTO.getRate());
            orderDetailVO.setExCurrencyId(orderVersionDTO.getExCurrencyId());
            orderDetailVO.setExSubTotal(orderVersionDTO.getExSubTotal() != null ? orderVersionDTO.getExSubTotal() : BigDecimal.ZERO);
            orderDetailVO.setExSubTotalCurrencyId(orderVersionDTO.getExCurrencyId());
            orderDetailVO.setExGrandTotal(orderVersionDTO.getExGrandTotal() != null ? orderVersionDTO.getExGrandTotal() :BigDecimal.ZERO);
            orderDetailVO.setExGrandTotalCurrencyId(orderVersionDTO.getExCurrencyId());
            orderDetailVO.setExTax(orderVersionDTO.getExTax());
            orderDetailVO.setExTaxCurrencyId(orderVersionDTO.getExTaxCurrencyId());
            orderDetailVO.setExShipping(orderVersionDTO.getExShipping());
            orderDetailVO.setExShippingCurrencyId(orderVersionDTO.getExShippingCurrencyId());
        }
        orderDetailVO.setCloseOrderNegotiation(orderVersionDTO.getCloseOrderNegotiation());
        orderDetailVO.setHideOversAndUnders(orderVersionDTO.getHideOversAndUnders());
        orderDetailVO.setIsAccepted(orderVersionDTO.isAccepted());
        orderDetailVO.setIsDraft(orderVersionDTO.isDraft());
        orderDetailVO.setIsPending(orderVersionDTO.isPending());
        orderDetailVO.setIsPendingSubmission(orderVersionDTO.isPendingSubmission());
        orderDetailVO.setIsQuickOrder(orderVersionDTO.isQuickOrder());
        orderDetailVO.setIsReordered(orderVersionDTO.isReordered());
        orderDetailVO.setIsRetracted(orderVersionDTO.isRetracted());
        orderDetailVO.setIsReplaced(orderVersionDTO.isReplaced());
        orderDetailVO.setIsPendingApproval(orderVersionDTO.isPendingApproval(orderVersionDTO.getRoutingSlip()));
        orderDetailVO.setNoSelection((orderVersionDTO.isPendingSubmission()
                || (orderVersionDTO.isPendingApproval(orderVersionDTO.getRoutingSlip())
                && !(orderVersionDTO.isRetracted() || orderVersionDTO.isCancelled()))
                || orderVersionDTO.getIsApproved() || orderVersionDTO.getIsDisapproved()));
        orderDetailVO.setCanManageShipment(orderVersionDTO.getCanManageShipment());
        orderDetailVO.setCanViewProjectBudget(orderVersionDTO.getCanViewProjectBudget());
        orderDetailVO.setShowPendingApproval(orderVersionDTO.getShowPendingApproval());
        orderDetailVO.setIsChangeOrder(orderVersionDTO.isChangeOrder());
        orderDetailVO.setIsCloseChangeOrder(orderVersionDTO.isClosingChangeOrder());
        orderDetailVO.setIsCompleted(orderVersionDTO.isCompleted());
        //TODO isEdit case
        orderDetailVO.setDiscountOrSurcharge(orderVersionDTO.getDiscountOrSurcharge() != null ? orderVersionDTO.getDiscountOrSurcharge() :
                (orderVersionDTO.getDors() != null ? orderVersionDTO.getDors() : BigDecimal.ZERO));
        orderDetailVO.setDiscountOrSurchargeCurrencyId(orderVersionDTO.getDorsCurrencyId());
        orderDetailVO.setOrderItemDiscountSurchargeTotal(orderVersionDTO.getOrderItemDiscountSurchargeTotal() != null
                ? orderVersionDTO.getOrderItemDiscountSurchargeTotal() : BigDecimal.ZERO);
        orderDetailVO.setOrderItemDiscountSurchargeTotalCurrencyId(valueCurrencyId);
        orderDetailVO.setDiscountOrSurchargeTotal(orderVersionDTO.getDiscountOrSurchargeTotal() != null
                ? orderVersionDTO.getDiscountOrSurchargeTotal() :BigDecimal.ZERO);
        orderDetailVO.setDiscountOrSurchargeTotalCurrencyId(valueCurrencyId);
        //TODO isEdit case
        if (isDualCurrency) {
            orderDetailVO.setExDiscountOrSurcharge(orderVersionDTO.getExDiscountOrSurcharge() != null ? orderVersionDTO.getExDiscountOrSurcharge() :
                    (orderVersionDTO.getExDors() != null ? orderVersionDTO.getExDors() : BigDecimal.ZERO));
            orderDetailVO.setExDiscountOrSurchargeCurrencyId(orderVersionDTO.getExDorsCurrencyId());
            orderDetailVO.setExOrderItemDiscountSurchargeTotal(orderVersionDTO.getExOrderItemDiscountSurchargeTotal() != null
                    ? orderVersionDTO.getExOrderItemDiscountSurchargeTotal() : BigDecimal.ZERO);
            orderDetailVO.setExOrderItemDiscountSurchargeTotalCurrencyId(orderVersionDTO.getExCurrencyId());
            orderDetailVO.setExDiscountOrSurchargeTotal(orderVersionDTO.getExDiscountOrSurchargeTotal() != null
                    ? orderVersionDTO.getExDiscountOrSurchargeTotal() : BigDecimal.ZERO);
            orderDetailVO.setExDiscountOrSurchargeTotalCurrencyId(orderVersionDTO.getExCurrencyId());
        }
        orderDetailVO.setInvoicingEnable(orderVersionDTO.getInvoicingEnable());
        orderDetailVO.setCompletedAndHasAcceptedClosingChangeOrder(orderVersionDTO.isCompletedAndHasAcceptedClosingChangeOrder());
        orderDetailVO.setAutoTimeOut(orderVersionDTO.getAutoTimeOut());
        orderDetailVO.setAllAtOnce(orderVersionDTO.getAllAtOnce());
        orderDetailVO.setManagerAllAtOnce(orderVersionDTO.getManagerAllAtOnce());
        orderDetailVO.setAutoMgrTimeOut(orderVersionDTO.getAutoMgrTimeOut());
        orderDetailVO.setQuickOrderApproval(orderVersionDTO.getQuickOrderApproval());
        orderDetailVO.setPaymentMethodId(orderVersionDTO.getPaymentMethodId());
        orderDetailVO.setBudgetTypeId(orderVersionDTO.getBugetTypeFieldId());
        orderDetailVO.setCustomPropertyId(orderVersionDTO.getCustomPropertyId());
        orderDetailVO.setCreationDate(orderVersionDTO.getCreateDate());
        boolean isEnableComplexVAT = orderVersionDTO.getIsEnableComplexVAT();
        orderDetailVO.setIsEnableComplexVAT(isEnableComplexVAT);
        if (isEnableComplexVAT) {
            orderDetailVO.setVatCode(orderVersionDTO.getVatCode());
            orderDetailVO.setVatRate(orderVersionDTO.getVatRate());
        }
        orderDetailVO.setIsSensitive(orderVersionDTO.getIsSensitive() == null ? Boolean.FALSE : orderVersionDTO.getIsSensitive());
        orderDetailVO.setIsShowSensitive(orderVersionDTO.getIsShowSensitive());
        ProjectDTO parentOwner = orderVersionDTO.getParent();
        if (orderVersionDTO.getParent().isClientProject() || orderVersionDTO.getParent().isSupplierProject()) {
            parentOwner = projectService.findProjectById(orderVersionDTO.getParent().getMasterProjectId());
        }
        orderDetailVO.setIsBrokerSellOrder(orderVersionDTO.isBrokerSellOrder(parentOwner));
        orderDetailVO.setIsOutsourcingSellOrder(orderVersionDTO.isOutsourcingSellOrder(parentOwner));
        boolean isPaperFlow = orderVersionDTO.getBuyerProject().getIsPaperFlow() && !orderVersionDTO.isOutsourcingSellOrder(parentOwner);
        orderDetailVO.setIsPaperFlow(isPaperFlow);

        //Invoice adjustment order
        List<OrderVersionDTO> invoiceAdjustmentOrders = orderVersionDTO.getInvoiceAdjustmentOrders();
        if (orderVersionDTO.getInvoiceAdjustmentParentOrder() != null) {
            OrderVersionDTO parentOrder = orderVersionDTO.getInvoiceAdjustmentParentOrder();
            String parentOrderName = parentOrder.getReference() + "-" + parentOrder.getOrderTitle();
            //NooshOneUrlUtil.composeorder
            orderDetailVO.setInvoiceAdjustmentParentOrderId(parentOrder.getOrderId());
            orderDetailVO.setInvoiceAdjustmentParentOrderName(parentOrderName);
            orderDetailVO.setInvoiceAdjustmentParentOrderLink(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(parentOwner.getId(), parentOrder.getOrderId()));
        } else if (invoiceAdjustmentOrders != null && !invoiceAdjustmentOrders.isEmpty()) {
            List<Map<String, Object>> invoiceAdjustmentOrderList = new ArrayList<>();
            for (OrderVersionDTO invoiceAdjustmentOrder : orderVersionDTO.getInvoiceAdjustmentOrders()) {
                Map<String, Object> invoiceAdjustmentOrderMap = new HashMap<>();
                String invoiceAdjustmentOrderName = invoiceAdjustmentOrder.getReference() + "-" + invoiceAdjustmentOrder.getTitle();
                invoiceAdjustmentOrderMap.put("orderId", invoiceAdjustmentOrder.getOrderId());
                invoiceAdjustmentOrderMap.put("orderName", invoiceAdjustmentOrderName);
                invoiceAdjustmentOrderMap.put("orderLink", NooshOneUrlUtil.composeViewOrderLinkToEnterprise(parentOwner.getId(), invoiceAdjustmentOrder.getOrderId()));
                invoiceAdjustmentOrderList.add(invoiceAdjustmentOrderMap);
            }
            orderDetailVO.setInvoiceAdjustmentOrders(invoiceAdjustmentOrderList);
        }

        OrderStateVO orderStateVO = new OrderStateVO();
        if (orderState != null) {
            orderStateVO.setComments(orderState.getComments());
            orderStateVO.setDescription(i18NUtils.getMessage(orderState.getObjectState().getDescriptionStrId()));
            orderStateVO.setDescriptionStrId(orderState.getObjectState().getDescriptionStrId() != null ? String.valueOf(orderState.getObjectState().getDescriptionStrId()) : null);
            orderStateVO.setStatusFormat(getStatusFormat(orderState));
            orderDetailVO.setOrderState(orderStateVO);
            orderDetailVO.setIsRejected(orderState.getObjectStateId() == ObjectStateID.ORDER_REJECTED);
        }
        ProjectDTO parent = orderVersionDTO.getParent();
        if (parent.isClientNotOnNoosh() && orderVersionDTO.getSupplierWorkgroupId() != null
                && orderVersionDTO.getBuyerWorkgroupId().longValue()
                == orderVersionDTO.getSupplierWorkgroupId().longValue()
                && orderVersionDTO.getBuClientWorkgroupId() != null
                && orderVersionDTO.getBuClientWorkgroupId() > 0) {
            orderDetailVO.setHasOfflineBuyer(true);
        }
        ProjectVO projectVO = new ProjectVO();
        projectVO.setId(parent.getId());
        projectVO.setName(parent.getTitle());
        projectVO.setIsBuyerProject(parent.isBuyerProject());
        projectVO.setIsClientProject(parent.isClientProject());
        projectVO.setIsSupplierProject(parent.isSupplierProject());
        projectVO.setClientWorkgroup(parent.getClientAccount());
        projectVO.setIsClientNotOnNoosh(parent.isClientNotOnNoosh());
        projectVO.setClientWorkgroupId(parent.getClientWorkgroupId());
        orderDetailVO.setParent(projectVO);

        ProjectVO buyerProjectVO = new ProjectVO();
        buyerProjectVO.setIsClientProject(orderVersionDTO.getBuyerProject().isClientProject());
        buyerProjectVO.setIsBuyerProject(orderVersionDTO.getBuyerProject().isBuyerProject());
        buyerProjectVO.setIsClientNotOnNoosh(orderVersionDTO.getBuyerProject().isClientNotOnNoosh());
        orderDetailVO.setBuyerProject(buyerProjectVO);

        WorkgroupVO buyerWg = new WorkgroupVO();
        buyerWg.setId(orderVersionDTO.getBuyerWorkgroupId());
        buyerWg.setName(orderVersionDTO.getBuyerWorkgroup().getName());
        buyerWg.setDecimalPlaces(orderVersionDTO.getBuyerWorkgroup().getDecimalPlaces());
        if (orderVersionDTO.getBuyer().getDefaultEmail() != null) {
            buyerWg.setEmail(orderVersionDTO.getBuyer().getDefaultEmail());
        }

        /*
        WorkgroupSd buyWorkgroupSd = workgroupSdRepository.findByWorkgroupId(orderVersionDTO.getBuyerWorkgroupId());
        if (buyWorkgroupSd != null) {
            buyerWg.setLogo(getLogoUrl(buyWorkgroupSd.getLogoOneUrl(), buyWorkgroupSd.getLogoUrl(),
                    orderVersionDTO.getBuyerWorkgroupId()));
        }*/
        buyerWg.setLogo(getLogoUrl(orderVersionDTO.getBuyerWorkgroupId()));

        WorkgroupAddress buyWorkgroupAddress = workgroupAddressRepository.findByWorkgroupId(orderVersionDTO.getBuyerWorkgroupId());
        if (buyWorkgroupAddress != null && buyWorkgroupAddress.getAddress() != null) {
            buyerWg.setCity(buyWorkgroupAddress.getAddress().getCity());
            buyerWg.setLine3(buyWorkgroupAddress.getAddress().getLine3());
            buyerWg.setLine1(buyWorkgroupAddress.getAddress().getLine1());
            buyerWg.setLine2(buyWorkgroupAddress.getAddress().getLine2());
            buyerWg.setPostalCode(buyWorkgroupAddress.getAddress().getPostal());
            buyerWg.setState(buyWorkgroupAddress.getAddress().getState());
            buyerWg.setCountry(i18NUtils.getMessage(buyWorkgroupAddress.getAddress().getCountry().getNameStrId()));
            buyerWg.setCountryStrId(buyWorkgroupAddress.getAddress().getCountry().getNameStrId() != null
                    ? buyWorkgroupAddress.getAddress().getCountry().getNameStrId().toString() : null);
        }

        orderDetailVO.setBuyerWorkgroup(buyerWg);

        WorkgroupVO supplierWg = new WorkgroupVO();
        supplierWg.setId(orderVersionDTO.getSupplierWorkgroupId());
        supplierWg.setName(orderVersionDTO.getSupplierWorkgroup() != null ? orderVersionDTO.getSupplierWorkgroup().getName() : "");
        supplierWg.setDecimalPlaces(orderVersionDTO.getSupplierWorkgroup() != null ? orderVersionDTO.getSupplierWorkgroup().getDecimalPlaces() : null);
        supplierWg.setSupplierFlag(supplierWorkgroupService.getSupplierFlagVO(orderDetailVO.getIsDraft() || orderVersionDTO.isPendingSubmission(),
                orderVersionDTO.getOrder().getParentOrderId(), ObjectClassID.ORDER,
                parentOwner.getOwnerWorkgroupId(),  supplierWg.getId()));
        supplierWg.setSupplierScore(ratingService.findSupplierScore(parentOwner.getOwnerWorkgroupId(), supplierWg.getId()));

        /*
        WorkgroupSd supplierWorkgroupSd = workgroupSdRepository.findByWorkgroupId(orderVersionDTO.getSupplierWorkgroupId());
        if (supplierWorkgroupSd != null) {
            supplierWg.setLogo(getLogoUrl(supplierWorkgroupSd.getLogoOneUrl(), supplierWorkgroupSd.getLogoUrl(),
                    orderVersionDTO.getSupplierWorkgroupId()));
        }
         */
        supplierWg.setLogo(getLogoUrl(orderVersionDTO.getSupplierWorkgroupId()));

        if (orderVersionDTO.getSupplierWorkgroup() != null) {
            WorkgroupAddress supplierWorkgroupAddress = workgroupAddressRepository.findByWorkgroupId(orderVersionDTO.getSupplierWorkgroupId());

            if (supplierWorkgroupAddress != null && supplierWorkgroupAddress.getAddress() != null) {
                supplierWg.setCity(supplierWorkgroupAddress.getAddress().getCity());
                supplierWg.setLine3(supplierWorkgroupAddress.getAddress().getLine3());
                supplierWg.setLine1(supplierWorkgroupAddress.getAddress().getLine1());
                supplierWg.setLine2(supplierWorkgroupAddress.getAddress().getLine2());
                supplierWg.setPostalCode(supplierWorkgroupAddress.getAddress().getPostal());
                supplierWg.setState(supplierWorkgroupAddress.getAddress().getState());
                supplierWg.setCountry(i18NUtils.getMessage(supplierWorkgroupAddress.getAddress().getCountry().getNameStrId()));
                supplierWg.setCountryStrId(supplierWorkgroupAddress.getAddress().getCountry().getNameStrId() != null ?
                        supplierWorkgroupAddress.getAddress().getCountry().getNameStrId().toString() : null);
            }
        }

        orderDetailVO.setSupplierWorkgroup(supplierWg);

        AccountUserVO buyer = new AccountUserVO();
        buyer.setId(orderVersionDTO.getBuyerUserId());
        buyer.setName(orderVersionDTO.getBuyer().getFullName());
        buyer.setProfileImg(orderVersionDTO.getBuyer().getProfileImg());
        orderDetailVO.setBuyer(buyer);

        AccountUserVO supplier = new AccountUserVO();
        supplier.setId(orderVersionDTO.getSupplierUserId());
        AccountUserDTO supplierDTO = orderVersionDTO.getSupplier();
        if (supplierDTO != null) {
            supplier.setName(supplierDTO.getFullName());
            supplier.setProfileImg(supplierDTO.getProfileImg());
            supplier.setFirstName(supplierDTO.getFirstName());
            supplier.setLastName(supplierDTO.getLastName());
        }
        orderDetailVO.setSupplier(supplier);

        orderDetailVO.setComments(orderVersionDTO.getComments() != null ? orderVersionDTO.getComments() : orderVersionDTO.getComments());
        orderDetailVO.setPaymentMethod(orderVersionDTO.getPaymentMethod() != null ?
                i18NUtils.getMessage(orderVersionDTO.getPaymentMethod().getDescriptionStrId()) : null);
        orderDetailVO.setPaymentMethodStrId(orderVersionDTO.getPaymentMethod() != null ?
                orderVersionDTO.getPaymentMethod().getDescriptionStrId().toString() : null);
        orderDetailVO.setPaymentReference(orderVersionDTO.getPaymentReference());
        orderDetailVO.setIsUserBuyer(orderVersionDTO.getBuyerWorkgroupId().longValue() == parent.getOwnerWorkgroupId().longValue());
        orderDetailVO.setIsUserSupplier(orderVersionDTO.getSupplierWorkgroupId().longValue() == parent.getOwnerWorkgroupId().longValue());
        if (orderVersionDTO.getReason() != null) {
            String token = orderVersionDTO.getReason().getToken();
            if(token != null && token.equalsIgnoreCase("REASON_DESC_OTHER")) {
                orderDetailVO.setReasonOther(orderVersionDTO.getReasonOther());
            }
            orderDetailVO.setReason(orderVersionDTO.getReason().getNameStrId() == null ? orderVersionDTO.getReason().getNameStr() :
                    i18NUtils.getMessage(orderVersionDTO.getReason().getNameStrId()));
            orderDetailVO.setReasonStrId(orderVersionDTO.getReason().getNameStrId() != null ? orderVersionDTO.getReason().getNameStrId().toString() : null);
            orderDetailVO.setReasonNameStr(orderVersionDTO.getReason().getNameStr());
            orderDetailVO.setReasonId(orderVersionDTO.getReasonId());
        }

        if(orderVersionDTO.getBugetTypeFieldId() != null && orderVersionDTO.getBugetTypeFieldId().longValue() != -1){
            if(orderVersionDTO.getCustomFieldDTO() != null){
                orderDetailVO.setBudgetTypeField(orderVersionDTO.getCustomFieldDTO().getLabel());
            }
        }

        List<OrBillingRecipient> billingRecipients = billingRecipientRepository.findByOrderId(orderVersionDTO.getOrderId());
        if (billingRecipients != null && billingRecipients.size() > 0) {
            List<BillingRecipientVO> billingRecipientVOs = new ArrayList<>();
            for (OrBillingRecipient billingRecipient : billingRecipients) {
                BillingRecipientVO billingRecipientVO = new BillingRecipientVO();
                billingRecipientVO.setId(billingRecipient.getId());
                billingRecipientVO.setComments(billingRecipient.getComments());
                billingRecipientVO.setContactName(billingRecipient.getContactId() != null ?
                        billingRecipient.getContact().getContactFullName() : null);
                billingRecipientVO.setContactId(billingRecipient.getContactId());
                billingRecipientVOs.add(billingRecipientVO);
            }
            orderDetailVO.setBillingRecipients(billingRecipientVOs);
        }
        orderDetailVO.setUndersPercent(orderVersionDTO.getUndersPercent() != null ? orderVersionDTO.getUndersPercent().doubleValue() : 0);
        orderDetailVO.setOversPercent(orderVersionDTO.getOversPercent() != null ? orderVersionDTO.getOversPercent().doubleValue() : 0);

        String valueCurrency = null;

        Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(orderVersionDTO.getBuyerWorkgroupId(),
                Arrays.asList(PreferenceID.PC_SUPPLIER_SELECTION_REASON_REQUIRED, PreferenceID.WORKGROUP_OPTION_ORDER_ITEMS_SORTING_BY_SPEC_NAME,
                        PreferenceID.WORKGROUP_OPTION_SUPPLIER_ADD_PAPER_DETAILS, PreferenceID.WORKGROUP_OPTION_BASELINE,
                        PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY));
        orderDetailVO.setIsSelectionReasonRequired(preferenceService.check(PreferenceID.PC_SUPPLIER_SELECTION_REASON_REQUIRED, buyerPrefs));
        if (preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_ITEMS_SORTING_BY_SPEC_NAME, buyerPrefs)
                && orderVersionDTO.getOrderItemDTOs() != null && orderVersionDTO.getOrderItemDTOs().size() > 0) {
            orderVersionDTO.getOrderItemDTOs().sort((OrderItemDTO o1, OrderItemDTO o2) -> o1.getSpec().getName().compareTo(o2.getSpec().getName()));
        }
        if (!orderVersionDTO.isUserSupplier() && orderVersionDTO.isDraft() && preferenceService.check(PreferenceID.WORKGROUP_OPTION_BASELINE, buyerPrefs)) {
            orderDetailVO.setCanShowBenchmark(true);
        }
        boolean isDualCurrencyPref = false;
        boolean hideBaseCurrency = false;
        if (orderVersionDTO.isOutsourcingSellOrder(parentOwner) || orderVersionDTO.isBrokerSellOrder(parentOwner)) {
            isDualCurrencyPref = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, buyerPrefs);
            hideBaseCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY, buyerPrefs);
        } else {
            Map<String, String> supplierPrefs = preferenceService.findGroupPrefs(orderVersionDTO.getSupplierWorkgroupId(),
                    Arrays.asList(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY));
            isDualCurrencyPref = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, supplierPrefs);
            hideBaseCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY, supplierPrefs);
        }

        orderDetailVO.setIsDualCurrency(isDualCurrencyPref && isDualCurrency);
        orderDetailVO.setHideBaseCurrency(hideBaseCurrency);

        if (orderVersionDTO.getOrderItemDTOs() != null && orderVersionDTO.getOrderItemDTOs().size() > 0) {
            List<OrderItemVO> orderItemVOs = new ArrayList<>();
            for (OrderItemDTO orderItemDTO : orderVersionDTO.getOrderItemDTOs()) {
                orderItemDTO.setOrderId(orderVersionDTO.getOrderId());
                OrderItemVO orderItemVO = orderItemMapper.toVO(orderItemDTO, projectVO.getId(), orderVersionDTO.isChangeOrder(),
                        orderVersionDTO.getCanManageShipment(), orderVersionDTO.getParent().isBuyerProject(), isDualCurrency);
                orderItemVO.setTaxLabelString(orderVersionDTO.getTaxLabelString());
                valueCurrency = orderItemVO.getValueCurrency();
                orderItemVO.setAllowedOversQty(orderItemDTO.getAllowedOversQty(
                        orderVersionDTO.getOversPercent() != null ?
                                orderVersionDTO.getOversPercent().doubleValue() : 0.0));
                orderItemVO.setAllowedUndersQty(orderItemDTO.getAllowedUndersQty(
                        orderVersionDTO.getUndersPercent() != null ?
                                orderVersionDTO.getUndersPercent().doubleValue() : 0.0));

                if (orderItemDTO.getExternalItemDTOs() != null && orderItemDTO.getExternalItemDTOs().size() > 0) {
                    orderItemVO.setExternalItems(externalItemMapper.toVOs(orderItemDTO.getExternalItemDTOs(), orderItemDTO.getQuantity()));
                }

                if (isPaperFlow) {
                    orderDetailVO.setCanAcceptInPaperFlow(true);
                    boolean noPaperValidation = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PAPER_NO_VALIDATION, buyerPrefs);
                    List<PropertyAttributeDTO> paperSelections = new ArrayList<>();
                    Property property = propertyRepository.findById(orderItemDTO.getSpec().getPropertyId()).orElse(null);
                    if (property != null) {
                        PropertyAttributeDTO paperSelection = propertyAttributeMapper.getPaperSelection(property);
                        if (paperSelection != null) {
                            paperSelections.add(paperSelection);
                        }
                        List<Property> childrenProperties = propertyRepository.findByParentPropertyIdAndPropertyName(property.getId(), "INKS_AND_PAPER");
                        if (childrenProperties != null && !childrenProperties.isEmpty()) {
                            for (Property childProperty : childrenProperties) {
                                PropertyAttributeDTO childPaperSelection = propertyAttributeMapper.getPaperSelection(childProperty);
                                if (childPaperSelection != null) {
                                    paperSelections.add(childPaperSelection);
                                }
                            }
                        }
                        orderItemVO.setPaperSelections(propertyAttributeMapper.toVOs(paperSelections));
                    }
                    if (orderVersionDTO.isUserSupplier() && !noPaperValidation) {
                        boolean hasPaperSelections = orderItemVO.getPaperSelections() != null && !orderItemVO.getPaperSelections().isEmpty();
                        boolean hasExternalItems = orderItemVO.getExternalItems() != null && !orderItemVO.getExternalItems().isEmpty();
                        if (hasPaperSelections && !hasExternalItems) {
                            orderDetailVO.setCanAcceptInPaperFlow(false);
                        }
                    }
                }

                orderItemVOs.add(orderItemVO);
            }
            orderDetailVO.setOrderItems(orderItemVOs);
        }

        //handle custom fields
        customFieldService.setCustomAttributes(orderDetailVO);
        // Display of Environmental Paper Impact and Logistics
        if (orderDetailVO.getOrderItems() != null && orderDetailVO.getOrderItems().size() > 0) {
            Map<String, String> prefs = preferenceService.findGroupPrefs(orderVersionDTO.getBuyerWorkgroupId(), Arrays.asList(PreferenceID.WORKGROUP_OPTION_SUPPLIER_ADD_PAPER_DETAILS, PreferenceID.WORKGROUP_OPTION_SUSTAINABILITY_LOGISTICS));
            boolean enableSupplierAddPaperDetails = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SUPPLIER_ADD_PAPER_DETAILS, prefs);
            boolean enableLogistics = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SUSTAINABILITY_LOGISTICS, prefs);
            orderDetailVO.setEnableSupplierAddPaperDetails(enableSupplierAddPaperDetails);
            orderDetailVO.setEnableLogistics(enableLogistics);
            boolean isCommunisis = accountService.isCommunisisPortal(parent, JwtUtil.getWorkgroupId());

            for (OrderItemVO orderItemVO : orderDetailVO.getOrderItems()) {
                if (enableLogistics || enableSupplierAddPaperDetails) {
                    if (orderVersionDTO.getOrderTypeId() == OrderTypeID.ORDER) {
                        Long estimateItemPriceId = orderItemVO.getEstimateItemPriceId();
                        if (estimateItemPriceId != null) {
                            EstimateItemPrice estimateItemPrice = estimateItemPriceRepository.findById(estimateItemPriceId).orElse(null);
                            if (estimateItemPrice != null) {
                                orderItemVO.setEstItemOptionValue(estimateItemPrice.getItemOption().getValue());
                            }
                        }
                    }
                }
                setEnvironmentalPaper(enableSupplierAddPaperDetails, isCommunisis, orderVersionDTO, orderItemVO);
                setEnvironmentalLogistics(enableLogistics, orderItemVO);
            }
        }

        orderDetailVO.setValueCurrency(valueCurrency);

        if (!isEdit) {
            orderDetailVO.setRoutingSlip(routingSlipMapper.toVO(orderVersionDTO.getRoutingSlip(),
                    orderVersionDTO.getApproveMap(), orderDetailVO));

            if (orderVersionDTO.getApproverRecipients() != null) {
                List<SlipRecipientVO> approverRecipientVOs = new ArrayList<SlipRecipientVO>();
                for (AccountUserDTO recipient : orderVersionDTO.getApproverRecipients()) {
                    SlipRecipientVO recipientVO = new SlipRecipientVO();
                    recipientVO.setName(recipient.getFullName());
                    recipientVO.setId(recipient.getUserId());
                    approverRecipientVOs.add(recipientVO);
                }
                orderDetailVO.setApproverRecipients(approverRecipientVOs);
            }

            if (orderVersionDTO.getManagerRecipients() != null) {
                List<SlipRecipientVO> managerRecipientVOs = new ArrayList<>();
                for (AccountUserDTO recipient : orderVersionDTO.getManagerRecipients()) {
                    SlipRecipientVO recipientVO = new SlipRecipientVO();
                    recipientVO.setName(recipient.getFullName());
                    recipientVO.setId(recipient.getUserId());
                    managerRecipientVOs.add(recipientVO);
                }
                orderDetailVO.setManagerRecipients(managerRecipientVOs);
            }
        }

        //Order Send to Supplier Date
        if(orderVersionDTO.isOutsourcingSellOrder(parent) || orderVersionDTO.isBrokerSellOrder(parent)) {
            orderDetailVO.setOrderSendToSupplierDate(null);
        } else {
            SyContainable syContainable = collaborationRepository.findFirstByObjectIdAndObjectClassIdOrderByCreateDateDesc(
                    orderVersionDTO.getOrderId(), ObjectClassID.ORDER);
            orderDetailVO.setOrderSendToSupplierDate(syContainable != null ? syContainable.getCreateDate() : null);
        }

        if (orderVersionDTO.isUserBuyer() || (orderVersionDTO.isUserSupplier() && (!parent.getIsPaperDirect()))
                || (parent.isSupplierProject() && (!parent.getIsPaperDirect()))) {
            orderDetailVO.setCanSeePrice(true);
        }
        return orderDetailVO;
    }

    public void setEnvironmentalLogistics(boolean enableLogistics, OrderItemVO orderItemVO) {
        if (enableLogistics) {
            Map<String, Object> logisticsDetails = new HashMap<String, Object>();
            Long originalAddressId = -1L;
            Map attributeMap = orderItemVO.getCustomAttributes();
            if (attributeMap != null) {
                Object originalAddressIdObj = attributeMap.get("LOG_ORI_ADDR_ID_num");
                if (originalAddressIdObj != null) {
                    originalAddressId = Long.parseLong(originalAddressIdObj.toString());
                }

                if (originalAddressId > 0) {
                    Address address = addressRepository.findById(originalAddressId).orElse(null);
                    if (address != null) {
                        AddressVO addressVO = addressMapper.toVO(address);
                        logisticsDetails.put("originalAddress", addressVO);
                    }
                }
                logisticsDetails.put("LOG_TOTAL_WEIGHT_num", attributeMap.get("LOG_TOTAL_WEIGHT_num"));
                logisticsDetails.put("LOG_UNIT_str", attributeMap.get("LOG_UNIT_str"));
                logisticsDetails.put("LOG_TRANSPORT_str", attributeMap.get("LOG_TRANSPORT_str"));
                logisticsDetails.put("LOG_WTW_CO2_EQU_num", attributeMap.get("LOG_WTW_CO2_EQU_num"));
            }
            orderItemVO.setLogisticsDetails(logisticsDetails);
        }
    }

    private void setEnvironmentalPaper(boolean enableSupplierAddPaperDetails, boolean isCommunisis, OrderVersionDTO orderVersionDTO, OrderItemVO orderItemVO) {
        if (enableSupplierAddPaperDetails) {
            boolean containsPaperInfo = false;
            if(orderItemVO.getCustomAttributes() != null) {
                containsPaperInfo = orderItemVO.getCustomAttributes().containsKey("PAP_NAME1_str");
            }
            if (containsPaperInfo) {
                boolean displayPaperOnOrder = displayPaperOnOrder(orderVersionDTO, orderItemVO);
                if (displayPaperOnOrder) {
                    SpecVO spec = orderItemVO.getSpec();
                    int paperNumber = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), "STOCKS_AND_INKS").size();
                    if (paperNumber == 0) {paperNumber = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), "INKS_AND_PAPER").size();}
                    List<PaperDetailVO> paperDetails;
                    List<PropertyAttributeDTO> propertyAttributeDTOList = new ArrayList<>();
                    if(orderItemVO.getCustomPropertyId() != null) {
                        propertyAttributeDTOList = propertyMyBatisMapper.getPaperDetailProperty(orderItemVO.getCustomPropertyId());
                    }
                    if (isCommunisis) {
                        paperDetails = OrderUtil.getPaperDetailForCommunisis(propertyAttributeDTOList, paperNumber);
                    } else {
                        paperDetails = OrderUtil.getPaperDetail(propertyAttributeDTOList, paperNumber);
                    }
                    orderItemVO.setPaperDetails(paperDetails);
                    orderItemVO.setPaperDetailSize(paperNumber);
                }
            }
        }
    }
    //for estimate order: display the paper info when estimate quantity is the same as estimate order quantity
    //always display for the closing change order and quick order
    private boolean displayPaperOnOrder(OrderVersionDTO orderVersionDTO, OrderItemVO orderItemVO) {
        if (orderVersionDTO.isClosingChangeOrder() || orderVersionDTO.getOrderTypeId()  == OrderTypeID.QUICK_ORDER) {return true;}
        List<PropertyAttributeDTO> propertyAttributeDTOList = new ArrayList<>();
        if(orderItemVO.getCustomPropertyId() != null) {
            propertyAttributeDTOList = propertyMyBatisMapper.getPaperDetailProperty(orderItemVO.getCustomPropertyId());
        }
        boolean isEstimateOrderWithDiffQty = false;
        String qty1 = (String)OrderUtil.getValue(propertyAttributeDTOList, "PAP_FST_QTY1_str");
        boolean weight = OrderUtil.getValue(propertyAttributeDTOList, "PAP_RE_WEIGHT1_bool") != null ? String.valueOf(OrderUtil.getValue(propertyAttributeDTOList, "PAP_RE_WEIGHT1_bool")).equals("1") ? true : false : false;
        if (orderVersionDTO.getOrderTypeId() == OrderTypeID.ORDER
                && orderItemVO.getEstItemOptionValue() != null && !orderItemVO.getEstItemOptionValue().equals(qty1)
                && !weight) {
            isEstimateOrderWithDiffQty = true;
        }
        return !isEstimateOrderWithDiffQty;
    }

    public void hideAcceptButtonByEnvironmental(OrderGeneralInfoVO orderGeneralInfoVO, OrderVersionDTO orderVersionDTO, OrderButtonVO orderButtonVO) {
        String acceptButton = orderButtonVO.getAcceptButton();
        if (acceptButton == null) {
            return;
        }

        //hide the accept button when supplier didn't fill the paper/logistics info for quick order and estimate order
        String portal = orderVersionDTO.getBuyerWorkgroup().getPortal();
        if (null == portal || !portal.equals("discoverfs")) {
            boolean isEnableSupplierAddPaperDetails = orderGeneralInfoVO.getEnableSupplierAddPaperDetails();
            boolean enableSupplierAddLogistics = orderGeneralInfoVO.getEnableLogistics();
            boolean isEstimateOrder = orderVersionDTO.getOrderTypeId() == OrderTypeID.ORDER;
            if (orderGeneralInfoVO.getIsCloseChangeOrder() && orderGeneralInfoVO.getIsUserSupplier()) {
                List<OrderItemVO> orderItemVOS = orderGeneralInfoVO.getOrderItems();
                if (null != orderItemVOS && orderItemVOS.size() > 0) {
                    for (OrderItemVO orderItemVO : orderItemVOS) {
                        if (isEnableSupplierAddPaperDetails) {
                            SpecVO spec = orderItemVO.getSpec();
                            int paperNumber = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), "STOCKS_AND_INKS").size();
                            if (paperNumber == 0) {
                                paperNumber = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), "INKS_AND_PAPER").size();
                            }
                            if (paperNumber > 0) {
                                if ("hh-sap".equals(portal)) {
                                    processNonPaperLogic(isEstimateOrder, orderItemVO, orderButtonVO);
                                } else {
                                    processPaperLogic(isEstimateOrder, orderItemVO, orderButtonVO);
                                }
                            }
                        }
                        if (enableSupplierAddLogistics) {
                            processLogisticsLogic(isEstimateOrder, orderItemVO, orderButtonVO);
                        }
                    }
                }
            }
        }
    }

    private void processLogisticsLogic(boolean isEstimateOrder, OrderItemVO orderItemVO, OrderButtonVO orderButtonVO) {
        String acceptButton = orderButtonVO.getAcceptButton();
        if (acceptButton == null) {
            return;
        }

        boolean hasFiledPaperInfo = (null != orderItemVO.getCustomAttributes() && orderItemVO.getCustomAttributes().containsKey("LOG_INFO_FILLED_str"));
        if (!hasFiledPaperInfo) {
            orderButtonVO.setAcceptButton(null);
            return;
        }
        if (isEstimateOrder) {//for estimate order, the accept button will also be hidden when the order isn't created from the first quantity
            if (null != orderItemVO.getCustomAttributes() && null != orderItemVO.getCustomAttributes().get("LOG_FST_QTY_str")) {
                if (null != orderItemVO.getQuantity() && !orderItemVO.getQuantity().toString().equals(orderItemVO.getCustomAttributes().get("LOG_FST_QTY_str"))) {
                    if (!orderItemVO.getCustomAttributes().containsKey("LOG_RE_WEIGHT_bool")) {
                        orderButtonVO.setAcceptButton(null);
                    }
                }
            }
        }
    }

    private void processPaperLogic(boolean isEstimateOrder, OrderItemVO orderItemVO, OrderButtonVO orderButtonVO) {
        String acceptButton = orderButtonVO.getAcceptButton();
        if (acceptButton == null) {
            return;
        }

        boolean hasFiledPaperInfo = (null != orderItemVO.getCustomAttributes() && orderItemVO.getCustomAttributes().containsKey("PAP_NAME1_str"));
        if (!hasFiledPaperInfo) {
            orderButtonVO.setAcceptButton(null);
            return;
        }
        if (isEstimateOrder) {//for estimate order, the accept button will also be hidden when the order isn't created from the first quantity
            if (null != orderItemVO.getCustomAttributes() && null != orderItemVO.getCustomAttributes().get("PAP_FST_QTY1_str")) {
                if (null != orderItemVO.getQuantity() && !orderItemVO.getQuantity().toString().equals(orderItemVO.getCustomAttributes().get("PAP_FST_QTY1_str"))) {
                    if (!orderItemVO.getCustomAttributes().containsKey("PAP_RE_WEIGHT1_bool")) {
                        orderButtonVO.setAcceptButton(null);
                    }
                }
            }
        }
    }

    private void processNonPaperLogic(boolean isEstimateOrder, OrderItemVO orderItemVO, OrderButtonVO orderButtonVO) {
        String acceptButton = orderButtonVO.getAcceptButton();
        if (acceptButton == null) {
            return;
        }
        boolean hasFiledPaperInfo = (null != orderItemVO.getCustomAttributes() && orderItemVO.getCustomAttributes().containsKey("NON_PAPER_NAME1_str"));
        if (!hasFiledPaperInfo) {
            orderButtonVO.setAcceptButton(null);
            return;
        }
        if (isEstimateOrder) {//for estimate order, the accept button will also be hidden when the order isn't created from the first quantity
            if (null != orderItemVO.getCustomAttributes() && null != orderItemVO.getCustomAttributes().get("NON_PAPER_FST_QTY1_str")) {
                if (null != orderItemVO.getQuantity() && !orderItemVO.getQuantity().toString().equals(orderItemVO.getCustomAttributes().get("NON_PAPER_FST_QTY1_str"))) {
                    if (!orderItemVO.getCustomAttributes().containsKey("NON_PAPER_RE_WEIGHT1_bool")) {
                        orderButtonVO.setAcceptButton(null);
                    }
                }
            }
        }
    }

    public void hideAcceptButtonByMaterialBT(OrderGeneralInfoVO orderGeneralInfoVO, OrderVersionDTO orderVersionDTO, OrderButtonVO orderButtonVO) {
        String acceptButton = orderButtonVO.getAcceptButton();
        if (acceptButton == null) {
            return;
        }
        String portal = orderVersionDTO.getBuyerWorkgroup().getPortal();
        if (portal != null && portal.startsWith("comm")) {
            List<OrderItemVO> orderItems = orderGeneralInfoVO.getOrderItems();
            boolean isEnableSupplierAddPaperDetails = orderGeneralInfoVO.getEnableSupplierAddPaperDetails();
            boolean canAccept = isEnableSupplierAddPaperDetails && orderVersionDTO.isUserSupplier();
            if (!orderGeneralInfoVO.getIsChangeOrder()) {
                canAccept = canAccept && orderVersionDTO.isQuickOrder();
            }
            if (canAccept) {
                for (OrderItemVO orderItemVO : orderItems) {
                    SpecVO spec = orderItemVO.getSpec();
                    String paperComponentName = "STOCKS_AND_INKS";
                    int paperNumber = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), paperComponentName).size();
                    if (paperNumber == 0) {
                        paperComponentName = "INKS_AND_PAPER";
                        paperNumber = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), paperComponentName).size();
                    }
                    if (paperNumber > 0) {
                        boolean isDirectedBuy = false;
                        List<PropertyAttributeDTO> papers = propertyMyBatisMapper.getChildrenProperty(spec.getPropertyId(), paperComponentName);
                        if (null != papers && papers.size() > 0) {
                            for (PropertyAttributeDTO paper : papers) {
                                if (paper.getParamName().equals("IS_DIRECTED_BUY")) {
                                    if (paper.getStringValue() != null && paper.getStringValue().equals("Use_CMS_Contract_Rate")) {
                                        isDirectedBuy = true;
                                        break;
                                    }
                                }
                            }
                            if (orderItemVO.getAllowBreakouts() && isDirectedBuy) {
                                List<BreakoutVO> breakouts = orderItemVO.getBreakouts();
                                if (null != breakouts && breakouts.size() > 0) {
                                    for (BreakoutVO breakoutVO : breakouts) {
                                        if (breakoutVO.getBreakoutType().getName().equalsIgnoreCase("MATERIAL")) {
                                            if (breakoutVO.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
                                                orderButtonVO.setAcceptButton(null);
                                                return;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public <T extends OrderDetailVO> T toDetailVO(OrderVersionDTO orderVersionDTO, T orderDetailVO) throws Exception {
        OrderStateDTO orderState = orderVersionDTO.getOrderState();
        orderDetailVO.setOrderId(orderVersionDTO.getOrderId());
        orderDetailVO.setOrderVersionId(orderVersionDTO.getId());
        orderDetailVO.setTitle(orderVersionDTO.getTitle() == null ? orderVersionDTO.getReference() : orderVersionDTO.getTitle());
        orderDetailVO.setCompletionDate(orderVersionDTO.getCompletionDate());
        orderDetailVO.setReference(orderVersionDTO.getReference());
        orderDetailVO.setIsPaperOrder(orderVersionDTO.getOrderClassificationId() == OrderClassificationID.PAPER);
        orderDetailVO.setSupplierReference(orderVersionDTO.getSupplierReference());
        orderDetailVO.setItemizedTaxAndShippingEnabled(orderVersionDTO.getItemized());

        orderDetailVO.setSubTotal(orderVersionDTO.getSubTotal() != null ? orderVersionDTO.getSubTotal().doubleValue() : 0.0);
        orderDetailVO.setGrandTotal(orderVersionDTO.getGrandTotal() != null ? orderVersionDTO.getGrandTotal().doubleValue() : 0.0);
        orderDetailVO.setTax(orderVersionDTO.getTax().doubleValue());
        orderDetailVO.setShipping(orderVersionDTO.getShipping().doubleValue());
        orderDetailVO.setDiscountOrSurcharge(orderVersionDTO.getDors() != null ? orderVersionDTO.getDors().doubleValue() : 0.0);
        orderDetailVO.setOrderItemDiscountSurchargeTotal(orderVersionDTO.getOrderItemDiscountSurchargeTotal() != null
                ? orderVersionDTO.getOrderItemDiscountSurchargeTotal().doubleValue() : 0.0);
        orderDetailVO.setDiscountOrSurchargeTotal(orderVersionDTO.getDiscountOrSurchargeTotal() != null
                ? orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue() : 0.0);
        orderDetailVO.setPaymentMethodId(orderVersionDTO.getPaymentMethodId());
        orderDetailVO.setBudgetTypeId(orderVersionDTO.getBugetTypeFieldId());
        orderDetailVO.setCreationDate(orderVersionDTO.getCreateDate());

        OrderStateVO orderStateVO = new OrderStateVO();
        if (orderState != null) {
            orderStateVO.setComments(orderState.getComments());
            orderStateVO.setDescription(i18NUtils.getMessage(orderState.getObjectState().getDescriptionStrId()));
            orderStateVO.setDescriptionStrId(orderState.getObjectState().getDescriptionStrId() != null ? String.valueOf(orderState.getObjectState().getDescriptionStrId()) : null);
            orderStateVO.setStatusFormat(getStatusFormat(orderState));
            orderDetailVO.setOrderState(orderStateVO);
            orderDetailVO.setIsRejected(orderState.getObjectStateId() == ObjectStateID.ORDER_REJECTED);
        }

        WorkgroupVO buyerWg = new WorkgroupVO();
        buyerWg.setId(orderVersionDTO.getBuyerWorkgroupId());
        buyerWg.setName(orderVersionDTO.getBuyerWorkgroup().getName());
        buyerWg.setDecimalPlaces(orderVersionDTO.getBuyerWorkgroup().getDecimalPlaces());
        orderDetailVO.setBuyerWorkgroup(buyerWg);

        WorkgroupVO supplierWg = new WorkgroupVO();
        supplierWg.setId(orderVersionDTO.getSupplierWorkgroupId());
        supplierWg.setName(orderVersionDTO.getSupplierWorkgroup() != null ? orderVersionDTO.getSupplierWorkgroup().getName() : "");
        supplierWg.setDecimalPlaces(orderVersionDTO.getSupplierWorkgroup() != null ? orderVersionDTO.getSupplierWorkgroup().getDecimalPlaces() : null);
        orderDetailVO.setSupplierWorkgroup(supplierWg);

        AccountUserVO buyer = new AccountUserVO();
        buyer.setId(orderVersionDTO.getBuyerUserId());
        buyer.setName(orderVersionDTO.getBuyer() != null ? orderVersionDTO.getBuyer().getFullName() : null);
        orderDetailVO.setBuyer(buyer);

        AccountUserVO supplier = new AccountUserVO();
        supplier.setId(orderVersionDTO.getSupplierUserId());
        supplier.setName(orderVersionDTO.getSupplier() != null ? orderVersionDTO.getSupplier().getFullName() : null);
        orderDetailVO.setSupplier(supplier);

        orderDetailVO.setComments(orderVersionDTO.getComments());

        orderDetailVO.setPaymentReference(orderVersionDTO.getPaymentReference());

        orderDetailVO.setUndersPercent(orderVersionDTO.getUndersPercent() != null ? orderVersionDTO.getUndersPercent().doubleValue() : 0);
        orderDetailVO.setOversPercent(orderVersionDTO.getOversPercent() != null ? orderVersionDTO.getOversPercent().doubleValue() : 0);
        boolean isPaperFlow = orderVersionDTO.getBuyerProject().getIsPaperFlow() && !orderVersionDTO.isOutsourcingSellOrder(orderVersionDTO.getParent());
        orderDetailVO.setIsPaperFlow(isPaperFlow);

        if (orderVersionDTO.getOrderItemDTOs() != null && orderVersionDTO.getOrderItemDTOs().size() > 0) {
            List<OrderItemVO> orderItemVOs = new ArrayList<>();
            for (OrderItemDTO orderItemDTO : orderVersionDTO.getOrderItemDTOs()) {
                orderItemDTO.setOrderId(orderVersionDTO.getOrderId());
                OrderItemVO orderItemVO = orderItemMapper.toVO(orderItemDTO, orderVersionDTO.getParent().getId(), orderVersionDTO.isChangeOrder(),
                        orderVersionDTO.getCanManageShipment(), orderVersionDTO.getParent().isBuyerProject());
                orderItemVO.setAllowedOversQty(orderItemDTO.getAllowedOversQty(
                        orderVersionDTO.getOversPercent() != null ?
                                orderVersionDTO.getOversPercent().doubleValue() : 0.0));
                orderItemVO.setAllowedUndersQty(orderItemDTO.getAllowedUndersQty(
                        orderVersionDTO.getUndersPercent() != null ?
                                orderVersionDTO.getUndersPercent().doubleValue() : 0.0));
                if (orderItemDTO.getExternalItemDTOs() != null && orderItemDTO.getExternalItemDTOs().size() > 0) {
                    orderItemVO.setExternalItems(externalItemMapper.toVOs(orderItemDTO.getExternalItemDTOs(), orderItemDTO.getQuantity()));
                }
                if (isPaperFlow) {
                    List<PropertyAttributeDTO> paperSelections = new ArrayList<>();
                    Property property = propertyRepository.findById(orderItemDTO.getSpec().getPropertyId()).orElse(null);
                    if (property != null) {
                        PropertyAttributeDTO paperSelection = propertyAttributeMapper.getPaperSelection(property);
                        if (paperSelection != null) {
                            paperSelections.add(paperSelection);
                        }
                        List<Property> childrenProperties = propertyRepository.findByParentPropertyIdAndPropertyName(property.getId(), "INKS_AND_PAPER");
                        if (childrenProperties != null && !childrenProperties.isEmpty()) {
                            for (Property childProperty : childrenProperties) {
                                PropertyAttributeDTO childPaperSelection = propertyAttributeMapper.getPaperSelection(childProperty);
                                if (childPaperSelection != null) {
                                    paperSelections.add(childPaperSelection);
                                }
                            }
                        }
                        orderItemVO.setPaperSelections(propertyAttributeMapper.toVOs(paperSelections));
                    }
                }
                orderItemVOs.add(orderItemVO);
            }
            orderDetailVO.setOrderItems(orderItemVOs);
        }

        return orderDetailVO;
    }

    public RatingOrderDetailVO toRatingDetailVO(OrderVersionDTO orderVersionDTO) {
        RatingOrderDetailVO orderDetailVO = new RatingOrderDetailVO();
        orderDetailVO.setOrderId(orderVersionDTO.getOrderId());
        orderDetailVO.setOrderName(orderVersionDTO.getTitle() == null ? orderVersionDTO.getReference() : orderVersionDTO.getTitle());
        orderDetailVO.setGrandTotal(orderVersionDTO.getGrandTotal() != null ? orderVersionDTO.getGrandTotal().doubleValue() : 0.0);
        orderDetailVO.setBuyerWorkgroupId(orderVersionDTO.getBuyerWorkgroupId());
        orderDetailVO.setOrderCompletionDate(orderVersionDTO.getCompletionDate());
        orderDetailVO.setSupplierName(orderVersionDTO.getSupplier() != null ? orderVersionDTO.getSupplier().getFullName() : null);
        orderDetailVO.setSupplierWorkgroup(orderVersionDTO.getSupplierWorkgroup() != null ? orderVersionDTO.getSupplierWorkgroup().getName() : "");
        orderDetailVO.setSupplierWorkgroupId(orderVersionDTO.getSupplierWorkgroupId());
        orderDetailVO.setIsChangeOrder(orderVersionDTO.isChangeOrder());
        return orderDetailVO;
    }

    public OrderGeneralInfoVO toGeneralDetailVO(OrderDetailDTO orderDetailDTO) throws Exception {
        OrderGeneralInfoVO orderGeneralInfoVO = new OrderGeneralInfoVO();
        toOrderGeneralInfoVO(orderDetailDTO.getOrderVersionDTO(), orderGeneralInfoVO);
        orderGeneralInfoVO.setCanAccept(orderDetailDTO.getCanAccept());
        return orderGeneralInfoVO;
    }

    public ChangeOrderDetailVO toChangeGeneralDetailVO(ChangeOrderDetailDTO changeOrderDetailDTO) throws Exception {
        // 1. map change order Dto to vo
        ChangeOrderDetailVO changeOrderDetailVO = new ChangeOrderDetailVO();
        ChangeOrderVersionDTO changeOrderDTO = changeOrderDetailDTO.getOrderVersionDTO();
        toOrderGeneralInfoVO(changeOrderDTO.getChangeOrder(), changeOrderDetailVO);

        // 2. map change order parent order dto to vo, set aggregated order
        OrderVersionDTO parentOrderDTO = changeOrderDTO.getParentOrder();
//        OrderGeneralInfoVO parentOrderDetail = new OrderGeneralInfoVO();
//        toOrderGeneralInfoVO(parentOrderDTO, parentOrderDetail);

        // 3. map change order base order dto to vo
        OrderVersionDTO baseOrderDTO = changeOrderDTO.getBaseOrder();
        OrderGeneralInfoVO baseOrderDetail = new OrderGeneralInfoVO();
        toOrderGeneralInfoVO(baseOrderDTO, baseOrderDetail);

        changeOrderDetailVO.setParentOrderItems(baseOrderDetail.getOrderItems());
        changeOrderDetailVO.setParentOrderReference(baseOrderDetail.getReference());
        Long valueCurrencyId = parentOrderDTO.getValueCurrencyId();
        changeOrderDetailVO.setOrderItemDiscountSurchargeTotal(changeOrderDTO.getChangeOrder().getOrderItemDiscountSurchargeTotal() != null
                ? changeOrderDTO.getChangeOrder().getOrderItemDiscountSurchargeTotal() : BigDecimal.ZERO);
        changeOrderDetailVO.setOrderItemDiscountSurchargeTotalCurrencyId(valueCurrencyId);
        changeOrderDetailVO.setDiscountOrSurchargeTotal(changeOrderDTO.getChangeOrder().getDiscountOrSurchargeTotal() != null
                ? changeOrderDTO.getChangeOrder().getDiscountOrSurchargeTotal() : BigDecimal.ZERO);
        changeOrderDetailVO.setDiscountOrSurchargeTotalCurrencyId(valueCurrencyId);
        OrderVersionDTO changeOrder = changeOrderDTO.getChangeOrder();
        boolean isDualCurrency = changeOrder.getRate() != null && changeOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && changeOrder.getExCurrencyId() != null;
        if (isDualCurrency) {
            changeOrderDetailVO.setExOrderItemDiscountSurchargeTotal(changeOrderDTO.getChangeOrder().getExOrderItemDiscountSurchargeTotal() != null
                    ? changeOrderDTO.getChangeOrder().getExOrderItemDiscountSurchargeTotal() : BigDecimal.ZERO);
            changeOrderDetailVO.setExOrderItemDiscountSurchargeTotalCurrencyId(changeOrderDTO.getChangeOrder().getExCurrencyId());
            changeOrderDetailVO.setExDiscountOrSurchargeTotal(changeOrderDTO.getChangeOrder().getExDiscountOrSurchargeTotal() != null
                    ? changeOrderDTO.getChangeOrder().getExDiscountOrSurchargeTotal() : BigDecimal.ZERO);
            changeOrderDetailVO.setExDiscountOrSurchargeTotalCurrencyId(changeOrderDTO.getChangeOrder().getExCurrencyId());
        }
        changeOrderDetailVO.setRequiredApprovalInvoice(changeOrderDTO.getChangeOrder().isRequiredApprovalInvoice());
        changeOrderDetailVO.setAutoAcceptFinalInvoice(changeOrderDTO.getChangeOrder().isAutoAcceptFinalInvoice());
        changeOrderDetailVO.setInvoiceSentOnClosedOrder(changeOrderDTO.getChangeOrder().isInvoiceSentOnClosedOrder());
        changeOrderDetailVO.setFinalInvoiceTemplate(invoiceMapper.toVO(changeOrderDTO.getChangeOrder().getFinalInvoiceTemplate()));
        changeOrderDetailVO.setCreationDate(changeOrderDTO.getChangeOrder().getCreateDate());
        changeOrderDetailVO.setOrderSendToSupplierDate(changeOrderDetailDTO.getOrderSendToSupplierDate());
        changeOrderDetailVO.setCanAccept(changeOrderDetailDTO.isCanAccept());
        changeOrderDetailVO.setIsDisableCarryOverChangeOrder(changeOrderDetailDTO.getIsDisableCarryOverChangeOrder());
        changeOrderDetailVO.setIsDisableAcceptAndCarryOver(changeOrderDetailDTO.getIsDisableAcceptAndCarryOver());
        return changeOrderDetailVO;
    }

    // Please use getLogoUrl(Long workgroupId) instead
    @Deprecated
    private String getLogoUrl(String logoOneUrl, String logoUrl, Long workgroupId) {
        String logoImgHref = null;
        if(logoOneUrl != null){
            // NooshOne
            logoImgHref = NooshOneUrlUtil.composeLinkToS3(logoOneUrl);
        }else if(logoUrl != null && (!logoUrl.contains("/logos/noosh.gif"))){
            // Enterprise
            logoImgHref = NooshOneUrlUtil.composeLinkToEnterprise(logoUrl);
            // code for old logo issue
            if(logoUrl.startsWith("noosh/")){
                logoImgHref = NooshOneUrlUtil.composeLinkToS3(logoUrl);
            }
        }

        Map<String, String> prefs = preferenceService.findGroupPrefs(workgroupId);
        ServiceProvider serviceProvider = serviceProviderRepository.findByWorkgroupId(workgroupId);
        // if NGE is enabled, use NGE's logo
        if(serviceProvider != null && serviceProvider.getLogoImg() != null && !("").equals(serviceProvider.getLogoImg())
                && preferenceService.check(PreferenceID.WORKGROUP_OPTION_NL_SITE_MANAGER, prefs)){
            logoImgHref = NooshOneUrlUtil.composeLinkToS3(serviceProvider.getLogoImg());
        }
        // default logo
        if (logoImgHref == null) {
            logoImgHref = "/assets/images/noosh_blue.png";
        }
        return logoImgHref;
    }

    private String getLogoUrl(Long workgroupId) {
        String logo = null;
        if (workgroupId > 0) {
            logo = webOpenFeignClient.getWorkgroupLogo(workgroupId);
        }
        return StringUtils.isNotEmpty(logo) ? logo : "/assets/images/noosh_blue.png";
    }

    public String getStatusFormat(OrderStateDTO orderState) {
        String format;
        if (orderState.getObjectStateId() == ObjectStateID.ORDER_ACCEPTED
                || orderState.getObjectStateId() == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED
                || orderState.getObjectStateId() == ObjectStateID.ORDER_ALLOCATION_ACTIVE
                || orderState.getObjectStateId() == ObjectStateID.ORDER_DELIVERED) {
            format = "status_accept";
        } else if (orderState.getObjectStateId() == ObjectStateID.ORDER_ALLOCATION_CANCELLED
                || orderState.getObjectStateId() == ObjectStateID.ORDER_CANCELLED
                || orderState.getObjectStateId() == ObjectStateID.ORDER_REPLACED
                || orderState.getObjectStateId() == ObjectStateID.ORDER_RETRACTED
                || orderState.getObjectStateId() == ObjectStateID.ORDER_REJECTED) {
            format = "status_reject";
        } else if (orderState.getObjectStateId() == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                || orderState.getObjectStateId() == ObjectStateID.ORDER_SHIPPED) {
            format = "status_pending";
        } else if (orderState.getObjectStateId() == ObjectStateID.ORDER_DRAFT
                || orderState.getObjectStateId() == ObjectStateID.ORDER_PENDING_SUBMISSION
                || orderState.getObjectStateId() == ObjectStateID.PENDING_APPROVAL
                || orderState.getObjectStateId() == ObjectStateID.ORDER_BUYER_TO_ACCEPT
                || orderState.getObjectStateId() == ObjectStateID.ORDER_CLIENT_TO_ACCEPT
                || orderState.getObjectStateId() == ObjectStateID.ORDER_OUTSOURCER_TO_ACCEPT
                || orderState.getObjectStateId() == ObjectStateID.ORDER_SUPPLIER_TO_ACCEPT
                ) {
            format = "status_draft";
        } else {
            format = "status_normal";
        }

        return format;
    }




}