package com.noosh.app.mapper.externalItem;

import com.noosh.app.commons.dto.externalItem.ExternalItemDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.entity.externalItem.ExternalItem;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.commons.vo.externalItem.ExternalItemVO;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import com.noosh.app.mapper.property.PropertyAttributeMapper;
import jakarta.inject.Inject;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * User: leilaz
 * Date: 3/31/22
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class ExternalItemMapper {
    @Inject
    private PropertyAttributeMapper propertyAttributeMapper;

    public abstract ExternalItemDTO toDTO(ExternalItem item);
    public abstract ExternalItemVO toVO(ExternalItemDTO dto);

    public List<ExternalItemDTO> toDTOs(List<ExternalItem> externalItems) {
        if (externalItems != null && !externalItems.isEmpty()) {
            List<ExternalItemDTO> externalItemDTOs = new ArrayList<ExternalItemDTO>();
            for (ExternalItem item : externalItems) {
                ExternalItemDTO externalItemDTO = toDTO(item);
                externalItemDTOs.add(externalItemDTO);
            }
            return externalItemDTOs;
        }
        return null;
    }

    public List<PropertyAttributeDTO> getPropertyAttributeDTOs(List<ExternalItem> externalItems) {
        if (externalItems == null || externalItems.isEmpty()) {
            return null;
        }
        ExternalItem externalItem = externalItems.get(0);
        if (externalItem.getItemPropertyId() == null || externalItem.getProperty() == null) {
            return null;
        }
        return propertyAttributeMapper.getPropertyAttributeDTOs(externalItem.getProperty());
    }

    public List<ExternalItemVO> toVOs(List<ExternalItemDTO> externalItemDTOs, BigDecimal qty) {
        if (externalItemDTOs != null && externalItemDTOs.size() > 0) {
            List<ExternalItemVO> vos = new ArrayList<ExternalItemVO>();
            for (ExternalItemDTO dto : externalItemDTOs) {
                ExternalItemVO vo = toVO(dto);
                vo.setMyQty(dto.getQtyFor(qty != null ? qty.longValue() : (long) 0));
                vo.setUnitPriceFor(dto.getUnitPriceFor(qty != null ? qty.longValue() : (long) 0));
                vo.setUnitPriceUOMFor(dto.getUnitPriceUOMFor(qty != null ? qty.longValue() : (long) 0));
                vo.setPriceFor(dto.getPriceFor(qty != null ? qty.longValue() : (long) 0));
                vos.add(vo);
            }
            return vos;
        }
        return null;
    }
}
