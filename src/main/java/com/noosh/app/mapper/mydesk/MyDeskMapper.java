package com.noosh.app.mapper.mydesk;

import com.noosh.app.commons.constant.OrderTypeID;
import com.noosh.app.commons.dto.invoice.InvoicePendingMyDeskDTO;
import com.noosh.app.commons.dto.mydesk.MyDeskPendingDTO;
import com.noosh.app.commons.dto.order.OrderInvoiceMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderPendingMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderStatusMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderSupplierMyDeskDTO;
import com.noosh.app.commons.vo.invoice.InvoicePendingMyDeskVO;
import com.noosh.app.commons.vo.mydesk.MyDeskDueWidgetVO;
import com.noosh.app.commons.vo.mydesk.MyDeskPendingVO;
import com.noosh.app.commons.vo.order.*;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 3/26/24
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class MyDeskMapper {
    @Autowired
    private I18NUtils i18NUtils;

    @Mapping(ignore = true, target = "orderExternalUrl")
    @Mapping(ignore = true, target = "projectExternalUrl")
    @Mapping(ignore = true, target = "isChangeOrder")
    public abstract OrderStatusMyDeskVO toStatusVO(OrderStatusMyDeskDTO dto);

    @Mapping(ignore = true, target = "orderExternalUrl")
    @Mapping(ignore = true, target = "projectExternalUrl")
    @Mapping(ignore = true, target = "isChangeOrder")
    public abstract OrderSupplierMyDeskVO toSupplierVO(OrderSupplierMyDeskDTO dto);

    @Mapping(ignore = true, target = "orderExternalUrl")
    @Mapping(ignore = true, target = "projectExternalUrl")
    @Mapping(ignore = true, target = "isChangeOrder")
    public abstract OrderInvoiceMyDeskVO toInvoiceVO(OrderInvoiceMyDeskDTO dto);

    @Mapping(ignore = true, target = "orderExternalUrl")
    @Mapping(ignore = true, target = "projectExternalUrl")
    public abstract OrderPendingMyDeskVO toOrderPendingVO(OrderPendingMyDeskDTO dto);

    @Mapping(ignore = true, target = "invoiceExternalUrl")
    @Mapping(ignore = true, target = "projectExternalUrl")
    public abstract InvoicePendingMyDeskVO toInvoicePendingVO(InvoicePendingMyDeskDTO dto);

    public List<OrderStatusMyDeskListVO> toOrderStatusVOs(List<OrderStatusMyDeskDTO> dtos, Long currentWorkgroupId) {
        List<OrderStatusMyDeskListVO> vos = new ArrayList<OrderStatusMyDeskListVO>();
        if (dtos == null) {
            return vos;
        }
        Comparator<OrderStatusMyDeskDTO> compareById = Comparator
                .comparing(OrderStatusMyDeskDTO::getOrderStateId)
                .thenComparing(OrderStatusMyDeskDTO::getOrderId);
        dtos = dtos.stream().sorted(compareById).collect(Collectors.toList());
        Map<Long, List<OrderStatusMyDeskDTO>> results =
                dtos.stream().collect(Collectors.groupingBy(OrderStatusMyDeskDTO::getOrderStateId));
        for (Long stateId : results.keySet()) {
            OrderStatusMyDeskListVO listVO = new OrderStatusMyDeskListVO();
            listVO.setStatusStr(i18NUtils.getObjectStateMessage(stateId));
            listVO.setStatusId(stateId);
            List<OrderStatusMyDeskVO> orders = new ArrayList<OrderStatusMyDeskVO>();
            List<OrderStatusMyDeskDTO> myDeskDTOs = results.get(stateId);
            if (myDeskDTOs != null && myDeskDTOs.size() > 0) {
                for (OrderStatusMyDeskDTO dto : myDeskDTOs) {
                    OrderStatusMyDeskVO vo = toStatusVO(dto);
                    vo.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(dto.getProjectId()));
                    if (dto.getOrderTypeId() == OrderTypeID.CHANGE_ORDER) {
                        vo.setOrderExternalUrl(NooshOneUrlUtil.composeViewChangeOrderLinkToEnterprise(dto.getProjectId(), dto.getParentOrderId(), dto.getOrderId()));
                        vo.setIsChangeOrder(true);
                    } else {
                        vo.setOrderExternalUrl(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(dto.getProjectId(), dto.getOrderId()));
                    }
                    vo.setIsUserBuyer(dto.getBuyerWorkgroupId() != null && dto.getBuyerWorkgroupId() == currentWorkgroupId.longValue());
                    vo.setIsUserSupplier(dto.getSupplierWorkgroupId() != null && dto.getSupplierWorkgroupId() == currentWorkgroupId.longValue());
                    vo.setIsAggregated(dto.getChangeOrderCount() != null && dto.getChangeOrderCount().longValue() > 0);
                    orders.add(vo);
                }
                listVO.setOrders(orders);
            }
            vos.add(listVO);
        }
        return vos;
    }

    public List<OrderSupplierMyDeskListVO> toOrderSupplierVOs(List<OrderSupplierMyDeskDTO> dtos, Long currentWorkgroupId) {
        List<OrderSupplierMyDeskListVO> vos = new ArrayList<OrderSupplierMyDeskListVO>();
        if (dtos == null) {
            return vos;
        }
        Map<Long, List<OrderSupplierMyDeskDTO>> results =
                dtos.stream().collect(Collectors.groupingBy(OrderSupplierMyDeskDTO::getSupplierWorkgroupId));
        for (Long supplierWgId : results.keySet()) {
            OrderSupplierMyDeskListVO listVO = new OrderSupplierMyDeskListVO();
            listVO.setSupplierWorkgroupName(results.get(supplierWgId).get(0).getSupplierWorkgroupName());
            List<OrderSupplierMyDeskVO> orders = new ArrayList<OrderSupplierMyDeskVO>();
            List<OrderSupplierMyDeskDTO> myDeskDTOs = results.get(supplierWgId);
            if (myDeskDTOs != null && myDeskDTOs.size() > 0) {
                for (OrderSupplierMyDeskDTO dto : myDeskDTOs) {
                    OrderSupplierMyDeskVO vo = toSupplierVO(dto);
                    vo.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(dto.getProjectId()));
                    if (dto.getOrderTypeId() == OrderTypeID.CHANGE_ORDER) {
                        vo.setOrderExternalUrl(NooshOneUrlUtil.composeViewChangeOrderLinkToEnterprise(dto.getProjectId(), dto.getParentOrderId(), dto.getOrderId()));
                        vo.setIsChangeOrder(true);
                    } else {
                        vo.setOrderExternalUrl(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(dto.getProjectId(), dto.getOrderId()));
                    }
                    vo.setIsUserBuyer(dto.getBuyerWorkgroupId() != null && dto.getBuyerWorkgroupId() == currentWorkgroupId.longValue());
                    vo.setIsUserSupplier(dto.getSupplierWorkgroupId() != null && dto.getSupplierWorkgroupId() == currentWorkgroupId.longValue());
                    vo.setAmount(dto.getAmount().add(dto.getTaxAndShipping()).doubleValue());
                    vo.setIsAggregated(dto.getChangeOrderCount() != null && dto.getChangeOrderCount().longValue() > 0);
                    orders.add(vo);
                }
                listVO.setOrders(orders);
            }
            vos.add(listVO);
        }
        return vos;
    }

    public OrderInvoiceMyDeskListVO toOrderInvoiceVO(List<OrderInvoiceMyDeskDTO> dtos, Long currentWorkgroupId) {
        OrderInvoiceMyDeskListVO orderInvoiceMyDeskListVO = new OrderInvoiceMyDeskListVO();
        if (dtos == null) {
            return orderInvoiceMyDeskListVO;
        }
        List<OrderInvoiceMyDeskVO> invoicedOrders = new ArrayList<OrderInvoiceMyDeskVO>();
        List<OrderInvoiceMyDeskVO> tempPartiallyInvoicedOrders = new ArrayList<OrderInvoiceMyDeskVO>();
        List<OrderInvoiceMyDeskVO> partiallyInvoicedOrders = new ArrayList<OrderInvoiceMyDeskVO>();
        List<OrderInvoiceMyDeskVO> notInvoicedOrders = new ArrayList<OrderInvoiceMyDeskVO>();
        Map<Long, BigDecimal> tempOrderMap = new HashMap<>();
        for (OrderInvoiceMyDeskDTO dto : dtos) {
            OrderInvoiceMyDeskVO vo = toInvoiceVO(dto);
            vo.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(dto.getProjectId()));
            if (dto.getOrderTypeId() == OrderTypeID.CHANGE_ORDER) {
                vo.setOrderExternalUrl(NooshOneUrlUtil.composeViewChangeOrderLinkToEnterprise(dto.getProjectId(), dto.getParentOrderId(), dto.getOrderId()));
                vo.setIsChangeOrder(true);
            } else {
                vo.setOrderExternalUrl(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(dto.getProjectId(), dto.getOrderId()));
            }
            vo.setIsUserBuyer(dto.getBuyerWorkgroupId() != null && dto.getBuyerWorkgroupId() == currentWorkgroupId.longValue());
            vo.setIsUserSupplier(dto.getSupplierWorkgroupId() != null && dto.getSupplierWorkgroupId() == currentWorkgroupId.longValue());
            vo.setIsAggregated(dto.getChangeOrderCount() != null && dto.getChangeOrderCount().longValue() > 0);
            if (dto.getInvoiceId() == null && dto.getOrderId() != null) {
                vo.setOrderAmount(dto.getOrderAmount().add(dto.getOrderTaxAndShipping()).doubleValue());
                notInvoicedOrders.add(vo);
            } else {
                if (dto.getInvoiceId() != null) {
                    BigDecimal totalInvoice = BigDecimal.ZERO;
                    BigDecimal totalOrder = BigDecimal.ZERO;
                    if (dto.getInvoiceAmount() != null) {
                        totalInvoice = dto.getInvoiceAmount();
                        if (dto.getInvoiceTaxAndShipping() != null) {
                            totalInvoice = totalInvoice.add(dto.getInvoiceTaxAndShipping());
                        }
                    }
                    if (dto.getOrderAmount() != null) {
                        totalOrder = dto.getOrderAmount();
                        if (dto.getOrderTaxAndShipping() != null) {
                            totalOrder = totalOrder.add(dto.getOrderTaxAndShipping());
                        }
                    }
                    if (totalInvoice.compareTo(totalOrder) < 0) {
                        vo.setOrderAmount(totalOrder.doubleValue());
                        BigDecimal totalInvoiceAmount = totalInvoice;
                        if (tempOrderMap.containsKey(dto.getOrderId()) && tempOrderMap.get(dto.getOrderId()) != null) {
                            totalInvoiceAmount = totalInvoiceAmount.add(tempOrderMap.get(dto.getOrderId()));
                        }
                        vo.setAmount(totalInvoiceAmount.doubleValue());
                        if (totalInvoiceAmount.compareTo(totalOrder) < 0) {
                            OrderInvoiceMyDeskVO existInvoicedOrder = tempPartiallyInvoicedOrders.stream().filter(
                                    t -> t.getOrderId() == dto.getOrderId().longValue()).findAny().orElse(null);
                            if (existInvoicedOrder != null) {
                                existInvoicedOrder.setAmount(totalInvoiceAmount.doubleValue());
                            } else {
                                tempPartiallyInvoicedOrders.add(vo);
                            }
                        } else if (totalInvoiceAmount.compareTo(totalOrder) == 0) {
                            invoicedOrders.add(vo);
                        }
                        // Need to remove the duplicated order id
                        tempOrderMap.put(dto.getOrderId(), totalInvoiceAmount);
                    } else if (totalInvoice.compareTo(totalOrder) == 0) {
                        vo.setAmount(totalInvoice.doubleValue());
                        vo.setOrderAmount(totalOrder.doubleValue());
                        invoicedOrders.add(vo);
                    }
                }
            }
        }
        for (OrderInvoiceMyDeskVO vo : tempPartiallyInvoicedOrders) {
            if (!invoicedOrders.stream().filter(s -> s.getOrderId().longValue() == vo.getOrderId()).findAny().isPresent()) {
                partiallyInvoicedOrders.add(vo);
            }
        }
        orderInvoiceMyDeskListVO.setInvoicedOrders(invoicedOrders);
        orderInvoiceMyDeskListVO.setNotInvoicedOrders(notInvoicedOrders);
        orderInvoiceMyDeskListVO.setNotFullyInvoicedOrders(partiallyInvoicedOrders);
        return orderInvoiceMyDeskListVO;
    }

    public MyDeskPendingVO toMyDeskPendingVO(MyDeskPendingDTO myDeskPendingDTO, Long currentWorkgroupId) {
        MyDeskPendingVO myDeskPendingVO = new MyDeskPendingVO();
        if (myDeskPendingDTO == null) {
            return myDeskPendingVO;
        }
        myDeskPendingVO.setCanViewInvoices(myDeskPendingDTO.getCanViewInvoices());
        myDeskPendingVO.setCanViewOrders(myDeskPendingDTO.getCanViewOrders());
        if (myDeskPendingDTO.getOrders() != null) {
            List<OrderPendingMyDeskDTO> orderPendingMyDeskDTOs = myDeskPendingDTO.getOrders();
            List<OrderPendingMyDeskVO> orderPendingMyDeskVOs = new ArrayList<OrderPendingMyDeskVO>();
            for (OrderPendingMyDeskDTO orderPendingMyDeskDTO : orderPendingMyDeskDTOs) {
                OrderPendingMyDeskVO orderPendingMyDeskVO = toOrderPendingVO(orderPendingMyDeskDTO);
                orderPendingMyDeskVO.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(
                        orderPendingMyDeskDTO.getProjectId()));

                orderPendingMyDeskVO.setOrderExternalUrl(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(
                        orderPendingMyDeskDTO.getProjectId(), orderPendingMyDeskDTO.getOrderId()));
                orderPendingMyDeskVO.setIsUserBuyer(orderPendingMyDeskDTO.getBuyerWorkgroupId() != null
                        && orderPendingMyDeskDTO.getBuyerWorkgroupId() == currentWorkgroupId.longValue());
                orderPendingMyDeskVO.setIsUserSupplier(orderPendingMyDeskDTO.getSupplierWorkgroupId() != null
                        && orderPendingMyDeskDTO.getSupplierWorkgroupId() == currentWorkgroupId.longValue());
                orderPendingMyDeskVO.setIsAggregated(orderPendingMyDeskDTO.getChangeOrderCount() != null && orderPendingMyDeskDTO.getChangeOrderCount().longValue() > 0);
                orderPendingMyDeskVOs.add(orderPendingMyDeskVO);
            }
            myDeskPendingVO.setOrders(orderPendingMyDeskVOs);
        }
        if (myDeskPendingDTO.getChangeOrders() != null) {
            List<OrderPendingMyDeskDTO> changeOrderDTOs = myDeskPendingDTO.getChangeOrders();
            List<OrderPendingMyDeskVO> changeOrderVOs = new ArrayList<OrderPendingMyDeskVO>();
            for (OrderPendingMyDeskDTO orderPendingMyDeskDTO : changeOrderDTOs) {
                OrderPendingMyDeskVO orderPendingMyDeskVO = toOrderPendingVO(orderPendingMyDeskDTO);
                orderPendingMyDeskVO.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(
                        orderPendingMyDeskDTO.getProjectId()));

                orderPendingMyDeskVO.setOrderExternalUrl(NooshOneUrlUtil.composeViewChangeOrderLinkToEnterprise(
                        orderPendingMyDeskDTO.getProjectId(), orderPendingMyDeskDTO.getParentOrderId(), orderPendingMyDeskDTO.getOrderId()));
                orderPendingMyDeskVO.setIsUserBuyer(orderPendingMyDeskDTO.getBuyerWorkgroupId() != null
                        && orderPendingMyDeskDTO.getBuyerWorkgroupId() == currentWorkgroupId.longValue());
                orderPendingMyDeskVO.setIsUserSupplier(orderPendingMyDeskDTO.getSupplierWorkgroupId() != null
                        && orderPendingMyDeskDTO.getSupplierWorkgroupId() == currentWorkgroupId.longValue());
                changeOrderVOs.add(orderPendingMyDeskVO);
            }
            myDeskPendingVO.setChangeOrders(changeOrderVOs);
        }
        if (myDeskPendingDTO.getInvoices() != null) {
            List<InvoicePendingMyDeskDTO> invoicePendingMyDeskDTOs = myDeskPendingDTO.getInvoices();
            List<InvoicePendingMyDeskVO> invoicePendingMyDeskVOs = new ArrayList<InvoicePendingMyDeskVO>();
            for (InvoicePendingMyDeskDTO invoicePendingMyDeskDTO : invoicePendingMyDeskDTOs) {
                InvoicePendingMyDeskVO invoicePendingMyDeskVO = toInvoicePendingVO(invoicePendingMyDeskDTO);
                invoicePendingMyDeskVO.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(
                        invoicePendingMyDeskDTO.getProjectId()));

                invoicePendingMyDeskVO.setInvoiceExternalUrl(NooshOneUrlUtil.composeViewInvoiceLinkToEnterprise(
                        invoicePendingMyDeskDTO.getProjectId(), invoicePendingMyDeskDTO.getInvoiceId()));
                invoicePendingMyDeskVOs.add(invoicePendingMyDeskVO);
            }
            myDeskPendingVO.setInvoices(invoicePendingMyDeskVOs);
        }
        return myDeskPendingVO;
    }

    public MyDeskDueWidgetVO toMyDeskDueWidgetVO(MyDeskPendingDTO myDeskPendingDTO, Long currentWorkgroupId) {
        MyDeskDueWidgetVO myDeskDueWidgetVO = new MyDeskDueWidgetVO();
        if (myDeskPendingDTO == null) {
            return myDeskDueWidgetVO;
        }
        myDeskDueWidgetVO.setCanViewInvoices(myDeskPendingDTO.getCanViewInvoices());
        myDeskDueWidgetVO.setCanViewOrders(myDeskPendingDTO.getCanViewOrders());
        if (myDeskPendingDTO.getOrders() != null) {
            List<OrderPendingMyDeskDTO> orderPendingMyDeskDTOs = myDeskPendingDTO.getOrders();
            List<OrderPendingMyDeskVO> orderPendingMyDeskVOs = new ArrayList<OrderPendingMyDeskVO>();
            for (OrderPendingMyDeskDTO orderPendingMyDeskDTO : orderPendingMyDeskDTOs) {
                OrderPendingMyDeskVO orderPendingMyDeskVO = toOrderPendingVO(orderPendingMyDeskDTO);
                orderPendingMyDeskVO.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(
                        orderPendingMyDeskDTO.getProjectId()));

                orderPendingMyDeskVO.setOrderExternalUrl(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(
                        orderPendingMyDeskDTO.getProjectId(), orderPendingMyDeskDTO.getOrderId()));
                orderPendingMyDeskVO.setIsUserBuyer(orderPendingMyDeskDTO.getBuyerWorkgroupId() != null
                        && orderPendingMyDeskDTO.getBuyerWorkgroupId() == currentWorkgroupId.longValue());
                orderPendingMyDeskVO.setIsUserSupplier(orderPendingMyDeskDTO.getSupplierWorkgroupId() != null
                        && orderPendingMyDeskDTO.getSupplierWorkgroupId() == currentWorkgroupId.longValue());
                orderPendingMyDeskVO.setIsAggregated(orderPendingMyDeskDTO.getChangeOrderCount() != null && orderPendingMyDeskDTO.getChangeOrderCount().longValue() > 0);
                orderPendingMyDeskVOs.add(orderPendingMyDeskVO);
            }
            myDeskDueWidgetVO.setOrders(orderPendingMyDeskVOs);
        }
        if (myDeskPendingDTO.getChangeOrders() != null) {
            List<OrderPendingMyDeskDTO> changeOrderDTOs = myDeskPendingDTO.getChangeOrders();
            List<OrderPendingMyDeskVO> changeOrderVOs = new ArrayList<OrderPendingMyDeskVO>();
            for (OrderPendingMyDeskDTO orderPendingMyDeskDTO : changeOrderDTOs) {
                OrderPendingMyDeskVO orderPendingMyDeskVO = toOrderPendingVO(orderPendingMyDeskDTO);
                orderPendingMyDeskVO.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(
                        orderPendingMyDeskDTO.getProjectId()));

                orderPendingMyDeskVO.setOrderExternalUrl(NooshOneUrlUtil.composeOrderWithChangesLinkToEnterprise(
                        orderPendingMyDeskDTO.getProjectId(), orderPendingMyDeskDTO.getParentOrderId()));
                orderPendingMyDeskVO.setIsUserBuyer(orderPendingMyDeskDTO.getBuyerWorkgroupId() != null
                        && orderPendingMyDeskDTO.getBuyerWorkgroupId() == currentWorkgroupId.longValue());
                orderPendingMyDeskVO.setIsUserSupplier(orderPendingMyDeskDTO.getSupplierWorkgroupId() != null
                        && orderPendingMyDeskDTO.getSupplierWorkgroupId() == currentWorkgroupId.longValue());
                orderPendingMyDeskVO.setIsChangeOrder(true);
                changeOrderVOs.add(orderPendingMyDeskVO);
            }
            if (myDeskDueWidgetVO.getOrders() != null && myDeskDueWidgetVO.getOrders().size() > 0) {
                myDeskDueWidgetVO.getOrders().addAll(changeOrderVOs);
            } else {
                myDeskDueWidgetVO.setOrders(changeOrderVOs);
            }
        }
        if (myDeskPendingDTO.getInvoices() != null) {
            List<InvoicePendingMyDeskDTO> invoicePendingMyDeskDTOs = myDeskPendingDTO.getInvoices();
            List<InvoicePendingMyDeskVO> invoicePendingMyDeskVOs = new ArrayList<InvoicePendingMyDeskVO>();
            for (InvoicePendingMyDeskDTO invoicePendingMyDeskDTO : invoicePendingMyDeskDTOs) {
                InvoicePendingMyDeskVO invoicePendingMyDeskVO = toInvoicePendingVO(invoicePendingMyDeskDTO);
                invoicePendingMyDeskVO.setProjectExternalUrl(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(
                        invoicePendingMyDeskDTO.getProjectId()));

                invoicePendingMyDeskVO.setInvoiceExternalUrl(NooshOneUrlUtil.composeViewInvoiceLinkToEnterprise(
                        invoicePendingMyDeskDTO.getProjectId(), invoicePendingMyDeskDTO.getInvoiceId()));
                invoicePendingMyDeskVO.setIsUserBuyer(invoicePendingMyDeskDTO.getBuyerWorkgroupId() != null
                        && invoicePendingMyDeskDTO.getBuyerWorkgroupId() == currentWorkgroupId.longValue());
                invoicePendingMyDeskVO.setIsUserSupplier(invoicePendingMyDeskDTO.getSupplierWorkgroupId() != null
                        && invoicePendingMyDeskDTO.getSupplierWorkgroupId() == currentWorkgroupId.longValue());
                invoicePendingMyDeskVOs.add(invoicePendingMyDeskVO);
            }
            myDeskDueWidgetVO.setInvoices(invoicePendingMyDeskVOs);
        }
        return myDeskDueWidgetVO;
    }
}
