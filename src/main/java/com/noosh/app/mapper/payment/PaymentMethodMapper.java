package com.noosh.app.mapper.payment;

import com.noosh.app.commons.dto.order.PaymentMethodDTO;
import com.noosh.app.commons.entity.order.PaymentMethod;
import com.noosh.app.service.util.I18NUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 8/7/19
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class PaymentMethodMapper {
    @Inject
    I18NUtils i18NUtils;

    @Mapping(target = "name", expression = "java(i18NUtils.getMessage(paymentMethod.getDescriptionStrId()))")
    public abstract PaymentMethodDTO toDTO(PaymentMethod paymentMethod);
}
