package com.noosh.app.mapper.collaboration;

import com.noosh.app.commons.dto.collaboration.SyContainableDTO;
import com.noosh.app.commons.entity.collaboration.SyContainable;
import com.noosh.app.commons.vo.collaboration.SyContainableVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * User: leilaz
 * Date: 9/19/19
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SyContainableMapper {
    SyContainableDTO toDTO(SyContainable syContainable);
    List<SyContainableDTO> toDTOs(List<SyContainable> syContainable);
    SyContainableVO toVO(SyContainableDTO syContainableDTO);
    List<SyContainableVO> toVOs(List<SyContainableDTO> syContainableDTO);
    SyContainable toEntity(SyContainableDTO syContainableDTO);
}
