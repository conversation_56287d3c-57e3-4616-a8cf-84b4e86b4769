package com.noosh.app.mapper;

import com.noosh.app.commons.constant.OrderTypeID;
import com.noosh.app.commons.dto.order.OrderItemDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.order.OrderItem;
import com.noosh.app.commons.entity.order.OrderVersion;
import com.noosh.app.mapper.account.AccountUserMapper;
import com.noosh.app.mapper.payment.PaymentMethodMapper;
import com.noosh.app.mapper.reason.PcReasonMapper;
import com.noosh.app.mapper.security.WorkgroupMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class OrderVersionMapper {

    @Inject
    private PcReasonMapper pcReasonMapper;
    @Inject
    private WorkgroupMapper workgroupMapper;
    @Inject
    private AccountUserMapper accountUserMapper;
    @Inject
    private PaymentMethodMapper paymentMethodMapper;
    @Inject
    private OrderMapper orderMapper;
    @Inject
    private OrderItemMapper orderItemMapper;

    @Mapping(source = "buyerWorkgroup", ignore = true, target = "buyerWorkgroup")
    @Mapping(source = "supplierWorkgroup", ignore = true, target = "supplierWorkgroup")
    @Mapping(source = "buyer", ignore = true, target = "buyer")
    @Mapping(source = "supplier", ignore = true, target = "supplier")
    @Mapping(source = "paymentMethod", ignore = true, target = "paymentMethod")
    @Mapping(source = "reason", ignore = true, target = "reason")
    @Mapping(source = "order", ignore = true, target = "order")
    @Mapping(source = "orderCreateDate", target = "createDate")
    public abstract OrderVersionDTO toBaseDTO(OrderVersion orderVersion);

    @Mapping(source = "orderItemDTOs", ignore = true, target = "orderItemDTOs")
    @Mapping(source = "invoiceAdjustmentParentOrder", ignore = true, target = "invoiceAdjustmentParentOrder")
    @Mapping(source = "invoiceAdjustmentOrders", ignore = true, target = "invoiceAdjustmentOrders")
    public abstract OrderVersionDTO toBaseCopy(OrderVersionDTO orderVersionDTO);

    public OrderVersionDTO toCopy(OrderVersionDTO orderVersionDTO) {
        OrderVersionDTO versionCopyDTO = toBaseCopy(orderVersionDTO);
        List<OrderItemDTO> itemCopyDTOs = orderVersionDTO.getOrderItemDTOs().stream().map(orderItemMapper::toCopy).collect(Collectors.toList());
        versionCopyDTO.setOrderItemDTOs(itemCopyDTOs);
        return versionCopyDTO;
    }

    public OrderVersionDTO toDTO(OrderVersion orderVersion) {
        OrderVersionDTO orderVersionDTO = toBaseDTO(orderVersion);
        orderVersionDTO.setOrder(orderMapper.toDTO(orderVersion.getOrder()));
        orderVersionDTO.setBuyerWorkgroup(workgroupMapper.toDTO(orderVersion.getBuyerWorkgroup()));
        orderVersionDTO.setSupplierWorkgroup(workgroupMapper.toDTO(orderVersion.getSupplierWorkgroup()));
        orderVersionDTO.setBuyer(accountUserMapper.toDTO(orderVersion.getBuyer()));
        orderVersionDTO.setSupplier(accountUserMapper.toDTO(orderVersion.getSupplier()));
        orderVersionDTO.setPaymentMethod(paymentMethodMapper.toDTO(orderVersion.getPaymentMethod()));
        orderVersionDTO.setReason(pcReasonMapper.toReasonDTO(orderVersion.getReason()));
        return orderVersionDTO;
    }

    public List<OrderVersionDTO> toDTOs(List<OrderVersion> orderVersionList) {
        if (orderVersionList == null) return null;
        List<OrderVersionDTO> orderVersionDTOList = new ArrayList<>();
        for (OrderVersion orderVersion : orderVersionList) {
            orderVersionDTOList.add(toDTO(orderVersion));
        }
        return orderVersionDTOList;
    }

    public OrderVersionDTO toSimpleDTO(OrderVersion orderVersion, List<OrderItem> orderItems) {
        OrderVersionDTO orderVersionDTO = this.toDTO(orderVersion);
        BigDecimal orderItemSubtotal = new BigDecimal(0);
        Long orderItemSubtotalCurrencyId = null;
        BigDecimal orderItemDiscountOrSurchargeTotal = new BigDecimal(0);

        // Load order item
        if (orderItems != null && orderItems.size() > 0) {
            List<OrderItemDTO> orderItemDTOs = orderItems.stream().map(orderItemMapper::toDTO).collect(Collectors.toList());
            orderVersionDTO.setOrderItemDTOs(orderItemDTOs);
        }

        if (orderVersionDTO.getOrderItemDTOs() != null) {
            for (OrderItemDTO o : orderVersionDTO.getOrderItemDTOs()) {
                orderItemSubtotal = orderItemSubtotal.add(o.getValue() != null ? o.getValue() : BigDecimal.ZERO);
                orderItemSubtotalCurrencyId = o.getValueCurrencyId();
                orderItemDiscountOrSurchargeTotal = orderItemDiscountOrSurchargeTotal.add(o.getDors() != null ? o.getDors() : BigDecimal.ZERO);
            }
        }

        // Handle order price
        orderVersionDTO.setSubTotal(orderVersion.getOrderTypeId() == OrderTypeID.CHANGE_ORDER ?
                orderItemSubtotal.add(orderVersion.getMiscCost()) : orderItemSubtotal);
        orderVersionDTO.setOrderItemSubtotal(orderItemSubtotal);
        orderVersionDTO.setOrderItemSubtotalCurrencyId(orderItemSubtotalCurrencyId);
        orderVersionDTO.setDiscountOrSurchargeTotal(orderItemDiscountOrSurchargeTotal.add(orderVersion.getDors() != null ? orderVersion.getDors() : BigDecimal.ZERO));
        orderVersionDTO.setOrderItemDiscountSurchargeTotal(orderItemDiscountOrSurchargeTotal);
        orderVersionDTO.setTax(orderVersion.getTax());
        orderVersionDTO.setTaxCurrencyId(orderVersion.getTaxCurrencyId());
        orderVersionDTO.setShipping(orderVersion.getShipping());
        orderVersionDTO.setShippingCurrencyId(orderVersion.getShippingCurrencyId());
        orderVersionDTO.setGrandTotal(orderItemSubtotal.add(orderVersion.getTax())
                .add(orderVersion.getMiscCost())
                .add(orderVersion.getShipping()));
        orderVersionDTO.setGrandTotalCurrencyId(orderItemSubtotalCurrencyId);
        return orderVersionDTO;
    }

}
