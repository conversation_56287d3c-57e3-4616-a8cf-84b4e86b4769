package com.noosh.app.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.noosh.app.commons.vo.ErrorVO;
import com.noosh.app.exception.ForbiddenException;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.feign.ProjectOpenFeignClient;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.springframework.http.HttpStatus;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * This filter is used to check whether current login user have the permission to access the specified projectId
 * either from URL parameter through GET or request body through POST http request
 */
public class ProjectPermissionFilter extends OncePerRequestFilter {
    public static final String PROJECT_ID = "projectId";

    private final ProjectOpenFeignClient projectOpenFeignClient;

    public ProjectPermissionFilter(ProjectOpenFeignClient projectOpenFeignClient) {
        this.projectOpenFeignClient = projectOpenFeignClient;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        long projectId = -1;

        // Get projectId from request parameter, GET/POST
        if (StringUtils.isNotEmpty(request.getParameter(PROJECT_ID))) {
            projectId = Long.parseLong(request.getParameter(PROJECT_ID));
        }

        //If we don't find projectId from request parameter, then check from POST body
        boolean checkFromPostBody = projectId == -1 && "POST".equalsIgnoreCase(request.getMethod());
        BufferedRequestWrapper bufferedRequest = null;


        if (checkFromPostBody) {
            bufferedRequest = new BufferedRequestWrapper(request);
            JSONObject jsonObject = getPostJSONObject(bufferedRequest);
            if (jsonObject != null && jsonObject.has(PROJECT_ID)) {
                projectId = jsonObject.getLong(PROJECT_ID);
            }
        }
        try {
            //If projectId is specified, check the project access permission for the current login user
            if (projectId > 0) {
                boolean canAccess = projectOpenFeignClient.canAccessProject(projectId);
                if (!canAccess) throw new NoPermissionException("You don't have permission to view this project!");
            }
        } catch (NotFoundException | ForbiddenException | NoPermissionException e) {
            //Filter is triggered before @RestControllerAdvice, we will handle the business exception manually
            int httpStatus = e instanceof NotFoundException ? HttpStatus.NOT_FOUND.value() : HttpStatus.FORBIDDEN.value();
            ErrorVO errorVO = new ErrorVO(e.getMessage());
            ObjectMapper mapper = new ObjectMapper();
            response.setContentType("application/json");
            response.setStatus(httpStatus);
            response.getWriter().write(mapper.writeValueAsString(errorVO));
            return;
        }
        filterChain.doFilter(checkFromPostBody ? bufferedRequest : request, response);
    }

    private JSONObject getPostJSONObject(BufferedRequestWrapper bufferedRequest) {
        JSONObject jsonObject = null;
        String requestBodyString = new String(bufferedRequest.buffer, StandardCharsets.UTF_8).trim();
        // Skip the array object, projectId should present in JSON root level
        if (requestBodyString.startsWith("{")) {
            jsonObject = new JSONObject(requestBodyString);
        }
        return jsonObject;
    }

    private static class BufferedRequestWrapper extends
            HttpServletRequestWrapper {
        private byte[] buffer = null;

        public BufferedRequestWrapper(HttpServletRequest req)
                throws IOException {
            super(req);
            InputStream is = req.getInputStream();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buf = new byte[1024];
            int read;
            while ((read = is.read(buf)) > 0) {
                baos.write(buf, 0, read);
            }
            this.buffer = baos.toByteArray();
        }

        @Override
        public ServletInputStream getInputStream() {
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(this.buffer);
            return new BufferedServletInputStream(byteArrayInputStream);
        }
    }

    private static class BufferedServletInputStream extends
            ServletInputStream {

        private final ByteArrayInputStream byteArrayInputStream;

        public BufferedServletInputStream(ByteArrayInputStream byteArrayInputStream) {
            this.byteArrayInputStream = byteArrayInputStream;
        }

        @Override
        public int available() {
            return this.byteArrayInputStream.available();
        }

        @Override
        public int read() {
            return this.byteArrayInputStream.read();
        }

        @Override
        public int read(byte[] buf, int off, int len) {
            return this.byteArrayInputStream.read(buf, off, len);
        }

        @Override
        public boolean isReady() {
            return false;
        }

        @Override
        public void setReadListener(ReadListener readListener) {
            //do nothing here
        }

        @Override
        public boolean isFinished() {
            return false;
        }
    }

}