package com.noosh.app.commons.enums.order;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 11/4/15
 */
public enum OrderTypeEnum {
    Order(1000000),
    QuickOrder(1000001),
    ChangeOrder(1000002),
    QuoteOrder(1000003);


    private long typeId;

    OrderTypeEnum(final long typeId) {
        this.typeId = typeId;
    }

    public long getOrderTypeId() {
        return typeId;
    }

    public static OrderTypeEnum getOrderType(final long typeId) {
        for(OrderTypeEnum ot : OrderTypeEnum.values()) {
            if(ot.getOrderTypeId() == typeId) return ot;
        }
        throw new IllegalArgumentException("Wrong typeId for OrderTypeEnum");
    }

    public String getOrderType() {
        if(this.typeId == 1000002) {
            return "change";
        } else if(this.typeId == 1000003) {
            return "sell";
        } else {
            return "buy";
        }
    }

    public String getOrderTypeForMobile() {
        if(this.typeId == 1000002) {
            return "Change Order";
        } else if(this.typeId == 1000003) {
            return "Sell Order";
        } else {
            return "Buy Order";
        }
    }
}
