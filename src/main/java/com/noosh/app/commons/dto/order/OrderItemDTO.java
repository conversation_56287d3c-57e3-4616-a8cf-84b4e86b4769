package com.noosh.app.commons.dto.order;

import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.externalItem.ExternalItemDTO;
import com.noosh.app.commons.dto.shipment.ShipmentDTO;
import com.noosh.app.commons.dto.shipment.UofmDTO;
import com.noosh.app.commons.dto.spec.SpecDTO;
import com.noosh.app.commons.dto.spec.SpecTypeDTO;

import java.time.LocalDateTime;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * User: leilaz
 * Date: 9/30/17
 */
public class OrderItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long orderId;
    private Long orderVersionId;

    private Long estimateItemPriceId;

    private String comments;

    private LocalDateTime completionDate;

    private Long uofmId;

    private BigDecimal value;

    private BigDecimal exValue;

    private BigDecimal quantity;

    private Integer itemIndex;

    private String valueCurrency;

    private Long valueCurrencyId;

    private Long exValueCurrencyId;

    private Boolean isSpecChanged;

    private Long customPropertyId;

    private Long addPriceCurrencyId;

    private Long exAddPriceCurrencyId;

    private BigDecimal addPrice;

    private BigDecimal exAddPrice;

    private BigDecimal addUofmId;

    private Long purchaseRequestItemId;

    private Long quoteItemId;

    private Long specId;

    private Long breakoutTypeId;

    private Boolean totalFromBreakouts;

    private Boolean allowBreakouts;

    private Long jobId;

    private Long specNodeId;

    private String itemAttribute;

    private BigDecimal dors;

    private Long dorsCurrencyId;

    private BigDecimal exDors;

    private Long exDorsCurrencyId;

    private Boolean isOverridingBreakouts;

    private BigDecimal markupPercent;

    private BigDecimal shipping;

    private Long shippingCurrencyId;

    private BigDecimal exShipping;

    private Long exShippingCurrencyId;

    private BigDecimal tax;

    private Long taxCurrencyId;

    private BigDecimal exTax;

    private Long exTaxCurrencyId;

    //VAT
    private String vatCode;
    private BigDecimal vatRate;

    private Long budgetCustomFieldId;

    private String taxLabelString;

    private Long reasonId;

    private String reasonOther;

    private String reason;

    private String reasonStrId;

    private String reasonStr;

    private ShipmentDTO shipment;

    private UofmDTO uofm;

    private UofmDTO defaultUofm;

    private List<BreakoutDTO> breakouts;

    private List<ExternalItemDTO> externalItemDTOs;

//    private PropertyDTO property;

    private SpecDTO spec;

    public List<ExternalItemDTO> getExternalItemDTOs() {
        return externalItemDTOs;
    }

    public void setExternalItemDTOs(List<ExternalItemDTO> externalItemDTOs) {
        this.externalItemDTOs = externalItemDTOs;
    }

    public String getReasonStrId() {
        return reasonStrId;
    }

    public void setReasonStrId(String reasonStrId) {
        this.reasonStrId = reasonStrId;
    }

    public String getReasonStr() {
        return reasonStr;
    }

    public void setReasonStr(String reasonStr) {
        this.reasonStr = reasonStr;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public String getReasonOther() {
        return reasonOther;
    }

    public void setReasonOther(String reasonOther) {
        this.reasonOther = reasonOther;
    }

    public UofmDTO getDefaultUofm() {
        return defaultUofm;
    }

    public void setDefaultUofm(UofmDTO defaultUofm) {
        this.defaultUofm = defaultUofm;
    }

    public String getTaxLabelString() {
        return taxLabelString;
    }

    public void setTaxLabelString(String taxLabelString) {
        this.taxLabelString = taxLabelString;
    }

    public String getValueCurrency() {
        return valueCurrency;
    }

    public void setValueCurrency(String valueCurrency) {
        this.valueCurrency = valueCurrency;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderVersionId() {
        return orderVersionId;
    }

    public void setOrderVersionId(Long orderVersionId) {
        this.orderVersionId = orderVersionId;
    }

    public Long getEstimateItemPriceId() {
        return estimateItemPriceId;
    }

    public void setEstimateItemPriceId(Long estimateItemPriceId) {
        this.estimateItemPriceId = estimateItemPriceId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getUofmId() {
        return uofmId;
    }

    public void setUofmId(Long uofmId) {
        this.uofmId = uofmId;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public BigDecimal getQuantity() {
        return quantity == null ? BigDecimal.ZERO : quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Integer getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Integer itemIndex) {
        this.itemIndex = itemIndex;
    }

    public Long getValueCurrencyId() {
        return valueCurrencyId;
    }

    public void setValueCurrencyId(Long valueCurrencyId) {
        this.valueCurrencyId = valueCurrencyId;
    }

    public Boolean getIsSpecChanged() {
        return isSpecChanged;
    }

    public void setIsSpecChanged(Boolean specChanged) {
        isSpecChanged = specChanged;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getAddPriceCurrencyId() {
        return addPriceCurrencyId;
    }

    public void setAddPriceCurrencyId(Long addPriceCurrencyId) {
        this.addPriceCurrencyId = addPriceCurrencyId;
    }

    public BigDecimal getAddPrice() {
        return addPrice;
    }

    public void setAddPrice(BigDecimal addPrice) {
        this.addPrice = addPrice;
    }

    public BigDecimal getAddUofmId() {
        return addUofmId;
    }

    public void setAddUofmId(BigDecimal addUofmId) {
        this.addUofmId = addUofmId;
    }

    public Long getPurchaseRequestItemId() {
        return purchaseRequestItemId;
    }

    public void setPurchaseRequestItemId(Long purchaseRequestItemId) {
        this.purchaseRequestItemId = purchaseRequestItemId;
    }

    public Long getQuoteItemId() {
        return quoteItemId;
    }

    public void setQuoteItemId(Long quoteItemId) {
        this.quoteItemId = quoteItemId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public Boolean getTotalFromBreakouts() {
        return totalFromBreakouts;
    }

    public void setTotalFromBreakouts(Boolean totalFromBreakouts) {
        this.totalFromBreakouts = totalFromBreakouts;
    }

    public Boolean getAllowBreakouts() {
        return allowBreakouts != null && allowBreakouts.booleanValue();
    }

    public void setAllowBreakouts(Boolean allowBreakouts) {
        this.allowBreakouts = allowBreakouts;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getSpecNodeId() {
        return specNodeId;
    }

    public void setSpecNodeId(Long specNodeId) {
        this.specNodeId = specNodeId;
    }

    public BigDecimal getDors() {
        return dors;
    }

    public void setDors(BigDecimal dors) {
        this.dors = dors;
    }

    public String getItemAttribute() {
        return itemAttribute;
    }

    public void setItemAttribute(String itemAttribute) {
        this.itemAttribute = itemAttribute;
    }

    public Long getDorsCurrencyId() {
        return dorsCurrencyId;
    }

    public void setDorsCurrencyId(Long dorsCurrencyId) {
        this.dorsCurrencyId = dorsCurrencyId;
    }

    public Boolean getIsOverridingBreakouts() {
        return isOverridingBreakouts;
    }

    public void setIsOverridingBreakouts(Boolean overridingBreakouts) {
        isOverridingBreakouts = overridingBreakouts;
    }

    public BigDecimal getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(BigDecimal markupPercent) {
        this.markupPercent = markupPercent;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public String getVatCode() {
        return this.vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public BigDecimal getVatRate() {
        return this.vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public BigDecimal getExValue() {
        return this.exValue;
    }

    public void setExValue(BigDecimal exValue) {
        this.exValue = exValue;
    }

    public Long getExValueCurrencyId() {
        return this.exValueCurrencyId;
    }

    public void setExValueCurrencyId(Long exValueCurrencyId) {
        this.exValueCurrencyId = exValueCurrencyId;
    }

    public Long getExAddPriceCurrencyId() {
        return this.exAddPriceCurrencyId;
    }

    public void setExAddPriceCurrencyId(Long exAddPriceCurrencyId) {
        this.exAddPriceCurrencyId = exAddPriceCurrencyId;
    }

    public BigDecimal getExAddPrice() {
        return this.exAddPrice;
    }

    public void setExAddPrice(BigDecimal exAddPrice) {
        this.exAddPrice = exAddPrice;
    }

    public BigDecimal getExDors() {
        return this.exDors;
    }

    public void setExDors(BigDecimal exDors) {
        this.exDors = exDors;
    }

    public Long getExDorsCurrencyId() {
        return this.exDorsCurrencyId;
    }

    public void setExDorsCurrencyId(Long exDorsCurrencyId) {
        this.exDorsCurrencyId = exDorsCurrencyId;
    }

    public BigDecimal getExShipping() {
        return this.exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return this.exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExTax() {
        return this.exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return this.exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public Long getBudgetCustomFieldId() {
        return budgetCustomFieldId;
    }

    public void setBudgetCustomFieldId(Long budgetCustomFieldId) {
        this.budgetCustomFieldId = budgetCustomFieldId;
    }

    public ShipmentDTO getShipment() {
        return shipment;
    }

    public void setShipment(ShipmentDTO shipment) {
        this.shipment = shipment;
    }

    public UofmDTO getUofm() {
        return uofm;
    }

    public void setUofm(UofmDTO uofm) {
        this.uofm = uofm;
    }

    public List<BreakoutDTO> getBreakouts() {
        return breakouts;
    }

    public void setBreakouts(List<BreakoutDTO> breakouts) {
        this.breakouts = breakouts;
    }

    public BreakoutDTO getBreakoutByTypeId(long breakoutTypeId) {
        List<BreakoutDTO> breakouts = getBreakouts();
        if (breakouts != null && breakouts.size() > 0) {
            for (BreakoutDTO breakout : breakouts) {
                if (breakout.getBreakoutTypeId() == breakoutTypeId)
                    return breakout;
            }
        }
        return null;
    }

    public SpecDTO getSpec() {
        return spec;
    }

    public void setSpec(SpecDTO spec) {
        this.spec = spec;
    }

    public BigDecimal getPricePerUnits(long perNumUnits) {
        if (getValue() == null) return null;
        /* MH: changed this in 5.6 to allow cost to be > 0 while quantity = 0
        if (super.getQuantity()==0) {
            setValue(new Money(0.0, getValue().getCurrencyId()));
            return new Money(0.0, getValue().getCurrencyId());
        }*/
        if (getQuantity() != null && getQuantity().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        if (perNumUnits == 0) perNumUnits = 1;
        double returnValue = getValue().doubleValue() / getQuantity().doubleValue() * perNumUnits;
        SpecTypeDTO specTypeDTO = getSpec().getSpecType();
        if(specTypeDTO.getIsTimeMaterials() != null && specTypeDTO.getIsTimeMaterials() == (short)1){
            List<BreakoutDTO> breakoutDTOS = getBreakouts();
            if(breakoutDTOS != null && breakoutDTOS.size() > 0){
                BigDecimal totalUnitsValue = breakoutDTOS.stream().map(BreakoutDTO::getUnits).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                if(totalUnitsValue.compareTo(new BigDecimal(0)) > 0){
                    returnValue = returnValue/totalUnitsValue.doubleValue();
                }
            }

        }
        return BigDecimal.valueOf(returnValue);
    }

    public BigDecimal getExPricePerUnits(long perNumUnits) {
        if (getExValue() == null) return null;
        /* MH: changed this in 5.6 to allow cost to be > 0 while quantity = 0
        if (super.getQuantity()==0) {
            setValue(new Money(0.0, getValue().getCurrencyId()));
            return new Money(0.0, getValue().getCurrencyId());
        }*/
        if (getQuantity() != null && getQuantity().compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }

        if (perNumUnits == 0) perNumUnits = 1;
        double returnValue = getExValue().doubleValue() / getQuantity().doubleValue() * perNumUnits;
        //TODO not cover yet
        SpecTypeDTO specTypeDTO = getSpec().getSpecType();
        if(specTypeDTO.getIsTimeMaterials() != null && specTypeDTO.getIsTimeMaterials() == (short)1){
            List<BreakoutDTO> breakoutDTOS = getBreakouts();
            if(breakoutDTOS != null && breakoutDTOS.size() > 0){
                BigDecimal totalUnitsValue = breakoutDTOS.stream().map(BreakoutDTO::getUnits).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                if(totalUnitsValue.compareTo(new BigDecimal(0)) > 0){
                    returnValue = returnValue/totalUnitsValue.doubleValue();
                }
            }

        }
        return BigDecimal.valueOf(returnValue);
    }

    public BigDecimal getSubTotal() {
        return (getValue() != null ? getValue() : BigDecimal.ZERO).add(getShipping() != null ? getShipping() : BigDecimal.ZERO).add(getTax() != null ? getTax() : BigDecimal.ZERO);
    }

    public BigDecimal getExSubTotal() {
        return (getExValue() != null ? getExValue() : BigDecimal.ZERO).add(getExShipping() != null ? getExShipping() : BigDecimal.ZERO).add(getExTax() != null ? getExTax() : BigDecimal.ZERO);
    }

    public Double getAllowedUndersQty(double percent) {
        double qty = getQuantity().doubleValue();
        if (percent > 0)
            qty -= (qty * percent) / 100.0;
        return new Double(qty);
    }

    public Double getAllowedOversQty(double percent) {
        double qty = this.getQuantity().doubleValue();
        if (percent > 0)
            qty += (qty * percent) / 100.0;
        return new Double(qty);
    }
}
