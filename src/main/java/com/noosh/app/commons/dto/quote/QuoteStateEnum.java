package com.noosh.app.commons.dto.quote;

/**
 * @Author: neals
 * @Date: 12/7/2015
 */
public enum QuoteStateEnum {

    RfqCancelled(2000006),
    Draft(2000007),
    PendingClientAcceptance(2000008),
    Retracted(2000009),
    Rejected(2000010),
    Accepted(2000011),
    PendingPrApproval(2000017),
    Cancelled(2000018),
    Closed(2500114),
    IncompleteDraft(2500212),
    ReviseAccepted(2500217),
    ReviseRejected(2500218),
    RevisePendingApproval(2500219),
    ReviseInactive(2500220);

    private long objectStateId;

    QuoteStateEnum(long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public long getObjectStateId() {
        return objectStateId;
    }

    public static QuoteStateEnum getQuoteState(final long objectSateId){
        for(QuoteStateEnum qs : QuoteStateEnum.values()){
            if(qs.getObjectStateId() == objectSateId) return qs;
        }
        throw new IllegalArgumentException("Wrong objectStateId for QuoteStateEnum");
    }
}
