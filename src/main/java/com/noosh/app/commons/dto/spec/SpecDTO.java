package com.noosh.app.commons.dto.spec;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import java.io.Serializable;
import java.util.Map;

/**
 * User: leilaz
 * Date: 5/30/16
 */
public class SpecDTO implements Serializable {

    private static final long serialVersionUID = 4375566533882483889L;

    private Long id;
    private String name;
    private Long originalSpecId;
    private Boolean isLocked;
    private Boolean isImmutable;
    private Boolean isItemVersion;
    private Long nodeId;
    private Long specUserStateId;
    private Long lockCount;
    private String specUserState;
    private String specUserStateStrId;
    private String specLink;
    private String createOptionLink;
    private String editSpecLink;
    private String deleteSpecLink;
    private String creator;
    private LocalDateTime createDate;
    private LocalDateTime modDate;
    private Long createUserId;
    private String productNumber;
    private String versionNumber;
    private Long spSpecTypeId;
    private Long propertyId;
    private Short includeAup;
    private String fullName;
    private BigDecimal quantity1;

    // association
    private SpecNodeDTO specNode;
    private SpecTypeDTO specType;
    private SpecReferenceDTO specReference;

    private Map<String, Object> smartFormValues;

    public String getSpecUserStateStrId() {
        return specUserStateStrId;
    }

    public void setSpecUserStateStrId(String specUserStateStrId) {
        this.specUserStateStrId = specUserStateStrId;
    }

    public SpecReferenceDTO getSpecReference() {
        return specReference;
    }

    public void setSpecReference(SpecReferenceDTO specReference) {
        this.specReference = specReference;
    }

    public SpecTypeDTO getSpecType() {
        return specType;
    }

    public void setSpecType(SpecTypeDTO specType) {
        this.specType = specType;
    }

    public SpecNodeDTO getSpecNode() {
        return this.specNode;
    }

    public void setSpecNode(SpecNodeDTO specNode) {
        this.specNode = specNode;
    }

    public Short getIncludeAup() {
        return includeAup;
    }

    public void setIncludeAup(Short includeAup) {
        this.includeAup = includeAup;
    }

    public Long getSpSpecTypeId() {
        return spSpecTypeId;
    }

    public void setSpSpecTypeId(Long spSpecTypeId) {
        this.spSpecTypeId = spSpecTypeId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Long getLockCount() {
        return lockCount;
    }

    public void setLockCount(Long lockCount) {
        this.lockCount = lockCount;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public Long getSpecUserStateId() {
        return specUserStateId;
    }

    public void setSpecUserStateId(Long specUserStateId) {
        this.specUserStateId = specUserStateId;
    }

    public String getSpecUserState() {
        return specUserState;
    }

    public void setSpecUserState(String specUserState) {
        this.specUserState = specUserState;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOriginalSpecId() {
        return originalSpecId;
    }

    public void setOriginalSpecId(Long originalSpecId) {
        this.originalSpecId = originalSpecId;
    }

    public Boolean getIsLocked() {
        return isLocked != null ? isLocked.booleanValue() : false;
    }

    public void setIsLocked(Boolean locked) {
        isLocked = locked;
    }

    public Boolean getIsImmutable() {
        return isImmutable;
    }

    public void setIsImmutable(Boolean immutable) {
        isImmutable = immutable;
    }

    public Boolean getIsItemVersion() {
        return isItemVersion != null ? isItemVersion.booleanValue() : false;
    }

    public void setIsItemVersion(Boolean itemVersion) {
        isItemVersion = itemVersion;
    }

    public String getSpecLink() {
        return specLink;
    }

    public void setSpecLink(String specLink) {
        this.specLink = specLink;
    }

    public String getCreateOptionLink() {
        return createOptionLink;
    }

    public void setCreateOptionLink(String createOptionLink) {
        this.createOptionLink = createOptionLink;
    }

    public String getEditSpecLink() {
        return editSpecLink;
    }

    public void setEditSpecLink(String editSpecLink) {
        this.editSpecLink = editSpecLink;
    }

    public String getDeleteSpecLink() {
        return deleteSpecLink;
    }

    public void setDeleteSpecLink(String deleteSpecLink) {
        this.deleteSpecLink = deleteSpecLink;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public String getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public BigDecimal getQuantity1() {
        return this.quantity1;
    }

    public void setQuantity1(BigDecimal quantity1) {
        this.quantity1 = quantity1;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public Map<String, Object> getSmartFormValues() {
        return smartFormValues;
    }

    public void setSmartFormValues(Map<String, Object> smartFormValues) {
        this.smartFormValues = smartFormValues;
    }
}
