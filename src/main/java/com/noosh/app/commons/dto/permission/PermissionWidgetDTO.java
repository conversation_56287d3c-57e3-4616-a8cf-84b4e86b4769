package com.noosh.app.commons.dto.permission;

import java.io.Serializable;

/**
 * @Author: neals
 * @Date: 12/29/2015
 */
public class PermissionWidgetDTO implements Serializable {
    private static final long serialVersionUID = 7085828368827715796L;
    private Long permissionId;
    private Long parentObjectClassId;
    private String accessIsAllowed;
    private String accessIsPartial;

    private Long objectId;
    private Long teamId;
    private Long roleId;
    private Long workgroupId;

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(Long permissionId) {
        this.permissionId = permissionId;
    }

    public Long getParentObjectClassId() {
        return parentObjectClassId;
    }

    public void setParentObjectClassId(Long parentObjectClassId) {
        this.parentObjectClassId = parentObjectClassId;
    }

    public String getAccessIsAllowed() {
        return accessIsAllowed;
    }

    public void setAccessIsAllowed(String accessIsAllowed) {
        this.accessIsAllowed = accessIsAllowed;
    }

    public String getAccessIsPartial() {
        return accessIsPartial;
    }

    public void setAccessIsPartial(String accessIsPartial) {
        this.accessIsPartial = accessIsPartial;
    }
}