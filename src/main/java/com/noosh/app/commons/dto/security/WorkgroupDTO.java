package com.noosh.app.commons.dto.security;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/31/16
 */
public class WorkgroupDTO implements Serializable {
    private static final long serialVersionUID = -6366784946452938467L;
    private Long id;
    private String name;
    private Long workGroupTypeId;
    private String portal;
    private Boolean isLocked;
    private Long decimalPlaces;

    public Boolean getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(Boolean locked) {
        isLocked = locked;
    }

    public String getPortal() {
        return portal;
    }

    public void setPortal(String portal) {
        this.portal = portal;
    }

    public Long getWorkGroupTypeId() {
        return workGroupTypeId;
    }

    public void setWorkGroupTypeId(Long workGroupTypeId) {
        this.workGroupTypeId = workGroupTypeId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getDecimalPlaces() {
        return decimalPlaces;
    }

    public void setDecimalPlaces(Long decimalPlaces) {
        this.decimalPlaces = decimalPlaces;
    }
}
