package com.noosh.app.commons.dto.invoice;

import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.spec.SpecDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @auther mario
 * @date 7/27/2020
 */
public class InvoiceItemDTO {

    private Long invoiceItemId;
    private Long invoiceId;
    private Long specId;
    private Long jobId;
    private BigDecimal quantity;
    private BigDecimal amount;
    private Long amountCurrencyId;
    private boolean includeBreakouts;
    private Long breakoutTypeId;
    private int itemIndex;
    private Long customPropertyId;
    private BigDecimal tax;
    private Long taxCurrencyId;
    private BigDecimal shipping;
    private Long shippingCurrencyId;
    private BigDecimal discountOrSurcharge;
    private Long discountOrSurchargeCurrencyId;
    private BigDecimal subTotal;

    //dual currency
    private BigDecimal exAmount;
    private Long exAmountCurrencyId;
    private BigDecimal exTax;
    private Long exTaxCurrencyId;
    private BigDecimal exShipping;
    private Long exShippingCurrencyId;
    private BigDecimal exDiscountOrSurcharge;
    private Long exDiscountOrSurchargeCurrencyId;
    private BigDecimal exSubTotal;

    private SpecDTO specDTO;
    private List<BreakoutDTO> breakouts;

    public Long getInvoiceItemId() {
        return invoiceItemId;
    }

    public void setInvoiceItemId(Long invoiceItemId) {
        this.invoiceItemId = invoiceItemId;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getAmountCurrencyId() {
        return amountCurrencyId;
    }

    public void setAmountCurrencyId(Long amountCurrencyId) {
        this.amountCurrencyId = amountCurrencyId;
    }

    public boolean getIncludeBreakouts() {
        return includeBreakouts;
    }

    public void setIncludeBreakouts(boolean includeBreakouts) {
        this.includeBreakouts = includeBreakouts;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public int getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(int itemIndex) {
        this.itemIndex = itemIndex;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public BigDecimal getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(BigDecimal discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public BigDecimal getSubTotal() {
        BigDecimal subTotal = this.getAmount();
        subTotal = subTotal.add(this.getTax() != null ? this.getTax() : BigDecimal.ZERO);
        subTotal = subTotal.add(this.getShipping() != null ? this.getShipping() : BigDecimal.ZERO);
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal) {
        this.subTotal = subTotal;
    }

    public BigDecimal getExAmount() {
        return exAmount;
    }

    public void setExAmount(BigDecimal exAmount) {
        this.exAmount = exAmount;
    }

    public Long getExAmountCurrencyId() {
        return exAmountCurrencyId;
    }

    public void setExAmountCurrencyId(Long exAmountCurrencyId) {
        this.exAmountCurrencyId = exAmountCurrencyId;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public BigDecimal getExSubTotal() {
        BigDecimal exSubTotal = this.getExAmount() != null ? this.getExAmount() : BigDecimal.ZERO;
        exSubTotal = exSubTotal.add(this.getExTax() != null ? this.getExTax() : BigDecimal.ZERO);
        exSubTotal = exSubTotal.add(this.getExShipping() != null ? this.getExShipping() : BigDecimal.ZERO);
        return exSubTotal;
    }

    public void setExSubTotal(BigDecimal exSubTotal) {
        this.exSubTotal = exSubTotal;
    }

    public SpecDTO getSpecDTO() {
        return specDTO;
    }

    public void setSpecDTO(SpecDTO specDTO) {
        this.specDTO = specDTO;
    }

    public List<BreakoutDTO> getBreakouts() {
        return breakouts;
    }

    public void setBreakouts(List<BreakoutDTO> breakouts) {
        this.breakouts = breakouts;
    }

}
