package com.noosh.app.commons.dto.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 4/9/24
 */
public class OrderPendingMyDeskDTO implements Serializable {
    private static final long serialVersionUID = -829762974955136302L;

    private Long orderId;

    private String orderName;

    private String projectName;

    private Long projectId;

    private Long orderStateId;

    private Long orderTypeId;

    private Long parentOrderId;

    private Long buyerWorkgroupId;

    private Long supplierWorkgroupId;

    private Long changeOrderCount;

    private String orderStatusStrId;

    public String getOrderStatusStrId() {
        return orderStatusStrId;
    }

    public void setOrderStatusStrId(String orderStatusStrId) {
        this.orderStatusStrId = orderStatusStrId;
    }

    public Long getChangeOrderCount() {
        return changeOrderCount;
    }

    public void setChangeOrderCount(Long changeOrderCount) {
        this.changeOrderCount = changeOrderCount;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getOrderStateId() {
        return orderStateId;
    }

    public void setOrderStateId(Long orderStateId) {
        this.orderStateId = orderStateId;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public Long getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(Long parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }
}
