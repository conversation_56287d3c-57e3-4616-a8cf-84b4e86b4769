package com.noosh.app.commons.dto.rating;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 7/8/19
 */
public class SupplierRatingOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String ratingComment;

    private Long rateId;

    private List<SupplierRatingItemDTO> ratingItem;

    public String getRatingComment() {
        return ratingComment;
    }

    public void setRatingComment(String ratingComment) {
        this.ratingComment = ratingComment;
    }

    public Long getRateId() {
        return rateId;
    }

    public void setRateId(Long rateId) {
        this.rateId = rateId;
    }

    public List<SupplierRatingItemDTO> getRatingItem() {
        return ratingItem;
    }

    public void setRatingItem(List<SupplierRatingItemDTO> ratingItem) {
        this.ratingItem = ratingItem;
    }
}
