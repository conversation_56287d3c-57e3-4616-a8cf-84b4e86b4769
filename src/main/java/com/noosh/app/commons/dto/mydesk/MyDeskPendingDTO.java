package com.noosh.app.commons.dto.mydesk;

import com.noosh.app.commons.dto.invoice.InvoicePendingMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderPendingMyDeskDTO;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 4/9/24
 */
public class MyDeskPendingDTO implements Serializable {
    private static final long serialVersionUID = 8872251099661396698L;

    private boolean canViewOrders;

    private boolean canViewInvoices;

    private List<OrderPendingMyDeskDTO> orders;

    private List<OrderPendingMyDeskDTO> changeOrders;

    private List<InvoicePendingMyDeskDTO> invoices;

    public List<InvoicePendingMyDeskDTO> getInvoices() {
        return invoices;
    }

    public void setInvoices(List<InvoicePendingMyDeskDTO> invoices) {
        this.invoices = invoices;
    }

    public boolean getCanViewOrders() {
        return canViewOrders;
    }

    public void setCanViewOrders(boolean canViewOrders) {
        this.canViewOrders = canViewOrders;
    }

    public boolean getCanViewInvoices() {
        return canViewInvoices;
    }

    public void setCanViewInvoices(boolean canViewInvoices) {
        this.canViewInvoices = canViewInvoices;
    }

    public List<OrderPendingMyDeskDTO> getOrders() {
        return orders;
    }

    public void setOrders(List<OrderPendingMyDeskDTO> orders) {
        this.orders = orders;
    }

    public List<OrderPendingMyDeskDTO> getChangeOrders() {
        return changeOrders;
    }

    public void setChangeOrders(List<OrderPendingMyDeskDTO> changeOrders) {
        this.changeOrders = changeOrders;
    }
}
