package com.noosh.app.commons.dto.invoice;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.StringID;
import com.noosh.app.commons.dto.order.OrderDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.entity.order.OrderVersion;
import java.time.LocalDateTime;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class InvoiceDTO implements Serializable {

    public static final Object[][] INVOICE_NON_BILLABLE_REASONS =
    {
            {"1",  (Long)StringID.INVOICE_NON_BILLABLE_REASON_PREPAYMENT},
            {"2",  (Long)StringID.INVOICE_NON_BILLABLE_REASON_CHANGE_ORDERS},
            {"99", (Long)StringID.INVOICE_NON_BILLABLE_REASON_OTHER}
    };

    private static final long serialVersionUID = 3927673618303684719L;
    
    private Long id;

    private Long orderId;

    private String reference;

    private String ownerReference;

    private Long buyerUserId;

    private Long buyerWorkgroupId;

    private Long supplierUserId;

    private Long supplierWorkgroupId;

    private LocalDateTime invoiceDate;

    private LocalDateTime dueDate;

    private LocalDateTime submitDate;

    private LocalDateTime acceptedDate;

    private LocalDateTime modDate;

    private String comments;

    private BigDecimal tax;

    private Long taxCurrencyId;

    private BigDecimal shipping;

    private Long shippingCurrencyId;

    private BigDecimal miscCost;

    private Long miscCostCurrencyId;

    private Long stateId;

    private String stateChangeComment;

    private Long customPropertyId;

    private Long prepareUserId;

    private Long invoiceCMContactId;

    private Boolean isNonBillable;

    private Long nonBillableReasonId;

    private Boolean isApproved;

    private Boolean isFinal;

    private BigDecimal discountOrSurcharge;

    private Long discountOrSurchargeCurrencyId;

    private Boolean isTemplate;

    private Boolean sendOnClosedOrder;

    private BigDecimal invoiceItemTotal;

    private Long invoiceItemTotalCurrencyId;

    private Long termsId;

    //dual currency
    private boolean isDualCurrency;

    private Long exCurrencyId;

    private BigDecimal rate;

    private BigDecimal exTax;

    private Long exTaxCurrencyId;

    private BigDecimal exShipping;

    private Long exShippingCurrencyId;

    private BigDecimal exMiscCost;

    private Long exMiscCostCurrencyId;

    private BigDecimal exDiscountOrSurcharge;

    private Long exDiscountOrSurchargeCurrencyId;

    private BigDecimal exInvoiceItemTotal;

    private Long exInvoiceItemTotalCurrencyId;

    private Set<PropertyAttributeDTO> customAttr;

    private ProjectDTO parent;

    private OrderDTO order;

    private OrderVersionDTO orderVersion;

    private List<InvoiceItemDTO> invoiceItemDTOList;

    public Set<PropertyAttributeDTO> getCustomAttr() {
        return customAttr;
    }

    public void setCustomAttr(Set<PropertyAttributeDTO> customAttr) {
        this.customAttr = customAttr;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public Long getBuyerUserId() {
        return buyerUserId;
    }

    public void setBuyerUserId(Long buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierUserId() {
        return supplierUserId;
    }

    public void setSupplierUserId(Long supplierUserId) {
        this.supplierUserId = supplierUserId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public LocalDateTime getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(LocalDateTime invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public LocalDateTime getAcceptedDate() {
        return acceptedDate;
    }

    public void setAcceptedDate(LocalDateTime acceptedDate) {
        this.acceptedDate = acceptedDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public BigDecimal getMiscCost() {
        return miscCost;
    }

    public void setMiscCost(BigDecimal miscCost) {
        this.miscCost = miscCost;
    }

    public Long getMiscCostCurrencyId() {
        return miscCostCurrencyId;
    }

    public void setMiscCostCurrencyId(Long miscCostCurrencyId) {
        this.miscCostCurrencyId = miscCostCurrencyId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public String getStateChangeComment() {
        return stateChangeComment;
    }

    public void setStateChangeComment(String stateChangeComment) {
        this.stateChangeComment = stateChangeComment;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getPrepareUserId() {
        return prepareUserId;
    }

    public void setPrepareUserId(Long prepareUserId) {
        this.prepareUserId = prepareUserId;
    }

    public Long getInvoiceCMContactId() {
        return invoiceCMContactId;
    }

    public void setInvoiceCMContactId(Long invoiceCMContactId) {
        this.invoiceCMContactId = invoiceCMContactId;
    }

    public Boolean getNonBillable() {
        return isNonBillable;
    }

    public void setNonBillable(Boolean nonBillable) {
        isNonBillable = nonBillable;
    }

    public Long getNonBillableReasonId() {
        return nonBillableReasonId;
    }

    public void setNonBillableReasonId(Long nonBillableReasonId) {
        this.nonBillableReasonId = nonBillableReasonId;
    }

    public Boolean getApproved() {
        return isApproved;
    }

    public void setApproved(Boolean approved) {
        isApproved = approved;
    }

    public Boolean getFinal() {
        return isFinal != null ? isFinal : false;
    }

    public void setFinal(Boolean aFinal) {
        isFinal = aFinal;
    }

    public BigDecimal getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(BigDecimal discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public Boolean getTemplate() {
        return isTemplate;
    }

    public void setTemplate(Boolean template) {
        isTemplate = template;
    }

    public Boolean getSendOnClosedOrder() {
        return sendOnClosedOrder;
    }

    public void setSendOnClosedOrder(Boolean sendOnClosedOrder) {
        this.sendOnClosedOrder = sendOnClosedOrder;
    }

    public BigDecimal getInvoiceItemTotal() {
        return invoiceItemTotal;
    }

    public void setInvoiceItemTotal(BigDecimal invoiceItemTotal) {
        this.invoiceItemTotal = invoiceItemTotal;
    }

    public Long getInvoiceItemTotalCurrencyId() {
        return invoiceItemTotalCurrencyId;
    }

    public void setInvoiceItemTotalCurrencyId(Long invoiceItemTotalCurrencyId) {
        this.invoiceItemTotalCurrencyId = invoiceItemTotalCurrencyId;
    }

    public Long getTermsId() {
        return termsId;
    }

    public void setTermsId(Long termsId) {
        this.termsId = termsId;
    }

    public boolean isDualCurrency() {
        return isDualCurrency;
    }

    public void setDualCurrency(boolean dualCurrency) {
        isDualCurrency = dualCurrency;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExMiscCost() {
        return exMiscCost;
    }

    public void setExMiscCost(BigDecimal exMiscCost) {
        this.exMiscCost = exMiscCost;
    }

    public Long getExMiscCostCurrencyId() {
        return exMiscCostCurrencyId;
    }

    public void setExMiscCostCurrencyId(Long exMiscCostCurrencyId) {
        this.exMiscCostCurrencyId = exMiscCostCurrencyId;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public BigDecimal getExInvoiceItemTotal() {
        return exInvoiceItemTotal;
    }

    public void setExInvoiceItemTotal(BigDecimal exInvoiceItemTotal) {
        this.exInvoiceItemTotal = exInvoiceItemTotal;
    }

    public Long getExInvoiceItemTotalCurrencyId() {
        return exInvoiceItemTotalCurrencyId;
    }

    public void setExInvoiceItemTotalCurrencyId(Long exInvoiceItemTotalCurrencyId) {
        this.exInvoiceItemTotalCurrencyId = exInvoiceItemTotalCurrencyId;
    }

    public BigDecimal getGrandTotal() {
        BigDecimal total = this.getTax().add(this.getShipping());
        total = total.add(this.getMiscCost() !=null ? this.getMiscCost() : BigDecimal.ZERO);
        total = total.add(this.getInvoiceItemTotal() !=null? this.getInvoiceItemTotal() : BigDecimal.ZERO);
        total = total.add(this.getDiscountOrSurcharge() !=null ? this.getDiscountOrSurcharge() : BigDecimal.ZERO);
        return total;
    }

    public Long getGrandTotalCurrencyId() {
        return this.getInvoiceItemTotalCurrencyId();
    }

    public BigDecimal getExGrandTotal() {
        BigDecimal exTotal = BigDecimal.ZERO;
        exTotal = exTotal.add(this.getExTax() != null? this.getExTax() : BigDecimal.ZERO);
        exTotal = exTotal.add(this.getExShipping() != null? this.getExShipping() : BigDecimal.ZERO);
        exTotal = exTotal.add(this.getExMiscCost() !=null ? this.getExMiscCost() : BigDecimal.ZERO);
        exTotal = exTotal.add(this.getExInvoiceItemTotal() !=null? this.getExInvoiceItemTotal() : BigDecimal.ZERO);
        exTotal = exTotal.add(this.getExDiscountOrSurcharge() !=null ? this.getExDiscountOrSurcharge() : BigDecimal.ZERO);
        return exTotal;
    }

    public Long getExGrandTotalCurrencyId() {
        return this.getExInvoiceItemTotalCurrencyId();
    }

    public ProjectDTO getParent() {
        return parent;
    }

    public void setParent(ProjectDTO parent) {
        this.parent = parent;
    }

    public OrderDTO getOrder() {
        return order;
    }

    public void setOrder(OrderDTO order) {
        this.order = order;
    }

    public OrderVersionDTO getOrderVersion() {
        return orderVersion;
    }

    public void setOrderVersion(OrderVersionDTO orderVersion) {
        this.orderVersion = orderVersion;
    }

    public List<InvoiceItemDTO> getInvoiceItemDTOList() {
        return invoiceItemDTOList;
    }

    public void setInvoiceItemDTOList(List<InvoiceItemDTO> invoiceItemDTOList) {
        this.invoiceItemDTOList = invoiceItemDTOList;
    }

    public boolean isPending() {
        return (this.getStateId() == ObjectStateID.INVOICE_PENDING_ACCEPTANCE) ? true : false;
    }

    public boolean isDraft() {
        return (this.getStateId() == ObjectStateID.INVOICE_DRAFT) ? true : false;
    }

    public boolean isAccepted() {
        return (this.getStateId() == ObjectStateID.INVOICE_ACCEPTED || this.getStateId() == ObjectStateID.INVOICE_PAID) ? true : false;
    }

    public boolean isRejected() {
        return (this.getStateId() == ObjectStateID.INVOICE_REJECTED) ? true : false;
    }

    public boolean isRetracted() {
        return (this.getStateId() == ObjectStateID.INVOICE_RETRACTED) ? true : false;
    }

    /**
     * @return true if this order is viewed within the context of a supplier project
     */
    public boolean isUserBuyer() {
        return (getBuyerWorkgroupId() == getParent().getOwnerWorkgroupId().longValue());
    }

    /**
     * @return true if this order is viewed within the context of a supplier project
     */
    public boolean isUserSupplier() {
        return (getSupplierWorkgroupId() == getParent().getOwnerWorkgroupId().longValue());
    }

}
