package com.noosh.app.commons.dto.terms;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 10/12/17
 */
public class AcTermsDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String text;

    private Long workgroupId;

    private Long termsTypeId;

    private Long versionNumber;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Long getTermsTypeId() {
        return termsTypeId;
    }

    public void setTermsTypeId(Long termsTypeId) {
        this.termsTypeId = termsTypeId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }
}
