package com.noosh.app.commons.dto.reason;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/4/20
 */
public class WorkgroupReasonDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long reasonId;

    private Long workgroupId;

    private Boolean isActive;

    private PcReasonDTO pcReason;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean active) {
        isActive = active;
    }

    public PcReasonDTO getPcReason() {
        return pcReason;
    }

    public void setPcReason(PcReasonDTO pcReason) {
        this.pcReason = pcReason;
    }
}
