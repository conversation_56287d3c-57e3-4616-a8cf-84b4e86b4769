package com.noosh.app.commons.dto.order;

import com.noosh.app.commons.dto.terms.AcTermsDTO;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 10/12/17
 */
public class OrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 9143439565247065855L;

    private OrderVersionDTO orderVersionDTO;
    private AcTermsDTO buyerTerms;
    private AcTermsDTO supplierTerms;

    private String editDraftButton;
    private String deleteDraftButton;
    private String editButton;
    private String acceptButton;
    private String routeForApprovalButton;
    private String routeForManagerApprovalButton;
    private String submitButton;
    private String rejectButton;
    private String reorderButton;
    private String cancelButton;
    private String retractButton;
    private String createChangeOrderButton;
    private String dismissButton;
    private String updateButton;
    private String editSupplierRefButton;
    private String editInfoButton;
    private String costCenterAllocationButton;
    private String editShipmentButton;
    private String supplierRatingButton;
    private String sourcingStrategiesButton;
    private String completeButton;
    private String routingApproveButton;
    private String routingDisapproveButton;
    private String showApprovalLink;
	private String V1000IReportLink;
    private String printVendorReportButton;

    private boolean canAccept;

    public String getV1000IReportLink() {
        return V1000IReportLink;
    }

    public void setV1000IReportLink(String v1000IReportLink) {
        V1000IReportLink = v1000IReportLink;
    }

    public String getShowApprovalLink() {
        return showApprovalLink;
    }

    public void setShowApprovalLink(String showApprovalLink) {
        this.showApprovalLink = showApprovalLink;
    }

    public boolean getCanAccept() {
        return canAccept;
    }

    public void setCanAccept(boolean canAccept) {
        this.canAccept = canAccept;
    }

    public String getRoutingApproveButton() {
        return routingApproveButton;
    }

    public void setRoutingApproveButton(String routingApproveButton) {
        this.routingApproveButton = routingApproveButton;
    }

    public String getRoutingDisapproveButton() {
        return routingDisapproveButton;
    }

    public void setRoutingDisapproveButton(String routingDisapproveButton) {
        this.routingDisapproveButton = routingDisapproveButton;
    }

    public String getCompleteButton() {
        return completeButton;
    }

    public void setCompleteButton(String completeButton) {
        this.completeButton = completeButton;
    }

    public String getSourcingStrategiesButton() {
        return sourcingStrategiesButton;
    }

    public void setSourcingStrategiesButton(String sourcingStrategiesButton) {
        this.sourcingStrategiesButton = sourcingStrategiesButton;
    }

    public String getEditShipmentButton() {
        return editShipmentButton;
    }

    public void setEditShipmentButton(String editShipmentButton) {
        this.editShipmentButton = editShipmentButton;
    }

    public String getSupplierRatingButton() {
        return supplierRatingButton;
    }

    public void setSupplierRatingButton(String supplierRatingButton) {
        this.supplierRatingButton = supplierRatingButton;
    }

    public String getCostCenterAllocationButton() {
        return costCenterAllocationButton;
    }

    public void setCostCenterAllocationButton(String costCenterAllocationButton) {
        this.costCenterAllocationButton = costCenterAllocationButton;
    }

    public String getEditInfoButton() {
        return editInfoButton;
    }

    public void setEditInfoButton(String editInfoButton) {
        this.editInfoButton = editInfoButton;
    }

    public String getEditSupplierRefButton() {
        return editSupplierRefButton;
    }

    public void setEditSupplierRefButton(String editSupplierRefButton) {
        this.editSupplierRefButton = editSupplierRefButton;
    }

    public String getUpdateButton() {
        return updateButton;
    }

    public void setUpdateButton(String updateButton) {
        this.updateButton = updateButton;
    }

    public String getDismissButton() {
        return dismissButton;
    }

    public void setDismissButton(String dismissButton) {
        this.dismissButton = dismissButton;
    }

    public String getCreateChangeOrderButton() {
        return createChangeOrderButton;
    }

    public void setCreateChangeOrderButton(String createChangeOrderButton) {
        this.createChangeOrderButton = createChangeOrderButton;
    }

    public String getCancelButton() {
        return cancelButton;
    }

    public void setCancelButton(String cancelButton) {
        this.cancelButton = cancelButton;
    }

    public String getRetractButton() {
        return retractButton;
    }

    public void setRetractButton(String retractButton) {
        this.retractButton = retractButton;
    }

    public String getReorderButton() {
        return reorderButton;
    }

    public void setReorderButton(String reorderButton) {
        this.reorderButton = reorderButton;
    }

    public String getRejectButton() {
        return rejectButton;
    }

    public void setRejectButton(String rejectButton) {
        this.rejectButton = rejectButton;
    }

    public String getSubmitButton() {
        return submitButton;
    }

    public void setSubmitButton(String submitButton) {
        this.submitButton = submitButton;
    }

    public String getRouteForApprovalButton() {
        return routeForApprovalButton;
    }

    public void setRouteForApprovalButton(String routeForApprovalButton) {
        this.routeForApprovalButton = routeForApprovalButton;
    }

    public String getRouteForManagerApprovalButton() {
        return routeForManagerApprovalButton;
    }

    public void setRouteForManagerApprovalButton(String routeForManagerApprovalButton) {
        this.routeForManagerApprovalButton = routeForManagerApprovalButton;
    }

    public String getAcceptButton() {
        return acceptButton;
    }

    public void setAcceptButton(String acceptButton) {
        this.acceptButton = acceptButton;
    }

    public String getEditButton() {
        return editButton;
    }

    public void setEditButton(String editButton) {
        this.editButton = editButton;
    }

    public String getDeleteDraftButton() {
        return deleteDraftButton;
    }

    public void setDeleteDraftButton(String deleteDraftButton) {
        this.deleteDraftButton = deleteDraftButton;
    }

    public String getEditDraftButton() {
        return editDraftButton;
    }

    public void setEditDraftButton(String editDraftButton) {
        this.editDraftButton = editDraftButton;
    }

    public OrderVersionDTO getOrderVersionDTO() {
        return orderVersionDTO;
    }

    public void setOrderVersionDTO(OrderVersionDTO orderVersionDTO) {
        this.orderVersionDTO = orderVersionDTO;
    }

    public AcTermsDTO getBuyerTerms() {
        return buyerTerms;
    }

    public void setBuyerTerms(AcTermsDTO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public AcTermsDTO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(AcTermsDTO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public String getPrintVendorReportButton() {
        return printVendorReportButton;
    }

    public void setPrintVendorReportButton(String printVendorReportButton) {
        this.printVendorReportButton = printVendorReportButton;
    }
}
