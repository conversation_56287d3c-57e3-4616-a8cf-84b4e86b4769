package com.noosh.app.commons.dto.order;

import com.noosh.app.commons.dto.terms.AcTermsDTO;
import java.time.LocalDateTime;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/27/17
 */
public class ChangeOrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 5322118107410614992L;

    private ChangeOrderVersionDTO orderVersionDTO;
    private AcTermsDTO buyerTerms;
    private AcTermsDTO supplierTerms;

    private String routeForApprovalButton;
    private String routeForManagerApprovalButton;
    private String submitButton;
    private String acceptButton;
    private String rejectButton;
    private String retractButton;
    private String editButton;
    private String cancelButton;
    private String carryOverChangeOrderButton;
    private String acceptAndCarryOverButton;
    private String routingApproveButton;
    private String routingDisapproveButton;

    private boolean canAccept;
    private boolean isDisableCarryOverChangeOrder;
    private boolean isDisableAcceptAndCarryOver;

    private String reorderButton;
    private String createChangeOrderButton;
    private String editSupplierRefButton;
    private String costCenterAllocationButton;
    private String editShipmentButton;
    private String supplierRatingButton;
    private String completeButton;
    private String dismissButton;
    private String sourcingStrategiesButton;
    private String editDraftButton;
    private String deleteDraftButton;
    private String preAcceptButton;
    private String showApprovalLink;
    private LocalDateTime orderSendToSupplierDate;

    public String getEditDraftButton() {
        return editDraftButton;
    }

    public void setEditDraftButton(String editDraftButton) {
        this.editDraftButton = editDraftButton;
    }

    public String getDeleteDraftButton() {
        return deleteDraftButton;
    }

    public void setDeleteDraftButton(String deleteDraftButton) {
        this.deleteDraftButton = deleteDraftButton;
    }

    public boolean getIsDisableCarryOverChangeOrder() {
        return isDisableCarryOverChangeOrder;
    }

    public void setIsDisableCarryOverChangeOrder(boolean disableCarryOverChangeOrder) {
        isDisableCarryOverChangeOrder = disableCarryOverChangeOrder;
    }

    public boolean getIsDisableAcceptAndCarryOver() {
        return isDisableAcceptAndCarryOver;
    }

    public void setIsDisableAcceptAndCarryOver(boolean disableAcceptAndCarryOver) {
        isDisableAcceptAndCarryOver = disableAcceptAndCarryOver;
    }

    public String getShowApprovalLink() {
        return showApprovalLink;
    }

    public void setShowApprovalLink(String showApprovalLink) {
        this.showApprovalLink = showApprovalLink;
    }

    public String getRoutingApproveButton() {
        return routingApproveButton;
    }

    public void setRoutingApproveButton(String routingApproveButton) {
        this.routingApproveButton = routingApproveButton;
    }

    public String getRoutingDisapproveButton() {
        return routingDisapproveButton;
    }

    public void setRoutingDisapproveButton(String routingDisapproveButton) {
        this.routingDisapproveButton = routingDisapproveButton;
    }

    public String getSourcingStrategiesButton() {
        return sourcingStrategiesButton;
    }

    public void setSourcingStrategiesButton(String sourcingStrategiesButton) {
        this.sourcingStrategiesButton = sourcingStrategiesButton;
    }

    public String getReorderButton() {
        return reorderButton;
    }

    public void setReorderButton(String reorderButton) {
        this.reorderButton = reorderButton;
    }

    public String getCreateChangeOrderButton() {
        return createChangeOrderButton;
    }

    public void setCreateChangeOrderButton(String createChangeOrderButton) {
        this.createChangeOrderButton = createChangeOrderButton;
    }

    public String getEditSupplierRefButton() {
        return editSupplierRefButton;
    }

    public void setEditSupplierRefButton(String editSupplierRefButton) {
        this.editSupplierRefButton = editSupplierRefButton;
    }

    public String getCostCenterAllocationButton() {
        return costCenterAllocationButton;
    }

    public void setCostCenterAllocationButton(String costCenterAllocationButton) {
        this.costCenterAllocationButton = costCenterAllocationButton;
    }

    public String getEditShipmentButton() {
        return editShipmentButton;
    }

    public void setEditShipmentButton(String editShipmentButton) {
        this.editShipmentButton = editShipmentButton;
    }

    public String getSupplierRatingButton() {
        return supplierRatingButton;
    }

    public void setSupplierRatingButton(String supplierRatingButton) {
        this.supplierRatingButton = supplierRatingButton;
    }

    public String getCompleteButton() {
        return completeButton;
    }

    public void setCompleteButton(String completeButton) {
        this.completeButton = completeButton;
    }

    public String getDismissButton() {
        return dismissButton;
    }

    public void setDismissButton(String dismissButton) {
        this.dismissButton = dismissButton;
    }

    public String getCarryOverChangeOrderButton() {
        return carryOverChangeOrderButton;
    }

    public void setCarryOverChangeOrderButton(String carryOverChangeOrderButton) {
        this.carryOverChangeOrderButton = carryOverChangeOrderButton;
    }

    public String getAcceptAndCarryOverButton() {
        return acceptAndCarryOverButton;
    }

    public void setAcceptAndCarryOverButton(String acceptAndCarryOverButton) {
        this.acceptAndCarryOverButton = acceptAndCarryOverButton;
    }

    public boolean isCanAccept() {
        return canAccept;
    }

    public void setCanAccept(boolean canAccept) {
        this.canAccept = canAccept;
    }

    public String getCancelButton() {
        return cancelButton;
    }

    public void setCancelButton(String cancelButton) {
        this.cancelButton = cancelButton;
    }

    public String getEditButton() {
        return editButton;
    }

    public void setEditButton(String editButton) {
        this.editButton = editButton;
    }

    public String getRetractButton() {
        return retractButton;
    }

    public void setRetractButton(String retractButton) {
        this.retractButton = retractButton;
    }

    public String getRejectButton() {
        return rejectButton;
    }

    public void setRejectButton(String rejectButton) {
        this.rejectButton = rejectButton;
    }

    public String getAcceptButton() {
        return acceptButton;
    }

    public void setAcceptButton(String acceptButton) {
        this.acceptButton = acceptButton;
    }

    public ChangeOrderVersionDTO getOrderVersionDTO() {
        return orderVersionDTO;
    }

    public void setOrderVersionDTO(ChangeOrderVersionDTO orderVersionDTO) {
        this.orderVersionDTO = orderVersionDTO;
    }

    public AcTermsDTO getBuyerTerms() {
        return buyerTerms;
    }

    public void setBuyerTerms(AcTermsDTO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public String getSubmitButton() {
        return submitButton;
    }

    public void setSubmitButton(String submitButton) {
        this.submitButton = submitButton;
    }

    public String getRouteForManagerApprovalButton() {
        return routeForManagerApprovalButton;
    }

    public void setRouteForManagerApprovalButton(String routeForManagerApprovalButton) {
        this.routeForManagerApprovalButton = routeForManagerApprovalButton;
    }

    public AcTermsDTO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(AcTermsDTO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public String getRouteForApprovalButton() {
        return routeForApprovalButton;
    }

    public void setRouteForApprovalButton(String routeForApprovalButton) {
        this.routeForApprovalButton = routeForApprovalButton;
    }

    public LocalDateTime getOrderSendToSupplierDate() {
        return orderSendToSupplierDate;
    }

    public void setOrderSendToSupplierDate(LocalDateTime orderSendToSupplierDate) {
        this.orderSendToSupplierDate = orderSendToSupplierDate;
    }

    public String getPreAcceptButton() {
        return preAcceptButton;
    }

    public void setPreAcceptButton(String preAcceptButton) {
        this.preAcceptButton = preAcceptButton;
    }
}
