package com.noosh.app.commons.dto.property;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 12/1/21
 */
public class PropertyAttributeUpdateDTO implements Serializable {
    private static final long serialVersionUID = -784085617583727347L;

    private Long prPropertyAttributeId;

    private String typeValue;

    private Long paramId;

    private String paramName;

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public Long getPrPropertyAttributeId() {
        return prPropertyAttributeId;
    }

    public void setPrPropertyAttributeId(Long prPropertyAttributeId) {
        this.prPropertyAttributeId = prPropertyAttributeId;
    }

    public String getTypeValue() {
        return typeValue;
    }

    public void setTypeValue(String typeValue) {
        this.typeValue = typeValue;
    }

    public Long getParamId() {
        return paramId;
    }

    public void setParamId(Long paramId) {
        this.paramId = paramId;
    }
}
