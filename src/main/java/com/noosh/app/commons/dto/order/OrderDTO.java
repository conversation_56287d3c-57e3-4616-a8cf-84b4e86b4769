package com.noosh.app.commons.dto.order;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.dto.project.ProjectDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import java.util.List;

/**
 * @auther mario
 * @date 2/25/2020
 */
public class OrderDTO implements Serializable {

    private long orderId;
    private Long orderVersionId;
    private Long versionNumber;
    private String reference;
    private String title;
    private String state;
    private Long stateId;
    private Long stateCreateUserId;
    private Long orderClassificationId;
    private LocalDateTime completionDate;
    private LocalDateTime lastModDate;
    private Long supplierWorkgroupId;
    private String supplierWorkgroupName;
    private Long buyerWorkgroupId;
    private String buyerWorkgroupName;
    private Long buyerUserId;
    private Long supplierUserId;
    private Double price;
    private Long priceCurrencyId;
    private Double changePrice;
    private Long changePriceCurrencyId;
    private Long orderTypeId;
    private String orderType;
    private String paymentReference;
    private Long parentOrderId;
    private Long origOrderId;
    private Long customPropertyId;
    private String parentOrderType;
    private boolean isSellOrder;
    private boolean isClosing;
    private boolean showSupplierWorkgroup;
    private Long parentSupplierWorkgroupId;
    private Long parentBuyerWorkgroupId;
    private Long buClientId;

    private Double tax;
    private Long taxCurrencyId;
    private Double shipping;
    private Long shippingCurrencyId;
    private Double miscCost;
    private Long miscCostCurrencyId;
    private Double discountOrSurcharge;
    private Long discountOrSurchargeCurrencyId;

    //dual currency
    private BigDecimal rate;
    private Long exCurrencyId;
    private Double exTax;
    private Long exTaxCurrencyId;
    private Double exShipping;
    private Long exShippingCurrencyId;
    private Double exMiscCost;
    private Long exMiscCostCurrencyId;
    private Double exDiscountOrSurcharge;
    private Long exDiscountOrSurchargeCurrencyId;

    private Long descriptionStrId;
    private Long invoiceAdjustmentParentOrderId;
    private ProjectDTO parent;
    private OrderVersionDTO orderVersion;

    public long getOrderId() {
        return this.orderId;
    }

    public void setOrderId(long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderVersionId() {
        return orderVersionId;
    }

    public void setOrderVersionId(Long orderVersionId) {
        this.orderVersionId = orderVersionId;
    }

    public Long getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public Long getStateCreateUserId() {
        return stateCreateUserId;
    }

    public void setStateCreateUserId(Long stateCreateUserId) {
        this.stateCreateUserId = stateCreateUserId;
    }

    public Long getOrderClassificationId() {
        return orderClassificationId;
    }

    public void setOrderClassificationId(Long orderClassificationId) {
        this.orderClassificationId = orderClassificationId;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getLastModDate() {
        return lastModDate;
    }

    public void setLastModDate(LocalDateTime lastModDate) {
        this.lastModDate = lastModDate;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public String getBuyerWorkgroupName() {
        return buyerWorkgroupName;
    }

    public void setBuyerWorkgroupName(String buyerWorkgroupName) {
        this.buyerWorkgroupName = buyerWorkgroupName;
    }

    public Long getBuyerUserId() {
        return buyerUserId;
    }

    public void setBuyerUserId(Long buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    public Long getSupplierUserId() {
        return supplierUserId;
    }

    public void setSupplierUserId(Long supplierUserId) {
        this.supplierUserId = supplierUserId;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getChangePrice() {
        return changePrice;
    }

    public void setChangePrice(Double changePrice) {
        this.changePrice = changePrice;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public Long getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(Long parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public Long getOrigOrderId() {
        return this.origOrderId;
    }

    public void setOrigOrderId(Long origOrderId) {
        this.origOrderId = origOrderId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public String getParentOrderType() {
        return parentOrderType;
    }

    public void setParentOrderType(String parentOrderType) {
        this.parentOrderType = parentOrderType;
    }

    public boolean isSellOrder() {
        return isSellOrder;
    }

    public void setSellOrder(boolean sellOrder) {
        isSellOrder = sellOrder;
    }

    public boolean getIsClosing() {
        return isClosing;
    }

    public void setIsClosing(boolean closing) {
        isClosing = closing;
    }

    public boolean isShowSupplierWorkgroup() {
        return showSupplierWorkgroup;
    }

    public void setShowSupplierWorkgroup(boolean showSupplierWorkgroup) {
        this.showSupplierWorkgroup = showSupplierWorkgroup;
    }

    public Long getParentSupplierWorkgroupId() {
        return parentSupplierWorkgroupId;
    }

    public void setParentSupplierWorkgroupId(Long parentSupplierWorkgroupId) {
        this.parentSupplierWorkgroupId = parentSupplierWorkgroupId;
    }

    public Long getParentBuyerWorkgroupId() {
        return parentBuyerWorkgroupId;
    }

    public void setParentBuyerWorkgroupId(Long parentBuyerWorkgroupId) {
        this.parentBuyerWorkgroupId = parentBuyerWorkgroupId;
    }

    public Long getBuClientId() {
        return buClientId;
    }

    public void setBuClientId(Long buClientId) {
        this.buClientId = buClientId;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public Double getShipping() {
        return shipping;
    }

    public void setShipping(Double shipping) {
        this.shipping = shipping;
    }

    public Double getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(Double discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public Long getInvoiceAdjustmentParentOrderId() {
        return invoiceAdjustmentParentOrderId;
    }

    public void setInvoiceAdjustmentParentOrderId(Long invoiceAdjustmentParentOrderId) {
        this.invoiceAdjustmentParentOrderId = invoiceAdjustmentParentOrderId;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public Long getMiscCostCurrencyId() {
        return miscCostCurrencyId;
    }

    public void setMiscCostCurrencyId(Long miscCostCurrencyId) {
        this.miscCostCurrencyId = miscCostCurrencyId;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public Long getExTaxCurrencyId() {
        return this.exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public Long getExShippingCurrencyId() {
        return this.exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return this.exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public BigDecimal getRate() {
        return this.rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return this.exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public Double getExTax() {
        return this.exTax;
    }

    public void setExTax(Double exTax) {
        this.exTax = exTax;
    }

    public Double getExShipping() {
        return this.exShipping;
    }

    public void setExShipping(Double exShipping) {
        this.exShipping = exShipping;
    }

    public Double getExDiscountOrSurcharge() {
        return this.exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(Double exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Double getMiscCost() {
        return this.miscCost;
    }

    public void setMiscCost(Double miscCost) {
        this.miscCost = miscCost;
    }

    public Double getExMiscCost() {
        return this.exMiscCost;
    }

    public void setExMiscCost(Double exMiscCost) {
        this.exMiscCost = exMiscCost;
    }

    public Long getExMiscCostCurrencyId() {
        return this.exMiscCostCurrencyId;
    }

    public void setExMiscCostCurrencyId(Long exMiscCostCurrencyId) {
        this.exMiscCostCurrencyId = exMiscCostCurrencyId;
    }

    public ProjectDTO getParent() {
        return parent;
    }

    public void setParent(ProjectDTO parent) {
        this.parent = parent;
    }

    public OrderVersionDTO getOrderVersion() {
        return orderVersion;
    }

    public void setOrderVersion(OrderVersionDTO orderVersion) {
        this.orderVersion = orderVersion;
    }

    public boolean isPendingSubmission() {
        if (getStateId() == null) return false;
        if (getStateId() == ObjectStateID.ORDER_PENDING_SUBMISSION)
            return true;
        return false;
    }

    public boolean isAccepted() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_ACCEPTED
                || getStateId() == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                || getStateId() == ObjectStateID.ORDER_SHIPPED
                || getStateId() == ObjectStateID.ORDER_DELIVERED
                || getStateId() == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED
                || getStateId() == ObjectStateID.ORDER_COMPLETED
                || getStateId() == ObjectStateID.ORDER_FINALIZED);
    }

    public boolean isDraft() {
        return this.getOrderVersionId() == null || this.getOrderVersionId() <= 0
                || (getStateId() != null && getStateId() == ObjectStateID.ORDER_DRAFT);
    }

    public boolean isCancelled() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_CANCELLED);
    }

    public boolean isRejected() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_REJECTED);
    }

    public boolean isRetracted() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_RETRACTED);
    }

    public boolean isCompleted() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_COMPLETED);
    }

    /**
     * @return true if this order is viewed within the context of a buyer project
     */
    public boolean isUserBuyer(Long ownerWorkgroupId) {
        return this.getBuyerWorkgroupId().longValue() == ownerWorkgroupId;
    }

}
