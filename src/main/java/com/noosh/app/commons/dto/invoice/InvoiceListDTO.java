package com.noosh.app.commons.dto.invoice;

import com.noosh.app.commons.constant.ObjectStateID;
import java.time.LocalDateTime;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public class InvoiceListDTO {
    // order
    private Long orderId;
    private String orderTitle;
    private String orderReference;
    private LocalDateTime orderAcceptDate;
    private Long orderTypeId;
    private Long orderBuyerWorkgroupId;
    private Long orderSupplierWorkgroupId;
    private Long orderObjectStateId;
    private String orderBuyerWorkgroupName;
    private String orderSupplierWorkgroupName;
    private Long orderClassificationId;
    private Long buClientId;
    private Long parentOrderId;
    private Long orderVersionId;

    // invoice
    private Long id;
    private String ownerReference;
    private Long stateId;
    private LocalDateTime acceptedDate;
    private LocalDateTime dueDate;
    private LocalDateTime modDate;
    private Boolean isNonBillable;
    private Long supplierWorkgroupId;
    private Boolean isApproved;
    private Boolean isFinal = false;
    private BigDecimal tax;
    private Long taxCurrencyId;
    private BigDecimal shipping;
    private Long shippingCurrencyId;
    private BigDecimal miscCost;
    private Long miscCostCurrencyId;
    private BigDecimal discountOrSurcharge;
    private Long discountOrSurchargeCurrencyId;

    //dual currency
    private BigDecimal exTax;
    private Long exTaxCurrencyId;
    private BigDecimal exShipping;
    private Long exShippingCurrencyId;
    private BigDecimal exMiscCost;
    private Long exMiscCostCurrencyId;
    private BigDecimal exDiscountOrSurcharge;
    private Long exDiscountOrSurchargeCurrencyId;
    private BigDecimal rate;
    private Long exCurrencyId;

    public Long getOrderVersionId() {
        return orderVersionId;
    }

    public void setOrderVersionId(Long orderVersionId) {
        this.orderVersionId = orderVersionId;
    }

    public Long getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(Long parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public Long getBuClientId() {
        return buClientId;
    }

    public void setBuClientId(Long buClientId) {
        this.buClientId = buClientId;
    }

    public Long getOrderBuyerWorkgroupId() {
        return orderBuyerWorkgroupId;
    }

    public void setOrderBuyerWorkgroupId(Long orderBuyerWorkgroupId) {
        this.orderBuyerWorkgroupId = orderBuyerWorkgroupId;
    }

    public Long getOrderSupplierWorkgroupId() {
        return orderSupplierWorkgroupId;
    }

    public void setOrderSupplierWorkgroupId(Long orderSupplierWorkgroupId) {
        this.orderSupplierWorkgroupId = orderSupplierWorkgroupId;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public String getOrderTitle() {
        return orderTitle;
    }

    public void setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle;
    }

    public Long getOrderObjectStateId() {
        return orderObjectStateId;
    }

    public void setOrderObjectStateId(Long orderObjectStateId) {
        this.orderObjectStateId = orderObjectStateId;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public LocalDateTime getOrderAcceptDate() {
        return orderAcceptDate;
    }

    public void setOrderAcceptDate(LocalDateTime orderAcceptDate) {
        this.orderAcceptDate = orderAcceptDate;
    }

    public String getOrderBuyerWorkgroupName() {
        return orderBuyerWorkgroupName;
    }

    public void setOrderBuyerWorkgroupName(String orderBuyerWorkgroupName) {
        this.orderBuyerWorkgroupName = orderBuyerWorkgroupName;
    }

    public String getOrderSupplierWorkgroupName() {
        return orderSupplierWorkgroupName;
    }

    public void setOrderSupplierWorkgroupName(String orderSupplierWorkgroupName) {
        this.orderSupplierWorkgroupName = orderSupplierWorkgroupName;
    }

    public Long getOrderClassificationId() {
        return orderClassificationId;
    }

    public void setOrderClassificationId(Long orderClassificationId) {
        this.orderClassificationId = orderClassificationId;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public boolean isPending() {
        return (this.getStateId() == ObjectStateID.INVOICE_PENDING_ACCEPTANCE);
    }

    public boolean isDraft() {
        return (this.getStateId() == ObjectStateID.INVOICE_DRAFT);
    }

    public boolean isAccepted() {
        return (this.getStateId() == ObjectStateID.INVOICE_ACCEPTED);
    }

    public boolean isRejected() {
        return (this.getStateId() == ObjectStateID.INVOICE_REJECTED);
    }

    public boolean isRetracted() {
        return (this.getStateId() == ObjectStateID.INVOICE_RETRACTED);
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public LocalDateTime getAcceptedDate() {
        return acceptedDate;
    }

    public void setAcceptedDate(LocalDateTime acceptedDate) {
        this.acceptedDate = acceptedDate;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public BigDecimal getMiscCost() {
        return miscCost;
    }

    public void setMiscCost(BigDecimal miscCost) {
        this.miscCost = miscCost;
    }

    public Long getMiscCostCurrencyId() {
        return miscCostCurrencyId;
    }

    public void setMiscCostCurrencyId(Long miscCostCurrencyId) {
        this.miscCostCurrencyId = miscCostCurrencyId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public Boolean getIsNonBillable() {
        return isNonBillable;
    }

    public void setIsNonBillable(Boolean nonBillable) {
        isNonBillable = nonBillable;
    }

    public Boolean getIsApproved() {
        return isApproved;
    }

    public void setIsApproved(Boolean approved) {
        isApproved = approved;
    }

    public Boolean getIsFinal() {
        return isFinal;
    }

    public void setIsFinal(Boolean aFinal) {
        isFinal = aFinal;
    }

    public BigDecimal getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(BigDecimal discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExMiscCost() {
        return exMiscCost;
    }

    public void setExMiscCost(BigDecimal exMiscCost) {
        this.exMiscCost = exMiscCost;
    }

    public Long getExMiscCostCurrencyId() {
        return exMiscCostCurrencyId;
    }

    public void setExMiscCostCurrencyId(Long exMiscCostCurrencyId) {
        this.exMiscCostCurrencyId = exMiscCostCurrencyId;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public String getOrderReference() {
        return orderReference;
    }

    public void setOrderReference(String orderReference) {
        this.orderReference = orderReference;
    }

    public boolean isOrderCancelled() {
        if (getOrderObjectStateId() == null) return false;
        long orderStateId = getOrderObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_CANCELLED);
    }

    public boolean isOrderompleted() {
        if (getOrderObjectStateId() == null) return false;
        long orderStateId = getOrderObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_COMPLETED);
    }
}
