package com.noosh.app.commons.dto.custom;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 11/29/17
 */
public class CustomFieldControlDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long customFieldTypeId;

    private String constantToken;

    private Long descriptionStrId;

    private CustomFieldTypeDTO customFieldType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCustomFieldTypeId() {
        return customFieldTypeId;
    }

    public void setCustomFieldTypeId(Long customFieldTypeId) {
        this.customFieldTypeId = customFieldTypeId;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public CustomFieldTypeDTO getCustomFieldType() {
        return customFieldType;
    }

    public void setCustomFieldType(CustomFieldTypeDTO customFieldType) {
        this.customFieldType = customFieldType;
    }
}
