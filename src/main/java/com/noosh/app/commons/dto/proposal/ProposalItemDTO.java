package com.noosh.app.commons.dto.proposal;

import com.noosh.app.commons.dto.quote.QuotePriceDTO;
import com.noosh.app.commons.dto.spec.ShipmentWidgetDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 3/10/2022
 */
public class ProposalItemDTO {

    private Long itemIndex;
    private String specName;
    private LocalDateTime completionDate;
    private List<SpecSummaryFieldValueDTO> specSummaryFieldValues;
    private List<QuotePriceDTO> quotePrices;
    private String quoteItemComments;
    private List<ShipmentWidgetDTO> shipTos;

    private String comments;
    private Long proposalItemId;
    private Long quoteItemId;
    private Long quoteItemCustomPropertyId;
    private Long customPropertyId;
    private Long proposalSpecSummaryId;
    private Boolean isShowAllSpecInfo;
    private Long quoteId;
    private Long specId;
    private Long jobId;
    private String versionNumber;
    private String refNumber;
    private String sku;
    private String categoryName;

    private ProposalSustainabilityDTO proposalSustainability;

    public ProposalSustainabilityDTO getProposalSustainability() {
        return proposalSustainability;
    }

    public void setProposalSustainability(ProposalSustainabilityDTO proposalSustainability) {
        this.proposalSustainability = proposalSustainability;
    }

    public List<ShipmentWidgetDTO> getShipTos() {
        return shipTos;
    }

    public void setShipTos(List<ShipmentWidgetDTO> shipTos) {
        this.shipTos = shipTos;
    }

    public List<SpecSummaryFieldValueDTO> getSpecSummaryFieldValues() {
        return specSummaryFieldValues;
    }

    public void setSpecSummaryFieldValues(List<SpecSummaryFieldValueDTO> specSummaryFieldValues) {
        this.specSummaryFieldValues = specSummaryFieldValues;
    }

    public String getQuoteItemComments() {
        return quoteItemComments;
    }

    public void setQuoteItemComments(String quoteItemComments) {
        this.quoteItemComments = quoteItemComments;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getProposalItemId() {
        return proposalItemId;
    }

    public void setProposalItemId(Long proposalItemId) {
        this.proposalItemId = proposalItemId;
    }

    public Long getQuoteItemId() {
        return quoteItemId;
    }

    public void setQuoteItemId(Long quoteItemId) {
        this.quoteItemId = quoteItemId;
    }

    public Long getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Long itemIndex) {
        this.itemIndex = itemIndex;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getProposalSpecSummaryId() {
        return proposalSpecSummaryId;
    }

    public void setProposalSpecSummaryId(Long proposalSpecSummaryId) {
        this.proposalSpecSummaryId = proposalSpecSummaryId;
    }

    public Boolean getShowAllSpecInfo() {
        return isShowAllSpecInfo;
    }

    public void setShowAllSpecInfo(Boolean showAllSpecInfo) {
        isShowAllSpecInfo = showAllSpecInfo;
    }

    public Long getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getRefNumber() {
        return refNumber;
    }

    public void setRefNumber(String refNumber) {
        this.refNumber = refNumber;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public List<QuotePriceDTO> getQuotePrices() {
        return quotePrices;
    }

    public void setQuotePrices(List<QuotePriceDTO> quotePrices) {
        this.quotePrices = quotePrices;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getQuoteItemCustomPropertyId() {
        return quoteItemCustomPropertyId;
    }

    public void setQuoteItemCustomPropertyId(Long quoteItemCustomPropertyId) {
        this.quoteItemCustomPropertyId = quoteItemCustomPropertyId;
    }
}
