package com.noosh.app.commons.dto.rfe;

import com.noosh.app.commons.constant.ObjectStateID;
import java.time.LocalDateTime;

import java.util.List;

/**
 * @Author: neals
 * @Date: 06/08/2016
 */
public class RfeSupplierDTO {

    private String supplierName;
    private Long workgroupId;
    private String workgroupName;

    private Long id;
    private Long rfeId;
    private Long userId;
    private Long groupId;
    private Long stateId;
    private String stateChangeComment;
    private LocalDateTime invitedDate;
    private Boolean isSelfInvited;
    private LocalDateTime lastViewDate;
    private Long lastViewUserId;

    public boolean isActive() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_ESTIMATING) ||
                (this.getStateId() == ObjectStateID.RFE_SUPPLIER_IN_AUCTION) ||
                (this.getStateId() == ObjectStateID.RFE_SUPPLIER_AWARD_AUCTION ) ||
                (this.getStateId() == ObjectStateID.RFE_SUPPLIER_CLOSED ) ||
                (this.getStateId() == ObjectStateID.RFE_SUPPLIER_PARTIALLY_ACCEPTED);
        return bRet;
    }

    public boolean isBidding() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_IN_AUCTION);
        return bRet;
    }

    public boolean isInAwarding() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_AWARD_AUCTION);
        return bRet;
    }

    public boolean isPendingSupplierAcceptance() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_RFE_SENT);
        return bRet;
    }

    public boolean isEstimating() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_ESTIMATING);
        return bRet;
    }

    public boolean isClosed() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_CLOSED);
        return bRet;
    }

    public boolean isDeclined() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_REJECTED);
        return bRet;
    }

    public boolean isPartiallyAccepted() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_PARTIALLY_ACCEPTED);
        return bRet;
    }

    public boolean isDismissed() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_DISMISSED);
        return bRet;
    }

    public boolean isAutoPricing() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_AUTO_PRICING);
        return bRet;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getWorkgroupName() {
        return workgroupName;
    }

    public void setWorkgroupName(String workgroupName) {
        this.workgroupName = workgroupName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRfeId() {
        return rfeId;
    }

    public void setRfeId(Long rfeId) {
        this.rfeId = rfeId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public String getStateChangeComment() {
        return stateChangeComment;
    }

    public void setStateChangeComment(String stateChangeComment) {
        this.stateChangeComment = stateChangeComment;
    }

    public LocalDateTime getInvitedDate() {
        return invitedDate;
    }

    public void setInvitedDate(LocalDateTime invitedDate) {
        this.invitedDate = invitedDate;
    }

    public Boolean getSelfInvited() {
        return isSelfInvited;
    }

    public void setSelfInvited(Boolean selfInvited) {
        isSelfInvited = selfInvited;
    }

    public LocalDateTime getLastViewDate() {
        return lastViewDate;
    }

    public void setLastViewDate(LocalDateTime lastViewDate) {
        this.lastViewDate = lastViewDate;
    }

    public Long getLastViewUserId() {
        return lastViewUserId;
    }

    public void setLastViewUserId(Long lastViewUserId) {
        this.lastViewUserId = lastViewUserId;
    }
}
