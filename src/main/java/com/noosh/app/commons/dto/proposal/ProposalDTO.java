package com.noosh.app.commons.dto.proposal;

import com.noosh.app.commons.dto.address.AddressDTO;
import com.noosh.app.commons.entity.breakout.Breakout;
import com.noosh.app.commons.entity.breakout.BreakoutType;
import com.noosh.app.commons.vo.userfield.UserFieldDefsWithValuesVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 12/22/2021
 */
public class ProposalDTO {
    // project
    private String projectName;
    private String projectNumber;
    private Map<String, Object> projectCustomAttributes;

    // Proposal details:
    private Long proposalId;
    private Long quoteId;
    private Long quoteCustomPropertyId;
    private UserFieldDefsWithValuesVO quoteUserFields;
    private String quoteComments;
    private String title;
    private String reference;
    private String ownerReference;
    private Long objectStateId;
    private String stateChangeComment;
    private LocalDateTime prepareDate;
    private LocalDateTime respondByDate;
    private LocalDateTime expirationDate;
    private LocalDateTime completionDate;

    // logo
    private Long logoId;
    private WorkgroupLogoDTO logo;
    private boolean hasLogo;
    // 1-Top Right, 2-Top Middle, 3-Top Left
    private Long logoPosition;

    // Client information:
    private String clientName;
    private Long clientAddressId;
    private String clientPhone;
    private String clientFax;
    private String clientUrl;
    private AddressDTO clientAddress;

    // Outsourcer information:
    private String outsourcerName;
    private String outsourcerAddressId;
    private String outsourcerPhone;
    private String outsourcerFax;
    private String outsourcerUrl;
    private AddressDTO outsourcerAddress;

    // Proposal Options:
    private boolean includeCoverPage;
    private boolean includeCoverLetter;
    private String saluteText;
    private String introText;
    private String conclusionText;
    private String closingText;
    private boolean includeTermsConditions;
    private Long termsId;
    private String termTexts;
    private boolean includeProposalNote;
    private String proposalNote;
    private boolean includeSignaturePage;

    // Prepared By:
    private Long preparedUserId;
    private String preparedName;
    private String preparedTitle;

    // Spec Summary Registration:
    private boolean useSpecSummary;
    // false-Table format  true-Paragraph format
    private boolean isSpecSummaryCompact;
    private boolean useSpecSummaryBorder;
    private int specSummaryNumColumns;

    // Page Layout:
    private boolean includePageNumber;
    private boolean isLandscape;

    // Pricing Information:
    private String layout;
    private boolean includePriceBreakouts;
    private boolean isMarkupVisible;
    private boolean isSeparateSpecPricing;
    private boolean isAggregationEnable;
    private boolean showAggregation;

    private List<ProposalItemDTO> proposalItems;
    private UserFieldDefsWithValuesVO quoteItemsUserFields;

    private BigDecimal totalPrice;
    private String taxLabelString;
    private BigDecimal tax;
    private BigDecimal shipping;
    private BigDecimal grandTotal;
    private String currencySymbol;

    private List<CategoryDTO> categories;
    private List<BreakoutType> breakoutTypes;

    // 1-Default, 2-Customized, 3-Workgroup Color
    private Long brandingColorsType;
    private String customizedColors;
    private boolean showSupplierInfo;
    private String currentDate;
    private Long propertyId;
    private Long serviceId;
    private Boolean isCreatedAtMarginOn;

    public Boolean getIsCreatedAtMarginOn() {
        return isCreatedAtMarginOn;
    }

    public void setIsCreatedAtMarginOn(Boolean createdAtMarginOn) {
        isCreatedAtMarginOn = createdAtMarginOn;
    }

    public String getQuoteComments() {
        return quoteComments;
    }

    public void setQuoteComments(String quoteComments) {
        this.quoteComments = quoteComments;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectNumber() {
        return projectNumber;
    }

    public void setProjectNumber(String projectNumber) {
        this.projectNumber = projectNumber;
    }

    public Map<String, Object> getProjectCustomAttributes() {
        return projectCustomAttributes;
    }

    public void setProjectCustomAttributes(Map<String, Object> projectCustomAttributes) {
        this.projectCustomAttributes = projectCustomAttributes;
    }

    public Long getProposalId() {
        return proposalId;
    }

    public void setProposalId(Long proposalId) {
        this.proposalId = proposalId;
    }

    public Long getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public LocalDateTime getPrepareDate() {
        return prepareDate;
    }

    public void setPrepareDate(LocalDateTime prepareDate) {
        this.prepareDate = prepareDate;
    }

    public LocalDateTime getRespondByDate() {
        return respondByDate;
    }

    public void setRespondByDate(LocalDateTime respondByDate) {
        this.respondByDate = respondByDate;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public String getSaluteText() {
        return saluteText;
    }

    public void setSaluteText(String saluteText) {
        this.saluteText = saluteText;
    }

    public String getIntroText() {
        return introText;
    }

    public void setIntroText(String introText) {
        this.introText = introText;
    }

    public String getConclusionText() {
        return conclusionText;
    }

    public void setConclusionText(String conclusionText) {
        this.conclusionText = conclusionText;
    }

    public String getClosingText() {
        return closingText;
    }

    public void setClosingText(String closingText) {
        this.closingText = closingText;
    }

    public boolean getIsMarkupVisible() {
        return isMarkupVisible;
    }

    public void setIsMarkupVisible(boolean markupVisible) {
        isMarkupVisible = markupVisible;
    }



    public String getOutsourcerName() {
        return outsourcerName;
    }

    public void setOutsourcerName(String outsourcerName) {
        this.outsourcerName = outsourcerName;
    }

    public String getOutsourcerAddressId() {
        return outsourcerAddressId;
    }

    public void setOutsourcerAddressId(String outsourcerAddressId) {
        this.outsourcerAddressId = outsourcerAddressId;
    }

    public String getOutsourcerPhone() {
        return outsourcerPhone;
    }

    public void setOutsourcerPhone(String outsourcerPhone) {
        this.outsourcerPhone = outsourcerPhone;
    }

    public String getOutsourcerFax() {
        return outsourcerFax;
    }

    public void setOutsourcerFax(String outsourcerFax) {
        this.outsourcerFax = outsourcerFax;
    }

    public String getOutsourcerUrl() {
        return outsourcerUrl;
    }

    public void setOutsourcerUrl(String outsourcerUrl) {
        this.outsourcerUrl = outsourcerUrl;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Long getClientAddressId() {
        return clientAddressId;
    }

    public void setClientAddressId(Long clientAddressId) {
        this.clientAddressId = clientAddressId;
    }

    public String getClientPhone() {
        return clientPhone;
    }

    public void setClientPhone(String clientPhone) {
        this.clientPhone = clientPhone;
    }

    public String getClientFax() {
        return clientFax;
    }

    public void setClientFax(String clientFax) {
        this.clientFax = clientFax;
    }

    public String getClientUrl() {
        return clientUrl;
    }

    public void setClientUrl(String clientUrl) {
        this.clientUrl = clientUrl;
    }

    public Long getTermsId() {
        return termsId;
    }

    public void setTermsId(Long termsId) {
        this.termsId = termsId;
    }

    public String getTermTexts() {
        return termTexts;
    }

    public void setTermTexts(String termTexts) {
        this.termTexts = termTexts;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public String getStateChangeComment() {
        return stateChangeComment;
    }

    public void setStateChangeComment(String stateChangeComment) {
        this.stateChangeComment = stateChangeComment;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getPreparedUserId() {
        return preparedUserId;
    }

    public void setPreparedUserId(Long preparedUserId) {
        this.preparedUserId = preparedUserId;
    }

    public boolean getIsAggregationEnable() {
        return isAggregationEnable;
    }

    public void setAggregationEnable(boolean aggregationEnable) {
        isAggregationEnable = aggregationEnable;
    }

    public boolean isShowAggregation() {
        return showAggregation;
    }

    public void setShowAggregation(boolean showAggregation) {
        this.showAggregation = showAggregation;
    }

    public String getPreparedName() {
        return preparedName;
    }

    public void setPreparedName(String preparedName) {
        this.preparedName = preparedName;
    }

    public String getPreparedTitle() {
        return preparedTitle;
    }

    public void setPreparedTitle(String preparedTitle) {
        this.preparedTitle = preparedTitle;
    }

    public Long getLogoId() {
        return logoId;
    }

    public void setLogoId(Long logoId) {
        this.logoId = logoId;
    }

    public boolean isUseSpecSummary() {
        return useSpecSummary;
    }

    public void setUseSpecSummary(boolean useSpecSummary) {
        this.useSpecSummary = useSpecSummary;
    }

    public boolean isSpecSummaryCompact() {
        return isSpecSummaryCompact;
    }

    public void setSpecSummaryCompact(boolean specSummaryCompact) {
        isSpecSummaryCompact = specSummaryCompact;
    }

    public boolean isIncludeCoverPage() {
        return includeCoverPage;
    }

    public void setIncludeCoverPage(boolean includeCoverPage) {
        this.includeCoverPage = includeCoverPage;
    }

    public boolean isIncludeCoverLetter() {
        return includeCoverLetter;
    }

    public void setIncludeCoverLetter(boolean includeCoverLetter) {
        this.includeCoverLetter = includeCoverLetter;
    }

    public boolean isIncludeTermsConditions() {
        return includeTermsConditions;
    }

    public void setIncludeTermsConditions(boolean includeTermsConditions) {
        this.includeTermsConditions = includeTermsConditions;
    }

    public boolean isIncludeSignaturePage() {
        return includeSignaturePage;
    }

    public void setIncludeSignaturePage(boolean includeSignaturePage) {
        this.includeSignaturePage = includeSignaturePage;
    }

    public boolean isIncludePageNumber() {
        return includePageNumber;
    }

    public void setIncludePageNumber(boolean includePageNumber) {
        this.includePageNumber = includePageNumber;
    }

    public String getLayout() {
        return layout;
    }

    public void setLayout(String layout) {
        this.layout = layout;
    }

    public boolean isIncludePriceBreakouts() {
        return includePriceBreakouts;
    }

    public void setIncludePriceBreakouts(boolean includePriceBreakouts) {
        this.includePriceBreakouts = includePriceBreakouts;
    }

    public boolean getIsLandscape() {
        return isLandscape;
    }

    public void setIsLandscape(boolean landscape) {
        isLandscape = landscape;
    }

    public boolean isUseSpecSummaryBorder() {
        return useSpecSummaryBorder;
    }

    public void setUseSpecSummaryBorder(boolean useSpecSummaryBorder) {
        this.useSpecSummaryBorder = useSpecSummaryBorder;
    }

    public int getSpecSummaryNumColumns() {
        return specSummaryNumColumns;
    }

    public void setSpecSummaryNumColumns(int specSummaryNumColumns) {
        this.specSummaryNumColumns = specSummaryNumColumns;
    }

    public boolean isIncludeProposalNote() {
        return includeProposalNote;
    }

    public void setIncludeProposalNote(boolean includeProposalNote) {
        this.includeProposalNote = includeProposalNote;
    }

    public String getProposalNote() {
        return proposalNote;
    }

    public void setProposalNote(String proposalNote) {
        this.proposalNote = proposalNote;
    }

    public boolean getIsSeparateSpecPricing() {
        return isSeparateSpecPricing;
    }

    public void setIsSeparateSpecPricing(boolean separateSpecPricing) {
        isSeparateSpecPricing = separateSpecPricing;
    }

    public Long getLogoPosition() {
        return logoPosition;
    }

    public void setLogoPosition(Long logoPosition) {
        this.logoPosition = logoPosition;
    }

    public Long getBrandingColorsType() {
        return brandingColorsType;
    }

    public void setBrandingColorsType(Long brandingColorsType) {
        this.brandingColorsType = brandingColorsType;
    }

    public String getCustomizedColors() {
        return customizedColors;
    }

    public void setCustomizedColors(String customizedColors) {
        this.customizedColors = customizedColors;
    }

    public WorkgroupLogoDTO getLogo() {
        return logo;
    }

    public void setLogo(WorkgroupLogoDTO logo) {
        this.logo = logo;
    }

    public AddressDTO getClientAddress() {
        return clientAddress;
    }

    public void setClientAddress(AddressDTO clientAddress) {
        this.clientAddress = clientAddress;
    }

    public AddressDTO getOutsourcerAddress() {
        return outsourcerAddress;
    }

    public void setOutsourcerAddress(AddressDTO outsourcerAddress) {
        this.outsourcerAddress = outsourcerAddress;
    }

    public boolean isHasLogo() {
        return hasLogo;
    }

    public void setHasLog(boolean hasLogo) {
        this.hasLogo = hasLogo;
    }

    public boolean isShowSupplierInfo() {
        return showSupplierInfo;
    }

    public void setShowSupplierInfo(boolean showSupplierInfo) {
        this.showSupplierInfo = showSupplierInfo;
    }

    public String getCurrentDate() {
        return currentDate;
    }

    public void setCurrentDate(String currentDate) {
        this.currentDate = currentDate;
    }

    public List<ProposalItemDTO> getProposalItems() {
        return proposalItems;
    }

    public void setProposalItems(List<ProposalItemDTO> proposalItems) {
        this.proposalItems = proposalItems;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getTaxLabelString() {
        return taxLabelString;
    }

    public void setTaxLabelString(String taxLabelString) {
        this.taxLabelString = taxLabelString;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public BigDecimal getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(BigDecimal grandTotal) {
        this.grandTotal = grandTotal;
    }

    public String getCurrencySymbol() {
        return currencySymbol;
    }

    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol;
    }

    public List<CategoryDTO> getCategories() {
        return categories;
    }

    public void setCategories(List<CategoryDTO> categories) {
        this.categories = categories;
    }

    public List<BreakoutType> getBreakoutTypes() {
        return breakoutTypes;
    }

    public void setBreakoutTypes(List<BreakoutType> breakoutTypes) {
        this.breakoutTypes = breakoutTypes;
    }

    public String formatPdfDateTime(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm a");
        return localDateTime.format(dateFormat);
    }

    public Long getQuoteCustomPropertyId() {
        return quoteCustomPropertyId;
    }

    public void setQuoteCustomPropertyId(Long quoteCustomPropertyId) {
        this.quoteCustomPropertyId = quoteCustomPropertyId;
    }

    public UserFieldDefsWithValuesVO getQuoteUserFields() {
        return quoteUserFields;
    }

    public void setQuoteUserFields(UserFieldDefsWithValuesVO quoteUserFields) {
        this.quoteUserFields = quoteUserFields;
    }

    public UserFieldDefsWithValuesVO getQuoteItemsUserFields() {
        return quoteItemsUserFields;
    }

    public void setQuoteItemsUserFields(UserFieldDefsWithValuesVO quoteItemsUserFields) {
        this.quoteItemsUserFields = quoteItemsUserFields;
    }
}
