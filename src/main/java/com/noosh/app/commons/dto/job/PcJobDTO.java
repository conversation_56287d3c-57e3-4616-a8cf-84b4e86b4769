package com.noosh.app.commons.dto.job;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 11/21/17
 */
public class PcJobDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String jobName;

    private Long ownerWorkgroupId;

    private Long spSpecReferenceId;

    private Long currentSpSpecId;

    private Short isTransferrable;

    private Long ocObjectStateId;


    private List<JobStatusDTO> jobStatuses;

    public List<JobStatusDTO> getJobStatuses() {
        return jobStatuses;
    }

    public void setJobStatuses(List<JobStatusDTO> jobStatuses) {
        this.jobStatuses = jobStatuses;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getSpSpecReferenceId() {
        return spSpecReferenceId;
    }

    public void setSpSpecReferenceId(Long spSpecReferenceId) {
        this.spSpecReferenceId = spSpecReferenceId;
    }

    public Long getCurrentSpSpecId() {
        return currentSpSpecId;
    }

    public void setCurrentSpSpecId(Long currentSpSpecId) {
        this.currentSpSpecId = currentSpSpecId;
    }

    public Short getTransferrable() {
        return isTransferrable;
    }

    public void setTransferrable(Short transferrable) {
        isTransferrable = transferrable;
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }
}
