package com.noosh.app.commons.dto.order;

import com.noosh.app.commons.dto.quote.QuoteItemDTO;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 3/30/19
 */
public class OrderDetailMarkupDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long jobId;
    private String specName;
    private String iconString;
    private String jobState;
    private String jobStateStrId;
    private BigDecimal effectiveMarkup;
    private BigDecimal marginAmount;
    private BigDecimal buyAmount;
    private BigDecimal sellAmount;
    private BigDecimal profitAmount;
    private BigDecimal buyTaxAmount;
    private BigDecimal sellTaxAmount;
    private BigDecimal buyShippingAmount;
    private BigDecimal sellShippingAmount;
    private int pendingBuyAmountCount;
    private int pendingSellAmountCount;
    private int defaultQuotedCostCount;

    private QuoteItemDTO quoteItem;

    public BigDecimal getBuyTaxAmount() {
        return buyTaxAmount;
    }

    public void setBuyTaxAmount(BigDecimal buyTaxAmount) {
        this.buyTaxAmount = buyTaxAmount;
    }

    public BigDecimal getSellTaxAmount() {
        return sellTaxAmount;
    }

    public void setSellTaxAmount(BigDecimal sellTaxAmount) {
        this.sellTaxAmount = sellTaxAmount;
    }

    public BigDecimal getBuyShippingAmount() {
        return buyShippingAmount;
    }

    public void setBuyShippingAmount(BigDecimal buyShippingAmount) {
        this.buyShippingAmount = buyShippingAmount;
    }

    public BigDecimal getSellShippingAmount() {
        return sellShippingAmount;
    }

    public void setSellShippingAmount(BigDecimal sellShippingAmount) {
        this.sellShippingAmount = sellShippingAmount;
    }

    public String getJobStateStrId() {
        return jobStateStrId;
    }

    public void setJobStateStrId(String jobStateStrId) {
        this.jobStateStrId = jobStateStrId;
    }

    public int getDefaultQuotedCostCount() {
        return defaultQuotedCostCount;
    }

    public void setDefaultQuotedCostCount(int defaultQuotedCostCount) {
        this.defaultQuotedCostCount = defaultQuotedCostCount;
    }

    public int getPendingBuyAmountCount() {
        return pendingBuyAmountCount;
    }

    public void setPendingBuyAmountCount(int pendingBuyAmountCount) {
        this.pendingBuyAmountCount = pendingBuyAmountCount;
    }

    public int getPendingSellAmountCount() {
        return pendingSellAmountCount;
    }

    public void setPendingSellAmountCount(int pendingSellAmountCount) {
        this.pendingSellAmountCount = pendingSellAmountCount;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public BigDecimal getMarginAmount() {
        return marginAmount;
    }

    public void setMarginAmount(BigDecimal marginAmount) {
        this.marginAmount = marginAmount;
    }

    public BigDecimal getProfitAmount() {
        return profitAmount;
    }

    public void setProfitAmount(BigDecimal profitAmount) {
        this.profitAmount = profitAmount;
    }

    public BigDecimal getBuyAmount() {
        return buyAmount;
    }

    public void setBuyAmount(BigDecimal buyAmount) {
        this.buyAmount = buyAmount;
    }

    public BigDecimal getSellAmount() {
        return sellAmount;
    }

    public void setSellAmount(BigDecimal sellAmount) {
        this.sellAmount = sellAmount;
    }

    public BigDecimal getEffectiveMarkup() {
        return effectiveMarkup;
    }

    public void setEffectiveMarkup(BigDecimal effectiveMarkup) {
        this.effectiveMarkup = effectiveMarkup;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getIconString() {
        return iconString;
    }

    public void setIconString(String iconString) {
        this.iconString = iconString;
    }

    public String getJobState() {
        return jobState;
    }

    public void setJobState(String jobState) {
        this.jobState = jobState;
    }

    public QuoteItemDTO getQuoteItem() {
        return quoteItem;
    }

    public void setQuoteItem(QuoteItemDTO quoteItem) {
        this.quoteItem = quoteItem;
    }
}
