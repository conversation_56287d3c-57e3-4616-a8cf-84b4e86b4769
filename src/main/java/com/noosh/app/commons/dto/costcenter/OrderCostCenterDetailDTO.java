package com.noosh.app.commons.dto.costcenter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * User: leilaz
 * Date: 10/8/20
 */
public class OrderCostCenterDetailDTO implements Serializable {
    private static final long serialVersionUID = -4295843073836872878L;

    private boolean canEditCostCenter;

    private boolean canAddCostCenter;

    private Long orderId;

    private String orderName;

    private String orderStateStrId;

    private String orderExternalLink;

    private boolean isOriginal;

    private boolean isSellOrder;

    private boolean isClosing;

    private boolean isChangeOrder;

    private Long baseOrderId;

    private LocalDateTime orderAcceptDate;

    private List<OrderCostCenterDTO> orderCostCenters;

    private String editCostCenterExternalLink;

    public LocalDateTime getOrderAcceptDate() {
        return orderAcceptDate;
    }

    public void setOrderAcceptDate(LocalDateTime orderAcceptDate) {
        this.orderAcceptDate = orderAcceptDate;
    }

    public Long getBaseOrderId() {
        return baseOrderId;
    }

    public void setBaseOrderId(Long baseOrderId) {
        this.baseOrderId = baseOrderId;
    }

    public boolean getIsChangeOrder() {
        return isChangeOrder;
    }

    public void setIsChangeOrder(boolean changeOrder) {
        isChangeOrder = changeOrder;
    }

    public boolean getIsClosing() {
        return isClosing;
    }

    public void setIsClosing(boolean closing) {
        isClosing = closing;
    }

    public boolean getIsSellOrder() {
        return isSellOrder;
    }

    public void setIsSellOrder(boolean sellOrder) {
        isSellOrder = sellOrder;
    }

    public String getOrderStateStrId() {
        return orderStateStrId;
    }

    public void setOrderStateStrId(String orderStateStrId) {
        this.orderStateStrId = orderStateStrId;
    }

    public String getOrderExternalLink() {
        return orderExternalLink;
    }

    public void setOrderExternalLink(String orderExternalLink) {
        this.orderExternalLink = orderExternalLink;
    }

    public boolean getIsOriginal() {
        return isOriginal;
    }

    public void setIsOriginal(boolean original) {
        isOriginal = original;
    }

    public String getEditCostCenterExternalLink() {
        return editCostCenterExternalLink;
    }

    public void setEditCostCenterExternalLink(String editCostCenterExternalLink) {
        this.editCostCenterExternalLink = editCostCenterExternalLink;
    }

    public boolean getCanAddCostCenter() {
        return canAddCostCenter;
    }

    public void setCanAddCostCenter(boolean canAddCostCenter) {
        this.canAddCostCenter = canAddCostCenter;
    }

    public boolean getCanEditCostCenter() {
        return canEditCostCenter;
    }

    public void setCanEditCostCenter(boolean canEditCostCenter) {
        this.canEditCostCenter = canEditCostCenter;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public List<OrderCostCenterDTO> getOrderCostCenters() {
        return orderCostCenters;
    }

    public void setOrderCostCenters(List<OrderCostCenterDTO> orderCostCenters) {
        this.orderCostCenters = orderCostCenters;
    }
}
