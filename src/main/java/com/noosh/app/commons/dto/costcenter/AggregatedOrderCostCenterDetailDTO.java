package com.noosh.app.commons.dto.costcenter;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 10/8/20
 */
public class AggregatedOrderCostCenterDetailDTO implements Serializable {

    private static final long serialVersionUID = -3926592383861606363L;

    private AggregatedOrderCostCenterDTO itemsTotal;
    private AggregatedOrderCostCenterDTO tax;
    private AggregatedOrderCostCenterDTO shipping;

    public AggregatedOrderCostCenterDTO getItemsTotal() {
        return itemsTotal;
    }

    public void setItemsTotal(AggregatedOrderCostCenterDTO itemsTotal) {
        this.itemsTotal = itemsTotal;
    }

    public AggregatedOrderCostCenterDTO getTax() {
        return tax;
    }

    public void setTax(AggregatedOrderCostCenterDTO tax) {
        this.tax = tax;
    }

    public AggregatedOrderCostCenterDTO getShipping() {
        return shipping;
    }

    public void setShipping(AggregatedOrderCostCenterDTO shipping) {
        this.shipping = shipping;
    }
}
