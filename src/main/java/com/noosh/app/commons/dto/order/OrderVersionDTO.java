package com.noosh.app.commons.dto.order;


import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.OrderClassificationID;
import com.noosh.app.commons.constant.OrderTypeID;
import com.noosh.app.commons.dto.account.AccountUserDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDTO;
import com.noosh.app.commons.dto.custom.CustomFieldDTO;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.reason.PcReasonDTO;
import com.noosh.app.commons.dto.routing.RoutingSlipDTO;
import com.noosh.app.commons.dto.security.WorkgroupDTO;

import java.io.Serializable;
import java.time.LocalDateTime;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author: neals
 * @Date: 08/04/2016
 */
public class OrderVersionDTO implements Serializable {
    private Long id;
    private Long orderId;
    private String title;
    private Long orderTypeId;
    private Long version;
    private String reference;
    private Long buyerWorkgroupId;
    private Long supplierWorkgroupId;
    private LocalDateTime completionDate;
    private Long paymentMethodId;
    private String paymentReference;
    private Long bugetTypeFieldId;
    private Long coServiceId;
    private Boolean isCurrent;
    private Long buyerUserId;
    private Long supplierUserId;
    private Long initByWorkgroupId;
    private LocalDateTime acceptDate;
    private Long buyerTermsId;
    private Long supplierTermsId;
    private Long customPropertyId;
    private String supplierReference;
    private Long quoteId;
    private Boolean isGenerated;
    private BigDecimal oversPercent;
    private BigDecimal undersPercent;
    private Long reasonId;
    private String reasonOther;
    private Boolean itemized;
    private Boolean isSensitive;
    private Long buClientWorkgroupId;
    private Long orderClassificationId;
    private Long reAcceptanceStatusId;
    private LocalDateTime createDate;
    private LocalDateTime modDate;
    private LocalDateTime approvedDate;
    private Long createUserId;
    private Long modUserId;
    private String comments;

    private BigDecimal tax;
    private Long taxCurrencyId;
    private BigDecimal shipping;
    private Long shippingCurrencyId;
    private BigDecimal dors;
    private Long dorsCurrencyId;
    private BigDecimal miscCost;
    private Long miscCostCurrencyId;
    private BigDecimal subTotal;
    private BigDecimal grandTotal;
    private Long grandTotalCurrencyId;
    private BigDecimal grandChangeOrderTotal;
    private BigDecimal orderItemSubtotal;
    private Long orderItemSubtotalCurrencyId;
    private BigDecimal discountOrSurcharge;
    private BigDecimal discountOrSurchargeTotal;
    private BigDecimal orderItemDiscountSurchargeTotal;

    private BigDecimal rate;
    private Long exCurrencyId;
    private BigDecimal exTax;
    private Long exTaxCurrencyId;
    private BigDecimal exShipping;
    private Long exShippingCurrencyId;
    private BigDecimal exDors;
    private Long exDorsCurrencyId;
    private BigDecimal exMiscCost;
    private Long exMiscCostCurrencyId;

    private BigDecimal exSubTotal;
    private BigDecimal exGrandTotal;
    private BigDecimal exGrandChangeOrderTotal;
    private BigDecimal exOrderItemSubtotal;
    private BigDecimal exDiscountOrSurcharge;
    private BigDecimal exDiscountOrSurchargeTotal;
    private BigDecimal exOrderItemDiscountSurchargeTotal;

    private OrderVersionDTO invoiceAdjustmentParentOrder;
    private List<OrderVersionDTO> invoiceAdjustmentOrders;

    private RoutingSlipDTO routingSlip;
    private Map<String, String> approveMap;
    private ProjectDTO parent;
    private ProjectDTO buyerProject;
    private OrderStateDTO orderState;
    private CustomFieldDTO customFieldDTO;
    private List<OrderItemDTO> orderItemDTOs;
    private WorkgroupDTO buyerWorkgroup;
    private WorkgroupDTO supplierWorkgroup;
    private OrderDTO order;
    private AccountUserDTO buyer;
    private AccountUserDTO supplier;
    private PaymentMethodDTO paymentMethod;
    private PcReasonDTO reason;

    private Boolean requiresApproval = false;
    private Boolean requiresManagerApproval = false;
    private Boolean requiresManagerRouting = false;
    private Boolean requiresSequentialRouting = false;
    private Boolean requiresQuickApproval = false;
    private Boolean requiresRouting = false;
    private Boolean isApproved = false;
    private Boolean isDisapproved = false;
    private Boolean isAllRecipientsApproved = false;
    private Boolean isManagerApproved = false;
    private Boolean allAtOnce = false;
    private Boolean managerAllAtOnce = false;
    private Boolean autoTimeOut = false;
    private Boolean autoMgrTimeOut = false;
    private Boolean quickOrderApproval = false;
    private boolean pcAllowBreakouts;
    private boolean closeOrderNegotiation;
    private boolean hideOversAndUnders;
    private String taxLabelString;
    private boolean canViewProjectBudget;
    private boolean invoicingEnable;
    private boolean canManageShipment;
    private boolean showPendingApproval;
    private boolean requiredApprovalInvoice;
    private boolean autoAcceptFinalInvoice;
    private boolean invoiceSentOnClosedOrder;
    private boolean isCompletedAndHasAcceptedClosingChangeOrder;
    private boolean canRoutingSupplier;
    private boolean isEnableComplexVAT;
    private String vatCode;
    private BigDecimal vatRate;
    private boolean isShowSensitive;

    private List<AccountUserDTO> managerRecipients;
    private List<AccountUserDTO> approverRecipients;
    private InvoiceDTO finalInvoiceTemplate;
    private LocalDateTime latestAcceptDate;
    private List<OrderCostCenterDTO> costCenters;

    public List<OrderCostCenterDTO> getCostCenters() {
        return costCenters;
    }

    public void setCostCenters(List<OrderCostCenterDTO> costCenters) {
        this.costCenters = costCenters;
    }

    public boolean getIsShowSensitive() {
        return isShowSensitive;
    }

    public void setIsShowSensitive(boolean showSensitive) {
        isShowSensitive = showSensitive;
    }

    public LocalDateTime getLatestAcceptDate() {
        return latestAcceptDate;
    }

    public void setLatestAcceptDate(LocalDateTime latestAcceptDate) {
        this.latestAcceptDate = latestAcceptDate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public BigDecimal getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(BigDecimal discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public boolean getIsEnableComplexVAT() {
        return isEnableComplexVAT;
    }

    public void setIsEnableComplexVAT(boolean enableComplexVAT) {
        isEnableComplexVAT = enableComplexVAT;
    }

    public String getVatCode() {
        return vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public BigDecimal getVatRate() {
        return vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public boolean getCanRoutingSupplier() {
        return canRoutingSupplier;
    }

    public void setCanRoutingSupplier(boolean canRoutingSupplier) {
        this.canRoutingSupplier = canRoutingSupplier;
    }

    public Boolean getQuickOrderApproval() {
        return quickOrderApproval;
    }

    public void setQuickOrderApproval(Boolean quickOrderApproval) {
        this.quickOrderApproval = quickOrderApproval;
    }

    public Boolean getManagerAllAtOnce() {
        return managerAllAtOnce;
    }

    public void setManagerAllAtOnce(Boolean managerAllAtOnce) {
        this.managerAllAtOnce = managerAllAtOnce;
    }

    public Boolean getAutoMgrTimeOut() {
        return autoMgrTimeOut;
    }

    public void setAutoMgrTimeOut(Boolean autoMgrTimeOut) {
        this.autoMgrTimeOut = autoMgrTimeOut;
    }

    public Boolean getAllAtOnce() {
        return allAtOnce;
    }

    public void setAllAtOnce(Boolean allAtOnce) {
        this.allAtOnce = allAtOnce;
    }

    public Boolean getAutoTimeOut() {
        return autoTimeOut;
    }

    public void setAutoTimeOut(Boolean autoTimeOut) {
        this.autoTimeOut = autoTimeOut;
    }

    public List<AccountUserDTO> getApproverRecipients() {
        return approverRecipients;
    }

    public void setApproverRecipients(List<AccountUserDTO> approverRecipients) {
        this.approverRecipients = approverRecipients;
    }

    public Boolean getRequiresManagerRouting() {
        return requiresManagerRouting;
    }

    public void setRequiresManagerRouting(Boolean requiresManagerRouting) {
        this.requiresManagerRouting = requiresManagerRouting;
    }

    public List<AccountUserDTO> getManagerRecipients() {
        return managerRecipients;
    }

    public void setManagerRecipients(List<AccountUserDTO> managerRecipients) {
        this.managerRecipients = managerRecipients;
    }

    public Boolean getRequiresRouting() {
        return requiresRouting;
    }

    public void setRequiresRouting(Boolean requiresRouting) {
        this.requiresRouting = requiresRouting;
    }

    public boolean isCompletedAndHasAcceptedClosingChangeOrder() {
        return isCompletedAndHasAcceptedClosingChangeOrder;
    }

    public void setCompletedAndHasAcceptedClosingChangeOrder(boolean completedAndHasAcceptedClosingChangeOrder) {
        isCompletedAndHasAcceptedClosingChangeOrder = completedAndHasAcceptedClosingChangeOrder;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public InvoiceDTO getFinalInvoiceTemplate() {
        return finalInvoiceTemplate;
    }

    public void setFinalInvoiceTemplate(InvoiceDTO finalInvoiceTemplate) {
        this.finalInvoiceTemplate = finalInvoiceTemplate;
    }

    public boolean isInvoiceSentOnClosedOrder() {
        return invoiceSentOnClosedOrder;
    }

    public void setInvoiceSentOnClosedOrder(boolean invoiceSentOnClosedOrder) {
        this.invoiceSentOnClosedOrder = invoiceSentOnClosedOrder;
    }

    public boolean isRequiredApprovalInvoice() {
        return requiredApprovalInvoice;
    }

    public void setRequiredApprovalInvoice(boolean requiredApprovalInvoice) {
        this.requiredApprovalInvoice = requiredApprovalInvoice;
    }

    public boolean isAutoAcceptFinalInvoice() {
        return autoAcceptFinalInvoice;
    }

    public void setAutoAcceptFinalInvoice(boolean autoAcceptFinalInvoice) {
        this.autoAcceptFinalInvoice = autoAcceptFinalInvoice;
    }

    public BigDecimal getOrderItemDiscountSurchargeTotal() {
        return orderItemDiscountSurchargeTotal;
    }

    public void setOrderItemDiscountSurchargeTotal(BigDecimal orderItemDiscountSurchargeTotal) {
        this.orderItemDiscountSurchargeTotal = orderItemDiscountSurchargeTotal;
    }

    public BigDecimal getRate() {
        return this.rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return this.exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public BigDecimal getExTax() {
        return this.exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return this.exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public BigDecimal getExShipping() {
        return this.exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return this.exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExDors() {
        return this.exDors;
    }

    public void setExDors(BigDecimal exDors) {
        this.exDors = exDors;
    }

    public Long getExDorsCurrencyId() {
        return this.exDorsCurrencyId;
    }

    public void setExDorsCurrencyId(Long exDorsCurrencyId) {
        this.exDorsCurrencyId = exDorsCurrencyId;
    }

    public BigDecimal getExMiscCost() {
        return this.exMiscCost;
    }

    public void setExMiscCost(BigDecimal exMiscCost) {
        this.exMiscCost = exMiscCost;
    }

    public Long getExMiscCostCurrencyId() {
        return this.exMiscCostCurrencyId;
    }

    public void setExMiscCostCurrencyId(Long exMiscCostCurrencyId) {
        this.exMiscCostCurrencyId = exMiscCostCurrencyId;
    }

    public BigDecimal getExSubTotal() {
        return this.exSubTotal;
    }

    public void setExSubTotal(BigDecimal exSubTotal) {
        this.exSubTotal = exSubTotal;
    }

    public BigDecimal getExGrandTotal() {
        return this.exGrandTotal;
    }

    public void setExGrandTotal(BigDecimal exGrandTotal) {
        this.exGrandTotal = exGrandTotal;
    }

    public BigDecimal getExGrandChangeOrderTotal() {
        return this.exGrandChangeOrderTotal;
    }

    public void setExGrandChangeOrderTotal(BigDecimal exGrandChangeOrderTotal) {
        this.exGrandChangeOrderTotal = exGrandChangeOrderTotal;
    }

    public BigDecimal getExOrderItemSubtotal() {
        return this.exOrderItemSubtotal;
    }

    public void setExOrderItemSubtotal(BigDecimal exOrderItemSubtotal) {
        this.exOrderItemSubtotal = exOrderItemSubtotal;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return this.exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public BigDecimal getExDiscountOrSurchargeTotal() {
        return this.exDiscountOrSurchargeTotal;
    }

    public void setExDiscountOrSurchargeTotal(BigDecimal exDiscountOrSurchargeTotal) {
        this.exDiscountOrSurchargeTotal = exDiscountOrSurchargeTotal;
    }

    public BigDecimal getExOrderItemDiscountSurchargeTotal() {
        return this.exOrderItemDiscountSurchargeTotal;
    }

    public void setExOrderItemDiscountSurchargeTotal(BigDecimal exOrderItemDiscountSurchargeTotal) {
        this.exOrderItemDiscountSurchargeTotal = exOrderItemDiscountSurchargeTotal;
    }

    public OrderVersionDTO getInvoiceAdjustmentParentOrder() {
        return invoiceAdjustmentParentOrder;
    }

    public void setInvoiceAdjustmentParentOrder(OrderVersionDTO invoiceAdjustmentParentOrder) {
        this.invoiceAdjustmentParentOrder = invoiceAdjustmentParentOrder;
    }

    public List<OrderVersionDTO> getInvoiceAdjustmentOrders() {
        return invoiceAdjustmentOrders;
    }

    public void setInvoiceAdjustmentOrders(List<OrderVersionDTO> invoiceAdjustmentOrders) {
        this.invoiceAdjustmentOrders = invoiceAdjustmentOrders;
    }

    public Boolean getRequiresQuickApproval() {
        return requiresQuickApproval;
    }

    public void setRequiresQuickApproval(Boolean requiresQuickApproval) {
        this.requiresQuickApproval = requiresQuickApproval;
    }

    public BigDecimal getDiscountOrSurchargeTotal() {
        return discountOrSurchargeTotal;
    }

    public void setDiscountOrSurchargeTotal(BigDecimal discountOrSurchargeTotal) {
        this.discountOrSurchargeTotal = discountOrSurchargeTotal;
    }

    public BigDecimal getOrderItemSubtotal() {
        return orderItemSubtotal;
    }

    public void setOrderItemSubtotal(BigDecimal orderItemSubtotal) {
        this.orderItemSubtotal = orderItemSubtotal;
    }

    public Long getOrderItemSubtotalCurrencyId() {
        return orderItemSubtotalCurrencyId;
    }

    public void setOrderItemSubtotalCurrencyId(Long orderItemSubtotalCurrencyId) {
        this.orderItemSubtotalCurrencyId = orderItemSubtotalCurrencyId;
    }

    public BigDecimal getGrandChangeOrderTotal() {
        return grandChangeOrderTotal;
    }

    public void setGrandChangeOrderTotal(BigDecimal grandChangeOrderTotal) {
        this.grandChangeOrderTotal = grandChangeOrderTotal;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getPaymentMethodId() {
        return paymentMethodId;
    }

    public void setPaymentMethodId(Long paymentMethodId) {
        this.paymentMethodId = paymentMethodId;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public Long getBugetTypeFieldId() {
        return bugetTypeFieldId;
    }

    public void setBugetTypeFieldId(Long bugetTypeFieldId) {
        this.bugetTypeFieldId = bugetTypeFieldId;
    }

    public Long getCoServiceId() {
        return coServiceId;
    }

    public void setCoServiceId(Long coServiceId) {
        this.coServiceId = coServiceId;
    }

    public Boolean getIsCurrent() {
        return this.isCurrent;
    }

    public void setIsCurrent(Boolean current) {
        this.isCurrent = current;
    }

    public Long getBuyerUserId() {
        return buyerUserId;
    }

    public void setBuyerUserId(Long buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    public Long getSupplierUserId() {
        return supplierUserId;
    }

    public void setSupplierUserId(Long supplierUserId) {
        this.supplierUserId = supplierUserId;
    }

    public Long getInitByWorkgroupId() {
        return initByWorkgroupId;
    }

    public void setInitByWorkgroupId(Long initByWorkgroupId) {
        this.initByWorkgroupId = initByWorkgroupId;
    }

    public LocalDateTime getAcceptDate() {
        return acceptDate;
    }

    public void setAcceptDate(LocalDateTime acceptDate) {
        this.acceptDate = acceptDate;
    }

    public Long getBuyerTermsId() {
        return buyerTermsId;
    }

    public void setBuyerTermsId(Long buyerTermsId) {
        this.buyerTermsId = buyerTermsId;
    }

    public Long getSupplierTermsId() {
        return supplierTermsId;
    }

    public void setSupplierTermsId(Long supplierTermsId) {
        this.supplierTermsId = supplierTermsId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public String getSupplierReference() {
        return supplierReference;
    }

    public void setSupplierReference(String supplierReference) {
        this.supplierReference = supplierReference;
    }

    public Long getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    public Boolean getIsGenerated() {
        return this.isGenerated != null && this.isGenerated;
    }

    public void setIsGenerated(Boolean generated) {
        this.isGenerated = generated;
    }

    public BigDecimal getOversPercent() {
        return oversPercent;
    }

    public void setOversPercent(BigDecimal oversPercent) {
        this.oversPercent = oversPercent;
    }

    public BigDecimal getUndersPercent() {
        return undersPercent;
    }

    public void setUndersPercent(BigDecimal undersPercent) {
        this.undersPercent = undersPercent;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public String getReasonOther() {
        return reasonOther;
    }

    public void setReasonOther(String reasonOther) {
        this.reasonOther = reasonOther;
    }

    public Boolean getItemized() {
        return itemized;
    }

    public void setItemized(Boolean itemized) {
        this.itemized = itemized;
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }

    public Long getOrderClassificationId() {
        return orderClassificationId;
    }

    public void setOrderClassificationId(Long orderClassificationId) {
        this.orderClassificationId = orderClassificationId;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public LocalDateTime getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(LocalDateTime approvedDate) {
        this.approvedDate = approvedDate;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Long getModUserId() {
        return modUserId;
    }

    public void setModUserId(Long modUserId) {
        this.modUserId = modUserId;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public BigDecimal getDors() {
        return this.dors;
    }

    public void setDors(BigDecimal dors) {
        this.dors = dors;
    }

    public Long getDorsCurrencyId() {
        return this.dorsCurrencyId;
    }

    public void setDorsCurrencyId(Long dorsCurrencyId) {
        this.dorsCurrencyId = dorsCurrencyId;
    }

    public BigDecimal getMiscCost() {
        return miscCost;
    }

    public void setMiscCost(BigDecimal miscCost) {
        this.miscCost = miscCost;
    }

    public Long getMiscCostCurrencyId() {
        return miscCostCurrencyId;
    }

    public void setMiscCostCurrencyId(Long miscCostCurrencyId) {
        this.miscCostCurrencyId = miscCostCurrencyId;
    }

    public WorkgroupDTO getBuyerWorkgroup() {
        return buyerWorkgroup;
    }

    public void setBuyerWorkgroup(WorkgroupDTO buyerWorkgroup) {
        this.buyerWorkgroup = buyerWorkgroup;
    }

    public WorkgroupDTO getSupplierWorkgroup() {
        return supplierWorkgroup;
    }

    public void setSupplierWorkgroup(WorkgroupDTO supplierWorkgroup) {
        this.supplierWorkgroup = supplierWorkgroup;
    }

    public AccountUserDTO getBuyer() {
        return buyer;
    }

    public void setBuyer(AccountUserDTO buyer) {
        this.buyer = buyer;
    }

    public AccountUserDTO getSupplier() {
        return supplier;
    }

    public void setSupplier(AccountUserDTO supplier) {
        this.supplier = supplier;
    }

    public PaymentMethodDTO getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PaymentMethodDTO paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public PcReasonDTO getReason() {
        return reason;
    }

    public void setReason(PcReasonDTO reason) {
        this.reason = reason;
    }

    public Map<String, String> getApproveMap() {
        return approveMap;
    }

    public void setApproveMap(Map<String, String> approveMap) {
        this.approveMap = approveMap;
    }

    public boolean getShowPendingApproval() {
        return showPendingApproval;
    }

    public void setShowPendingApproval(boolean showPendingApproval) {
        this.showPendingApproval = showPendingApproval;
    }

    public RoutingSlipDTO getRoutingSlip() {
        return this.routingSlip;
    }

    public void setRoutingSlip(RoutingSlipDTO routingSlip) {
        this.routingSlip = routingSlip;
    }

    public boolean getCanManageShipment() {
        return canManageShipment;
    }

    public void setCanManageShipment(boolean canManageShipment) {
        this.canManageShipment = canManageShipment;
    }

    public boolean getInvoicingEnable() {
        return invoicingEnable;
    }

    public void setInvoicingEnable(boolean invoicingEnable) {
        this.invoicingEnable = invoicingEnable;
    }

    public boolean getCanViewProjectBudget() {
        return canViewProjectBudget;
    }

    public void setCanViewProjectBudget(boolean canViewProjectBudget) {
        this.canViewProjectBudget = canViewProjectBudget;
    }

    public boolean getCloseOrderNegotiation() {
        return closeOrderNegotiation;
    }

    public void setCloseOrderNegotiation(boolean closeOrderNegotiation) {
        this.closeOrderNegotiation = closeOrderNegotiation;
    }

    public boolean getHideOversAndUnders() {
        return hideOversAndUnders;
    }

    public void setHideOversAndUnders(boolean hideOversAndUnders) {
        this.hideOversAndUnders = hideOversAndUnders;
    }

    public String getTaxLabelString() {
        return taxLabelString;
    }

    public void setTaxLabelString(String taxLabelString) {
        this.taxLabelString = taxLabelString;
    }

    public boolean getPcAllowBreakouts() {
        return pcAllowBreakouts;
    }

    public void setPcAllowBreakouts(boolean pcAllowBreakouts) {
        this.pcAllowBreakouts = pcAllowBreakouts;
    }

    public CustomFieldDTO getCustomFieldDTO() {
        return customFieldDTO;
    }

    public void setCustomFieldDTO(CustomFieldDTO customFieldDTO) {
        this.customFieldDTO = customFieldDTO;
    }

    public Boolean getIsManagerApproved() {
        return isManagerApproved;
    }

    public void setIsManagerApproved(Boolean managerApproved) {
        isManagerApproved = managerApproved;
    }

    public Boolean getIsAllRecipientsApproved() {
        return isAllRecipientsApproved;
    }

    public void setIsAllRecipientsApproved(Boolean allRecipientsApproved) {
        isAllRecipientsApproved = allRecipientsApproved;
    }

    public Boolean getIsDisapproved() {
        return isDisapproved;
    }

    public Boolean getIsSensitive() {
        return this.isSensitive;
    }

    public void setIsSensitive(Boolean sensitive) {
        this.isSensitive = sensitive;
    }

    public void setIsDisapproved(Boolean disapproved) {
        isDisapproved = disapproved;
    }

    public Boolean getIsApproved() {
        return isApproved;
    }

    public void setIsApproved(Boolean approved) {
        isApproved = approved;
    }

    public Boolean getRequiresSequentialRouting() {
        return requiresSequentialRouting;
    }

    public void setRequiresSequentialRouting(Boolean requiresSequentialRouting) {
        this.requiresSequentialRouting = requiresSequentialRouting;
    }

    public Boolean getRequiresManagerApproval() {
        return requiresManagerApproval;
    }

    public void setRequiresManagerApproval(Boolean requiresManagerApproval) {
        this.requiresManagerApproval = requiresManagerApproval;
    }

    public Boolean getRequiresApproval() {
        return requiresApproval;
    }

    public void setRequiresApproval(boolean requiresApproval) {
        this.requiresApproval = requiresApproval;
    }

    public BigDecimal getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal) {
        this.subTotal = subTotal;
    }

    public BigDecimal getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(BigDecimal grandTotal) {
        this.grandTotal = grandTotal;
    }

    public Long getGrandTotalCurrencyId() {
        return grandTotalCurrencyId;
    }

    public void setGrandTotalCurrencyId(Long grandTotalCurrencyId) {
        this.grandTotalCurrencyId = grandTotalCurrencyId;
    }

    public List<OrderItemDTO> getOrderItemDTOs() {
        return orderItemDTOs;
    }

    public void setOrderItemDTOs(List<OrderItemDTO> orderItemDTOs) {
        this.orderItemDTOs = orderItemDTOs;
    }

    public ProjectDTO getParent() {
        return parent;
    }

    public void setParent(ProjectDTO parent) {
        this.parent = parent;
    }

    public ProjectDTO getBuyerProject() {
        return buyerProject;
    }

    public void setBuyerProject(ProjectDTO buyerProject) {
        this.buyerProject = buyerProject;
    }

    public OrderDTO getOrder() {
        return this.order;
    }

    public void setOrder(OrderDTO order) {
        this.order = order;
    }

    public OrderStateDTO getOrderState() {
        return orderState;
    }

    public void setOrderState(OrderStateDTO orderState) {
        this.orderState = orderState;
    }

    public boolean isPending() {
        return (isPendingBuyerAcceptance() || isPendingSupplierAcceptance());
    }

//    public boolean isNew() {
//        return getOrderVersion().getId() == null || getOrderVersion().getId().longValue() < 0;
//    }

    public boolean isPendingBuyerAcceptance() {
        if (getOrderState() == null) return false;
        long orderStateId = getOrderState().getObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_BUYER_TO_ACCEPT || orderStateId == ObjectStateID.ORDER_CLIENT_TO_ACCEPT);
    }

    public boolean isPendingSupplierAcceptance() {
        if (getOrderState() == null) return false;
        long orderStateId = getOrderState().getObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_SUPPLIER_TO_ACCEPT || orderStateId == ObjectStateID.ORDER_OUTSOURCER_TO_ACCEPT);
    }

    /**
     * @return true if this order is viewed within the context of a supplier project
     */
    public boolean isUserSupplier() {
        return this.getSupplierWorkgroupId() != null && this.getSupplierWorkgroupId().longValue() == getParent().getOwnerWorkgroupId().longValue();
    }


    /**
     * @return true if this order is viewed within the context of a buyer project
     */
    public boolean isUserBuyer() {
        return this.getBuyerWorkgroupId().longValue() == getParent().getOwnerWorkgroupId().longValue();

    }

    public boolean isOutsourcingSellOrder(ProjectDTO parentOwner) {
        ProjectDTO project = getParent();
        if (hasOfflineBuyer())
            return true;
        if (isUserBuyer() && project.isClientProject() && parentOwner.isOutsourcerProject())
            return true;
        if (isUserSupplier() && project.isOutsourcerProject() )
            return true;
        return false;
    }

    public boolean isBrokerSellOrder(ProjectDTO parentOwner) {
        ProjectDTO project = getParent();
        if (hasOfflineBuyer())
            return true;
        if (isUserBuyer() && project.isClientProject() && parentOwner.isBrokerOutsourcerProject())
            return true;
        if (isUserSupplier() && project.isBrokerOutsourcerProject() )
            return true;
        return false;
    }

    public boolean isSellOrder(ProjectDTO parentOwner) {
        return isOutsourcingSellOrder(parentOwner) || isBrokerSellOrder(parentOwner);
    }

    public boolean isPendingSubmission() {
        if (getOrderState() == null) return false;
        long orderStateId = getOrderState().getObjectStateId();
        if (orderStateId == ObjectStateID.ORDER_PENDING_SUBMISSION)
            return true;
        return false;
    }


    public boolean isDraft() {
        if (this.getId() <= 0 )
            return true;

        if (getOrderState() == null) return false;
        return  getOrderState().getObjectStateId() == ObjectStateID.ORDER_DRAFT;
    }

    public boolean hasOfflineBuyer() {
        ProjectDTO project = this.getParent();
        if (project.isClientNotOnNoosh() && this.getSupplierWorkgroupId() != null
                && this.getBuyerWorkgroupId().longValue() == this.getSupplierWorkgroupId().longValue()
                && this.getBuClientWorkgroupId() != null && this.getBuClientWorkgroupId() > 0)
            return true;

        return false;
    }

    public boolean isChangeOrder() {
        return (this.getOrderTypeId() == OrderTypeID.CHANGE_ORDER);
    }

    public boolean isClosingChangeOrder() {
        return isChangeOrder() && this.getOrder().getIsClosing();
    }

    public boolean isClosing() {
        return this.getOrder().getIsClosing();
    }

    public boolean isQuickOrder() {
        return (this.getOrderTypeId() == OrderTypeID.QUICK_ORDER);
    }

    public boolean isAccepted() {
        if (getOrderState() == null) return false;
        long orderStateId = getOrderState().getObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_ACCEPTED
                || orderStateId == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                || orderStateId == ObjectStateID.ORDER_SHIPPED
                || orderStateId == ObjectStateID.ORDER_DELIVERED
                || orderStateId == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED
                || orderStateId == ObjectStateID.ORDER_COMPLETED
                || orderStateId == ObjectStateID.ORDER_FINALIZED);
    }

    public boolean isPaperOrder() {
        return this.getOrderClassificationId() != null && this.getOrderClassificationId() == OrderClassificationID.PAPER;
    }

    public boolean isReordered() {
        return this.getOrder().getOrigOrderId() != null &&  this.getOrder().getOrigOrderId().longValue() > 0;
    }

    public boolean isRetracted() {
        if (getOrderState() == null) return false;
        long orderStateId = getOrderState().getObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_RETRACTED);
    }

    public boolean isPendingApproval(RoutingSlipDTO routingSlipBean) {
        if (routingSlipBean != null && (routingSlipBean.getApprovedCount() == 0 && routingSlipBean.getDisapprovedCount() == 0))
            return true;
        return false;
    }

    public boolean isCancelled() {
        if (getOrderState() == null) return false;
        long orderStateId = getOrderState().getObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_CANCELLED);
    }

    public boolean isRejected() {
        if (getOrderState() == null) return false;
        long orderStateId = getOrderState().getObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_REJECTED);
    }

    public boolean isCompleted() {
        if (getOrderState() == null) return false;
        long orderStateId = getOrderState().getObjectStateId();
        return (orderStateId == ObjectStateID.ORDER_COMPLETED);
    }

    public boolean isReplaced() {
        long orderStateId = getOrderState().getObjectStateId();
        return orderStateId == ObjectStateID.ORDER_REPLACED;
    }

    public boolean isCreatedByTheUserOfParentGroup(long parentGroupId) {
        if (this.getOrderState().getCreator().getWorkgroupId() == parentGroupId){
            return true;
        }
        return false;
    }

    public String getOrderTitle() {
        if (title == null || title.length() == 0) {
            String ref = getReference();
            title = (ref == null) ? "" : ref;
        }
        return title;
    }

    public BigDecimal getOrderItemTotal() {
        BigDecimal orderItemSubtotal = new BigDecimal(0);
        // Load order item
        if (this.getOrderItemDTOs() != null) {
            for (OrderItemDTO o : this.getOrderItemDTOs()) {
                orderItemSubtotal = orderItemSubtotal.add(o.getValue());
            }
        }
        return orderItemSubtotal;
    }

    public BigDecimal getOrderTotalQuantity() {
        BigDecimal totalQuantity = new BigDecimal(0);
        List<OrderItemDTO> orderItemDTOs = this.getOrderItemDTOs();
        if (orderItemDTOs != null) {
            for (OrderItemDTO orderItemDTO : this.getOrderItemDTOs()) {
                totalQuantity = totalQuantity.add(orderItemDTO.getQuantity());
            }
        }
        return totalQuantity;
    }

    public BigDecimal getExOrderItemTotal() {
        BigDecimal exOrderItemSubtotal = new BigDecimal(0);
        // Load order item
        if (this.getOrderItemDTOs() != null) {
            for (OrderItemDTO o : this.getOrderItemDTOs()) {
                exOrderItemSubtotal = exOrderItemSubtotal.add(o.getExValue());
            }
        }
        return exOrderItemSubtotal;
    }

    public Long getValueCurrencyId() {
        Long valueCurrencyId = null;
        List<OrderItemDTO> orderItemDTOS = this.getOrderItemDTOs();
        if (orderItemDTOS != null && orderItemDTOS.size() > 0 && orderItemDTOS.get(0).getValue() != null) {
            valueCurrencyId = orderItemDTOS.get(0).getValueCurrencyId();
        }
        return valueCurrencyId;
    }

    public Long getReAcceptanceStatusId() {
        return reAcceptanceStatusId == null ? -1 : reAcceptanceStatusId;
    }

    public void setReAcceptanceStatusId(Long reAcceptanceStatusId) {
        this.reAcceptanceStatusId = reAcceptanceStatusId;
    }
}
