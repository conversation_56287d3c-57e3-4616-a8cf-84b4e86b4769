package com.noosh.app.commons.dto.breakout;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * User: leilaz
 * Date: 10/8/17
 */
public class BreakoutDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long breakoutTypeId;

    private Long nestingLevel;

    private Boolean isLeafNode;

    private Boolean hasQuantity;

    private Long value;

    private BigDecimal price;

    private Long priceCurrencyId;

    private BigDecimal exPrice;

    private Long exPriceCurrencyId;

    private Long objectId;

    private Long objectClassId;

    private String custom1;

    private BigDecimal preMarkup;

    private Long preMarkupCurrencyId;

    private BigDecimal exPreMarkup;

    private Long exPreMarkupCurrencyId;

    private BigDecimal markupPercent;

    private BigDecimal markupFixed;

    private Long markupCurrencyId;

    private BigDecimal exMarkupFixed;

    private Long exMarkupFixedCurrencyId;

    private BreakoutTypeDTO breakoutType;

    private List<BreakoutDTO> descendents;

    private BreakoutDTO ancestor;

    private BigDecimal units;

    private BigDecimal rates;

    private Long ratesCurrencyId;

    private Integer level;

    public List<BreakoutDTO> getDescendents() {
        return descendents;
    }

    public void setDescendents(List<BreakoutDTO> descendents) {
        this.descendents = descendents;
    }

    public BreakoutDTO getAncestor() {
        return ancestor;
    }

    public void setAncestor(BreakoutDTO ancestor) {
        this.ancestor = ancestor;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public Long getNestingLevel() {
        return nestingLevel;
    }

    public void setNestingLevel(Long nestingLevel) {
        this.nestingLevel = nestingLevel;
    }

    public Boolean getLeafNode() {
        return isLeafNode;
    }

    public void setLeafNode(Boolean leafNode) {
        isLeafNode = leafNode;
    }

    public Boolean getHasQuantity() {
        return hasQuantity;
    }

    public void setHasQuantity(Boolean hasQuantity) {
        this.hasQuantity = hasQuantity;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getPriceCurrencyId() {
        return priceCurrencyId;
    }

    public void setPriceCurrencyId(Long priceCurrencyId) {
        this.priceCurrencyId = priceCurrencyId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public Long getPreMarkupCurrencyId() {
        return preMarkupCurrencyId;
    }

    public void setPreMarkupCurrencyId(Long preMarkupCurrencyId) {
        this.preMarkupCurrencyId = preMarkupCurrencyId;
    }

    public BigDecimal getPreMarkup() {
        return preMarkup;
    }

    public void setPreMarkup(BigDecimal preMarkup) {
        this.preMarkup = preMarkup;
    }

    public BigDecimal getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(BigDecimal markupPercent) {
        this.markupPercent = markupPercent;
    }

    public Long getMarkupCurrencyId() {
        return markupCurrencyId;
    }

    public void setMarkupCurrencyId(Long markupCurrencyId) {
        this.markupCurrencyId = markupCurrencyId;
    }

    public BigDecimal getMarkupFixed() {
        return markupFixed;
    }

    public void setMarkupFixed(BigDecimal markupFixed) {
        this.markupFixed = markupFixed;
    }

    public BigDecimal getExPrice() {
        return this.exPrice;
    }

    public void setExPrice(BigDecimal exPrice) {
        this.exPrice = exPrice;
    }

    public Long getExPriceCurrencyId() {
        return this.exPriceCurrencyId;
    }

    public void setExPriceCurrencyId(Long exPriceCurrencyId) {
        this.exPriceCurrencyId = exPriceCurrencyId;
    }

    public BigDecimal getExPreMarkup() {
        return this.exPreMarkup;
    }

    public void setExPreMarkup(BigDecimal exPreMarkup) {
        this.exPreMarkup = exPreMarkup;
    }

    public Long getExPreMarkupCurrencyId() {
        return this.exPreMarkupCurrencyId;
    }

    public void setExPreMarkupCurrencyId(Long exPreMarkupCurrencyId) {
        this.exPreMarkupCurrencyId = exPreMarkupCurrencyId;
    }

    public BigDecimal getExMarkupFixed() {
        return this.exMarkupFixed;
    }

    public void setExMarkupFixed(BigDecimal exMarkupFixed) {
        this.exMarkupFixed = exMarkupFixed;
    }

    public Long getExMarkupFixedCurrencyId() {
        return this.exMarkupFixedCurrencyId;
    }

    public void setExMarkupFixedCurrencyId(Long exMarkupFixedCurrencyId) {
        this.exMarkupFixedCurrencyId = exMarkupFixedCurrencyId;
    }

    public BreakoutTypeDTO getBreakoutType() {
        return breakoutType;
    }

    public void setBreakoutType(BreakoutTypeDTO breakoutType) {
        this.breakoutType = breakoutType;
    }

    public BigDecimal getUnits() {
        return units;
    }

    public void setUnits(BigDecimal units) {
        this.units = units;
    }

    public BigDecimal getRates() {
        return rates;
    }

    public void setRates(BigDecimal rates) {
        this.rates = rates;
    }

    public Long getRatesCurrencyId() {
        return ratesCurrencyId;
    }

    public void setRatesCurrencyId(Long ratesCurrencyId) {
        this.ratesCurrencyId = ratesCurrencyId;
    }

    public Integer getLevel() {
        return this.level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
