package com.noosh.app.commons.dto.breakout;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 10/8/17
 */
public class BreakoutDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long breakoutTypeId;

    private Long nestingLevel;

    private Boolean isLeafNode;

    private Boolean hasQuantity;

    private Long value;

    private BigDecimal price;

    private Long priceCurrencyId;

    private Long objectId;

    private Long objectClassId;

    private String custom1;

    private BigDecimal preMarkup;

    private Long preMarkupCurrencyId;

    private BigDecimal markupPercent;

    private BigDecimal markupFixed;

    private Long markupCurrencyId;

    private BigDecimal units;

    private BigDecimal rates;

    private BreakoutTypeDTO breakoutType;

    public BreakoutTypeDTO getBreakoutType() {
        return breakoutType;
    }

    public void setBreakoutType(BreakoutTypeDTO breakoutType) {
        this.breakoutType = breakoutType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public Long getNestingLevel() {
        return nestingLevel;
    }

    public void setNestingLevel(Long nestingLevel) {
        this.nestingLevel = nestingLevel;
    }

    public Boolean getLeafNode() {
        return isLeafNode;
    }

    public void setLeafNode(Boolean leafNode) {
        isLeafNode = leafNode;
    }

    public Boolean getHasQuantity() {
        return hasQuantity;
    }

    public void setHasQuantity(Boolean hasQuantity) {
        this.hasQuantity = hasQuantity;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public BigDecimal getPrice() {
        return price != null ? price : BigDecimal.ZERO;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getPriceCurrencyId() {
        return priceCurrencyId;
    }

    public void setPriceCurrencyId(Long priceCurrencyId) {
        this.priceCurrencyId = priceCurrencyId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public Long getPreMarkupCurrencyId() {
        return preMarkupCurrencyId;
    }

    public void setPreMarkupCurrencyId(Long preMarkupCurrencyId) {
        this.preMarkupCurrencyId = preMarkupCurrencyId;
    }

    public BigDecimal getPreMarkup() {
        return preMarkup;
    }

    public void setPreMarkup(BigDecimal preMarkup) {
        this.preMarkup = preMarkup;
    }

    public BigDecimal getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(BigDecimal markupPercent) {
        this.markupPercent = markupPercent;
    }

    public Long getMarkupCurrencyId() {
        return markupCurrencyId;
    }

    public void setMarkupCurrencyId(Long markupCurrencyId) {
        this.markupCurrencyId = markupCurrencyId;
    }

    public BigDecimal getMarkupFixed() {
        return markupFixed;
    }

    public void setMarkupFixed(BigDecimal markupFixed) {
        this.markupFixed = markupFixed;
    }

    public BigDecimal getUnits() {
        return units;
    }

    public void setUnits(BigDecimal units) {
        this.units = units;
    }

    public BigDecimal getRates() {
        return rates;
    }

    public void setRates(BigDecimal rates) {
        this.rates = rates;
    }
}
