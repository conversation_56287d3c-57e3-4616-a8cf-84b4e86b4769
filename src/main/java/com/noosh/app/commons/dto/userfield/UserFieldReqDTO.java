package com.noosh.app.commons.dto.userfield;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 9/9/2022
 */
public class UserFieldReqDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -9191719940164056054L;

    @JsonProperty(required = true)
    @NotNull(message = "parameter 'workgroupId' is required")
    private Long workgroupId;

    @JsonProperty(required = true)
    @NotNull(message = "parameter 'fieldClassId' is required")
    private Long fieldClassId;

    private List<Long> propertyIds;

    private Boolean isSupplierView;

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getFieldClassId() {
        return fieldClassId;
    }

    public void setFieldClassId(Long fieldClassId) {
        this.fieldClassId = fieldClassId;
    }

    public List<Long> getPropertyIds() {
        return propertyIds;
    }

    public void setPropertyIds(List<Long> propertyIds) {
        this.propertyIds = propertyIds;
    }

    public Boolean getIsSupplierView() {
        return isSupplierView;
    }

    public void setIsSupplierView(Boolean isSupplierView) {
        this.isSupplierView = isSupplierView;
    }
}
