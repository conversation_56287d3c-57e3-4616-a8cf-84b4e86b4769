package com.noosh.app.commons.dto.routing;

import com.noosh.app.commons.dto.account.AccountUserDTO;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * User: lukez
 * Date: 6/29/22
 */
public class RoutingRecipientDTO implements Serializable {

    private static final long serialVersionUID = -8246752783048413231L;

    private Long id;

    private Long psRoutingSlipId;

    private Long toUserId;

    private Long routingOrder;

    private Long response;

    private LocalDateTime dateRouted;

    private LocalDateTime dateResponded;

    private Long retriesRemaining;

    private String comments;

    private AccountUserDTO toUser;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPsRoutingSlipId() {
        return this.psRoutingSlipId;
    }

    public void setPsRoutingSlipId(Long psRoutingSlipId) {
        this.psRoutingSlipId = psRoutingSlipId;
    }

    public Long getToUserId() {
        return this.toUserId;
    }

    public void setToUserId(Long toUserId) {
        this.toUserId = toUserId;
    }

    public Long getRoutingOrder() {
        return this.routingOrder;
    }

    public void setRoutingOrder(Long routingOrder) {
        this.routingOrder = routingOrder;
    }

    public Long getResponse() {
        return response == null ? RoutingSlipDTO.RS_INDETERMINATE : response;
    }

    public void setResponse(Long response) {
        this.response = response;
    }

    public LocalDateTime getDateRouted() {
        return this.dateRouted;
    }

    public void setDateRouted(LocalDateTime dateRouted) {
        this.dateRouted = dateRouted;
    }

    public LocalDateTime getDateResponded() {
        return this.dateResponded;
    }

    public void setDateResponded(LocalDateTime dateResponded) {
        this.dateResponded = dateResponded;
    }

    public Long getRetriesRemaining() {
        return this.retriesRemaining;
    }

    public void setRetriesRemaining(Long retriesRemaining) {
        this.retriesRemaining = retriesRemaining;
    }

    public String getComments() {
        return this.comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public AccountUserDTO getToUser() {
        return this.toUser;
    }

    public void setToUser(AccountUserDTO toUser) {
        this.toUser = toUser;
    }


}
