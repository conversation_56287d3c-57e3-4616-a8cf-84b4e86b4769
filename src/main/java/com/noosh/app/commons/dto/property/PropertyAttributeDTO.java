package com.noosh.app.commons.dto.property;

import com.noosh.app.commons.constant.DataTypeID;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class PropertyAttributeDTO implements Serializable {
    private static final long serialVersionUID = 4455600318184534266L;
    private Long prPropertyId;
    private Long prPropertyAttributeId;

    private BigDecimal numberValue;

    private LocalDateTime dateValue;

    private String stringValue;

    private Long prDataTypeId;

    private String paramName;

    private Object value;

    public PropertyAttributeDTO() {
    }

    public PropertyAttributeDTO(Long prPropertyId,
                                Long prPropertyAttributeId, BigDecimal numberValue, LocalDateTime dateValue, String stringValue,
                                Long prDataTypeId, String paramName) {
        this.prPropertyId = prPropertyId;
        this.prPropertyAttributeId = prPropertyAttributeId;
        this.numberValue = numberValue;
        this.dateValue = dateValue;
        this.stringValue = stringValue;
        this.prDataTypeId = prDataTypeId;
        this.paramName = paramName;
    }


    public Long getPrPropertyId() {
        return prPropertyId;
    }

    public void setPrPropertyId(Long prPropertyId) {
        this.prPropertyId = prPropertyId;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public Long getPrPropertyAttributeId() {
        return prPropertyAttributeId;
    }

    public void setPrPropertyAttributeId(Long prPropertyAttributeId) {
        this.prPropertyAttributeId = prPropertyAttributeId;
    }

    public BigDecimal getNumberValue() {
        return numberValue;
    }

    public void setNumberValue(BigDecimal numberValue) {
        this.numberValue = numberValue;
    }

    public LocalDateTime getDateValue() {
        return dateValue;
    }

    public void setDateValue(LocalDateTime dateValue) {
        this.dateValue = dateValue;
    }

    public String getStringValue() {
        return stringValue;
    }

    public void setStringValue(String stringValue) {
        this.stringValue = stringValue;
    }

    public Long getPrDataTypeId() {
        return prDataTypeId;
    }

    public void setPrDataTypeId(Long prDataTypeId) {
        this.prDataTypeId = prDataTypeId;
    }

    public Object getValue() {
        long dataTypeId = this.getPrDataTypeId();
        switch ((int) dataTypeId) {
            case (int) DataTypeID.STRING:
                return this.getStringValue();

            case (int) DataTypeID.LONG:
                return this.getNumberValue();

            case (int) DataTypeID.DOUBLE:
                return this.getNumberValue();

            case (int) DataTypeID.DATE:
                return this.getDateValue();

            case (int) DataTypeID.BOOLEAN:
                return this.getNumberValue() == null ?
                        false :
                        this.getNumberValue().longValue() == 1;

            default: //DataTypeID.STRING:
                return this.getStringValue();
        }
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
