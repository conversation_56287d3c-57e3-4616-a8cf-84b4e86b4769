package com.noosh.app.commons.dto.proposal;

import com.noosh.app.commons.entity.breakout.Breakout;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 11/1/2021
 */
public class CategoryDTO {

    private Long categoryId;
    private String name;
    private String description;
    private Long categoryClassId;
    private Long objectId;

    // proposal pdf template
    private Map<String, Breakout> breakoutMap;
    private List<Long> qtyList;
    private Map<String, List<BigDecimal>> priceMap;
    private List<BigDecimal> itemPrices;
    private List<BigDecimal> taxs;
    private List<BigDecimal> shippings;
    private List<BigDecimal> totalPrices;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCategoryClassId() {
        return categoryClassId;
    }

    public void setCategoryClassId(Long categoryClassId) {
        this.categoryClassId = categoryClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Map<String, Breakout> getBreakoutMap() {
        return breakoutMap;
    }

    public void setBreakoutMap(Map<String, Breakout> breakoutMap) {
        this.breakoutMap = breakoutMap;
    }

    public List<Long> getQtyList() {
        return qtyList;
    }

    public void setQtyList(List<Long> qtyList) {
        this.qtyList = qtyList;
    }

    public Map<String, List<BigDecimal>> getPriceMap() {
        return priceMap;
    }

    public void setPriceMap(Map<String, List<BigDecimal>> priceMap) {
        this.priceMap = priceMap;
    }

    public List<BigDecimal> getItemPrices() {
        return itemPrices;
    }

    public void setItemPrices(List<BigDecimal> itemPrices) {
        this.itemPrices = itemPrices;
    }

    public List<BigDecimal> getTaxs() {
        return taxs;
    }

    public void setTaxs(List<BigDecimal> taxs) {
        this.taxs = taxs;
    }

    public List<BigDecimal> getShippings() {
        return shippings;
    }

    public void setShippings(List<BigDecimal> shippings) {
        this.shippings = shippings;
    }

    public List<BigDecimal> getTotalPrices() {
        return totalPrices;
    }

    public void setTotalPrices(List<BigDecimal> totalPrices) {
        this.totalPrices = totalPrices;
    }
}
