package com.noosh.app.commons.dto.proposal;

/**
 * <AUTHOR>
 * @date 11/21/2021
 */
public class SpecSummaryDTO {

    private Long specSummaryId;
    private String name;
    private String iconUrl;
    private Long specTypeId;
    private Long specTypeLabelStrId;
    private String specTypeLabelStr;
    private boolean isDefault;

    public Long getSpecSummaryId() {
        return specSummaryId;
    }

    public void setSpecSummaryId(Long specSummaryId) {
        this.specSummaryId = specSummaryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public Long getSpecTypeId() {
        return specTypeId;
    }

    public void setSpecTypeId(Long specTypeId) {
        this.specTypeId = specTypeId;
    }

    public Long getSpecTypeLabelStrId() {
        return specTypeLabelStrId;
    }

    public void setSpecTypeLabelStrId(Long specTypeLabelStrId) {
        this.specTypeLabelStrId = specTypeLabelStrId;
    }

    public String getSpecTypeLabelStr() {
        return specTypeLabelStr;
    }

    public void setSpecTypeLabelStr(String specTypeLabelStr) {
        this.specTypeLabelStr = specTypeLabelStr;
    }

    public boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public String getIconHref() {
        String icon = getIconUrl();
        if (icon == null) {
            icon = "images/spec/other.gif";
        }
        return icon;
    }
}
