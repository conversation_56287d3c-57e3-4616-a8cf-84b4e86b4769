package com.noosh.app.commons.dto.invoice;

import java.time.LocalDateTime;

/**
 * User: leilaz
 * Date: 12/1/17
 */
public class RequestDTO {
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long shipmentId;
    private String phone;
    private String company;
    private Long addressId;
    private LocalDateTime deliveryDate;
    private Long carrierId;
    private String carrierOther;
    private Long methodId;
    private String methodOther;
    private Long quantity;
    private String instruction;
    private String email;
    private String firstName;
    private String lastName;
    private String middleName;
    private String description;
    private Long requestTypeId;
    private Long propertyId;
    private Long ordinalNumber;
    private Boolean useSpecPackaging;
    private String workgroupName;
    private Long requesteeUserId;
    private Long whLocationId;
    private Long contactId;
    private Long sourceTypeId;
    private Long invoiceItemId;
    private String shipmentRecord;
    private LocalDateTime shippedDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShipmentId() {
        return shipmentId;
    }

    public void setShipmentId(Long shipmentId) {
        this.shipmentId = shipmentId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public LocalDateTime getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(LocalDateTime deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getCarrierOther() {
        return carrierOther;
    }

    public void setCarrierOther(String carrierOther) {
        this.carrierOther = carrierOther;
    }

    public Long getMethodId() {
        return methodId;
    }

    public void setMethodId(Long methodId) {
        this.methodId = methodId;
    }

    public String getMethodOther() {
        return methodOther;
    }

    public void setMethodOther(String methodOther) {
        this.methodOther = methodOther;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public String getInstruction() {
        return instruction;
    }

    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getRequestTypeId() {
        return requestTypeId;
    }

    public void setRequestTypeId(Long requestTypeId) {
        this.requestTypeId = requestTypeId;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public Long getOrdinalNumber() {
        return ordinalNumber;
    }

    public void setOrdinalNumber(Long ordinalNumber) {
        this.ordinalNumber = ordinalNumber;
    }

    public boolean getUseSpecPackaging() {
        return useSpecPackaging != null && useSpecPackaging.booleanValue();
    }

    public void setUseSpecPackaging(Boolean useSpecPackaging) {
        this.useSpecPackaging = useSpecPackaging;
    }

    public String getWorkgroupName() {
        return workgroupName;
    }

    public void setWorkgroupName(String workgroupName) {
        this.workgroupName = workgroupName;
    }

    public Long getRequesteeUserId() {
        return requesteeUserId;
    }

    public void setRequesteeUserId(Long requesteeUserId) {
        this.requesteeUserId = requesteeUserId;
    }

    public Long getWhLocationId() {
        return whLocationId;
    }

    public void setWhLocationId(Long whLocationId) {
        this.whLocationId = whLocationId;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public Long getInvoiceItemId() {
        return invoiceItemId;
    }

    public void setInvoiceItemId(Long invoiceItemId) {
        this.invoiceItemId = invoiceItemId;
    }

    public String getShipmentRecord() {
        return shipmentRecord;
    }

    public void setShipmentRecord(String shipmentRecord) {
        this.shipmentRecord = shipmentRecord;
    }

    public LocalDateTime getShippedDate() {
        return shippedDate;
    }

    public void setShippedDate(LocalDateTime shippedDate) {
        this.shippedDate = shippedDate;
    }
}
