package com.noosh.app.commons.dto.rating;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 1/20/19
 */
public class SupplierRatingDTO implements Serializable {
    private static final long serialVersionUID = 7771281972934566616L;

    private Long supplierWorkgroupId;

    private Long projectId;

    private boolean isRateByOrder;

    private boolean isCreate;

    private Long orderId;

    private Long createUserId;

    private Long currentWorkgroupId;

    private List<SupplierRatingOrderDTO> rateByOrder;

    private List<SupplierRatingOrderItemDTO> rateByOrderItem;

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Long getCurrentWorkgroupId() {
        return currentWorkgroupId;
    }

    public void setCurrentWorkgroupId(Long currentWorkgroupId) {
        this.currentWorkgroupId = currentWorkgroupId;
    }

    public boolean getIsCreate() {
        return isCreate;
    }

    public void setIsCreate(boolean create) {
        isCreate = create;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public boolean getIsRateByOrder() {
        return isRateByOrder;
    }

    public void setIsRateByOrder(boolean rateByOrder) {
        isRateByOrder = rateByOrder;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public List<SupplierRatingOrderDTO> getRateByOrder() {
        return rateByOrder;
    }

    public void setRateByOrder(List<SupplierRatingOrderDTO> rateByOrder) {
        this.rateByOrder = rateByOrder;
    }

    public List<SupplierRatingOrderItemDTO> getRateByOrderItem() {
        return rateByOrderItem;
    }

    public void setRateByOrderItem(List<SupplierRatingOrderItemDTO> rateByOrderItem) {
        this.rateByOrderItem = rateByOrderItem;
    }
}
