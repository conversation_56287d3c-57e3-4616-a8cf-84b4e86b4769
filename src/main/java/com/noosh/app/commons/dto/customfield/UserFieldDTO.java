package com.noosh.app.commons.dto.customfield;

import java.time.LocalDateTime;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 5/11/16
 */
public class UserFieldDTO implements Serializable {
    private static final long serialVersionUID = 5020805521281937008L;

    private Long id;

    private Long prPropertyAttributeId;

    private BigDecimal numberValue;

    private LocalDateTime dateValue;

    private String stringValue;

    private Long prDataTypeId;

    private String paramName;

    private String fieldValues;

    private String label;

    private Long customFieldControlId;

    private Long paramId;

    private String attributes;

    private Boolean isRequired;

    private Boolean includeInTotal;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean required) {
        isRequired = required;
    }

    public Boolean getIncludeInTotal() {
        return this.includeInTotal;
    }

    public void setIncludeInTotal(Boolean includeInTotal) {
        this.includeInTotal = includeInTotal;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public Long getParamId() {
        return paramId;
    }

    public void setParamId(Long paramId) {
        this.paramId = paramId;
    }

    public Long getCustomFieldControlId() {
        return customFieldControlId;
    }

    public void setCustomFieldControlId(Long customFieldControlId) {
        this.customFieldControlId = customFieldControlId;
    }

    public String getFieldValues() {
        return fieldValues;
    }

    public void setFieldValues(String fieldValues) {
        this.fieldValues = fieldValues;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Long getPrPropertyAttributeId() {
        return prPropertyAttributeId;
    }

    public void setPrPropertyAttributeId(Long prPropertyAttributeId) {
        this.prPropertyAttributeId = prPropertyAttributeId;
    }

    public BigDecimal getNumberValue() {
        return numberValue;
    }

    public void setNumberValue(BigDecimal numberValue) {
        this.numberValue = numberValue;
    }

    public LocalDateTime getDateValue() {
        return dateValue;
    }

//    public void setDateValueWithLocalDate(LocalDate dateValue) {
//        this.dateValue = dateValue;
//    }

    public void setDateValue(LocalDateTime dateValue) {
        this.dateValue = dateValue;
    }

    public String getStringValue() {
        return stringValue;
    }

    public void setStringValue(String stringValue) {
        this.stringValue = stringValue;
    }

    public Long getPrDataTypeId() {
        return prDataTypeId;
    }

    public void setPrDataTypeId(Long prDataTypeId) {
        this.prDataTypeId = prDataTypeId;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }
}
