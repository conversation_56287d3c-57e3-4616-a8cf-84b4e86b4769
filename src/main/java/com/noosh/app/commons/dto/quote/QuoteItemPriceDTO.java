package com.noosh.app.commons.dto.quote;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 3/28/19
 */
public class QuoteItemPriceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Double markupPercent;
    private BigDecimal markupFixed;

    public Double getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(Double markupPercent) {
        this.markupPercent = markupPercent;
    }

    public BigDecimal getMarkupFixed() {
        return markupFixed;
    }

    public void setMarkupFixed(BigDecimal markupFixed) {
        this.markupFixed = markupFixed;
    }
}
