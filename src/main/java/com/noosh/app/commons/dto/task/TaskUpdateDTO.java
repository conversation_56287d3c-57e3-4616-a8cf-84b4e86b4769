package com.noosh.app.commons.dto.task;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/28/15
 * Time: 11:00 PM
 */
public class TaskUpdateDTO implements Serializable {
    private static final long serialVersionUID = -5622353766297253960L;
    private String revisedStartDate = "-1";
    private String revisedEndDate = "-1";
    private String actualStartDate = "-1";
    private String actualEndDate = "-1";
    private String recordDate = "-1";
    private Double hours = (double) -1;
    private Long taskStatusId = (long) -1;
    private Long taskId = (long) -1;
    private Long projectId = (long) -1;
    private String projectName = "-1";
    private String taskTitle = "-1";
    private Long readStatusFlag = (long) -1;
    private Long taskTypeId = (long) -1;
    private String description = "-1";
    private Long assignedToUserId = (long) -1;
    private List<Long> contributors = new ArrayList<>();
    private Long customTaskStatus = (long) -1;
    private Long priorityId = (long) -1;
    private String comments = "-1";
    private Long percentComplete = (long) -1;
    private Double revisedDuration = (double) -1;
    private List<TaskRecordTime> recordTimeCard = new ArrayList<>();

    public Long getPercentComplete() {
        return percentComplete;
    }

    public void setPercentComplete(Long percentComplete) {
        this.percentComplete = percentComplete;
    }

    public List<TaskRecordTime> getRecordTimeCard() {
        return recordTimeCard;
    }

    public void setRecordTimeCard(List<TaskRecordTime> recordTimeCard) {
        this.recordTimeCard = recordTimeCard;
    }

    public Double getRevisedDuration() {
        return revisedDuration;
    }

    public void setRevisedDuration(Double revisedDuration) {
        this.revisedDuration = revisedDuration;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getAssignedToUserId() {
        return assignedToUserId;
    }

    public void setAssignedToUserId(Long assignedToUserId) {
        this.assignedToUserId = assignedToUserId;
    }

    public List<Long> getContributors() {
        return contributors;
    }

    public void setContributors(List<Long> contributors) {
        this.contributors = contributors;
    }

    public Long getCustomTaskStatus() {
        return customTaskStatus;
    }

    public void setCustomTaskStatus(Long customTaskStatus) {
        this.customTaskStatus = customTaskStatus;
    }

    public Long getPriorityId() {
        return priorityId;
    }

    public void setPriorityId(Long priorityId) {
        this.priorityId = priorityId;
    }

    public Long getTaskTypeId() {
        return taskTypeId;
    }

    public void setTaskTypeId(Long taskTypeId) {
        this.taskTypeId = taskTypeId;
    }

    public Long getReadStatusFlag() {
        return readStatusFlag;
    }

    public void setReadStatusFlag(Long readStatusFlag) {
        this.readStatusFlag = readStatusFlag;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getTaskTitle() {
        return taskTitle;
    }

    public void setTaskTitle(String taskTitle) {
        this.taskTitle = taskTitle;
    }

    public String getRevisedEndDate() {
        return revisedEndDate;
    }

    public void setRevisedEndDate(String revisedEndDate) {
        this.revisedEndDate = revisedEndDate;
    }

    public String getActualStartDate() {
        return actualStartDate;
    }

    public void setActualStartDate(String actualStartDate) {
        this.actualStartDate = actualStartDate;
    }

    public String getActualEndDate() {
        return actualEndDate;
    }

    public void setActualEndDate(String actualEndDate) {
        this.actualEndDate = actualEndDate;
    }

    public String getRevisedStartDate() {
        return revisedStartDate;
    }

    public void setRevisedStartDate(String revisedStartDate) {
        this.revisedStartDate = revisedStartDate;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getRecordDate() {
        return recordDate;
    }

    public void setRecordDate(String recordDate) {
        this.recordDate = recordDate;
    }

    public Double getHours() {
        return hours;
    }

    public void setHours(Double hours) {
        this.hours = hours;
    }

    public Long getTaskStatusId() {
        return taskStatusId;
    }

    public void setTaskStatusId(Long taskStatusId) {
        this.taskStatusId = taskStatusId;
    }
}
