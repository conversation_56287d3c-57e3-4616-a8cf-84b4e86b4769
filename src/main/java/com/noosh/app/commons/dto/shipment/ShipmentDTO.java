package com.noosh.app.commons.dto.shipment;

import com.noosh.app.commons.dto.job.PcJobDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 10/8/17
 */
public class ShipmentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long shShipmentId;

    private String name;

    private String comments;

    private Long ownerAcWorkgroupId;

    private Long customPrPropertyId;

    private Long ocObjectStateId;

    private Long pcJobId;

    private Short isTemplate;


    private ProjectDTO parent;

    private PcJobDTO job;

    private long totalReqQty;

    private double totalShippedQty;

    private double totalReceivedQty;

    private List<ShRequestDTO> requests;

    public long getTotalReqQty() {
        return totalReqQty;
    }

    public void setTotalReqQty(long totalReqQty) {
        this.totalReqQty = totalReqQty;
    }

    public double getTotalShippedQty() {
        return totalShippedQty;
    }

    public void setTotalShippedQty(double totalShippedQty) {
        this.totalShippedQty = totalShippedQty;
    }

    public double getTotalReceivedQty() {
        return totalReceivedQty;
    }

    public void setTotalReceivedQty(double totalReceivedQty) {
        this.totalReceivedQty = totalReceivedQty;
    }

    public PcJobDTO getJob() {
        return job;
    }

    public void setJob(PcJobDTO job) {
        this.job = job;
    }

    public ProjectDTO getParent() {
        return parent;
    }

    public void setParent(ProjectDTO parent) {
        this.parent = parent;
    }

    public Long getShShipmentId() {
        return shShipmentId;
    }

    public void setShShipmentId(Long shShipmentId) {
        this.shShipmentId = shShipmentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getCustomPrPropertyId() {
        return customPrPropertyId;
    }

    public void setCustomPrPropertyId(Long customPrPropertyId) {
        this.customPrPropertyId = customPrPropertyId;
    }

    public Long getOwnerAcWorkgroupId() {
        return ownerAcWorkgroupId;
    }

    public void setOwnerAcWorkgroupId(Long ownerAcWorkgroupId) {
        this.ownerAcWorkgroupId = ownerAcWorkgroupId;
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }

    public Long getPcJobId() {
        return pcJobId;
    }

    public void setPcJobId(Long pcJobId) {
        this.pcJobId = pcJobId;
    }

    public Short getIsTemplate() {
        return isTemplate;
    }

    public void setIsTemplate(Short template) {
        isTemplate = template;
    }

    public List<ShRequestDTO> getRequests() {
        return requests;
    }

    public void setRequests(List<ShRequestDTO> requests) {
        this.requests = requests;
    }
}
