package com.noosh.app.commons.dto.contacts;

import com.noosh.app.commons.dto.security.AddressDTO;
import com.noosh.app.commons.vo.contacts.ContactVO;
import com.noosh.app.commons.vo.address.AddressVO;

/**
 * Contact Data Transfer Object for Invoice Administration
 * Based on ContactBean structure for API communication
 */
public class ContactDTO {
    public Long id;
    public String firstName;
    public String lastName;
    public String middleName;
    public String title;
    public String phoneNumber1;
    public String phoneNumber2;
    public String mobileNumber;
    public String pagerNumber;
    public String faxNumber;
    public String email;
    public String website;
    public String companyName;
    public Long addressId;
    public String contactNotes;
    public Long ownerTypeId;
    public Long ownerObjectId;
    public Boolean isLiveUser;
    public Long contactUserId;
    public Long customPropertyId;
    public Boolean isSystemGenerated;
    public Long contactTypeId;
    public Boolean isLocked;
    public Boolean isObsolete;
    public String workgroupName;
    private AddressDTO address;

    public ContactDTO() {}


    /**
     * Get full name in "lastname, firstname" format
     */
    public String getFullName() {
        return getFullName(", ");
    }

    public String getFullName(String separator) {
        if (lastName != null && firstName != null) {
            return lastName + separator + firstName;
        } else if (lastName != null) {
            return lastName;
        } else if (firstName != null) {
            return firstName;
        }
        return null;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getMiddleName() { return middleName; }
    public void setMiddleName(String middleName) { this.middleName = middleName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getPhoneNumber1() { return phoneNumber1; }
    public void setPhoneNumber1(String phoneNumber1) { this.phoneNumber1 = phoneNumber1; }

    public String getPhoneNumber2() { return phoneNumber2; }
    public void setPhoneNumber2(String phoneNumber2) { this.phoneNumber2 = phoneNumber2; }

    public String getMobileNumber() { return mobileNumber; }
    public void setMobileNumber(String mobileNumber) { this.mobileNumber = mobileNumber; }

    public String getPagerNumber() { return pagerNumber; }
    public void setPagerNumber(String pagerNumber) { this.pagerNumber = pagerNumber; }

    public String getFaxNumber() { return faxNumber; }
    public void setFaxNumber(String faxNumber) { this.faxNumber = faxNumber; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getWebsite() { return website; }
    public void setWebsite(String website) { this.website = website; }

    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }

    public String getContactNotes() { return contactNotes; }
    public void setContactNotes(String contactNotes) { this.contactNotes = contactNotes; }

    public Long getOwnerTypeId() { return ownerTypeId; }
    public void setOwnerTypeId(Long ownerTypeId) { this.ownerTypeId = ownerTypeId; }

    public Long getOwnerObjectId() { return ownerObjectId; }
    public void setOwnerObjectId(Long ownerObjectId) { this.ownerObjectId = ownerObjectId; }

    public Boolean getIsLiveUser() { return isLiveUser; }
    public void setIsLiveUser(Boolean isLiveUser) { this.isLiveUser = isLiveUser; }

    public Long getContactUserId() { return contactUserId; }
    public void setContactUserId(Long contactUserId) { this.contactUserId = contactUserId; }

    public Long getCustomPropertyId() { return customPropertyId; }
    public void setCustomPropertyId(Long customPropertyId) { this.customPropertyId = customPropertyId; }

    public Boolean getIsSystemGenerated() { return isSystemGenerated; }
    public void setIsSystemGenerated(Boolean isSystemGenerated) { this.isSystemGenerated = isSystemGenerated; }

    public Long getContactTypeId() { return contactTypeId; }
    public void setContactTypeId(Long contactTypeId) { this.contactTypeId = contactTypeId; }

    public Boolean getIsLocked() { return isLocked; }
    public void setIsLocked(Boolean isLocked) { this.isLocked = isLocked; }

    public Boolean getIsObsolete() { return isObsolete; }
    public void setIsObsolete(Boolean isObsolete) { this.isObsolete = isObsolete; }

    public String getWorkgroupName() { return workgroupName; }
    public void setWorkgroupName(String workgroupName) { this.workgroupName = workgroupName; }

    public AddressDTO getAddress() { return address; }
    public void setAddress(AddressDTO address) { this.address = address; }
}