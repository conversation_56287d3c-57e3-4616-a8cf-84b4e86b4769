package com.noosh.app.commons.dto.costcenter;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 11/29/21
 */
public class CostCenterDetailUpdateDTO implements Serializable {
    private static final long serialVersionUID = 6759259144995965415L;

    private Long objectId;

    private Long objectClassId;

    private boolean isTax;

    private boolean isShipping;

    private List<CostCenterAllocationUpdateDTO> costCenters;

    public boolean getIsTax() {
        return isTax;
    }

    public void setIsTax(boolean tax) {
        isTax = tax;
    }

    public boolean getIsShipping() {
        return isShipping;
    }

    public void setIsShipping(boolean shipping) {
        isShipping = shipping;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public List<CostCenterAllocationUpdateDTO> getCostCenters() {
        return costCenters;
    }

    public void setCostCenters(List<CostCenterAllocationUpdateDTO> costCenters) {
        this.costCenters = costCenters;
    }
}
