package com.noosh.app.commons.dto.order;


import com.noosh.app.commons.dto.account.AccountUserDTO;
import com.noosh.app.commons.dto.security.ObjectStateDTO;

import java.time.LocalDateTime;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 6/30/16
 */
public class OrderStateDTO implements Serializable {
    private static final long serialVersionUID = -5192062132777958712L;
    private Long id;

    private Long orderId;

    private Long objectStateId;

    private Boolean isCurrent;

    private Long origOrderId;

    private String comments;

    private Long stateCreateUserId;

    private LocalDateTime stateCreateDate;

    private LocalDateTime lastChangeDate;

    private Long createUserId;

    private LocalDateTime createDate;

    private Long modUserId;

    private LocalDateTime modDate;

    private Long taskId;

    private String orderState;

    private AccountUserDTO creator;

    private ObjectStateDTO objectState;

    public String getOrderState() {
        return orderState;
    }

    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public Boolean getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Boolean current) {
        isCurrent = current;
    }

    public Long getOrigOrderId() {
        return origOrderId;
    }

    public void setOrigOrderId(Long origOrderId) {
        this.origOrderId = origOrderId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getStateCreateDate() {
        return stateCreateDate;
    }

    public void setStateCreateDate(LocalDateTime stateCreateDate) {
        this.stateCreateDate = stateCreateDate;
    }

    public Long getStateCreateUserId() {
        return stateCreateUserId;
    }

    public void setStateCreateUserId(Long stateCreateUserId) {
        this.stateCreateUserId = stateCreateUserId;
    }

    public LocalDateTime getLastChangeDate() {
        return lastChangeDate;
    }

    public void setLastChangeDate(LocalDateTime lastChangeDate) {
        this.lastChangeDate = lastChangeDate;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public Long getModUserId() {
        return modUserId;
    }

    public void setModUserId(Long modUserId) {
        this.modUserId = modUserId;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public AccountUserDTO getCreator() {
        return creator;
    }

    public void setCreator(AccountUserDTO creator) {
        this.creator = creator;
    }

    public ObjectStateDTO getObjectState() {
        return this.objectState;
    }

    public void setObjectState(ObjectStateDTO objectState) {
        this.objectState = objectState;
    }
}
