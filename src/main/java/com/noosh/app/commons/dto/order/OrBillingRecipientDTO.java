package com.noosh.app.commons.dto.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/8/19
 */
public class OrBillingRecipientDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String contactName;

    private String comments;

    private Long contactId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }
}
