package com.noosh.app.commons.dto.custom;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 11/28/17
 */
public class CustomFieldDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long customFieldClassId;

    private Long customFieldControlId;

    private Long ownerWorkgroupId;

    private Long ordinalNumber;

    private String label;

    private Boolean isRequired;

    private String fieldValues;

    private String attributes;

    private Boolean includeInTotal;

    private Long propertyParamId;

    private CustomFieldControlDTO customFieldControl;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCustomFieldClassId() {
        return customFieldClassId;
    }

    public void setCustomFieldClassId(Long customFieldClassId) {
        this.customFieldClassId = customFieldClassId;
    }

    public Long getCustomFieldControlId() {
        return customFieldControlId;
    }

    public void setCustomFieldControlId(Long customFieldControlId) {
        this.customFieldControlId = customFieldControlId;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getOrdinalNumber() {
        return ordinalNumber;
    }

    public void setOrdinalNumber(Long ordinalNumber) {
        this.ordinalNumber = ordinalNumber;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Boolean getRequired() {
        return isRequired;
    }

    public void setRequired(Boolean required) {
        isRequired = required;
    }

    public String getFieldValues() {
        return fieldValues;
    }

    public void setFieldValues(String fieldValues) {
        this.fieldValues = fieldValues;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public Boolean getIncludeInTotal() {
        return includeInTotal;
    }

    public void setIncludeInTotal(Boolean includeInTotal) {
        this.includeInTotal = includeInTotal;
    }

    public Long getPropertyParamId() {
        return propertyParamId;
    }

    public void setPropertyParamId(Long propertyParamId) {
        this.propertyParamId = propertyParamId;
    }

    public CustomFieldControlDTO getCustomFieldControl() {
        return customFieldControl;
    }

    public void setCustomFieldControl(CustomFieldControlDTO customFieldControl) {
        this.customFieldControl = customFieldControl;
    }
}
