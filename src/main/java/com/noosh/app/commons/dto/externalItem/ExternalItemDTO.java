package com.noosh.app.commons.dto.externalItem;

import com.noosh.app.commons.dto.property.PropertyAttributeDTO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * User: leilaz
 * Date: 3/31/22
 */
public class ExternalItemDTO implements Serializable {
    private static final long serialVersionUID = 3730446538947173009L;

    private Long id;

    private Long objectId;

    private Long objectClassId;

    private Long specId;

    private Long itemPropertyId;

    private Boolean isManuallyCreated;

    private Long itemIndex;

    private String description;

    private String sku;

    private String qtyUom;

    private Long qtyUomFactor;

    private String priceUom;

    private Long priceUomFactor;

    private java.math.BigDecimal unitPrice;

    private Long unitPriceCurrencyId;

    private java.math.BigDecimal minUomQty;

    private Long qtyIncrement;

    private String uomFactors;

    private Long forQty1;

    private BigDecimal qty1;

    private java.math.BigDecimal price1;

    private Long price1CurrencyId;

    private Long forQty2;

    private BigDecimal qty2;

    private java.math.BigDecimal price2;

    private Long price2CurrencyId;

    private Long forQty3;

    private BigDecimal qty3;

    private java.math.BigDecimal price3;

    private Long price3CurrencyId;

    private Long forQty4;

    private BigDecimal qty4;

    private java.math.BigDecimal price4;

    private Long price4CurrencyId;

    private Long forQty5;

    private BigDecimal qty5;

    private java.math.BigDecimal price5;

    private Long price5CurrencyId;

    private java.math.BigDecimal unitPrice1;

    private Long unitPrice1CurrencyId;

    private String up1Uom;

    private java.math.BigDecimal unitPrice2;

    private Long unitPrice2CurrencyId;

    private String up2Uom;

    private java.math.BigDecimal unitPrice3;

    private Long unitPrice3CurrencyId;

    private String up3Uom;

    private java.math.BigDecimal unitPrice4;

    private Long unitPrice4CurrencyId;

    private String up4Uom;

    private java.math.BigDecimal unitPrice5;

    private Long unitPrice5CurrencyId;

    private String up5Uom;

    private Float paperWeight1;

    private Float paperWeight2;

    private Float paperWeight3;

    private Float paperWeight4;

    private Float paperWeight5;

    private BigDecimal actualQty1;

    private BigDecimal actualQty2;

    private BigDecimal actualQty3;

    private BigDecimal actualQty4;

    private BigDecimal actualQty5;

    private BigDecimal pricePerTon;

    private Long pricePerTonCurrencyId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getItemPropertyId() {
        return itemPropertyId;
    }

    public void setItemPropertyId(Long itemPropertyId) {
        this.itemPropertyId = itemPropertyId;
    }

    public Boolean getIsManuallyCreated() {
        return isManuallyCreated == null ? false : isManuallyCreated;
    }

    public void setIsManuallyCreated(Boolean manuallyCreated) {
        isManuallyCreated = manuallyCreated;
    }

    public Long getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Long itemIndex) {
        this.itemIndex = itemIndex;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getQtyUom() {
        return qtyUom;
    }

    public void setQtyUom(String qtyUom) {
        this.qtyUom = qtyUom;
    }

    public Long getQtyUomFactor() {
        return qtyUomFactor;
    }

    public void setQtyUomFactor(Long qtyUomFactor) {
        this.qtyUomFactor = qtyUomFactor;
    }

    public String getPriceUom() {
        return priceUom;
    }

    public void setPriceUom(String priceUom) {
        this.priceUom = priceUom;
    }

    public Long getPriceUomFactor() {
        return priceUomFactor;
    }

    public void setPriceUomFactor(Long priceUomFactor) {
        this.priceUomFactor = priceUomFactor;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Long getUnitPriceCurrencyId() {
        return unitPriceCurrencyId;
    }

    public void setUnitPriceCurrencyId(Long unitPriceCurrencyId) {
        this.unitPriceCurrencyId = unitPriceCurrencyId;
    }

    public BigDecimal getMinUomQty() {
        return minUomQty;
    }

    public void setMinUomQty(BigDecimal minUomQty) {
        this.minUomQty = minUomQty;
    }

    public Long getQtyIncrement() {
        return qtyIncrement;
    }

    public void setQtyIncrement(Long qtyIncrement) {
        this.qtyIncrement = qtyIncrement;
    }

    public String getUomFactors() {
        return uomFactors;
    }

    public void setUomFactors(String uomFactors) {
        this.uomFactors = uomFactors;
    }

    public Long getForQty1() {
        return forQty1;
    }

    public void setForQty1(Long forQty1) {
        this.forQty1 = forQty1;
    }

    public BigDecimal getQty1() {
        return qty1;
    }

    public void setQty1(BigDecimal qty1) {
        this.qty1 = qty1;
    }

    public BigDecimal getPrice1() {
        return price1;
    }

    public void setPrice1(BigDecimal price1) {
        this.price1 = price1;
    }

    public Long getPrice1CurrencyId() {
        return price1CurrencyId;
    }

    public void setPrice1CurrencyId(Long price1CurrencyId) {
        this.price1CurrencyId = price1CurrencyId;
    }

    public Long getForQty2() {
        return forQty2;
    }

    public void setForQty2(Long forQty2) {
        this.forQty2 = forQty2;
    }

    public BigDecimal getQty2() {
        return qty2;
    }

    public void setQty2(BigDecimal qty2) {
        this.qty2 = qty2;
    }

    public BigDecimal getPrice2() {
        return price2;
    }

    public void setPrice2(BigDecimal price2) {
        this.price2 = price2;
    }

    public Long getPrice2CurrencyId() {
        return price2CurrencyId;
    }

    public void setPrice2CurrencyId(Long price2CurrencyId) {
        this.price2CurrencyId = price2CurrencyId;
    }

    public Long getForQty3() {
        return forQty3;
    }

    public void setForQty3(Long forQty3) {
        this.forQty3 = forQty3;
    }

    public BigDecimal getQty3() {
        return qty3;
    }

    public void setQty3(BigDecimal qty3) {
        this.qty3 = qty3;
    }

    public BigDecimal getPrice3() {
        return price3;
    }

    public void setPrice3(BigDecimal price3) {
        this.price3 = price3;
    }

    public Long getPrice3CurrencyId() {
        return price3CurrencyId;
    }

    public void setPrice3CurrencyId(Long price3CurrencyId) {
        this.price3CurrencyId = price3CurrencyId;
    }

    public Long getForQty4() {
        return forQty4;
    }

    public void setForQty4(Long forQty4) {
        this.forQty4 = forQty4;
    }

    public BigDecimal getQty4() {
        return qty4;
    }

    public void setQty4(BigDecimal qty4) {
        this.qty4 = qty4;
    }

    public BigDecimal getPrice4() {
        return price4;
    }

    public void setPrice4(BigDecimal price4) {
        this.price4 = price4;
    }

    public Long getPrice4CurrencyId() {
        return price4CurrencyId;
    }

    public void setPrice4CurrencyId(Long price4CurrencyId) {
        this.price4CurrencyId = price4CurrencyId;
    }

    public Long getForQty5() {
        return forQty5;
    }

    public void setForQty5(Long forQty5) {
        this.forQty5 = forQty5;
    }

    public BigDecimal getQty5() {
        return qty5;
    }

    public void setQty5(BigDecimal qty5) {
        this.qty5 = qty5;
    }

    public BigDecimal getPrice5() {
        return price5;
    }

    public void setPrice5(BigDecimal price5) {
        this.price5 = price5;
    }

    public Long getPrice5CurrencyId() {
        return price5CurrencyId;
    }

    public void setPrice5CurrencyId(Long price5CurrencyId) {
        this.price5CurrencyId = price5CurrencyId;
    }

    public BigDecimal getUnitPrice1() {
        return unitPrice1;
    }

    public void setUnitPrice1(BigDecimal unitPrice1) {
        this.unitPrice1 = unitPrice1;
    }

    public Long getUnitPrice1CurrencyId() {
        return unitPrice1CurrencyId;
    }

    public void setUnitPrice1CurrencyId(Long unitPrice1CurrencyId) {
        this.unitPrice1CurrencyId = unitPrice1CurrencyId;
    }

    public String getUp1Uom() {
        return up1Uom;
    }

    public void setUp1Uom(String up1Uom) {
        this.up1Uom = up1Uom;
    }

    public BigDecimal getUnitPrice2() {
        return unitPrice2;
    }

    public void setUnitPrice2(BigDecimal unitPrice2) {
        this.unitPrice2 = unitPrice2;
    }

    public Long getUnitPrice2CurrencyId() {
        return unitPrice2CurrencyId;
    }

    public void setUnitPrice2CurrencyId(Long unitPrice2CurrencyId) {
        this.unitPrice2CurrencyId = unitPrice2CurrencyId;
    }

    public String getUp2Uom() {
        return up2Uom;
    }

    public void setUp2Uom(String up2Uom) {
        this.up2Uom = up2Uom;
    }

    public BigDecimal getUnitPrice3() {
        return unitPrice3;
    }

    public void setUnitPrice3(BigDecimal unitPrice3) {
        this.unitPrice3 = unitPrice3;
    }

    public Long getUnitPrice3CurrencyId() {
        return unitPrice3CurrencyId;
    }

    public void setUnitPrice3CurrencyId(Long unitPrice3CurrencyId) {
        this.unitPrice3CurrencyId = unitPrice3CurrencyId;
    }

    public String getUp3Uom() {
        return up3Uom;
    }

    public void setUp3Uom(String up3Uom) {
        this.up3Uom = up3Uom;
    }

    public BigDecimal getUnitPrice4() {
        return unitPrice4;
    }

    public void setUnitPrice4(BigDecimal unitPrice4) {
        this.unitPrice4 = unitPrice4;
    }

    public Long getUnitPrice4CurrencyId() {
        return unitPrice4CurrencyId;
    }

    public void setUnitPrice4CurrencyId(Long unitPrice4CurrencyId) {
        this.unitPrice4CurrencyId = unitPrice4CurrencyId;
    }

    public String getUp4Uom() {
        return up4Uom;
    }

    public void setUp4Uom(String up4Uom) {
        this.up4Uom = up4Uom;
    }

    public BigDecimal getUnitPrice5() {
        return unitPrice5;
    }

    public void setUnitPrice5(BigDecimal unitPrice5) {
        this.unitPrice5 = unitPrice5;
    }

    public Long getUnitPrice5CurrencyId() {
        return unitPrice5CurrencyId;
    }

    public void setUnitPrice5CurrencyId(Long unitPrice5CurrencyId) {
        this.unitPrice5CurrencyId = unitPrice5CurrencyId;
    }

    public String getUp5Uom() {
        return up5Uom;
    }

    public void setUp5Uom(String up5Uom) {
        this.up5Uom = up5Uom;
    }

    public Float getPaperWeight1() {
        return paperWeight1;
    }

    public void setPaperWeight1(Float paperWeight1) {
        this.paperWeight1 = paperWeight1;
    }

    public Float getPaperWeight2() {
        return paperWeight2;
    }

    public void setPaperWeight2(Float paperWeight2) {
        this.paperWeight2 = paperWeight2;
    }

    public Float getPaperWeight3() {
        return paperWeight3;
    }

    public void setPaperWeight3(Float paperWeight3) {
        this.paperWeight3 = paperWeight3;
    }

    public Float getPaperWeight4() {
        return paperWeight4;
    }

    public void setPaperWeight4(Float paperWeight4) {
        this.paperWeight4 = paperWeight4;
    }

    public Float getPaperWeight5() {
        return paperWeight5;
    }

    public void setPaperWeight5(Float paperWeight5) {
        this.paperWeight5 = paperWeight5;
    }

    public BigDecimal getActualQty1() {
        return actualQty1;
    }

    public void setActualQty1(BigDecimal actualQty1) {
        this.actualQty1 = actualQty1;
    }

    public BigDecimal getActualQty2() {
        return actualQty2;
    }

    public void setActualQty2(BigDecimal actualQty2) {
        this.actualQty2 = actualQty2;
    }

    public BigDecimal getActualQty3() {
        return actualQty3;
    }

    public void setActualQty3(BigDecimal actualQty3) {
        this.actualQty3 = actualQty3;
    }

    public BigDecimal getActualQty4() {
        return actualQty4;
    }

    public void setActualQty4(BigDecimal actualQty4) {
        this.actualQty4 = actualQty4;
    }

    public BigDecimal getActualQty5() {
        return actualQty5;
    }

    public void setActualQty5(BigDecimal actualQty5) {
        this.actualQty5 = actualQty5;
    }

    public BigDecimal getPricePerTon() {
        return pricePerTon;
    }

    public void setPricePerTon(BigDecimal pricePerTon) {
        this.pricePerTon = pricePerTon;
    }

    public Long getPricePerTonCurrencyId() {
        return pricePerTonCurrencyId;
    }

    public void setPricePerTonCurrencyId(Long pricePerTonCurrencyId) {
        this.pricePerTonCurrencyId = pricePerTonCurrencyId;
    }

    public BigDecimal getQtyFor(long quantity) {
        if (this.getForQty1() != null && this.getForQty1() == quantity)
            return getQty1();
        if (this.getForQty2() != null && this.getForQty2() == quantity)
            return getQty2();
        if (this.getForQty3() != null && this.getForQty3() == quantity)
            return getQty3();
        if (this.getForQty4() != null && this.getForQty4() == quantity)
            return getQty4();
        if (this.getForQty5() != null && this.getForQty5() == quantity)
            return getQty5();
        return BigDecimal.ZERO;
    }

    public BigDecimal getUnitPriceFor(long quantity) {
        if (this.getForQty1() != null && this.getForQty1() == quantity)
            return getUnitPrice1();
        if (this.getForQty2() != null && this.getForQty2() == quantity)
            return getUnitPrice2();
        if (this.getForQty3() != null && this.getForQty3() == quantity)
            return getUnitPrice3();
        if (this.getForQty4() != null && this.getForQty4() == quantity)
            return getUnitPrice4();
        if (this.getForQty5() != null && this.getForQty5() == quantity)
            return getUnitPrice5();
        return null;
    }

    public String getUnitPriceUOMFor(long quantity) {
        if (this.getForQty1() != null && this.getForQty1() == quantity)
            return getUp1Uom();
        if (this.getForQty2() != null && this.getForQty2() == quantity)
            return getUp2Uom();
        if (this.getForQty3() != null && this.getForQty3() == quantity)
            return getUp3Uom();
        if (this.getForQty4() != null && this.getForQty4() == quantity)
            return getUp4Uom();
        if (this.getForQty5() != null && this.getForQty5() == quantity)
            return getUp5Uom();
        return null;
    }

    public BigDecimal getPriceFor(long quantity) {
        if (this.getForQty1() != null && this.getForQty1() == quantity)
            return getPrice1();
        if (this.getForQty2() != null && this.getForQty2() == quantity)
            return getPrice2();
        if (this.getForQty3() != null && this.getForQty3() == quantity)
            return getPrice3();
        if (this.getForQty4() != null && this.getForQty4() == quantity)
            return getPrice4();
        if (this.getForQty5() != null && this.getForQty5() == quantity)
            return getPrice5();
        return null;
    }
}
