package com.noosh.app.commons.dto.security;

import java.io.Serializable;

/**
 * User: lukez
 * Date: 6/29/22
 */
public class ObjectStateDTO implements Serializable {

    private static final long serialVersionUID = -808995711627031344L;

    private Long id;

    private String token;

    private Long descriptionStrId;

    private Long objectClassId;

    private String icon;

    private Long sortOrder;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getToken() {
        return this.token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getDescriptionStrId() {
        return this.descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public Long getObjectClassId() {
        return this.objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public String getIcon() {
        return this.icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Long getSortOrder() {
        return this.sortOrder;
    }

    public void setSortOrder(Long sortOrder) {
        this.sortOrder = sortOrder;
    }
}
