package com.noosh.app.commons.dto.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/7/19
 */
public class PaymentMethodDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String token;

    private Long descriptionStrId;

    private String name;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
