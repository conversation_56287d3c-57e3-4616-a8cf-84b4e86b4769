package com.noosh.app.commons.dto.breakout;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 10/8/17
 */
public class BreakoutTypeDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long workgroupId;
    private Long parentTypeId;
    private Long rootTypeId;
    private Boolean isLocked;
    private Boolean isCurrent;
    private String name;
    private String description;
    private Boolean isRequired;
    private Boolean isQuantity;
    private Boolean allSuppliers;
    private Long sortOrder;
    private String custom1;
    private Long cloneOf;
    private Boolean isIncluded;
    private String pricePer;
    private Long propertyId;
    private String code;
    private Boolean allClients;
    private Integer level;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getParentTypeId() {
        return parentTypeId;
    }

    public void setParentTypeId(Long parentTypeId) {
        this.parentTypeId = parentTypeId;
    }

    public Long getRootTypeId() {
        return rootTypeId;
    }

    public void setRootTypeId(Long rootTypeId) {
        this.rootTypeId = rootTypeId;
    }

    public Boolean getIsLocked() {
        return isLocked == null ? false : isLocked;
    }

    public void setIsLocked(Boolean locked) {
        isLocked = locked;
    }

    public Boolean getIsCurrent() {
        return isCurrent == null ? false : isCurrent;
    }

    public void setIsCurrent(Boolean current) {
        isCurrent = current;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsRequired() {
        return isRequired == null ? false : isRequired;
    }

    public void setIsRequired(Boolean required) {
        isRequired = required;
    }

    public Boolean getIsQuantity() {
        return isQuantity == null ? false : isQuantity;
    }

    public void setIsQuantity(Boolean quantity) {
        isQuantity = quantity;
    }

    public Boolean getAllSuppliers() {
        return allSuppliers == null ? false : allSuppliers;
    }

    public void setAllSuppliers(Boolean allSuppliers) {
        this.allSuppliers = allSuppliers;
    }

    public Long getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Long sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public Long getCloneOf() {
        return cloneOf;
    }

    public void setCloneOf(Long cloneOf) {
        this.cloneOf = cloneOf;
    }

    public Boolean getIsIncluded() {
        return isIncluded == null ? false : isIncluded;
    }

    public void setIsIncluded(Boolean included) {
        isIncluded = included;
    }

    public String getPricePer() {
        return pricePer;
    }

    public void setPricePer(String pricePer) {
        this.pricePer = pricePer;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getAllClients() {
        return allClients == null ? false : allClients;
    }

    public void setAllClients(Boolean allClients) {
        this.allClients = allClients;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public double getPricePerInDouble() {
        String pricePerStr = getPricePer();
        double pricePer = (pricePerStr != null && pricePerStr.length() > 0) ? Double.parseDouble(pricePerStr) : 0.0;
        return pricePer;
    }
}
