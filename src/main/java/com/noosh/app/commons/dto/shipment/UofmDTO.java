package com.noosh.app.commons.dto.shipment;

import com.noosh.app.commons.constant.StringID;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 10/8/17
 */
public class UofmDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String constantToken;

    private Long descriptionStrId;

    private Long conversionFactor;

    private Long relativeToBuUofmId;

    private Long pluralDescStrId;

    private String ansiToken;

    private Short isSystem;

    private Long acWorkgroupId;

    private String container;

    private Short isDisabled;

    private String unuomCode;

    private String pluralDescription;

    private boolean isConvertible;

    private String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean getIsConvertible() {
        return isConvertible;
    }

    public void setIsConvertible(boolean convertible) {
        isConvertible = convertible;
    }

    public String getPluralDescription() {
        return pluralDescription;
    }

    public void setPluralDescription(String pluralDescription) {
        this.pluralDescription = pluralDescription;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public Long getConversionFactor() {
        return conversionFactor;
    }

    public void setConversionFactor(Long conversionFactor) {
        this.conversionFactor = conversionFactor;
    }

    public Long getRelativeToBuUofmId() {
        return relativeToBuUofmId;
    }

    public void setRelativeToBuUofmId(Long relativeToBuUofmId) {
        this.relativeToBuUofmId = relativeToBuUofmId;
    }

    public Long getPluralDescStrId() {
        return pluralDescStrId;
    }

    public void setPluralDescStrId(Long pluralDescStrId) {
        this.pluralDescStrId = pluralDescStrId;
    }

    public String getAnsiToken() {
        return ansiToken;
    }

    public void setAnsiToken(String ansiToken) {
        this.ansiToken = ansiToken;
    }

    public Short getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Short system) {
        isSystem = system;
    }

    public Long getAcWorkgroupId() {
        return acWorkgroupId;
    }

    public void setAcWorkgroupId(Long acWorkgroupId) {
        this.acWorkgroupId = acWorkgroupId;
    }

    public String getContainer() {
        return container;
    }

    public void setContainer(String container) {
        this.container = container;
    }

    public Short getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Short disabled) {
        isDisabled = disabled;
    }

    public String getUnuomCode() {
        return unuomCode;
    }

    public void setUnuomCode(String unuomCode) {
        this.unuomCode = unuomCode;
    }

    /**
     * returns defalut conversion factor (num of units)
     *
     * return long
     */
    public long getDefault()
    {
        if( !this.getIsConvertible() )
            return 1;
        return getConversionFactor();
    }

    public Long getBreakoutRatesStrId() {
        if(getIsSystem() !=null && getIsSystem() ==1) {
            if (StringID.DAY_PLURAL == getPluralDescStrId()) {
                return StringID.PC_BREAKOUT_RATES_PER_DAY;
            } else if (StringID.HOUR_PLURAL == getPluralDescStrId()) {
                return StringID.PC_BREAKOUT_RATES_PER_HOUR;
            } else if (StringID.UNIT_PLURAL == getPluralDescStrId()) {
                return StringID.PC_BREAKOUT_RATES_PER_UNIT;
            } else if (StringID.MONTHS_PLURAL == getPluralDescStrId()) {
                return StringID.PC_BREAKOUT_RATES_PER_MONTH;
            } else {
                return null;
            }
        }
        return null;
    }

    public Long getBreakoutUnitsStrId()
    {
        if(getIsSystem() !=null && getIsSystem() ==1){
            if(StringID.DAY_PLURAL == getPluralDescStrId()){
                return StringID.PC_BREAKOUT_UNITS_DAYS;
            }else if(StringID.HOUR_PLURAL == getPluralDescStrId()){
                return StringID.PC_BREAKOUT_UNITS_HOURS;
            }else if(StringID.UNIT_PLURAL == getPluralDescStrId()){
                return StringID.PC_BREAKOUT_UNITS_UNITS;
            }else if(StringID.MONTHS_PLURAL == getPluralDescStrId()){
                return StringID.PC_BREAKOUT_UNITS_MONTHS;
            }else{
                return null;
            }
        }
        return null;
    }
}
