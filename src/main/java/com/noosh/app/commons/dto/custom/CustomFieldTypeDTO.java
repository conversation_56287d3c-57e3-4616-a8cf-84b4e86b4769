package com.noosh.app.commons.dto.custom;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 11/29/17
 */
public class CustomFieldTypeDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String constantToken;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }
}
