package com.noosh.app.commons.dto.estimate;

import com.noosh.app.commons.constant.ObjectStateID;
import java.time.LocalDateTime;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 4/28/17
 */
public class EstimateDTO implements Serializable {
    private static final long serialVersionUID = 9047049653276505092L;
    private Long id;

    private Long rfeId;

    private String reference;

    private String ownerReference;

    private String title;

    private String description;

    private String comments;

    private LocalDateTime expirationDate;

    private LocalDateTime submitDate;

    private Long submitUserId;

    private Long customPropertyId;

    private Long ownerWorkgroupId;

    private Long ownerUserId;

    private Long termsId;

    private Long stateId;

    private Boolean isAllOrNone;

    private Boolean autoGenerated;

    private Long behalfUserId;

    private String stateChangeComments;

    private String objectAttr;

    private Boolean itemizedTaxAndShipping;

    private Long sourceTypeId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRfeId() {
        return rfeId;
    }

    public void setRfeId(Long rfeId) {
        this.rfeId = rfeId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public Long getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(Long submitUserId) {
        this.submitUserId = submitUserId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getOwnerUserId() {
        return ownerUserId;
    }

    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    public Long getTermsId() {
        return termsId;
    }

    public void setTermsId(Long termsId) {
        this.termsId = termsId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public Boolean getIsAllOrNone() {
        return isAllOrNone;
    }

    public void setIsAllOrNone(Boolean isAllOrNone) {
        this.isAllOrNone = isAllOrNone;
    }

    public Boolean getAutoGenerated() {
        return autoGenerated;
    }

    public void setAutoGenerated(Boolean autoGenerated) {
        this.autoGenerated = autoGenerated;
    }

    public Long getBehalfUserId() {
        return behalfUserId;
    }

    public void setBehalfUserId(Long behalfUserId) {
        this.behalfUserId = behalfUserId;
    }

    public String getStateChangeComments() {
        return stateChangeComments;
    }

    public void setStateChangeComments(String stateChangeComments) {
        this.stateChangeComments = stateChangeComments;
    }

    public String getObjectAttr() {
        return objectAttr;
    }

    public void setObjectAttr(String objectAttr) {
        this.objectAttr = objectAttr;
    }

    public Boolean getItemizedTaxAndShipping() {
        return itemizedTaxAndShipping;
    }

    public void setItemizedTaxAndShipping(Boolean itemizedTaxAndShipping) {
        this.itemizedTaxAndShipping = itemizedTaxAndShipping;
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public boolean isDraft()
    {
        return getStateId() == ObjectStateID.ESTIMATE_DRAFT;
    }

    public boolean isSent()
    {
        return getStateId() == ObjectStateID.ESTIMATE_SENT;
    }

    public boolean isInvalidated()
    {
        return getStateId() == ObjectStateID.ESTIMATE_INVALIDATED ;
    }

    public boolean isRetracted()
    {
        return getStateId() == ObjectStateID.ESTIMATE_RETRACTED;
    }

    public boolean isRejected()
    {
        return getStateId() == ObjectStateID.ESTIMATE_REJECTED;
    }

    public boolean isStateSent()
    {
        return getStateId()==ObjectStateID.ESTIMATE_SENT;
    }

    public boolean isInactive() {
        boolean bRet = (this.getAutoGenerated() && this.getBehalfUserId() > 0);
        return bRet;
    }
}
