package com.noosh.app.commons.dto.spec;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 1/11/21
 */
public class SpecReferenceDTO implements Serializable {
    private static final long serialVersionUID = 3223364703406270171L;

    private Long spSpecReferenceId;

    private String specName;

    private String refNumber;

    private Long ownerWorkgroupId;

    private Long preferredSpSpecId;

    private Long spSpecTypeId;

    private String sku;

    private Short isMaster;

    private String description;

    private Long buClientWorkgroupId;

    private Long buUofmId;

    private String ownerDesc;

    private Long customPrPropertyId;

    private Short isObsolete;

    public Long getSpSpecReferenceId() {
        return spSpecReferenceId;
    }

    public void setSpSpecReferenceId(Long spSpecReferenceId) {
        this.spSpecReferenceId = spSpecReferenceId;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName == null ? null : specName.trim();
    }

    public String getRefNumber() {
        return refNumber;
    }

    public void setRefNumber(String refNumber) {
        this.refNumber = refNumber == null ? null : refNumber.trim();
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getPreferredSpSpecId() {
        return preferredSpSpecId;
    }

    public void setPreferredSpSpecId(Long preferredSpSpecId) {
        this.preferredSpSpecId = preferredSpSpecId;
    }

    public Long getSpSpecTypeId() {
        return spSpecTypeId;
    }

    public void setSpSpecTypeId(Long spSpecTypeId) {
        this.spSpecTypeId = spSpecTypeId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public Short getIsMaster() {
        return isMaster;
    }

    public void setIsMaster(Short isMaster) {
        this.isMaster = isMaster;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }

    public Long getBuUofmId() {
        return buUofmId;
    }

    public void setBuUofmId(Long buUofmId) {
        this.buUofmId = buUofmId;
    }

    public String getOwnerDesc() {
        return ownerDesc;
    }

    public void setOwnerDesc(String ownerDesc) {
        this.ownerDesc = ownerDesc == null ? null : ownerDesc.trim();
    }

    public Long getCustomPrPropertyId() {
        return customPrPropertyId;
    }

    public void setCustomPrPropertyId(Long customPrPropertyId) {
        this.customPrPropertyId = customPrPropertyId;
    }

    public Short getIsObsolete() {
        return isObsolete;
    }

    public void setIsObsolete(Short isObsolete) {
        this.isObsolete = isObsolete;
    }
}
