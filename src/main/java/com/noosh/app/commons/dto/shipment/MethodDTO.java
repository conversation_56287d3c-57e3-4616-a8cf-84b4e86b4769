package com.noosh.app.commons.dto.shipment;

import java.io.Serial;
import java.io.Serializable;

public class MethodDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long nameStrId;
    private Long isActive;
    private String nameStr;
    private Long workgroupId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public Long getIsActive() {
        return isActive;
    }

    public void setIsActive(Long isActive) {
        this.isActive = isActive;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }
}
