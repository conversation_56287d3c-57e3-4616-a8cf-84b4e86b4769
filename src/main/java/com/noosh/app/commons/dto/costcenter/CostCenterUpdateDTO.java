package com.noosh.app.commons.dto.costcenter;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 11/24/21
 */
public class CostCenterUpdateDTO implements Serializable {
    private static final long serialVersionUID = -4397674389302854165L;

    private boolean isCopyToOrder;

    private Long projectId;

    private List<CostCenterDetailUpdateDTO> details;

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public boolean getIsCopyToOrder() {
        return isCopyToOrder;
    }

    public void setCopyToOrder(boolean copyToOrder) {
        isCopyToOrder = copyToOrder;
    }

    public List<CostCenterDetailUpdateDTO> getDetails() {
        return details;
    }

    public void setDetails(List<CostCenterDetailUpdateDTO> details) {
        this.details = details;
    }
}
