package com.noosh.app.commons.dto.order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * User: leilaz
 * Date: 5/18/20
 */
public class OrderDetailAnalysisDTO implements Serializable {

    private static final long serialVersionUID = -8502672001430382200L;

    private int totalPendingAmountCount;

    private int totalDefaultQuotedCostCount;

    private BigDecimal buyTotal;

    private BigDecimal markupTotal;

    private BigDecimal marginTotal;

    private BigDecimal sellTotal;

    private BigDecimal profitTotal;

    private List<OrderDetailMarkupDTO> details;

    public BigDecimal getMarkupTotal() {
        return markupTotal;
    }

    public void setMarkupTotal(BigDecimal markupTotal) {
        this.markupTotal = markupTotal;
    }

    public BigDecimal getMarginTotal() {
        return marginTotal;
    }

    public void setMarginTotal(BigDecimal marginTotal) {
        this.marginTotal = marginTotal;
    }

    public int getTotalPendingAmountCount() {
        return totalPendingAmountCount;
    }

    public void setTotalPendingAmountCount(int totalPendingAmountCount) {
        this.totalPendingAmountCount = totalPendingAmountCount;
    }

    public int getTotalDefaultQuotedCostCount() {
        return totalDefaultQuotedCostCount;
    }

    public void setTotalDefaultQuotedCostCount(int totalDefaultQuotedCostCount) {
        this.totalDefaultQuotedCostCount = totalDefaultQuotedCostCount;
    }

    public BigDecimal getBuyTotal() {
        return buyTotal;
    }

    public void setBuyTotal(BigDecimal buyTotal) {
        this.buyTotal = buyTotal;
    }

    public BigDecimal getSellTotal() {
        return sellTotal;
    }

    public void setSellTotal(BigDecimal sellTotal) {
        this.sellTotal = sellTotal;
    }

    public BigDecimal getProfitTotal() {
        return profitTotal;
    }

    public void setProfitTotal(BigDecimal profitTotal) {
        this.profitTotal = profitTotal;
    }

    public List<OrderDetailMarkupDTO> getDetails() {
        return details;
    }

    public void setDetails(List<OrderDetailMarkupDTO> details) {
        this.details = details;
    }
}
