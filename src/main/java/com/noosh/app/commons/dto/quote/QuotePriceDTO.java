package com.noosh.app.commons.dto.quote;

import com.noosh.app.commons.dto.breakout.BreakoutDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 3/15/2022
 */
public class QuotePriceDTO {

    private Long quantity;
    private BigDecimal price;
    private BigDecimal unitPrice;
    private BigDecimal preMarkup;
    private BigDecimal markupPercent;
    private BigDecimal markupFixed;
    private BigDecimal marginPercent;
    private String supplierName;
    private boolean hasSubQty;
    private boolean isChoosePrice;
    private List<BreakoutDTO> breakouts;
    private BigDecimal tax;
    private BigDecimal shipping;
    private boolean isVisibleToBuyer;

    public boolean getIsVisibleToBuyer() {
        return isVisibleToBuyer;
    }

    public void setIsVisibleToBuyer(boolean visibleToBuyer) {
        isVisibleToBuyer = visibleToBuyer;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getPreMarkup() {
        return preMarkup;
    }

    public void setPreMarkup(BigDecimal preMarkup) {
        this.preMarkup = preMarkup;
    }

    public BigDecimal getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(BigDecimal markupPercent) {
        this.markupPercent = markupPercent;
    }

    public BigDecimal getMarkupFixed() {
        return markupFixed;
    }

    public void setMarkupFixed(BigDecimal markupFixed) {
        this.markupFixed = markupFixed;
    }

    public BigDecimal getMarginPercent() {
        return marginPercent;
    }

    public void setMarginPercent(BigDecimal marginPercent) {
        this.marginPercent = marginPercent;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public boolean isHasSubQty() {
        return hasSubQty;
    }

    public void setHasSubQty(boolean hasSubQty) {
        this.hasSubQty = hasSubQty;
    }

    public boolean getIsChoosePrice() {
        return isChoosePrice;
    }

    public void setIsChoosePrice(boolean choosePrice) {
        isChoosePrice = choosePrice;
    }

    public List<BreakoutDTO> getBreakouts() {
        return breakouts;
    }

    public void setBreakouts(List<BreakoutDTO> breakouts) {
        this.breakouts = breakouts;
    }
}
