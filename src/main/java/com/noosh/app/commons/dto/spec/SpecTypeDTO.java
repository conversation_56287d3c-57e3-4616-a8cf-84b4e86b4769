package com.noosh.app.commons.dto.spec;

import java.time.LocalDateTime;

import java.io.Serializable;

/**
 * User: <PERSON>
 * Date: 5/6/2018
 */
public class SpecTypeDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long labelStrId;

    private String description;

    private Long coServiceId;

    private Long prPropertyTypeId;

    private String templateBaseName;

    private Short isDeprecated;

    private String deprecationReason;

    private LocalDateTime deprecationDate;

    private Long replacedBySpecTypeId;

    private String labelStr;

    private String icon;

    private Short isDirectMail;

    private Short isVisible;

    private String image;

    private Short isImEnabled;

    private Short isCampaign;

    private Long acCustomFormId;

    private Long originalSpecTypeId;

    private Long parentSpecTypeId;

    private Long versionNumber;

    private Short isCurrent;

    private String iconUrl;

    private Long acSourceTypeId;

    private Short isTimeMaterials;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLabelStrId() {
        return labelStrId;
    }

    public void setLabelStrId(Long labelStrId) {
        this.labelStrId = labelStrId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCoServiceId() {
        return coServiceId;
    }

    public void setCoServiceId(Long coServiceId) {
        this.coServiceId = coServiceId;
    }

    public Long getPrPropertyTypeId() {
        return prPropertyTypeId;
    }

    public void setPrPropertyTypeId(Long prPropertyTypeId) {
        this.prPropertyTypeId = prPropertyTypeId;
    }

    public String getTemplateBaseName() {
        return templateBaseName;
    }

    public void setTemplateBaseName(String templateBaseName) {
        this.templateBaseName = templateBaseName;
    }

    public Short getIsDeprecated() {
        return isDeprecated;
    }

    public void setIsDeprecated(Short isDeprecated) {
        this.isDeprecated = isDeprecated;
    }

    public String getDeprecationReason() {
        return deprecationReason;
    }

    public void setDeprecationReason(String deprecationReason) {
        this.deprecationReason = deprecationReason;
    }

    public LocalDateTime getDeprecationDate() {
        return deprecationDate;
    }

    public void setDeprecationDate(LocalDateTime deprecationDate) {
        this.deprecationDate = deprecationDate;
    }

    public Long getReplacedBySpecTypeId() {
        return replacedBySpecTypeId;
    }

    public void setReplacedBySpecTypeId(Long replacedBySpecTypeId) {
        this.replacedBySpecTypeId = replacedBySpecTypeId;
    }

    public String getLabelStr() {
        return labelStr;
    }

    public void setLabelStr(String labelStr) {
        this.labelStr = labelStr;
    }

    public String getIcon() {
        if (icon == null) {
            icon = "images/spec/other.gif";
        }
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Short getIsDirectMail() {
        return isDirectMail;
    }

    public void setIsDirectMail(Short isDirectMail) {
        this.isDirectMail = isDirectMail;
    }

    public Short getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Short isVisible) {
        this.isVisible = isVisible;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public Short getIsImEnabled() {
        return isImEnabled;
    }

    public void setIsImEnabled(Short isImEnabled) {
        this.isImEnabled = isImEnabled;
    }

    public Short getIsCampaign() {
        return isCampaign;
    }

    public void setIsCampaign(Short isCampaign) {
        this.isCampaign = isCampaign;
    }

    public Long getAcCustomFormId() {
        return acCustomFormId;
    }

    public void setAcCustomFormId(Long acCustomFormId) {
        this.acCustomFormId = acCustomFormId;
    }

    public Long getOriginalSpecTypeId() {
        return originalSpecTypeId;
    }

    public void setOriginalSpecTypeId(Long originalSpecTypeId) {
        this.originalSpecTypeId = originalSpecTypeId;
    }

    public Long getParentSpecTypeId() {
        return parentSpecTypeId;
    }

    public void setParentSpecTypeId(Long parentSpecTypeId) {
        this.parentSpecTypeId = parentSpecTypeId;
    }

    public Long getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Short getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Short isCurrent) {
        this.isCurrent = isCurrent;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public Long getAcSourceTypeId() {
        return acSourceTypeId;
    }

    public void setAcSourceTypeId(Long acSourceTypeId) {
        this.acSourceTypeId = acSourceTypeId;
    }

    public Short getIsTimeMaterials() {
        return isTimeMaterials;
    }

    public void setIsTimeMaterials(Short isTimeMaterials) {
        this.isTimeMaterials = isTimeMaterials;
    }
}
