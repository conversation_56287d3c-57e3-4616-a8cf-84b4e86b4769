package com.noosh.app.commons.dto.quote;

import java.time.LocalDateTime;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 12/9/15
 * Time: 12:27 AM
 */
public class RfqWidgetDTO {
    private String rfqTitle;
    private String state;
    private String stateStrId;
    private Long stateId;
    private int quoteCount;
    private LocalDateTime dueDate;
    private LocalDateTime submittedDate;
    private String projectName;
    private String clientWorkgroupName;
    private String supplierWorkgroupName;
    private Long projectId;
    private Long rfqId;
    private boolean showSupplierWorkgroup;
    private String externalLink;
    private String projectExternalLink;
    private Long clientWorkgroupId;

    public String getStateStrId() {
        return stateStrId;
    }

    public void setStateStrId(String stateStrId) {
        this.stateStrId = stateStrId;
    }

    public Long getClientWorkgroupId() {
        return clientWorkgroupId;
    }

    public void setClientWorkgroupId(Long clientWorkgroupId) {
        this.clientWorkgroupId = clientWorkgroupId;
    }

    public String getProjectExternalLink() {
        return projectExternalLink;
    }

    public void setProjectExternalLink(String projectExternalLink) {
        this.projectExternalLink = projectExternalLink;
    }

    public String getExternalLink() {
        return externalLink;
    }

    public void setExternalLink(String externalLink) {
        this.externalLink = externalLink;
    }

    public boolean isShowSupplierWorkgroup() {
        return showSupplierWorkgroup;
    }

    public void setShowSupplierWorkgroup(boolean showSupplierWorkgroup) {
        this.showSupplierWorkgroup = showSupplierWorkgroup;
    }

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getRfqId() {
        return rfqId;
    }

    public void setRfqId(Long rfqId) {
        this.rfqId = rfqId;
    }

    public String getRfqTitle() {
        return rfqTitle;
    }

    public void setRfqTitle(String rfqTitle) {
        this.rfqTitle = rfqTitle;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
        this.state = RfqStateEnum.getRfqState(stateId).name();
    }

    public int getQuoteCount() {
        return quoteCount;
    }

    public void setQuoteCount(int quoteCount) {
        this.quoteCount = quoteCount;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getSubmittedDate() {
        return submittedDate;
    }

    public void setSubmittedDate(LocalDateTime submittedDate) {
        this.submittedDate = submittedDate;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getClientWorkgroupName() {
        return clientWorkgroupName;
    }

    public void setClientWorkgroupName(String clientWorkgroupName) {
        this.clientWorkgroupName = clientWorkgroupName;
    }
}
