package com.noosh.app.commons.dto.proposal;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 10/31/2021
 */
public class WorkgroupLogoDTO {

    private Long logoId;
    private String name;
    private String userFilename;
    private String resourcePath;
    private String width;
    private String height;
    private boolean isActive;
    private LocalDateTime createdDate;
    private String createdFirstName;
    private String createdMiddleName;
    private String createdLastName;
    private LocalDateTime lastUpdatedDate;
    private String updatedFirstName;
    private String updatedMiddleName;
    private String updatedLastName;
    private String logoImage;

    public String getLogoImage() {
        return logoImage;
    }

    public void setLogoImage(String logoImage) {
        this.logoImage = logoImage;
    }

    public Long getLogoId() {
        return logoId;
    }

    public void setLogoId(Long logoId) {
        this.logoId = logoId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserFilename() {
        return userFilename;
    }

    public void setUserFilename(String userFilename) {
        this.userFilename = userFilename;
    }

    public String getResourcePath() {
        return resourcePath;
    }

    public void setResourcePath(String resourcePath) {
        this.resourcePath = resourcePath;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(boolean active) {
        isActive = active;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedFirstName() {
        return createdFirstName;
    }

    public void setCreatedFirstName(String createdFirstName) {
        this.createdFirstName = createdFirstName;
    }

    public String getCreatedMiddleName() {
        return createdMiddleName;
    }

    public void setCreatedMiddleName(String createdMiddleName) {
        this.createdMiddleName = createdMiddleName;
    }

    public String getCreatedLastName() {
        return createdLastName;
    }

    public void setCreatedLastName(String createdLastName) {
        this.createdLastName = createdLastName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getUpdatedFirstName() {
        return updatedFirstName;
    }

    public void setUpdatedFirstName(String updatedFirstName) {
        this.updatedFirstName = updatedFirstName;
    }

    public String getUpdatedMiddleName() {
        return updatedMiddleName;
    }

    public void setUpdatedMiddleName(String updatedMiddleName) {
        this.updatedMiddleName = updatedMiddleName;
    }

    public String getUpdatedLastName() {
        return updatedLastName;
    }

    public void setUpdatedLastName(String updatedLastName) {
        this.updatedLastName = updatedLastName;
    }

    public String getCreatedFullName() {
        String fullName = "";
        //AES/REVIEW: Prepend rather than append spaces for each concat after firstName.
        if (getCreatedFirstName() != null) {
            fullName += getCreatedFirstName().trim() + " ";
        }
        if (getCreatedMiddleName() != null) {
            fullName += getCreatedMiddleName().trim() + " ";
        }
        if (getCreatedLastName() != null) {
            fullName += getCreatedLastName().trim();
        }
        return fullName;
    }

    public String getUpdatedFullName() {
        String fullName = "";
        //AES/REVIEW: Prepend rather than append spaces for each concat after firstName.
        if (getUpdatedFirstName() != null) {
            fullName += getUpdatedFirstName().trim() + " ";
        }
        if (getUpdatedMiddleName() != null) {
            fullName += getUpdatedMiddleName().trim() + " ";
        }
        if (getUpdatedLastName() != null) {
            fullName += getUpdatedLastName().trim();
        }
        return fullName;
    }
}
