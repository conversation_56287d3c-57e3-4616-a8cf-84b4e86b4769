package com.noosh.app.commons.dto.costcenter;

import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.entity.costcenter.CostCenter;
import com.noosh.app.commons.entity.costcenter.CostCenterAllocType;
import com.noosh.app.commons.entity.costcenter.CostCenterAllocation;
import com.noosh.app.commons.entity.order.OrderItem;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 9/14/20
 */
public class CostCenterAllocationDTO implements Serializable {
    private static final long serialVersionUID = -938362003558347858L;

    private CostCenterAllocation costCenterAllocation;
    private CostCenter costCenter;
    private OrderItem orderItem;
    private CostCenterAllocType costCenterAllocType;

    private Long costCenterAllocationId;

    private String costCenterName;

    private String costCenterDesc;

    private Double percent;

    private Double amount;

    private Double invoiceAmount;

    private Long costCenterId;

    private List<PropertyAttributeDTO> customFields;
	
	public CostCenterAllocationDTO() { }

    public CostCenterAllocationDTO( CostCenterAllocation costCenterAllocation) {
        this.costCenterAllocation = costCenterAllocation;
    }

    public OrderItem getOrderItem() {
        return orderItem;
    }

    public void setOrderItem(OrderItem orderItem) {
        this.orderItem = orderItem;
    }

    public CostCenterAllocType getCostCenterAllocType() {
        return costCenterAllocType;
    }

    public void setCostCenterAllocType(CostCenterAllocType costCenterAllocType) {
        this.costCenterAllocType = costCenterAllocType;
    }

    public CostCenter getCostCenter() {
        return costCenter;
    }

    public void setCostCenter(CostCenter costCenter) {
        this.costCenter = costCenter;
    }

    public CostCenterAllocation getCostCenterAllocation() {
        return costCenterAllocation;
    }

    public void setCostCenterAllocation(CostCenterAllocation costCenterAllocation) {
        this.costCenterAllocation = costCenterAllocation;
    }

    public Double getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(Double invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public String getCostCenterDesc() {
        return costCenterDesc;
    }

    public void setCostCenterDesc(String costCenterDesc) {
        this.costCenterDesc = costCenterDesc;
    }

    public Long getCostCenterAllocationId() {
        return costCenterAllocationId;
    }

    public void setCostCenterAllocationId(Long costCenterAllocationId) {
        this.costCenterAllocationId = costCenterAllocationId;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public Double getPercent() {
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public List<PropertyAttributeDTO> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<PropertyAttributeDTO> customFields) {
        this.customFields = customFields;
    }
}