package com.noosh.app.commons.dto.costcenter;


import com.noosh.app.commons.entity.costcenter.CostCenterAllocation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * User: yangx
 * Date: 6/22/2020
 */
public class AggregatedCostCenterAllocationDTO extends CostCenterAllocationDTO {

    private List<CostCenterAllocation> ccaList = new ArrayList();
    private List<Double> amountList = new ArrayList();
    private Double aggregatedPercent;

    public AggregatedCostCenterAllocationDTO(CostCenterAllocation cca) {
        super(cca);
    }

    public void add(CostCenterAllocation cca, Double amount) {
        ccaList.add(cca);
        amountList.add(amount);
    }

    public Double aggregateAllocationsPercent(BigDecimal total) {
        if (total != null && ccaList.size() > 0) {
            double totalAmount = 0;
            double percent = 0;
            for (int i = 0; i < ccaList.size(); i++) {
                CostCenterAllocation cca = ccaList.get(i);
                Double thisAmount = amountList.get(i);
                totalAmount += (thisAmount * cca.getPercent()) / 100;
                percent = cca.getPercent();
            }
            //To avoid NaN Error(NKB142761), we should keep an eye on it why the total value is 0.
            if(total.doubleValue() != 0){
                percent = (totalAmount * 100) / total.doubleValue() ;
            }
            this.aggregatedPercent = percent;
            return percent;
        } else {
            return null;
        }
    }

    public BigDecimal computePercentAmount(BigDecimal total)
    {
        if (aggregatedPercent == null) {
            aggregateAllocationsPercent(total);
        }
        if (total != null && aggregatedPercent != null ) {
            BigDecimal amount = total.multiply(new BigDecimal(getAggregatedPercent())).multiply(new BigDecimal(0.01));
            return amount;
        }
        return null;
    }

    public boolean equals(Object other) {
        if(!(other instanceof AggregatedCostCenterAllocationDTO) || other==null) return false;

        AggregatedCostCenterAllocationDTO otherBean = (AggregatedCostCenterAllocationDTO) other;
        Long thisCostCenterId = this.getCostCenterAllocation().getCostCenterId();
        Long otherCostCenterId = otherBean.getCostCenterAllocation().getCostCenterId();
        if ((thisCostCenterId == null || otherCostCenterId == null) && (thisCostCenterId != otherCostCenterId)) {
            return false;
        }
        if (thisCostCenterId.longValue() != otherCostCenterId.longValue()) {
            return false;
        }
        return true;
    }

    public int hashCode() {
        /*
         ** in order for the aggr. alloc to be "equal", the first criteria
         ** is having the same costCenterId()
         ** thus, hashcode is the cost center id
         */
        if (this.getCostCenterAllocation() == null) {
            return -1;
        }
        Long id = this.getCostCenterAllocation().getCostCenterId();
        if (id == null) {
            return -1;
        }
        return (int) id.longValue();
    }

    public Double getAggregatedPercent() {
        return aggregatedPercent;
    }
}
