package com.noosh.app.commons.dto.proposal;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 11/1/2021
 */
public class ProposalTemplateDTO {

    private Long templateId;
    private String name;
    private String resourcePath;
    private LocalDateTime createdDate;
    private String createdFirstName;
    private String createdMiddleName;
    private String createdLastName;
    private LocalDateTime lastUpdatedDate;
    private String updatedFirstName;
    private String updatedMiddleName;
    private String updatedLastName;
    private Boolean isDefault;

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getResourcePath() {
        return resourcePath;
    }

    public void setResourcePath(String resourcePath) {
        this.resourcePath = resourcePath;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedFirstName() {
        return createdFirstName;
    }

    public void setCreatedFirstName(String createdFirstName) {
        this.createdFirstName = createdFirstName;
    }

    public String getCreatedMiddleName() {
        return createdMiddleName;
    }

    public void setCreatedMiddleName(String createdMiddleName) {
        this.createdMiddleName = createdMiddleName;
    }

    public String getCreatedLastName() {
        return createdLastName;
    }

    public void setCreatedLastName(String createdLastName) {
        this.createdLastName = createdLastName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getUpdatedFirstName() {
        return updatedFirstName;
    }

    public void setUpdatedFirstName(String updatedFirstName) {
        this.updatedFirstName = updatedFirstName;
    }

    public String getUpdatedMiddleName() {
        return updatedMiddleName;
    }

    public void setUpdatedMiddleName(String updatedMiddleName) {
        this.updatedMiddleName = updatedMiddleName;
    }

    public String getUpdatedLastName() {
        return updatedLastName;
    }

    public void setUpdatedLastName(String updatedLastName) {
        this.updatedLastName = updatedLastName;
    }

    public String getCreatedFullName() {
        String fullName = "";
        //AES/REVIEW: Prepend rather than append spaces for each concat after firstName.
        if (getCreatedFirstName() != null) {
            fullName += getCreatedFirstName().trim() + " ";
        }
        if (getCreatedMiddleName() != null) {
            fullName += getCreatedMiddleName().trim() + " ";
        }
        if (getCreatedLastName() != null) {
            fullName += getCreatedLastName().trim();
        }
        return fullName;
    }

    public String getUpdatedFullName() {
        String fullName = "";
        //AES/REVIEW: Prepend rather than append spaces for each concat after firstName.
        if (getUpdatedFirstName() != null) {
            fullName += getUpdatedFirstName().trim() + " ";
        }
        if (getUpdatedMiddleName() != null) {
            fullName += getUpdatedMiddleName().trim() + " ";
        }
        if (getUpdatedLastName() != null) {
            fullName += getUpdatedLastName().trim();
        }
        return fullName;
    }
}
