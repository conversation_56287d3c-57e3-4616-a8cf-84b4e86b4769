package com.noosh.app.commons.dto.proposal;

import java.math.BigDecimal;

public class ProposalSustainabilityDTO {
    private String specName;

    private BigDecimal material; // null is N/A
    private BigDecimal estMaterial; // null is N/A
    private Boolean materialIsEstimated = false; // material == null && estMaterial != null
    private BigDecimal production;
    private BigDecimal estProduction;
    private Boolean productionIsEstimated = true; // always true, production not have actual value, production == null && estProduction != null
    private BigDecimal logistics;  // unit t, multiplied by 1000 to get Kg CO2e
    private BigDecimal estLogistics;  // unit t, multiplied by 1000 to get Kg CO2e
    private Boolean logisticsIsEstimated = false; // logistics == null && estLogistics != null

    // if no actual, use est value instead to calculate total, if both are null, then no calculation
    private BigDecimal total; // CO2 = material 8 + estProduction 5 + logistics 0.025 * 1000 = 38 Kg CO2e
    private BigDecimal estimatedTotal; // CO2 = estMaterial 8 + estProduction 5 + estLogistics 0.032 * 1000 = 45 Kg CO2e
    private BigDecimal comparison; // Comparison = (total - estimatedTotal)/estimatedTotal
    private BigDecimal miles; // total * 2.5

    public BigDecimal getEstMaterial() {
        return estMaterial;
    }

    public void setEstMaterial(BigDecimal estMaterial) {
        this.estMaterial = estMaterial;
    }

    public BigDecimal getEstProduction() {
        return estProduction;
    }

    public void setEstProduction(BigDecimal estProduction) {
        this.estProduction = estProduction;
    }

    public BigDecimal getEstLogistics() {
        return estLogistics;
    }

    public void setEstLogistics(BigDecimal estLogistics) {
        this.estLogistics = estLogistics;
    }

    public BigDecimal getEstimatedTotal() {
        return estimatedTotal;
    }

    public void setEstimatedTotal(BigDecimal estimatedTotal) {
        this.estimatedTotal = estimatedTotal;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public BigDecimal getMaterial() {
        return material;
    }

    public void setMaterial(BigDecimal material) {
        this.material = material;
    }

    public Boolean getMaterialIsEstimated() {
        return materialIsEstimated;
    }

    public void setMaterialIsEstimated(Boolean materialIsEstimated) {
        this.materialIsEstimated = materialIsEstimated;
    }

    public BigDecimal getProduction() {
        return production;
    }

    public void setProduction(BigDecimal production) {
        this.production = production;
    }

    public Boolean getProductionIsEstimated() {
        return productionIsEstimated;
    }

    public void setProductionIsEstimated(Boolean productionIsEstimated) {
        this.productionIsEstimated = productionIsEstimated;
    }

    public BigDecimal getLogistics() {
        return logistics;
    }

    public void setLogistics(BigDecimal logistics) {
        this.logistics = logistics;
    }

    public Boolean getLogisticsIsEstimated() {
        return logisticsIsEstimated;
    }

    public void setLogisticsIsEstimated(Boolean logisticsIsEstimated) {
        this.logisticsIsEstimated = logisticsIsEstimated;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public BigDecimal getComparison() {
        return comparison;
    }

    public void setComparison(BigDecimal comparison) {
        this.comparison = comparison;
    }

    public BigDecimal getMiles() {
        return miles;
    }

    public void setMiles(BigDecimal miles) {
        this.miles = miles;
    }
}
