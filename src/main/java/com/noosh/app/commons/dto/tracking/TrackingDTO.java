package com.noosh.app.commons.dto.tracking;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * User: leilaz
 * Date: 9/17/19
 */
public class TrackingDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long trTrackingId;

    private Long trTrackingTypeId;

    private LocalDateTime recordedDate;

    private Long enactingUserId;

    private String comments;

    private String i18nData;

    private Long customPrPropertyId;

    private Short isUserPosted;

    private Long acSourceTypeId;

    private Long objectId;

    private Long objectClassId;

    private Map i18nDataMap;

    private Long createUserId;

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Map getI18nDataMap() {
        return i18nDataMap;
    }

    public void setI18nDataMap(Map i18nDataMap) {
        this.i18nDataMap = i18nDataMap;
    }

    public Long getTrTrackingId() {
        return trTrackingId;
    }

    public void setTrTrackingId(Long trTrackingId) {
        this.trTrackingId = trTrackingId;
    }

    public Long getTrTrackingTypeId() {
        return trTrackingTypeId;
    }

    public void setTrTrackingTypeId(Long trTrackingTypeId) {
        this.trTrackingTypeId = trTrackingTypeId;
    }

    public LocalDateTime getRecordedDate() {
        return recordedDate;
    }

    public void setRecordedDate(LocalDateTime recordedDate) {
        this.recordedDate = recordedDate;
    }

    public Long getEnactingUserId() {
        return enactingUserId;
    }

    public void setEnactingUserId(Long enactingUserId) {
        this.enactingUserId = enactingUserId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getI18nData() {
        return i18nData;
    }

    public void setI18nData(String i18nData) {
        this.i18nData = i18nData;
    }

    public Long getCustomPrPropertyId() {
        return customPrPropertyId;
    }

    public void setCustomPrPropertyId(Long customPrPropertyId) {
        this.customPrPropertyId = customPrPropertyId;
    }

    public Short getIsUserPosted() {
        return isUserPosted;
    }

    public void setIsUserPosted(Short userPosted) {
        isUserPosted = userPosted;
    }

    public Long getAcSourceTypeId() {
        return acSourceTypeId;
    }

    public void setAcSourceTypeId(Long acSourceTypeId) {
        this.acSourceTypeId = acSourceTypeId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }
}
