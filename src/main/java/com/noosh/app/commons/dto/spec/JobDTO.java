package com.noosh.app.commons.dto.spec;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 5/19/16
 */
public class JobDTO implements Serializable {
    private static final long serialVersionUID = -6504084843118084402L;
    private Long jobId;
    private Long jobUserStateId;
    private String jobUserState;
    private List<JobStatusDTO> jobStatusDTOList;

    public Long getJobUserStateId() {
        return jobUserStateId;
    }

    public void setJobUserStateId(Long jobUserStateId) {
        this.jobUserStateId = jobUserStateId;
    }

    public String getJobUserState() {
        return jobUserState;
    }

    public void setJobUserState(String jobUserState) {
        this.jobUserState = jobUserState;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public List<JobStatusDTO> getJobStatusDTOList() {
        return jobStatusDTOList;
    }

    public void setJobStatusDTOList(List<JobStatusDTO> jobStatusDTOList) {
        this.jobStatusDTOList = jobStatusDTOList;
    }
}
