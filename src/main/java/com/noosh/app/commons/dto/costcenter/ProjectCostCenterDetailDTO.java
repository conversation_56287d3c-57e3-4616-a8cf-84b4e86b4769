package com.noosh.app.commons.dto.costcenter;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 11/14/21
 */
public class ProjectCostCenterDetailDTO implements Serializable {
    private static final long serialVersionUID = 8617804210067592735L;

    private List<CostCenterAllocationDTO> costCenters;

    private boolean canEditCostCenter;

    private boolean canAddCostCenter;

    private String projectExternalLink;

    public String getProjectExternalLink() {
        return projectExternalLink;
    }

    public void setProjectExternalLink(String projectExternalLink) {
        this.projectExternalLink = projectExternalLink;
    }

    public boolean isCanAddCostCenter() {
        return canAddCostCenter;
    }

    public void setCanAddCostCenter(boolean canAddCostCenter) {
        this.canAddCostCenter = canAddCostCenter;
    }

    public boolean getCanEditCostCenter() {
        return canEditCostCenter;
    }

    public void setCanEditCostCenter(boolean canEditCostCenter) {
        this.canEditCostCenter = canEditCostCenter;
    }

    public List<CostCenterAllocationDTO> getCostCenters() {
        return costCenters;
    }

    public void setCostCenters(List<CostCenterAllocationDTO> costCenters) {
        this.costCenters = costCenters;
    }
}
