package com.noosh.app.commons.dto.costcenter;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 9/9/20
 */
public class OrderCostCenterDTO implements Serializable {
    private static final long serialVersionUID = 7641367089887269381L;

    private Long specId;
    
    private Long itemId;

    private String itemName;

    private double itemValue;

    private double quantity;

    private String itemExternalLink;

    private List<CostCenterAllocationDTO> costCenters;

    private OrderCostCenterDTO orderItemTax;
    private OrderCostCenterDTO orderItemShipping;

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    public OrderCostCenterDTO getOrderItemTax() {
        return orderItemTax;
    }

    public void setOrderItemTax(OrderCostCenterDTO orderItemTax) {
        this.orderItemTax = orderItemTax;
    }

    public OrderCostCenterDTO getOrderItemShipping() {
        return orderItemShipping;
    }

    public void setOrderItemShipping(OrderCostCenterDTO orderItemShipping) {
        this.orderItemShipping = orderItemShipping;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getItemExternalLink() {
        return itemExternalLink;
    }

    public void setItemExternalLink(String itemExternalLink) {
        this.itemExternalLink = itemExternalLink;
    }

    public double getItemValue() {
        return itemValue;
    }

    public void setItemValue(double itemValue) {
        this.itemValue = itemValue;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public List<CostCenterAllocationDTO> getCostCenters() {
        return costCenters;
    }

    public void setCostCenters(List<CostCenterAllocationDTO> costCenters) {
        this.costCenters = costCenters;
    }
}