package com.noosh.app.commons.dto.job;

import java.time.LocalDateTime;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 5/19/16
 */
public class JobStatusDTO implements Serializable {

    private static final long serialVersionUID = 4451627407965195959L;
    private Long pcJobStatusId;

    private Long buyerAcWorkgroupId;

    private Long ownerAcWorkgroupId;

    private Long ocObjectStateId;

    private Integer rfeCount;

    private Integer estimateCount;

    private Integer rfqCount;

    private Integer quoteCount;

    private Integer orderPendingCount;

    private Integer orderAcceptedCount;

    private LocalDateTime createDate;

    private LocalDateTime modDate;

    private Long createUserId;

    private Long modUserId;

    private Long orderCompletedCount;

    private Long pcJobId;

    private Long pmProjectId;

    private Integer prCount;

    private Long buClientWorkgroupId;

    private Long userStateId;

    private Long totalOrderCount;

    private String userState;

    private boolean isSupplierStatus;

    private boolean isForClient;

    public boolean getIsForClient() {
        return buClientWorkgroupId != null && buClientWorkgroupId > 0;
    }

    public boolean getIsSupplierStatus() {
        return isSupplierStatus;
    }

    public void setIsSupplierStatus(boolean supplierStatus) {
        isSupplierStatus = supplierStatus;
    }

    public String getUserState() {
        return userState;
    }

    public void setUserState(String userState) {
        this.userState = userState;
    }

    public Long getTotalOrderCount() {
        return totalOrderCount;
    }

    public void setTotalOrderCount(Long totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }

    public Long getUserStateId() {
        return userStateId;
    }

    public void setUserStateId(Long userStateId) {
        this.userStateId = userStateId;
    }

    public Long getPcJobStatusId() {
        return pcJobStatusId;
    }

    public void setPcJobStatusId(Long pcJobStatusId) {
        this.pcJobStatusId = pcJobStatusId;
    }

    public Long getBuyerAcWorkgroupId() {
        return buyerAcWorkgroupId;
    }

    public void setBuyerAcWorkgroupId(Long buyerAcWorkgroupId) {
        this.buyerAcWorkgroupId = buyerAcWorkgroupId;
    }

    public Long getOwnerAcWorkgroupId() {
        return ownerAcWorkgroupId;
    }

    public void setOwnerAcWorkgroupId(Long ownerAcWorkgroupId) {
        this.ownerAcWorkgroupId = ownerAcWorkgroupId;
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }

    public Integer getRfeCount() {
        return rfeCount;
    }

    public void setRfeCount(Integer rfeCount) {
        this.rfeCount = rfeCount;
    }

    public Integer getEstimateCount() {
        return estimateCount;
    }

    public void setEstimateCount(Integer estimateCount) {
        this.estimateCount = estimateCount;
    }

    public Integer getRfqCount() {
        return rfqCount;
    }

    public void setRfqCount(Integer rfqCount) {
        this.rfqCount = rfqCount;
    }

    public Integer getQuoteCount() {
        return quoteCount;
    }

    public void setQuoteCount(Integer quoteCount) {
        this.quoteCount = quoteCount;
    }

    public Integer getOrderPendingCount() {
        return orderPendingCount;
    }

    public void setOrderPendingCount(Integer orderPendingCount) {
        this.orderPendingCount = orderPendingCount;
    }

    public Integer getOrderAcceptedCount() {
        return orderAcceptedCount;
    }

    public void setOrderAcceptedCount(Integer orderAcceptedCount) {
        this.orderAcceptedCount = orderAcceptedCount;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Long getModUserId() {
        return modUserId;
    }

    public void setModUserId(Long modUserId) {
        this.modUserId = modUserId;
    }

    public Long getOrderCompletedCount() {
        return orderCompletedCount;
    }

    public void setOrderCompletedCount(Long orderCompletedCount) {
        this.orderCompletedCount = orderCompletedCount;
    }

    public Long getPcJobId() {
        return pcJobId;
    }

    public void setPcJobId(Long pcJobId) {
        this.pcJobId = pcJobId;
    }

    public Long getPmProjectId() {
        return pmProjectId;
    }

    public void setPmProjectId(Long pmProjectId) {
        this.pmProjectId = pmProjectId;
    }

    public Integer getPrCount() {
        return prCount;
    }

    public void setPrCount(Integer prCount) {
        this.prCount = prCount;
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }

    public long getTotalProcurementCount() {
        long count = getRfeCount() + getEstimateCount();
        count += getRfqCount() + getQuoteCount();
        count += getOrderAcceptedCount() + getOrderCompletedCount() + getOrderPendingCount() + getPrCount();
        return count;
    }
}
