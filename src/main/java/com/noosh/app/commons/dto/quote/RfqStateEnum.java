package com.noosh.app.commons.dto.quote;

/**
 * User: leilaz
 * Date: 12/9/15
 * Time: 10:41 PM
 */
public enum RfqStateEnum {
    Draft(2000001),
    Open(2000002),
    Closed(2000003),
    Cancelled(2000004),
    QuoteSubmitted(2000005),
    Declined(2500133);

    private long objectStateId;

    RfqStateEnum(final long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public long getObjectStateId() {
        return objectStateId;
    }

    public static RfqStateEnum getRfqState(final long objectStateId) {
        for(RfqStateEnum rs : RfqStateEnum.values()) {
            if(rs.getObjectStateId() == objectStateId) return rs;
        }
        throw new IllegalArgumentException("Wrong objectStateId for RfqStateEnum");
    }
}
