package com.noosh.app.commons.dto.reason;

import com.noosh.app.commons.constant.ReasonID;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/4/20
 */
public class PcReasonDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long nameStrId;

    private String token;

    private String nameStr;

    private Boolean isSystem;

    private String reasonName;

    public String getReasonName() {
        return reasonName;
    }

    public void setReasonName(String reasonName) {
        this.reasonName = reasonName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean system) {
        isSystem = system;
    }

    public boolean isOther() {
        return this.getId() == ReasonID.REASON_DESC_OTHER;
    }
}
