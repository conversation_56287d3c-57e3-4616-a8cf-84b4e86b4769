package com.noosh.app.commons.dto.costcenter;

import com.noosh.app.commons.dto.property.PropertyAttributeUpdateDTO;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/1/21
 */
public class CostCenterAllocationUpdateDTO implements Serializable {
    private static final long serialVersionUID = 6270985858197786850L;

    private Long id;

    private Double percent;

    private Long costCenterId;

    private List<PropertyAttributeUpdateDTO> customFields;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Double getPercent() {
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public List<PropertyAttributeUpdateDTO> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<PropertyAttributeUpdateDTO> customFields) {
        this.customFields = customFields;
    }
}
