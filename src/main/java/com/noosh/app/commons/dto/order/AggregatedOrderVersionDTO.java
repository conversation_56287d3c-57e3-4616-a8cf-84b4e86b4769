package com.noosh.app.commons.dto.order;

import java.math.BigDecimal;

/**
 * User: <PERSON>
 * Date: 6/21/18
 */
public class AggregatedOrderVersionDTO {
    private OrderVersionDTO originalOrder;
    private OrderVersionDTO aggregatedOrder;
    private boolean enableCOReason;      // change order reason

    private Boolean hasPendingChangeOrder;
    private BigDecimal pendingChangeGrandTotal;
    private Long pendingChangeGrandTotalCurrencyId;
    private BigDecimal exPendingChangeGrandTotal;

    public boolean getEnableCOReason() {
        return enableCOReason;
    }

    public void setEnableCOReason(boolean enableCOReason) {
        this.enableCOReason = enableCOReason;
    }

    public OrderVersionDTO getOriginalOrder() {
        return originalOrder;
    }

    public void setOriginalOrder(OrderVersionDTO originalOrder) {
        this.originalOrder = originalOrder;
    }

    public OrderVersionDTO getAggregatedOrder() {
        return aggregatedOrder;
    }

    public void setAggregatedOrder(OrderVersionDTO aggregatedOrder) {
        this.aggregatedOrder = aggregatedOrder;
    }

    public Boolean getHasPendingChangeOrder() {
        return hasPendingChangeOrder;
    }

    public void setHasPendingChangeOrder(Boolean hasPendingChangeOrder) {
        this.hasPendingChangeOrder = hasPendingChangeOrder;
    }

    public BigDecimal getPendingChangeGrandTotal() {
        return pendingChangeGrandTotal;
    }

    public void setPendingChangeGrandTotal(BigDecimal pendingChangeGrandTotal) {
        this.pendingChangeGrandTotal = pendingChangeGrandTotal;
    }

    public Long getPendingChangeGrandTotalCurrencyId() {
        return pendingChangeGrandTotalCurrencyId;
    }

    public void setPendingChangeGrandTotalCurrencyId(Long pendingChangeGrandTotalCurrencyId) {
        this.pendingChangeGrandTotalCurrencyId = pendingChangeGrandTotalCurrencyId;
    }

    public BigDecimal getExPendingChangeGrandTotal() {
        return exPendingChangeGrandTotal;
    }

    public void setExPendingChangeGrandTotal(BigDecimal exPendingChangeGrandTotal) {
        this.exPendingChangeGrandTotal = exPendingChangeGrandTotal;
    }
}
