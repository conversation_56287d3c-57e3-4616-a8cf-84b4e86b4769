package com.noosh.app.commons.dto.shipment;

import java.io.Serial;
import java.io.Serializable;

public class RequestTypeDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long nameStrId;
    private String nameStr;
    private Long descriptionStrId;
    private String descriptionStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public String getDescriptionStr() {
        return descriptionStr;
    }

    public void setDescriptionStr(String descriptionStr) {
        this.descriptionStr = descriptionStr;
    }
}
