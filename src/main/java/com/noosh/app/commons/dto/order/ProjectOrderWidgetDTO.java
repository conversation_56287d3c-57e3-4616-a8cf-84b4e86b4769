package com.noosh.app.commons.dto.order;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.dto.routing.RoutingSlipDTO;
import com.noosh.app.commons.entity.routing.RoutingSlip;
import com.noosh.app.commons.vo.account.SupplierFlagVO;
import com.noosh.app.commons.vo.rating.SupplierScoreVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 5/10/16
 */
public class ProjectOrderWidgetDTO implements Serializable {
    private static final long serialVersionUID = -3158543389143191271L;
    private Long orderId;
    private Long orderVersionId;
    private Long versionNumber;
    private String reference;
    private String title;
    private String state;
    private Long stateId;
    private LocalDateTime completionDate;
    private LocalDateTime lastModDate;
    private Long supplierWorkgroupId;
    private String supplierWorkgroupName;
    private Long buyerWorkgroupId;
    private String buyerWorkgroupName;
    private Double price;
    private Long priceCurrencyId;
    private Double changePrice;
    private Long changePriceCurrencyId;
    private Long orderTypeId;
    private String orderType;
    private String paymentReference;
    private Long parentOrderId;
    private Long customPropertyId;
    private String parentOrderType;
    private boolean isSellOrder;
    private boolean isClosing;
    private String externalLink;
    private String orderWithChangesLink;
    private String orderDetailsWithSpecLink;
    private String supplierRatingLink;
    private String sourcingStrategiesLink;
    private String cancelOrderLink;
    private boolean showSupplierWorkgroup;
    private Long parentSupplierWorkgroupId;
    private Long parentBuyerWorkgroupId;
    private Long buClientId;
    private Double tax;
    private Long taxCurrencyId;
    private Double shipping;
    private Long shippingCurrencyId;
    private Double discountOrSurcharge;
    private Long discountOrSurchargeCurrencyId;

    private BigDecimal rate;
    private Long exCurrencyId;
    private Double exPrice;
    private Long exPriceCurrencyId;
    private Double exChangePrice;
    private Long exChangePriceCurrencyId;
    private Double exTax;
    private Long exTaxCurrencyId;
    private Double exShipping;
    private Long exShippingCurrencyId;
    private Double exDiscountOrSurcharge;
    private Long exDiscountOrSurchargeCurrencyId;

    private String showPendingApproverLink;
    private List<ProjectOrderWidgetDTO> changeOrderList;
    private SupplierFlagVO supplierFlag;
    private SupplierScoreVO supplierScore;
    private Map<String, Object> customAttributes;
    private Double totalSaving;
    private String editSourcingStrategyExternalLink;
    private Long stateCreateUserId;

    public Double getTotalSaving() {
        return totalSaving;
    }

    public void setTotalSaving(Double totalSaving) {
        this.totalSaving = totalSaving;
    }

    public String getEditSourcingStrategyExternalLink() {
        return editSourcingStrategyExternalLink;
    }

    public void setEditSourcingStrategyExternalLink(String editSourcingStrategyExternalLink) {
        this.editSourcingStrategyExternalLink = editSourcingStrategyExternalLink;
    }

    public Long getStateCreateUserId() {
        return stateCreateUserId;
    }

    public void setStateCreateUserId(Long stateCreateUserId) {
        this.stateCreateUserId = stateCreateUserId;
    }

    public Map<String, Object> getCustomAttributes() {
        return customAttributes;
    }

    public void setCustomAttributes(Map<String, Object> customAttributes) {
        this.customAttributes = customAttributes;
    }

    public String getSourcingStrategiesLink() {
        return sourcingStrategiesLink;
    }

    public void setSourcingStrategiesLink(String sourcingStrategiesLink) {
        this.sourcingStrategiesLink = sourcingStrategiesLink;
    }

    public String getCancelOrderLink() {
        return cancelOrderLink;
    }

    public void setCancelOrderLink(String cancelOrderLink) {
        this.cancelOrderLink = cancelOrderLink;
    }

    public SupplierScoreVO getSupplierScore() {
        return supplierScore;
    }

    public void setSupplierScore(SupplierScoreVO supplierScore) {
        this.supplierScore = supplierScore;
    }

    public Double getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(Double discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public BigDecimal getRate() {
        return this.rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return this.exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public Double getExPrice() {
        return this.exPrice;
    }

    public void setExPrice(Double exPrice) {
        this.exPrice = exPrice;
    }

    public Double getExChangePrice() {
        return this.exChangePrice;
    }

    public void setExChangePrice(Double exChangePrice) {
        this.exChangePrice = exChangePrice;
    }

    public Double getExTax() {
        return this.exTax;
    }

    public void setExTax(Double exTax) {
        this.exTax = exTax;
    }

    public Double getExShipping() {
        return this.exShipping;
    }

    public void setExShipping(Double exShipping) {
        this.exShipping = exShipping;
    }

    public Double getExDiscountOrSurcharge() {
        return this.exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(Double exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Long getPriceCurrencyId() {
        return this.priceCurrencyId;
    }

    public void setPriceCurrencyId(Long priceCurrencyId) {
        this.priceCurrencyId = priceCurrencyId;
    }

    public Long getChangePriceCurrencyId() {
        return this.changePriceCurrencyId;
    }

    public void setChangePriceCurrencyId(Long changePriceCurrencyId) {
        this.changePriceCurrencyId = changePriceCurrencyId;
    }

    public Long getTaxCurrencyId() {
        return this.taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public Long getShippingCurrencyId() {
        return this.shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return this.discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public Long getExPriceCurrencyId() {
        return this.exPriceCurrencyId;
    }

    public void setExPriceCurrencyId(Long exPriceCurrencyId) {
        this.exPriceCurrencyId = exPriceCurrencyId;
    }

    public Long getExChangePriceCurrencyId() {
        return this.exChangePriceCurrencyId;
    }

    public void setExChangePriceCurrencyId(Long exChangePriceCurrencyId) {
        this.exChangePriceCurrencyId = exChangePriceCurrencyId;
    }

    public Long getExTaxCurrencyId() {
        return this.exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public Long getExShippingCurrencyId() {
        return this.exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return this.exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public SupplierFlagVO getSupplierFlag() {
        return supplierFlag;
    }

    public void setSupplierFlag(SupplierFlagVO supplierFlag) {
        this.supplierFlag = supplierFlag;
    }

    public List<ProjectOrderWidgetDTO> getChangeOrderList() {
        return changeOrderList;
    }

    public void setChangeOrderList(List<ProjectOrderWidgetDTO> changeOrderList) {
        this.changeOrderList = changeOrderList;
    }

    public String getShowPendingApproverLink() {
        return showPendingApproverLink;
    }

    public void setShowPendingApproverLink(String showPendingApproverLink) {
        this.showPendingApproverLink = showPendingApproverLink;
    }

    public boolean isAccepted() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_ACCEPTED
                || getStateId() == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                || getStateId() == ObjectStateID.ORDER_SHIPPED
                || getStateId() == ObjectStateID.ORDER_DELIVERED
                || getStateId() == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED
                || getStateId() == ObjectStateID.ORDER_COMPLETED
                || getStateId() == ObjectStateID.ORDER_FINALIZED);
    }

    public boolean isPending() {
        if (isNew())
            return false;
        return (isPendingBuyerAcceptance() || isPendingSupplierAcceptance());
    }

    public boolean isNew() {
        return this.getOrderVersionId() == null || getOrderVersionId().longValue() < 0;
    }

    public boolean isDraft() {
        return this.getOrderVersionId() == null || this.getOrderVersionId() <= 0
                || (getStateId() != null && getStateId() == ObjectStateID.ORDER_DRAFT);
    }

    public boolean isPendingBuyerAcceptance() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_BUYER_TO_ACCEPT || getStateId() == ObjectStateID.ORDER_CLIENT_TO_ACCEPT);
    }

    public boolean isPendingSupplierAcceptance() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_SUPPLIER_TO_ACCEPT || getStateId() == ObjectStateID.ORDER_OUTSOURCER_TO_ACCEPT);
    }

    /**
     * @return true if this order is viewed within the context of a buyer project
     */
    public boolean isUserBuyer(Long ownerWorkgroupId) {
        return this.getBuyerWorkgroupId().longValue() == ownerWorkgroupId;

    }

    public boolean hasOfflineBuyer(boolean isClientNotOnNoosh) {
        if (isClientNotOnNoosh
                && getBuyerWorkgroupId() == getSupplierWorkgroupId()
                && getBuClientId() > 0)
            return true;

        return false;
    }

    public boolean isPendingSubmission() {
        if (getStateId() == null) return false;
        if (getStateId() == ObjectStateID.ORDER_PENDING_SUBMISSION)
            return true;
        return false;
    }

    public boolean isRetracted() {
        if (getStateId() == null) return false;
        return (getStateId() == ObjectStateID.ORDER_RETRACTED);
    }

    public boolean isReplaced() {
        if (getStateId() == null) return false;
        return getStateId() == ObjectStateID.ORDER_REPLACED;
    }

    public boolean isPendingApproval(RoutingSlipDTO routingSlipBean) {
        if (routingSlipBean != null && (routingSlipBean.getApprovedCount() == 0 && routingSlipBean.getDisapprovedCount() == 0))
            return true;
        return false;
    }

    public Double getTax() {
        return tax;
    }

    public void setTax(Double tax) {
        this.tax = tax;
    }

    public Double getShipping() {
        return shipping;
    }

    public void setShipping(Double shipping) {
        this.shipping = shipping;
    }

    public Long getBuClientId() {
        return buClientId;
    }

    public void setBuClientId(Long buClientId) {
        this.buClientId = buClientId;
    }

    public Long getParentBuyerWorkgroupId() {
        return parentBuyerWorkgroupId;
    }

    public void setParentBuyerWorkgroupId(Long parentBuyerWorkgroupId) {
        this.parentBuyerWorkgroupId = parentBuyerWorkgroupId;
    }

    public Long getParentSupplierWorkgroupId() {
        return parentSupplierWorkgroupId;
    }

    public void setParentSupplierWorkgroupId(Long parentSupplierWorkgroupId) {
        this.parentSupplierWorkgroupId = parentSupplierWorkgroupId;
    }

    public boolean isShowSupplierWorkgroup() {
        return showSupplierWorkgroup;
    }

    public void setShowSupplierWorkgroup(boolean showSupplierWorkgroup) {
        this.showSupplierWorkgroup = showSupplierWorkgroup;
    }

    public String getSupplierRatingLink() {
        return supplierRatingLink;
    }

    public void setSupplierRatingLink(String supplierRatingLink) {
        this.supplierRatingLink = supplierRatingLink;
    }

    public Long getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Double getChangePrice() {
        return changePrice;
    }

    public void setChangePrice(Double changePrice) {
        this.changePrice = changePrice;
    }

    public String getOrderDetailsWithSpecLink() {
        return orderDetailsWithSpecLink;
    }

    public void setOrderDetailsWithSpecLink(String orderDetailsWithSpecLink) {
        this.orderDetailsWithSpecLink = orderDetailsWithSpecLink;
    }

    public boolean getIsClosing() {
        return isClosing;
    }

    public void setIsClosing(boolean closing) {
        isClosing = closing;
    }

    public boolean getIsSellOrder() {
        return isSellOrder;
    }

    public void setIsSellOrder(boolean sellOrder) {
        isSellOrder = sellOrder;
    }

    public String getParentOrderType() {
        return parentOrderType;
    }

    public void setParentOrderType(String parentOrderType) {
        this.parentOrderType = parentOrderType;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(Long parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public String getBuyerWorkgroupName() {
        return buyerWorkgroupName;
    }

    public void setBuyerWorkgroupName(String buyerWorkgroupName) {
        this.buyerWorkgroupName = buyerWorkgroupName;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderVersionId() {
        return orderVersionId;
    }

    public void setOrderVersionId(Long orderVersionId) {
        this.orderVersionId = orderVersionId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getLastModDate() {
        return lastModDate;
    }

    public void setLastModDate(LocalDateTime lastModDate) {
        this.lastModDate = lastModDate;
    }

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public String getOrderType() {
        return this.orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getExternalLink() {
        return externalLink;
    }

    public void setExternalLink(String externalLink) {
        this.externalLink = externalLink;
    }

    public String getOrderWithChangesLink() {
        return orderWithChangesLink;
    }

    public void setOrderWithChangesLink(String orderWithChangesLink) {
        this.orderWithChangesLink = orderWithChangesLink;
    }

}
