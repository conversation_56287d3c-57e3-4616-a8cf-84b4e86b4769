package com.noosh.app.commons.dto.order;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 3/27/24
 */
public class OrderSupplierMyDeskDTO implements Serializable {
    private static final long serialVersionUID = -2319448819366093094L;

    private Long orderId;

    private String orderName;

    private String projectName;

    private Long projectId;

    private Long orderStateId;

    private Long orderTypeId;

    private Long parentOrderId;

    private Long buyerWorkgroupId;

    private Long supplierWorkgroupId;

    private String supplierWorkgroupName;

    private BigDecimal amount;

    private BigDecimal taxAndShipping;

    private Long changeOrderCount;

    public Long getChangeOrderCount() {
        return changeOrderCount;
    }

    public void setChangeOrderCount(Long changeOrderCount) {
        this.changeOrderCount = changeOrderCount;
    }

    public BigDecimal getTaxAndShipping() {
        return taxAndShipping;
    }

    public void setTaxAndShipping(BigDecimal taxAndShipping) {
        this.taxAndShipping = taxAndShipping;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public Long getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(Long parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getOrderStateId() {
        return orderStateId;
    }

    public void setOrderStateId(Long orderStateId) {
        this.orderStateId = orderStateId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }
}
