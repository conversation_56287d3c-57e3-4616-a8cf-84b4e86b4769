package com.noosh.app.commons.dto.property;

import java.io.Serializable;
import java.util.Set;

/**
 * User: leilaz
 * Date: 10/8/17
 */
public class PropertyDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long prPropertyId;

    private String propertyName;

    private Long prPropertyTypeId;

    private Long parentPropertyId;

    private Long originalPropertyId;

    private Long clonedFromPropertyId;

    private Long objectId;

    private Long objectClassId;

    private Long ownerAcWorkgroupId;

    private Set<PropertyAttributeDTO> propertyAttributeSet;

    public Long getPrPropertyId() {
        return prPropertyId;
    }

    public void setPrPropertyId(Long prPropertyId) {
        this.prPropertyId = prPropertyId;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName;
    }

    public Long getPrPropertyTypeId() {
        return prPropertyTypeId;
    }

    public void setPrPropertyTypeId(Long prPropertyTypeId) {
        this.prPropertyTypeId = prPropertyTypeId;
    }

    public Long getParentPropertyId() {
        return parentPropertyId;
    }

    public void setParentPropertyId(Long parentPropertyId) {
        this.parentPropertyId = parentPropertyId;
    }

    public Long getOriginalPropertyId() {
        return originalPropertyId;
    }

    public void setOriginalPropertyId(Long originalPropertyId) {
        this.originalPropertyId = originalPropertyId;
    }

    public Long getClonedFromPropertyId() {
        return clonedFromPropertyId;
    }

    public void setClonedFromPropertyId(Long clonedFromPropertyId) {
        this.clonedFromPropertyId = clonedFromPropertyId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getOwnerAcWorkgroupId() {
        return ownerAcWorkgroupId;
    }

    public void setOwnerAcWorkgroupId(Long ownerAcWorkgroupId) {
        this.ownerAcWorkgroupId = ownerAcWorkgroupId;
    }

    public Set<PropertyAttributeDTO> getPropertyAttributeSet() {
        return propertyAttributeSet;
    }

    public void setPropertyAttributeSet(Set<PropertyAttributeDTO> propertyAttributeSet) {
        this.propertyAttributeSet = propertyAttributeSet;
    }
}
