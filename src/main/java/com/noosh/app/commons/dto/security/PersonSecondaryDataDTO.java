package com.noosh.app.commons.dto.security;

public class PersonSecondaryDataDTO {

    private Long id;
    private Long personId;
    private String title;
    private String loginName;
    private String password;
    private String passwordHistory;
    private java.util.Date passwordModDate;
    private Long passwordFailedAttempts;
    private String phoneNumber;
    private String faxNumber;
    private String pagerNumber;
    private String pagerEmail;
    private Boolean acceptUserAgreement;
    private Boolean agreementAccepted;
    private Boolean isTemporaryPassword;

    // association 
    // collection 

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPersonId() {
        return personId;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPasswordHistory() {
        return passwordHistory;
    }

    public void setPasswordHistory(String passwordHistory) {
        this.passwordHistory = passwordHistory;
    }

    public java.util.Date getPasswordModDate() {
        return passwordModDate;
    }

    public void setPasswordModDate(java.util.Date passwordModDate) {
        this.passwordModDate = passwordModDate;
    }

    public Long getPasswordFailedAttempts() {
        return passwordFailedAttempts;
    }

    public void setPasswordFailedAttempts(Long passwordFailedAttempts) {
        this.passwordFailedAttempts = passwordFailedAttempts;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getFaxNumber() {
        return faxNumber;
    }

    public void setFaxNumber(String faxNumber) {
        this.faxNumber = faxNumber;
    }

    public String getPagerNumber() {
        return pagerNumber;
    }

    public void setPagerNumber(String pagerNumber) {
        this.pagerNumber = pagerNumber;
    }

    public String getPagerEmail() {
        return pagerEmail;
    }

    public void setPagerEmail(String pagerEmail) {
        this.pagerEmail = pagerEmail;
    }

    public Boolean getAcceptUserAgreement() {
        return acceptUserAgreement;
    }

    public void setAcceptUserAgreement(Boolean acceptUserAgreement) {
        this.acceptUserAgreement = acceptUserAgreement;
    }

    public Boolean getAgreementAccepted() {
        return agreementAccepted;
    }

    public void setAgreementAccepted(Boolean agreementAccepted) {
        this.agreementAccepted = agreementAccepted;
    }

    public Boolean getIsTemporaryPassword() {
        return isTemporaryPassword;
    }

    public void setIsTemporaryPassword(Boolean isTemporaryPassword) {
        this.isTemporaryPassword = isTemporaryPassword;
    }

}