package com.noosh.app.commons.dto.invoice;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 4/10/24
 */
public class InvoicePendingMyDeskDTO implements Serializable {
    private static final long serialVersionUID = 1215776287933369984L;

    private String projectName;

    private Long projectId;

    private Long invoiceId;

    private Long buyerWorkgroupId;

    private Long supplierWorkgroupId;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }
}
