package com.noosh.app.commons.dto.routing;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.entity.routing.RoutingRecipient;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * User: lukez
 * Date: 6/29/22
 */
public class RoutingSlipDTO implements Serializable {

    private static final long serialVersionUID = -628893042495511593L;

    public static final long RS_APPROVED = 1;
    public static final long RS_APPROVED_WITH_CHANGES = 2;
    public static final long RS_DISAPPROVED = 0;
    public static final long RS_INDETERMINATE = -1;

    private Long id;

    private Long acWorkgroupId;

    private String slipName;

    private Long objectId;

    private Long objectClassId;

    private Long fromUserId;

    private String subject;

    private String messageText;

    private LocalDateTime dateSubmitted;

    private Short isAllAtOnce;

    private Long currentRecipientId;

    private Long recipientTimeoutPeriod;

    private Short trackStatus;

    private Short notifyWhenDone;

    private Short firstResponseMode;

    private Long parentObjectId;

    private Long parentObjectClassId;

    private Long ocObjectStateId;

    private Short stopRoutingOnDisapproval;

    private Short isSequentialApprovals;

    private Short isManagerApprovals;

    private Short hasManagerApproval;

    private Short isManagerRoutingSlip;

    private Short isEdit;

    private List<RoutingRecipientDTO> routingRecipients;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAcWorkgroupId() {
        return this.acWorkgroupId;
    }

    public void setAcWorkgroupId(Long acWorkgroupId) {
        this.acWorkgroupId = acWorkgroupId;
    }

    public String getSlipName() {
        return this.slipName;
    }

    public void setSlipName(String slipName) {
        this.slipName = slipName;
    }

    public Long getObjectId() {
        return this.objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return this.objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getFromUserId() {
        return this.fromUserId;
    }

    public void setFromUserId(Long fromUserId) {
        this.fromUserId = fromUserId;
    }

    public String getSubject() {
        return this.subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMessageText() {
        return this.messageText;
    }

    public void setMessageText(String messageText) {
        this.messageText = messageText;
    }

    public LocalDateTime getDateSubmitted() {
        return this.dateSubmitted;
    }

    public void setDateSubmitted(LocalDateTime dateSubmitted) {
        this.dateSubmitted = dateSubmitted;
    }

    public Short getIsAllAtOnce() {
        return this.isAllAtOnce;
    }

    public void setIsAllAtOnce(Short isAllAtOnce) {
        this.isAllAtOnce = isAllAtOnce;
    }

    public Long getCurrentRecipientId() {
        return this.currentRecipientId;
    }

    public void setCurrentRecipientId(Long currentRecipientId) {
        this.currentRecipientId = currentRecipientId;
    }

    public Long getRecipientTimeoutPeriod() {
        return this.recipientTimeoutPeriod;
    }

    public void setRecipientTimeoutPeriod(Long recipientTimeoutPeriod) {
        this.recipientTimeoutPeriod = recipientTimeoutPeriod;
    }

    public Short getTrackStatus() {
        return this.trackStatus;
    }

    public void setTrackStatus(Short trackStatus) {
        this.trackStatus = trackStatus;
    }

    public Short getNotifyWhenDone() {
        return this.notifyWhenDone;
    }

    public void setNotifyWhenDone(Short notifyWhenDone) {
        this.notifyWhenDone = notifyWhenDone;
    }

    public Short getFirstResponseMode() {
        return this.firstResponseMode;
    }

    public void setFirstResponseMode(Short firstResponseMode) {
        this.firstResponseMode = firstResponseMode;
    }

    public Long getParentObjectId() {
        return this.parentObjectId;
    }

    public void setParentObjectId(Long parentObjectId) {
        this.parentObjectId = parentObjectId;
    }

    public Long getParentObjectClassId() {
        return this.parentObjectClassId;
    }

    public void setParentObjectClassId(Long parentObjectClassId) {
        this.parentObjectClassId = parentObjectClassId;
    }

    public Long getOcObjectStateId() {
        return this.ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }

    public Short getStopRoutingOnDisapproval() {
        return this.stopRoutingOnDisapproval;
    }

    public void setStopRoutingOnDisapproval(Short stopRoutingOnDisapproval) {
        this.stopRoutingOnDisapproval = stopRoutingOnDisapproval;
    }

    public Short getIsSequentialApprovals() {
        return this.isSequentialApprovals;
    }

    public void setIsSequentialApprovals(Short isSequentialApprovals) {
        this.isSequentialApprovals = isSequentialApprovals;
    }

    public Short getIsManagerApprovals() {
        return this.isManagerApprovals;
    }

    public void setIsManagerApprovals(Short isManagerApprovals) {
        this.isManagerApprovals = isManagerApprovals;
    }

    public Short getHasManagerApproval() {
        return this.hasManagerApproval;
    }

    public void setHasManagerApproval(Short hasManagerApproval) {
        this.hasManagerApproval = hasManagerApproval;
    }

    public Short getIsManagerRoutingSlip() {
        return this.isManagerRoutingSlip;
    }

    public void setIsManagerRoutingSlip(Short isManagerRoutingSlip) {
        this.isManagerRoutingSlip = isManagerRoutingSlip;
    }

    public Short getIsEdit() {
        return this.isEdit;
    }

    public void setIsEdit(Short isEdit) {
        this.isEdit = isEdit;
    }

    public List<RoutingRecipientDTO> getRoutingRecipients() {
        return this.routingRecipients;
    }

    public void setRoutingRecipients(List<RoutingRecipientDTO> routingRecipients) {
        this.routingRecipients = routingRecipients;
    }

    public long getAllApprovedCount() {
        return getCount(RS_APPROVED) + getCount(RS_APPROVED_WITH_CHANGES);
    }

    public long getApprovedCount() {
        return getCount(RS_APPROVED);
    }

    public long getApprovedWithChangesCount() {
        return getCount(RS_APPROVED_WITH_CHANGES);
    }

    public long getDisapprovedCount() {
        return getCount(RS_DISAPPROVED);
    }

    public long getPendingCount() {
        return getCount(RS_INDETERMINATE);
    }

    public long getCount(long state) {
        long count = 0;
        List<RoutingRecipientDTO> rrb = this.getRoutingRecipients();
        if (rrb != null) {
            for (int i = 0; i < rrb.size(); i++) {
                if (rrb.get(i).getResponse() == state) {
                    count++;
                }
            }
        }
        return count;
    }

    public boolean compareCurrentRecipient(RoutingRecipient recipient) {
        return recipient.getId() == this.getCurrentRecipientId();
    }

    public boolean getIsActive() {
        return this.getOcObjectStateId() == ObjectStateID.ROUTING_SLIP_ACTIVE;
    }

    public boolean getIsCompleted() {
        return this.getOcObjectStateId() == ObjectStateID.ROUTING_SLIP_COMPLETED;
    }

    public boolean getIsCancelled() {
        return this.getOcObjectStateId() == ObjectStateID.ROUTING_SLIP_CANCELLED;
    }

    public boolean getIsApproved() {
        return (this.getIsCompleted() && this.getAllApprovedCount() > 0 && this.getDisapprovedCount() == 0);
    }

    public boolean getIsDisapproved() {
        return (this.getIsCompleted() && this.getAllApprovedCount() == 0 && this.getDisapprovedCount() > 0);
    }

    public boolean getHasAllRecipientsApproved() {
        List<RoutingRecipientDTO> rrb = this.getRoutingRecipients();
        if (rrb == null)
            return false;
        return (getAllApprovedCount() == rrb.size());
    }

    public boolean getIsAllRecipientsApprovedNoChanges() {
        List<RoutingRecipientDTO> rrb = this.getRoutingRecipients();
        if (rrb == null)
            return false;
        return (getApprovedCount() == rrb.size());
    }

    //I just need one manager to have approved
    public boolean getIsManagerApproved() {
        if (getIsManagerRoutingSlip() != null && getIsManagerRoutingSlip().shortValue() == 1) {
            if (getRoutingRecipients() != null) {
                for (int i = 0; i < getRoutingRecipients().size(); i++) {
                    if (getRoutingRecipients().get(i).getResponse() == RS_APPROVED) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public boolean isAllRecipientsApproved() {
        List<RoutingRecipientDTO> rrb = this.getRoutingRecipients();
        if (rrb == null)
            return false;
        for (int i = 0; i < rrb.size(); i++) {
            if (rrb.get(i).getResponse() != RS_APPROVED) {
                return false;
            }
        }
        return true;
    }
}
