package com.noosh.app.commons.dto.account;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDateTime;

import java.io.Serializable;
import java.util.Locale;
import java.util.Objects;

/**
 * User: leilaz
 * Date: 8/30/19
 */
public class AccountUserDTO implements Serializable {

    private static final long serialVersionUID = -998891609094607697L;
    private Long id;

    private Long workgroupId;

    private Long personId;

    private Boolean isDefault;

    private Long objectStateId;

    private Boolean isLocked;

    private LocalDateTime activationDate;

    private LocalDateTime createDate;

    private LocalDateTime modDate;

    private Long createUserId;

    private Long modUserId;

    //attributes from Person
    private String firstName;

    private String middleName;

    private String lastName;

    //attributes from PersonSd
    private String loginName;

    private String password;

    private String phoneNumber;

    //attribute from PersonEmail
    private String defaultEmail;

    private Locale locale;

    private Long workgroupTypeId;

    private String profileImg;

    public AccountUserDTO() {}

    public Long getWorkgroupTypeId() {
        return workgroupTypeId;
    }

    public void setWorkgroupTypeId(Long workgroupTypeId) {
        this.workgroupTypeId = workgroupTypeId;
    }

    public Locale getLocale() {
        return locale;
    }

    public void setLocale(Locale locale) {
        this.locale = locale;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return id;
    }

    public void setUserId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getPersonId() {
        return personId;
    }

    public void setPersonId(Long personId) {
        this.personId = personId;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public Boolean getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(Boolean isLocked) {
        this.isLocked = isLocked;
    }

    public LocalDateTime getActivationDate() {
        return activationDate;
    }

    public void setActivationDate(LocalDateTime activationDate) {
        this.activationDate = activationDate;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Long getModUserId() {
        return modUserId;
    }

    public void setModUserId(Long modUserId) {
        this.modUserId = modUserId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getDefaultEmail() {
        return defaultEmail;
    }

    public void setDefaultEmail(String defaultEmail) {
        this.defaultEmail = defaultEmail;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getProfileImg() {
        return this.profileImg;
    }

    public void setProfileImg(String profileImg) {
        this.profileImg = profileImg;
    }

    @JsonIgnore
    public String getFullName() {
        return firstName + " " + (middleName == null ? "" : middleName + " ") + lastName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        AccountUserDTO accountUserDTO = (AccountUserDTO) o;

        if ( ! Objects.equals(id, accountUserDTO.id)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "AccountUserDTO{" +
                "id=" + id +
                ", workgroupId='" + workgroupId + "'" +
                ", personId='" + personId + "'" +
                ", isDefault='" + isDefault + "'" +
                ", objectStateId='" + objectStateId + "'" +
                ", isLocked='" + isLocked + "'" +
                '}';
    }
}
