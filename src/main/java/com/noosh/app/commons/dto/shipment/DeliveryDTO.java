package com.noosh.app.commons.dto.shipment;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class DeliveryDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private Long requestId;
    private LocalDateTime shippedDate;
    private BigDecimal shippedQuantity;
    private Long carrierId;
    private String carrierOther;
    private Long methodId;
    private String methodOther;
    private Long units;
    private Long uofmId;
    private BigDecimal weights;
    private Long weightUofmId;
    private String trackingnumber;
    private BigDecimal cost;
    private Long costCurrencyId;
    private BigDecimal tax;
    private Long taxCurrencyId;
    private LocalDateTime receivedDate;
    private BigDecimal receivedQuantity;
    private String comments;
    private String receivedComments;
    private Long customPropertyId;

    private MethodDTO method;
    private CarrierDTO carrier;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public LocalDateTime getShippedDate() {
        return shippedDate;
    }

    public void setShippedDate(LocalDateTime shippedDate) {
        this.shippedDate = shippedDate;
    }

    public BigDecimal getShippedQuantity() {
        return shippedQuantity;
    }

    public void setShippedQuantity(BigDecimal shippedQuantity) {
        this.shippedQuantity = shippedQuantity;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getCarrierOther() {
        return carrierOther;
    }

    public void setCarrierOther(String carrierOther) {
        this.carrierOther = carrierOther;
    }

    public Long getMethodId() {
        return methodId;
    }

    public void setMethodId(Long methodId) {
        this.methodId = methodId;
    }

    public String getMethodOther() {
        return methodOther;
    }

    public void setMethodOther(String methodOther) {
        this.methodOther = methodOther;
    }

    public Long getUnits() {
        return units;
    }

    public void setUnits(Long units) {
        this.units = units;
    }

    public Long getUofmId() {
        return uofmId;
    }

    public void setUofmId(Long uofmId) {
        this.uofmId = uofmId;
    }

    public BigDecimal getWeights() {
        return weights;
    }

    public void setWeights(BigDecimal weights) {
        this.weights = weights;
    }

    public Long getWeightUofmId() {
        return weightUofmId;
    }

    public void setWeightUofmId(Long weightUofmId) {
        this.weightUofmId = weightUofmId;
    }

    public String getTrackingnumber() {
        return trackingnumber;
    }

    public void setTrackingnumber(String trackingnumber) {
        this.trackingnumber = trackingnumber;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public Long getCostCurrencyId() {
        return costCurrencyId;
    }

    public void setCostCurrencyId(Long costCurrencyId) {
        this.costCurrencyId = costCurrencyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public LocalDateTime getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDateTime receivedDate) {
        this.receivedDate = receivedDate;
    }

    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getReceivedComments() {
        return receivedComments;
    }

    public void setReceivedComments(String receivedComments) {
        this.receivedComments = receivedComments;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public MethodDTO getMethod() {
        return method;
    }

    public void setMethod(MethodDTO method) {
        this.method = method;
    }

    public CarrierDTO getCarrier() {
        return carrier;
    }

    public void setCarrier(CarrierDTO carrier) {
        this.carrier = carrier;
    }
}
