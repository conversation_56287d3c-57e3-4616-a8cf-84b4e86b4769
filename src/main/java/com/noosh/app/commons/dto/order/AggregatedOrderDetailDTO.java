package com.noosh.app.commons.dto.order;

import com.noosh.app.commons.dto.terms.AcTermsDTO;

import java.io.Serializable;

/**
 * User: <PERSON> Shan
 * Date: 6/21/18
 */
public class AggregatedOrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private AggregatedOrderVersionDTO orderVersionDTO;
    private AcTermsDTO buyerTerms;
    private AcTermsDTO supplierTerms;

    private String routeForApprovalButton;
    private String routeForManagerApprovalButton;
    private String submitButton;
    private String acceptButton;
    private String rejectButton;
    private String retractButton;
    private String editButton;
    private String cancelButton;
    private String costCenterButton;
    private String carryOverChangeOrderButton;
    private String acceptAndCarryOverButton;

    private boolean canAccept;

    private String reorderButton;
    private String createChangeOrderButton;
    private String editSupplierRefButton;
    private String costCenterAllocationButton;
    private String editShipmentButton;
    private String supplierRatingButton;
    private String completeButton;
    private String dismissButton;
    private String sourcingStrategiesButton;

    public String getSourcingStrategiesButton() {
        return sourcingStrategiesButton;
    }

    public void setSourcingStrategiesButton(String sourcingStrategiesButton) {
        this.sourcingStrategiesButton = sourcingStrategiesButton;
    }

    public String getReorderButton() {
        return reorderButton;
    }

    public void setReorderButton(String reorderButton) {
        this.reorderButton = reorderButton;
    }

    public String getCreateChangeOrderButton() {
        return createChangeOrderButton;
    }

    public void setCreateChangeOrderButton(String createChangeOrderButton) {
        this.createChangeOrderButton = createChangeOrderButton;
    }

    public String getEditSupplierRefButton() {
        return editSupplierRefButton;
    }

    public void setEditSupplierRefButton(String editSupplierRefButton) {
        this.editSupplierRefButton = editSupplierRefButton;
    }

    public String getCostCenterAllocationButton() {
        return costCenterAllocationButton;
    }

    public void setCostCenterAllocationButton(String costCenterAllocationButton) {
        this.costCenterAllocationButton = costCenterAllocationButton;
    }

    public String getEditShipmentButton() {
        return editShipmentButton;
    }

    public void setEditShipmentButton(String editShipmentButton) {
        this.editShipmentButton = editShipmentButton;
    }

    public String getSupplierRatingButton() {
        return supplierRatingButton;
    }

    public void setSupplierRatingButton(String supplierRatingButton) {
        this.supplierRatingButton = supplierRatingButton;
    }

    public String getCompleteButton() {
        return completeButton;
    }

    public void setCompleteButton(String completeButton) {
        this.completeButton = completeButton;
    }

    public String getDismissButton() {
        return dismissButton;
    }

    public void setDismissButton(String dismissButton) {
        this.dismissButton = dismissButton;
    }

    public String getCarryOverChangeOrderButton() {
        return carryOverChangeOrderButton;
    }

    public void setCarryOverChangeOrderButton(String carryOverChangeOrderButton) {
        this.carryOverChangeOrderButton = carryOverChangeOrderButton;
    }

    public String getAcceptAndCarryOverButton() {
        return acceptAndCarryOverButton;
    }

    public void setAcceptAndCarryOverButton(String acceptAndCarryOverButton) {
        this.acceptAndCarryOverButton = acceptAndCarryOverButton;
    }

    public boolean isCanAccept() {
        return canAccept;
    }

    public void setCanAccept(boolean canAccept) {
        this.canAccept = canAccept;
    }

    public String getCostCenterButton() {
        return costCenterButton;
    }

    public void setCostCenterButton(String costCenterButton) {
        this.costCenterButton = costCenterButton;
    }

    public String getCancelButton() {
        return cancelButton;
    }

    public void setCancelButton(String cancelButton) {
        this.cancelButton = cancelButton;
    }

    public String getEditButton() {
        return editButton;
    }

    public void setEditButton(String editButton) {
        this.editButton = editButton;
    }

    public String getRetractButton() {
        return retractButton;
    }

    public void setRetractButton(String retractButton) {
        this.retractButton = retractButton;
    }

    public String getRejectButton() {
        return rejectButton;
    }

    public void setRejectButton(String rejectButton) {
        this.rejectButton = rejectButton;
    }

    public String getAcceptButton() {
        return acceptButton;
    }

    public void setAcceptButton(String acceptButton) {
        this.acceptButton = acceptButton;
    }

    public AggregatedOrderVersionDTO getOrderVersionDTO() {
        return orderVersionDTO;
    }

    public void setOrderVersionDTO(AggregatedOrderVersionDTO orderVersionDTO) {
        this.orderVersionDTO = orderVersionDTO;
    }

    public AcTermsDTO getBuyerTerms() {
        return buyerTerms;
    }

    public void setBuyerTerms(AcTermsDTO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public String getSubmitButton() {
        return submitButton;
    }

    public void setSubmitButton(String submitButton) {
        this.submitButton = submitButton;
    }

    public String getRouteForManagerApprovalButton() {
        return routeForManagerApprovalButton;
    }

    public void setRouteForManagerApprovalButton(String routeForManagerApprovalButton) {
        this.routeForManagerApprovalButton = routeForManagerApprovalButton;
    }

    public AcTermsDTO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(AcTermsDTO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public String getRouteForApprovalButton() {
        return routeForApprovalButton;
    }

    public void setRouteForApprovalButton(String routeForApprovalButton) {
        this.routeForApprovalButton = routeForApprovalButton;
    }
}
