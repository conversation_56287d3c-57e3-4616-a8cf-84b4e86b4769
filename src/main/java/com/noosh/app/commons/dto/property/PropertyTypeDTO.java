package com.noosh.app.commons.dto.property;

/**
 * <AUTHOR>
 * @date 11/29/2021
 */
public class PropertyTypeDTO {

    private Long propertyTypeId;
    private String typeName;
    private String description;
    private Long parentPropertyTypeId;

    public Long getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Long propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getParentPropertyTypeId() {
        return parentPropertyTypeId;
    }

    public void setParentPropertyTypeId(Long parentPropertyTypeId) {
        this.parentPropertyTypeId = parentPropertyTypeId;
    }
}
