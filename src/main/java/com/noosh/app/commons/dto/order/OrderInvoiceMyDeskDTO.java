package com.noosh.app.commons.dto.order;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 4/6/24
 */
public class OrderInvoiceMyDeskDTO implements Serializable {
    private static final long serialVersionUID = 7179829257710213362L;

    private Long orderId;

    private String orderName;

    private String projectName;

    private Long projectId;

    private Long orderTypeId;

    private Long parentOrderId;

    private Long invoiceId;

    private BigDecimal orderAmount;

    private BigDecimal orderTaxAndShipping;

    private BigDecimal invoiceAmount;

    private BigDecimal invoiceTaxAndShipping;

    private Long buyerWorkgroupId;

    private Long supplierWorkgroupId;

    private Long changeOrderCount;

    private BigDecimal invoiceQuantity;

    private BigDecimal orderQuantity;

    public BigDecimal getInvoiceQuantity() {
        return invoiceQuantity;
    }

    public void setInvoiceQuantity(BigDecimal invoiceQuantity) {
        this.invoiceQuantity = invoiceQuantity;
    }

    public BigDecimal getOrderQuantity() {
        return orderQuantity;
    }

    public void setOrderQuantity(BigDecimal orderQuantity) {
        this.orderQuantity = orderQuantity;
    }

    public Long getChangeOrderCount() {
        return changeOrderCount;
    }

    public void setChangeOrderCount(Long changeOrderCount) {
        this.changeOrderCount = changeOrderCount;
    }

    public BigDecimal getInvoiceTaxAndShipping() {
        return invoiceTaxAndShipping;
    }

    public void setInvoiceTaxAndShipping(BigDecimal invoiceTaxAndShipping) {
        this.invoiceTaxAndShipping = invoiceTaxAndShipping;
    }

    public BigDecimal getOrderTaxAndShipping() {
        return orderTaxAndShipping;
    }

    public void setOrderTaxAndShipping(BigDecimal orderTaxAndShipping) {
        this.orderTaxAndShipping = orderTaxAndShipping;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public Long getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(Long parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }
}
