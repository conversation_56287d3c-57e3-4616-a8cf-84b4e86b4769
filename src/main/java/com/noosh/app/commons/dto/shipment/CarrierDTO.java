package com.noosh.app.commons.dto.shipment;

import java.io.Serial;
import java.io.Serializable;


public class CarrierDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;
    private String description;
    private Long nameStrId;
    private String nameStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }
}
