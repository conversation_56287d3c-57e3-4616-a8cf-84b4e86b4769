package com.noosh.app.commons.dto.apijob.builder;

import com.noosh.app.commons.dto.apijob.ScheduledApiDto;
import org.springframework.http.HttpHeaders;

import java.util.Date;

public class ScheduledApiDtoBuilder {

    public static Builder builder() {
        return new BuilderImpl();
    }

    public interface Builder {
        Builder protocol(String protocol);

        Builder host(String host);

        Builder path(String path);

        Builder method(String method);

        Builder headers(HttpHeaders headers);

        Builder body(String body);

        Builder callbackUrl(String callbackUrl);

        Builder createUserId(Integer createUserId);

        Builder createDate(Date createDate);

        ScheduledApiDto build();
    }

    static final class BuilderImpl implements Builder {
        private String protocol;

        private String host;

        private String path;

        private String method;

        private HttpHeaders headers;

        private String body;

        private String callbackUrl;

        private Integer createUserId;

        private Date createDate;

        @Override
        public Builder protocol(String protocol) {
            this.protocol = protocol;
            return this;
        }

        @Override
        public Builder host(String host) {
            this.host = host;
            return this;
        }

        @Override
        public Builder path(String path) {
            this.path = path;
            return this;
        }

        @Override
        public Builder method(String method) {
            this.method = method;
            return this;
        }

        @Override
        public Builder headers(HttpHeaders headers) {
            this.headers = headers;
            return this;
        }

        @Override
        public Builder body(String body) {
            this.body = body;
            return this;
        }

        @Override
        public Builder callbackUrl(String callbackUrl) {
            this.callbackUrl = callbackUrl;
            return this;
        }

        @Override
        public Builder createUserId(Integer createUserId) {
            this.createUserId = createUserId;
            return this;
        }

        @Override
        public Builder createDate(Date createDate) {
            this.createDate = createDate;
            return this;
        }

        @Override
        public ScheduledApiDto build() {
            ScheduledApiDto scheduledApiDto = new ScheduledApiDto();
            scheduledApiDto.setProtocol(protocol);
            scheduledApiDto.setHost(host);
            scheduledApiDto.setPath(path);
            scheduledApiDto.setMethod(method);
            scheduledApiDto.setHeaders(headers);
            scheduledApiDto.setBody(body);
            scheduledApiDto.setCallbackUrl(callbackUrl);
            scheduledApiDto.setCreateUserId(createUserId);
            scheduledApiDto.setCreateDate(createDate);
            return scheduledApiDto;
        }
    }
}