package com.noosh.app.commons.dto.quote;


import com.noosh.app.commons.constant.ObjectStateID;

import java.time.LocalDateTime;

import java.math.BigDecimal;


public class QuoteWidgetDTO {

    private Long id;
    private String title;
    private String state;
    private String stateStrId;
    private Long stateId;
    private LocalDateTime submitDate;
    private LocalDateTime completionDate;

    private Long projectId;
    private String projectName;
    private String externalLink;

    private String clientWorkgroupName;
    private String supplierWorkgroupName;
    private BigDecimal itemTotal;
    private Long itemTotalCurrencyId;
    private BigDecimal exItemTotal;
    private Long exItemTotalCurrencyId;
    private Boolean isClient;
    private String projectExternalLink;
    private BigDecimal tax;
    private BigDecimal exTax;
    private BigDecimal shipping;
    private BigDecimal exShipping;
    private BigDecimal rate;
    private Long exCurrencyId;
    private BigDecimal total;
    private Long clientWorkgroupId;
    private Long supplierWorkgroupId;
    private Long buyerWorkgroupId;
    private String printDetailLink;
    private String createBuyOrderLink;

    public String getStateStrId() {
        return stateStrId;
    }

    public void setStateStrId(String stateStrId) {
        this.stateStrId = stateStrId;
    }

    public String getPrintDetailLink() {
        return printDetailLink;
    }

    public void setPrintDetailLink(String printDetailLink) {
        this.printDetailLink = printDetailLink;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public Long getClientWorkgroupId() {
        return clientWorkgroupId;
    }

    public void setClientWorkgroupId(Long clientWorkgroupId) {
        this.clientWorkgroupId = clientWorkgroupId;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public BigDecimal getTotal() {
        return getItemTotal().add(getTax()).add(getShipping());
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public Long getTotalCurrencyId() {
        return this.getItemTotalCurrencyId();
    }

    public BigDecimal getExTotal() {
        return getExItemTotal().add(getExTax()).add(getExShipping());
    }

    public Long getExTotalCurrencyId() {
        return this.getExItemTotalCurrencyId();
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax != null ? tax : new BigDecimal("0");
    }

    public BigDecimal getExTax() {
        return exTax != null ? exTax : new BigDecimal("0");
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping != null ? shipping : new BigDecimal("0");
    }

    public BigDecimal getExShipping() {
        return exShipping != null ? exShipping : new BigDecimal("0");
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public String getProjectExternalLink() {
        return projectExternalLink;
    }

    public void setProjectExternalLink(String projectExternalLink) {
        this.projectExternalLink = projectExternalLink;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
        this.state = QuoteStateEnum.getQuoteState(stateId).name();
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getClientWorkgroupName() {
        return clientWorkgroupName;
    }

    public void setClientWorkgroupName(String clientWorkgroupName) {
        this.clientWorkgroupName = clientWorkgroupName;
    }

    public BigDecimal getItemTotal() {
        return itemTotal;
    }

    public void setItemTotal(BigDecimal itemTotal) {
        this.itemTotal = itemTotal;
    }

    public Long getItemTotalCurrencyId() {
        return itemTotalCurrencyId;
    }

    public void setItemTotalCurrencyId(Long itemTotalCurrencyId) {
        this.itemTotalCurrencyId = itemTotalCurrencyId;
    }

    public BigDecimal getExItemTotal() {
        return exItemTotal;
    }

    public void setExItemTotal(BigDecimal exItemTotal) {
        this.exItemTotal = exItemTotal;
    }

    public Long getExItemTotalCurrencyId() {
        return exItemTotalCurrencyId;
    }

    public void setExItemTotalCurrencyId(Long exItemTotalCurrencyId) {
        this.exItemTotalCurrencyId = exItemTotalCurrencyId;
    }

    public String getExternalLink() {
        return externalLink;
    }

    public void setExternalLink(String externalLink) {
        this.externalLink = externalLink;
    }

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public Boolean isClient() {
        return isClient;
    }

    public void setClient(Boolean client) {
        isClient = client;
    }

    public boolean isRejected() {
        return (this.getStateId() == ObjectStateID.QUOTE_REJECTED);
    }

    public boolean isCancelled() {
        return (this.getStateId() == ObjectStateID.QUOTE_CANCELLED);
    }

    public boolean isRetracted() {
        return (this.getStateId() == ObjectStateID.QUOTE_RETRACTED);
    }

    public boolean isAccepted() {
        return (this.getStateId() == ObjectStateID.QUOTE_ACCEPTED);
    }

    public boolean isPending() {
        return (this.getStateId() == ObjectStateID.QUOTE_PENDING_CLIENT_ACCEPTANCE);
    }

    public boolean isDraft() {
        return (this.getStateId() == ObjectStateID.QUOTE_DRAFT || ObjectStateID.QUOTE_INCOMPLETE_DRAFT == this.getStateId());
    }

    public String getCreateBuyOrderLink() {
        return this.createBuyOrderLink;
    }

    public void setCreateBuyOrderLink(String createBuyOrderLink) {
        this.createBuyOrderLink = createBuyOrderLink;
    }
}
