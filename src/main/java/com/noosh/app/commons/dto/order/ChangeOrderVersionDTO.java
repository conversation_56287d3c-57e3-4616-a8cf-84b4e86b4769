package com.noosh.app.commons.dto.order;

import java.util.List;

/**
 * User: leilaz
 * Date: 1/2/18
 */
public class ChangeOrderVersionDTO {
    private OrderVersionDTO changeOrder; // original or change order
    private OrderVersionDTO parentOrder; // aggregated order
    private OrderVersionDTO baseOrder; // aggregated order
    private boolean enableCOReason;      // change order reason
    private boolean isManagerPresentAtCostLevel;

    public boolean getIsManagerPresentAtCostLevel() {
        return isManagerPresentAtCostLevel;
    }

    public void setIsManagerPresentAtCostLevel(boolean managerPresentAtCostLevel) {
        isManagerPresentAtCostLevel = managerPresentAtCostLevel;
    }

    public OrderVersionDTO getBaseOrder() {
        return baseOrder;
    }

    public void setBaseOrder(OrderVersionDTO baseOrder) {
        this.baseOrder = baseOrder;
    }

    public boolean getEnableCOReason() {
        return enableCOReason;
    }

    public void setEnableCOReason(boolean enableCOReason) {
        this.enableCOReason = enableCOReason;
    }

    public OrderVersionDTO getChangeOrder() {
        return changeOrder;
    }

    public void setChangeOrder(OrderVersionDTO changeOrder) {
        this.changeOrder = changeOrder;
    }

    public OrderVersionDTO getParentOrder() {
        return parentOrder;
    }

    public void setParentOrder(OrderVersionDTO parentOrder) {
        this.parentOrder = parentOrder;
    }
}
