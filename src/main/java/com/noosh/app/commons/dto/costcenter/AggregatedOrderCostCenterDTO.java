package com.noosh.app.commons.dto.costcenter;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 1/11/21
 */
public class AggregatedOrderCostCenterDTO implements Serializable {
    private static final long serialVersionUID = -4033856566508984002L;

    private double aggregatedAmount;

    private List<CostCenterAllocationDTO> costCenters;

    public List<CostCenterAllocationDTO> getCostCenters() {
        return costCenters;
    }

    public void setCostCenters(List<CostCenterAllocationDTO> costCenters) {
        this.costCenters = costCenters;
    }

    public double getAggregatedAmount() {
        return aggregatedAmount;
    }

    public void setAggregatedAmount(double aggregatedAmount) {
        this.aggregatedAmount = aggregatedAmount;
    }
}
