package com.noosh.app.commons.constant;

/**
 * Constant interface: ObjectStateID
 * Constant source:    DB
 * Constant query:     select OC_OBJECT_STATE_id, constant_token from OC_OBJECT_STATE order by 1 asc
 *
 * Copyright 1998-2007 Noosh, Inc.
 *
 * <AUTHOR>
 * @since  Thu Oct 04 22:24:54 GMT 2007
 */
public interface ObjectStateID
{
    public static final long RFQ_DRAFT = 2000001;
    public static final long RFQ_OPEN = 2000002;
    public static final long RFQ_CLOSED = 2000003;
    public static final long RFQ_CANCELLED = 2000004;
    public static final long RFQ_QUOTE_SUBMITTED = 2000005;
    public static final long QUOTE_RFQ_CANCELLED = 2000006;
    public static final long QUOTE_DRAFT = 2000007;
    public static final long QUOTE_PENDING_CLIENT_ACCEPTANCE = 2000008;
    public static final long QUOTE_RETRACTED = 2000009;
    public static final long QUOTE_REJECTED = 2000010;
    public static final long QUOTE_ACCEPTED = 2000011;
    public static final long PROPOSAL_DRAFT = 2000012;
    public static final long PROPOSAL_PENDING = 2000013;
    public static final long PROPOSAL_ACCEPTED = 2000014;
    public static final long PROPOSAL_REJECTED = 2000015;
    public static final long PROPOSAL_RETRACTED = 2000016;
    public static final long QUOTE_PENDING_PR_APPROVAL = 2000017;
    public static final long QUOTE_CANCELLED = 2000018;
    public static final long PROPOSAL_CANCELLED = 2000019;
    public static final long JOB_NEW = 2000020;
    public static final long JOB_IN_ESTIMATING = 2000021;
    public static final long JOB_IN_QUOTING = 2000022;
    public static final long JOB_ORDER_PENDING = 2000023;
    public static final long JOB_ORDER_ACCEPTED = 2000024;
    public static final long JOB_COMPLETED = 2000025;
    public static final long JOB_LOST = 2000026;
    public static final long JOB_IN_PR = 2000027;
    public static final long ORDER_PENDING_SUBMISSION = 2000030;
    public static final long ORDER_ALLOCATION_ACTIVE = 2000040;
    public static final long ORDER_ALLOCATION_CANCELLED = 2000041;
    public static final long WORKGROUP_ACTIVATED = 2000054;
    public static final long WORKGROUP_INACTIVATED = 2000055;
    public static final long ACCOUNT_USER_ACTIVATED = 2000056;
    public static final long ACCOUNT_USER_INACTIVATED = 2000057;
    public static final long ACCOUNT_USER_INVITED = 2000058;
    public static final long TASK_CREATED = 2000059;
    public static final long TASK_IN_PROGRESS = 2000060;
    public static final long TASK_CANCELLED = 2000062;
    public static final long TASK_COMPLETED = 2000063;
    public static final long PROJECT_ACTIVATED = 2000064;
    public static final long PROJECT_DELETED = 2000065;
    public static final long PROJECT_INACTIVATED = 2000066;
    public static final long PROJECT_STAGING = 2000087;
    public static final long RFE_DELETED = 2000067;
    public static final long RFE_SENT = 2000068;
    public static final long RFE_CLOSED = 2000069;
    public static final long RFE_DRAFT = 2000070;
    public static final long RFE_CANCELLED = 2000071;
    public static final long ESTIMATE_CREATED = 2000072;
    public static final long ESTIMATE_DELETED = 2000073;
    public static final long ESTIMATE_DRAFT = 2000074;
    public static final long ESTIMATE_SENT = 2000075;
    public static final long ESTIMATE_ACCEPTED = 2000076;
    public static final long ESTIMATE_REJECTED = 2000077;
    public static final long ORDER_ACCEPTED = 2000078;
    public static final long ORDER_REJECTED = 2000080;
    public static final long ORDER_DRAFT = 2000079;
    public static final long ORDER_SUPPLIER_TO_ACCEPT = 2000081;
    public static final long ORDER_BUYER_TO_ACCEPT = 2000082;
    public static final long ORDER_CANCELLED = 2000083;
    public static final long ORDER_RETRACTED = 2000084;
    public static final long ORDER_OUTSOURCER_TO_ACCEPT = 2000085;
    public static final long ORDER_CLIENT_TO_ACCEPT = 2000086;
    public static final long MESSAGE_UNREAD = 2000100;
    public static final long MESSAGE_FOLLOWUP = 2000101;
    public static final long MESSAGE_READ = 2000102;
    public static final long MESSAGE_DELETED = 2000103;
    public static final long ACCOUNT_USER_UNINVITED = 2000120;
    public static final long WHLOCATION_ACTIVE = 2000121;
    public static final long WHLOCATION_INACTIVE = 2000122;
    public static final long JOB_SPEC_USER_STATE_ACTIVE = 2000130;
    public static final long JOB_SPEC_USER_STATE_ARCHIVED = 2000131;
    public static final long RFE_SUPPLIER_PARTIALLY_ACCEPTED = 2500035;
    public static final long RFE_SUPPLIER_ESTIMATING = 2500036;
    public static final long RFE_SUPPLIER_REJECTED = 2500037;
    public static final long RFE_SUPPLIER_DISMISSED = 2500038;
    public static final long COLLABORATION_OBJECT_ACTIVE = 2500039;
    public static final long CONTAINABLE_ACTIVE = 2500040;
    public static final long COLLABORATION_OBJECT_INACTIVE = 2500041;
    public static final long CONTAINABLE_DEACTIVATED = 2500042;
    public static final long ORDER_SHIPPED = 2500043;
    public static final long ORDER_DELIVERED = 2500044;
    public static final long ORDER_COMPLETED = 2500045;
    public static final long ORDER_PARTIALLY_SHIPPED = 2500064;
    public static final long ORDER_ACCEPTED_NOT_YET_SHIPPED = 2500065;
    public static final long ORDER_FINALIZED = 2500074;
    public static final long RFE_SUPPLIER_SEND_PENDING = 2500066;
    public static final long RFE_SUPPLIER_SEND_FAILED = 2500067;
    public static final long RFE_SUPPLIER_DRAFT = 2500068;
    public static final long CONTAINABLE_DELETED = 2500069;
    public static final long TASK_ON_HOLD = 2500070;
    public static final long ITEM_VERSION_NEW = 2500071;
    public static final long ITEM_VERSION_ACTIVE = 2500072;
    public static final long ITEM_VERSION_OBSOLETE = 2500073;
    public static final long ITEM_VERSION_INACTIVE = 2500075;
    public static final long PURCHASE_REQUEST_CREATED = 2500080;
    public static final long PURCHASE_REQUEST_SENT = 2500081;
    public static final long PURCHASE_REQUEST_APPROVED = 2500082;
    public static final long PURCHASE_REQUEST_DELETED = 2500083;
    public static final long SHIPMENT_NOT_SHIPPED = 2500084;
    public static final long SHIPMENT_PARTIALLY_SHIPPED = 2500085;
    public static final long SHIPMENT_SHIPPED = 2500086;
    public static final long SHIPMENT_DELIVERED = 2500087;
    public static final long INVOICE_DRAFT = 2500088;
    public static final long INVOICE_PENDING_ACCEPTANCE = 2500089;
    public static final long INVOICE_ACCEPTED = 2500090;
    public static final long INVOICE_REJECTED = 2500091;
    public static final long INVOICE_RETRACTED = 2500092;
    public static final long RFE_RECALLED = 2500093;
    public static final long ESTIMATE_RETRACTED = 2500094;
    public static final long ESTIMATE_INVALIDATED = 2500095;
    public static final long RFE_SUPPLIER_RECALLED = 2500096;
    public static final long RFE_SUPPLIER_CLOSED = 2500097;
    public static final long PENDING_APPROVAL = 2500098;
    public static final long APPROVED = 2500099;
    public static final long DISAPPROVED = 2500100;
    public static final long ROUTING_SLIP_ACTIVE = 2500101;
    public static final long ROUTING_SLIP_COMPLETED = 2500102;
    public static final long ROUTING_SLIP_CANCELLED = 2500103;
    public static final long RFE_SUPPLIER_AUTO_PRICING = 2500104;
    public static final long TEAMOBJECT_ACTIVATED = 2500105;
    public static final long TEAMOBJECT_DELETED = 2500106;
    public static final long TEAMOBJECT_INACTIVATED = 2500107;
    public static final long INVENTORY_STATUS_INBOUND = 2500108;
    public static final long INVENTORY_STATUS_AVAILABLE = 2500109;
    public static final long INVENTORY_STATUS_RESERVED = 2500110;
    public static final long INVENTORY_STATUS_RELEASED = 2500111;
    public static final long INVENTORY_STATUS_QUARANTINED = 2500112;
    public static final long INVENTORY_STATUS_DESTROYED = 2500113;
    public static final long QUOTE_CLOSED = 2500114;
    public static final long REPOSITORY_REQUEST_QUEUED = 2500115;
    public static final long REPOSITORY_REQUEST_PROCESSING = 2500116;
    public static final long REPOSITORY_REQUEST_SUCCESSFUL = 2500120;
    public static final long REPOSITORY_REQUEST_RETRYING = 2500121;
    public static final long REPOSITORY_REQUEST_FAILED = 2500122;
    public static final long REPOSITORY_REQUEST_ERROR_PROCESSOR = 2500123;
    public static final long REPOSITORY_REQUEST_ERROR_PROCESSOR_NOT_AVAILABLE = 2500124;
    public static final long REPOSITORY_REQUEST_ERROR_RESOURCE = 2500125;
    public static final long REPOSITORY_REQUEST_ERROR_RESOURCE_NOT_AVAILABLE = 2500126;
    public static final long REPOSITORY_REQUEST_ERROR_RESOURCE_ACCESS_DENIED = 2500127;
    public static final long PSF_DRAFT = 2500128;
    public static final long PSF_SUBMITTED = 2500129;
    public static final long PSF_ACCEPTED = 2500130;
    public static final long PSF_CANCELLED = 2500131;
    public static final long PSF_DELETED = 2500132;
    public static final long RFQ_DECLINED = 2500133;
    public static final long RFE_IN_AUCTION = 2500134;
    public static final long RFE_SUPPLIER_IN_AUCTION = 2500135;
    public static final long RFE_AWARD_AUCTION = 2500136;
    public static final long RFE_SUPPLIER_AWARD_AUCTION = 2500137;
    public static final long RFE_SUPPLIER_RFE_SENT = 2500138;

    public static final long PSF_IN_PRICING = 2500140;
    public static final long PSF_PENDING_REQUESTER_ACCEPTANCE = 2500141;
    public static final long PSF_REJECTED = 2500142;

    public static final long JOB_REQUEST_DRAFT = 2500200;
    public static final long JOB_REQUEST_SUBMITTED = 2500201;
    public static final long JOB_REQUEST_ACCEPTED = 2500202;
    public static final long JOB_REQUEST_CANCELLED = 2500203;
    public static final long JOB_REQUEST_DELETED = 2500204;
    public static final long JOB_REQUEST_IN_PRICING = 2500205;
    public static final long JOB_REQUEST_PENDING_REQUESTER_ACCEPTANCE = 2500206;
    public static final long JOB_REQUEST_REJECTED = 2500207;

    public static final long SITE_ACTIVE = 2500210;
    public static final long SITE_INACTIVE = 2500211;

    public static final long QUOTE_INCOMPLETE_DRAFT = 2500212;
    public static final long ORDER_REPLACED = 2500213;

    public static final long PROPOSAL_EMAILED_TO_CLIENT = 2500214;
    public static final long PROPOSAL_DOWNLOADABLE_BY_CLIENT = 2500215;

    public static final long ACCOUNT_USER_ARCHIVED = 2000216;

}
// Max ID: 2500216
// Count:  129



