package com.noosh.app.commons.constant;

/**
 * @Author: neals
 * @Date: 12/29/2015
 */
//todo: use EnterpriseLink instead of NavbarDestination for non-navbar-link
public interface NavBarDestination {
    // Main menu
     String MYDESK =                  "/noosh/home/<USER>";
     String PRODUCTIONMATRIX =        "/noosh/project/productionMatrix/run";
     String PROJECTMANAGER =          "/noosh/project/projectManager";
     String CONTACT =                 "/noosh/contacts/listContacts";
     String REPORT =                  "/noosh/report/list";
     String ADVANCED_EPORT =          "/noosh/report/advancedReport";
     String SEARCH =                  "/noosh/home/<USER>/main";
     String INTERNAL =                "/noosh/internal/accounts/home";

    // My Desk menu
     String MYDESK_SUMMARY =          "/noosh/home/<USER>";
     String MYDESK_CUSTOMIZE =        "/noosh/home/<USER>";
     String MYDESK_CHOOSE_GROUP =     "/noosh/home/<USER>";
     String MYDESK_EVENTS =           "/noosh/tracking/myDeskTrackingManager";
     String MYDESK_TASKS =            "/noosh/tasks/homeTasks";
     String MYDESK_TIMECARD =         "/noosh/tasks/timecard/list";
     String MYDESK_MESSAGES =         "/noosh/messages/homeMessages";
     String MYDESK_DASHBOARD =        "/noosh/report/dashboard";

    // Object Managers
     String PROJECT_LIST =            "/noosh/project/projectManager";
     String RFE_MANAGER =             "/noosh/procurement/estimating/rfe/rfeManager";
     String ESTIMATE_MANAGER =        "/noosh/procurement/estimating/estimate/estimateManager";
     String ORDER_MANAGER =           "/noosh/procurement/ordering/order/orderManager";
     String RFQ_MANAGER =             "/noosh/procurement/estimating/quote/rfqManager";
     String QUOTE_MANAGER =           "/noosh/procurement/estimating/quote/quoteManager";
     String PR_MANAGER =              "/noosh/procurement/pr/prManager";
     String JOB_MANAGER =             "/noosh/procurement/job/jobManager";

    // Workspace menu
     String PROJECT_MILESTONE =       "/noosh/project/productionMatrix/run";
     String PROJECT_OVERVIEW_UPDATE = "/noosh/project/projectGrid/getProjectOverviewList";
     String WORKSPACE_SPECS =         "/noosh/project/spec/findSpecs";
     String WORKSPACE_INVENTORY =     "/noosh/procurement/inventory/list";
     String WORKSPACE_INVENTORY_UPLOAD_MGR = "/noosh/procurement/inventory/uploadMgr";
     String WORKSPACE_CATEGORY =      "/noosh/project/category/listCategories";
     String WORKSPACE_CURRENT =       "/noosh/project/home";
     String WORKSPACE_ITEM_MASTER =   "/noosh/spec/item/list";

    // Contacts menu
     String CONTACT_LIST =            "/noosh/contacts/listContacts";
     String CONTACT_CATEGORY =        "/noosh/contacts/listCategories";
     String CONTACT_TEAMTEMPLATE =    "/noosh/teams/teamTemplateList";

    // Report menu
     String REPORT_LIST =             "/noosh/report/list";

    // Admin menu
     String ADMIN_PERSONAL =          "/noosh/accounts/personal/home";
     String ADMIN_WORKGROUP =         "/noosh/accounts/workgroup/home";
     String ADMIN_NOOSH_LITE_HOME =   "/noosh/nooshlite/site/home";
     String ADMIN_NOOSH_LITE_JOB_REQUESTS = "/noosh/nooshlite/site/jobrequest/home";

    // Internal menu
     String INTERNAL_ACCOUNTS =       "/noosh/internal/accounts/home";
     String INTERNAL_SUPPORT =        "/noosh/internal/support/home";
     String INTERNAL_PSO =            "/noosh/internal/customize/home";
     String INTERNAL_PARTNER =        "/noosh/internal/accounts/workgroup/home";
     String INTERNAL_MANAGEMENT =     "/noosh/internal/admin/home";

    // project bar
     String CREATE_PROJECT =          "/noosh/project/createProject";
     String RECENT_PROJECTS =          "/noosh/home/<USER>";

     String LOGIN =                   "/noosh/home/<USER>";
     String LOGOUT =                   "/noosh/home/<USER>";
    
    String ACTION_EDIT_PREFERENCE_BETA =  "/noosh/accounts/personal/editPreferenceBeta";

    // Internal Support
    String SYSTEM_ANNOUNCEMENTS_LINK = "/noosh/internal/support/systemAnnouncementList";
    String ANNOUNCEMENTS_LINK = "/noosh/internal/support/announcementList";
    String USERS_LINK = "/noosh/internal/support/userList";
    String EVENTS_LINK = "/noosh/internal/support/trackingList";
    String SESSIONS_LINK = "/noosh/internal/support/userSession";

    // Internal Professional Service
    String CUSTOMIZE_SPEC = "/noosh/internal/customize/spec/home";
    String CUSTOMIZE_ROLE = "/noosh/internal/customize/roles/home";
    String CUSTOMIZE_PROPERTY = "/noosh/internal/customize/property/home";
    String CUSTOMIZE_RESOURCES = "/noosh/internal/customize/resources";
    String CUSTOMIZE_DX_ACCOUNTS = "/noosh/internal/customize/dx/home";
    String CUSTOMIZE_DX_GROUPS = "/noosh/internal/customize/dx/senderGroups";
    String CUSTOMIZE_DX_REQUESTS = "/noosh/internal/customize/dx/serverRequests";

    //Internal Partner Service
    String MANAGED_SPECS = "/noosh/internal/accounts/workgroup/listManagedSpecs";
    String MANAGED_ROLES = "/noosh/internal/accounts/workgroup/listManagedRoles";
    String ADMIN_MANAGEMENT = "/noosh/internal/accounts/workgroup/listAdminManagement";

    // DX
    String AMAS_LINK = "/noosh/integration/forwardDxCall";
}
