package com.noosh.app.commons.constant;


public interface PermissionID
{
    public static long EDIT_WORKGROUP = 1000313;
    public static long VIEW_WORKGROUP = 1000314;
    public static long EDIT_ACCOUNT_USER = 1000316;
    public static long CREATE_FILE = 1000321;
    public static long EDIT_FILE = 1000322;
    public static long VIEW_FILE = 1000323;
    public static long CREATE_MESSAGE = 1000325;
    public static long VIEW_MESSAGE = 1000327;
    public static long EDIT_TEAM_MEMBER = 1000330;
    public static long DELETE_TEAM_MEMBER = 1000332;
    public static long CREATE_TRACKING_ITEM = 1000333;
    public static long VIEW_TRACKING_ITEM = 1000335;
    public static long CREATE_TASK = 1000337;
    public static long EDIT_TASK = 1000338;
    public static long VIEW_TASK = 1000339;
    public static long CREATE_NEW_PROJECT = 1000345;
    public static long CREATE_PROJECT = 1000345;
    public static long EDIT_PROJECT = 1000346;
    public static long VIEW_PROJECT = 1000347;
    public static long DELETE_PROJECT = 1000348;
    public static long CREATE_SPEC = 1000357;
    public static long EDIT_SPEC = 1000358;
    public static long VIEW_SPEC = 1000359;
    public static long DELETE_SPEC = 1000360;
    public static long CREATE_RFE = 1000361;
    public static long EDIT_RFE = 1000362;
    public static long VIEW_RFE = 1000363;
    public static long DELETE_RFE = 1000364;
    public static long SEND_RFE = 1000365;
    public static long CREATE_ESTIMATE = 1000366;
    public static long EDIT_ESTIMATE = 1000367;
    public static long VIEW_ESTIMATE = 1000368;
    public static long DELETE_ESTIMATE = 1000369;
    public static long SEND_ESTIMATE = 1000370;
    public static long CREATE_ORDER = 1000371;
    public static long EDIT_ORDER = 1000372;
    public static long VIEW_ORDER = 1000373;
    public static long ACCEPT_ORDER = 1000375;
    public static long REJECT_ORDER = 1000376;
    public static long CANCEL_ORDER = 1000377;
    public static long CREATE_CHANGE_ORDER = 1000378;
    public static long EDIT_CHANGE_ORDER = 1000379;
    public static long VIEW_CHANGE_ORDER = 1000380;
    public static long INVITE_TEAM_MEMBER = 1000400;
    public static long VIEW_WORKGROUP_PROJECTS = 1000420;
    public static long EDIT_SHARED_CONTACT = 1000441;
    public static long DELETE_SHARED_CONTACT = 1000442;
    public static long REJECT_RFE = 1000443;
    public static long ATTACH_PROJECT = 1000460;
    public static long DETACH_PROJECT = 1000461;
    public static long CREATE_REPORT_SPEC = 1000480;
    public static long EDIT_REPORT_SPEC = 1000481;
    public static long VIEW_REPORT_SPEC = 1000482;
    public static long DELETE_REPORT_SPEC = 1000483;
    public static long EDIT_REPORT_RESULT = 1000485;
    public static long VIEW_REPORT_RESULT = 1000486;
    public static long DELETE_REPORT_RESULT = 1000487;
    public static long CLOSE_RFE = 2000000;
    public static long CANCEL_RFE = 2000001;
    public static long ACCEPT_ESTIMATE = 2000014;
    public static long REJECT_ESTIMATE = 2000015;
    public static long RESPOND_RFE = 2000016;
    public static long RETRACT_ORDER = 2000048;
    public static long RECEIVE_NOTIFICATION_ESTIMATE_RECEIVED = 2000070;
    public static long RECEIVE_NOTIFICATION_RFE_SENT = 2000071;
    public static long RECEIVE_NOTIFICATION_RFE_REJECTED = 2000072;
    public static long RECEIVE_NOTIFICATION_ORDER_CREATED = 2000073;
    public static long RECEIVE_NOTIFICATION_ORDER_REJECTED = 2000074;
    public static long RECEIVE_NOTIFICATION_ORDER_ACCEPTED = 2000075;
    public static long RECEIVE_NOTIFICATION_ORDER_CANCELED = 2000076;
    public static long RECEIVE_NOTIFICATION_PROJECT_DELETED = 2000077;
    public static long RECEIVE_NOTIFICATION_PROJECT_DEACTIVATED = 2000078;
    public static long RECEIVE_NOTIFICATION_PROJECT_REACTIVATED = 2000079;
    public static long RECEIVE_NOTIFICATION_ORDER_RETRACTED = 2000106;
    public static long RECEIVE_NOTIFICATION_ORDER_UPDATED = 2000107;
    public static long EDIT_TERMS_AND_CONDITIONS = 2000367;
    public static long VIEW_ADMIN_CUSTOMER = 2500000;
    public static long EDIT_ADMIN_CUSTOMER = 2500001;
    public static long VIEW_ADMIN_ANNOUNCEMENT = 2500020;
    public static long EDIT_ADMIN_ANNOUNCEMENT = 2500021;
    public static long VIEW_ADMIN_CUSTOMIZATION = 2500022;
    public static long EDIT_ADMIN_CUSTOMIZATION = 2500023;
    public static long EDIT_ADMIN_USER = 2500024;
    public static long MANAGE_PRICE_BREAKOUT_TYPE = 2500041;
    public static long RECEIVE_NOTIFICATION_ESTIMATE_REJECTED = 2500042;
    public static long CREATE_SHARED_CONTACT = 2500043;
    public static long CREATE_SHARED_CONTACT_CATEGORY = 2500044;
    public static long EDIT_SHARED_CONTACT_CATEGORY = 2500046;
    public static long DELETE_SHARED_CONTACT_CATEGORY = 2500047;
    public static long COPY_PROJECT = 2500060;
    public static long ACCEPT_CHANGE_ORDER = 2500061;
    public static long REJECT_CHANGE_ORDER = 2500062;
    public static long CANCEL_CHANGE_ORDER = 2500063;
    public static long RETRACT_CHANGE_ORDER = 2500064;
    public static long RECEIVE_NOTIFICATION_CHANGE_ORDER_CREATED = 2500065;
    public static long RECEIVE_NOTIFICATION_CHANGE_ORDER_REJECTED = 2500066;
    public static long RECEIVE_NOTIFICATION_CHANGE_ORDER_ACCEPTED = 2500067;
    public static long RECEIVE_NOTIFICATION_CHANGE_ORDER_CANCELED = 2500068;
    public static long RECEIVE_NOTIFICATION_CHANGE_ORDER_RETRACTED = 2500069;
    public static long RECEIVE_NOTIFICATION_CHANGE_ORDER_UPDATED = 2500070;
    public static long CREATE_OPEN_BID_RFE = 2500080;
    public static long VIEW_REPORT_ABOUT_ENTIRE_WORKGROUP = 2500100;
    public static long VIEW_PROJECT_STATUS_OPTIONS = 2500120;
    public static long CREATE_PROJECT_STATUS_OPTIONS = 2500121;
    public static long EDIT_PROJECT_STATUS_OPTIONS = 2500122;
    public static long DELETE_PROJECT_STATUS_OPTIONS = 2500123;
    public static long EDIT_PROJECT_STATUS = 2500124;
    public static long RECEIVE_NOTIFICATION_PROJECT_STATUS_UPDATED = 2500125;
    public static long DISMISS_SUPPLIER = 2500140;
    public static long RECEIVE_NOTIFICATION_SUPPLIER_DISMISSED = 2500141;
    public static long EDIT_PROJECT_OVERVIEW = 2500180;
    public static long EDIT_PROJECT_ACTIVATION = 2500181;
    public static long MARK_ORDER_ACCEPTED = 2500200;
    public static long MARK_ORDER_SHIPPED = 2500201;
    public static long MARK_ORDER_DELIVERED = 2500202;
    public static long MARK_ORDER_COMPLETED = 2500203;
    public static long RECEIVE_NOTIFICATION_ORDER_STATUS_CHANGED = 2500204;
    public static long VIEW_SUPPLIER_RATING_ADMIN = 2500220;
    public static long MANAGE_SUPPLIER_RATING_ADMIN = 2500221;
    public static long CREATE_SUPPLIER_RATING = 2500222;
    public static long EDIT_SUPPLIER_RATING = 2500223;
    public static long VIEW_SUPPLIER_RATING = 2500224;
    public static long QUERY_SUPPORT_TOOLS_TRACKING = 2500240;
    public static long QUERY_SUPPORT_TOOLS_USERS = 2500241;
    public static long QUERY_SUPPORT_TOOLS_LOGINS = 2500242;
    public static long IMPERSONATE_SUPPORT_TOOLS = 2500243;
    public static long MANAGE_TASK_STATUS = 2500260;
    public static long MANAGE_TASK_TYPE = 2500262;
    public static long MANAGE_TASK_PLAN = 2500264;
    public static long VIEW_SPEC_TEMPLATE = 2500280;
    public static long MANAGE_SPEC_TEMPLATE = 2500281;
    public static long VIEW_TASK_TEMPLATE = 2500282;
    public static long MANAGE_TASK_TEMPLATE = 2500283;
    public static long CREATE_SPEC_FROM_TEMPLATE = 2500300;
    public static long MANAGE_PROJECT_CATEGORY = 2500321;
    public static long ASSIGN_PROJECT_CATEGORY_FOR_PROJECT = 2500322;
    public static long RUN_SUPPLIER_RATING_REPORT = 2500342;
    public static long MANAGE_SHIPMENT = 2500360;
    public static long UPDATE_SHIPMENT_REQUESTED = 2500361;
    public static long UPDATE_SHIPMENT_ACTUAL = 2500362;
    public static long UPDATE_SHIPMENT_RECEIVED = 2500363;
    public static long MANAGE_SHIPMENT_CARRIER = 2500364;
    public static long VIEW_SHIPMENT_CARRIER = 2500365;
    public static long MANAGE_DATA_EXCHANGE = 2500380;
    public static long MANAGE_SPEC_REFERENCE_COUNTER = 2500400;
    public static long MANAGE_TRACKING_TYPE = 2500420;
    public static long TRANSFER_PROJECT = 2500440;
    public static long ADD_ORDER_COST_CENTER = 2500441;
    public static long EDIT_ORDER_COST_CENTER = 2500442;
    public static long VIEW_ORDER_COST_CENTER = 2500443;
    public static long MANAGE_COST_CENTER_ADMIN = 2500444;
    public static long VIEW_COST_CENTER_ADMIN = 2500445;
    public static long CREATE_PURCHASE_REQUEST = 2500446;
    public static long EDIT_PURCHASE_REQUEST = 2500447;
    public static long VIEW_PURCHASE_REQUEST = 2500448;
    public static long APPROVE_PURCHASE_REQUEST = 2500449;
    public static long EDIT_SUBMITTED_TIMECARD = 2500461;
    public static long RUN_ALL_ACTIVITY_REPORT = 2500480;
    public static long RUN_LIMITED_ACTIVITY_REPORT = 2500481;
    public static long MANAGE_ADDITIONAL_UNIT_PRICE = 2500500;
    public static long MANAGE_APPROVED_SUPPLIER = 2500501;
    public static long RECEIVE_NOTIFICATION_CUSTOM_EVENT_POSTED = 2500502;
    public static long VIEW_TASK_PLAN = 2500520;
    public static long ANNOTATE_FILE = 2500521;
    public static long MANAGE_WORKGROUP_ATTRIBUTES = 2500541;
    public static long MANAGE_ROLE_GROUP = 2500581;
    public static long RUN_ORDER_ACTIVITY_REPORT = 2500601;
    public static long RUN_COST_CENTER_REPORT = 2500602;
    public static long RUN_PROJECT_STATUS_REPORT = 2500603;
    public static long RUN_ORDER_STATUS_REPORT = 2500604;
    public static long RUN_PROJECT_PARTICIPATION_REPORT = 2500621;
    public static long RUN_ESTIMATE_ANALYSIS_REPORT = 2500641;
    public static long RUN_ESTIMATE_WINLOSS_REPORT = 2500642;
    public static long VIEW_PREMARKUP_QUOTE = 2500646;
    public static long SEND_QUOTE = 2500647;
    public static long VIEW_QUOTE = 2500648;
    public static long REVISE_QUOTE = 2500649;
    public static long REJECT_QUOTE = 2500650;
    public static long VIEW_RFQ = 2500651;
    public static long SEND_RFQ = 2500652;
    public static long CLOSE_RFQ = 2500653;
    public static long MANAGE_CLIENT = 2500665;
    public static long VIEW_CUSTOM_FIELD = 2500685;
    public static long MANAGE_CUSTOM_FIELD = 2500686;
    public static long ADD_PROJECT_COST_CENTER = 2500687;
    public static long VIEW_PROJECT_COST_CENTER = 2500688;
    public static long RUN_OUTSOURCE_PROFIT_REPORT = 2500705;
    public static long SHARE_COLLABORATION_OBJECT = 2500725;
    public static long RETRACT_QUOTE = 2500726;
    public static long RUN_WHERE_USED_REPORT = 2500745;
    public static long RUN_DIRECT_MAIL_MATRIX_REPORT = 2500746;
    public static long VIEW_BUDGET_CATEGORY = 2500765;
    public static long EDIT_BUDGET_CATEGORY = 2500766;
    public static long VIEW_PROJECT_BUDGET = 2500767;
    public static long EDIT_PROJECT_BUDGET = 2500768;
    public static long REGISTER_ADMIN_CUSTOMER = 2500786;
    public static long VIEW_SHIPMENT = 2500806;
    public static long CREATE_SPEC_REFERENCE = 2500825;
    public static long MANAGE_TASK_PREFERENCES = 2500845;
    public static long CREATE_QUICK_ORDER = 2500865;
    public static long RUN_SPEC_QUERY = 2500885;
    public static long MANAGE_RESOURCES = 2500905;
    public static long RUN_PROJECT_BUDGET_REPORT = 2500945;
    public static long CREATE_FOLDER = 2501017;
    public static long VIEW_FOLDER = 2501018;
    public static long EDIT_FOLDER = 2501019;
    public static long DELETE_FOLDER = 2501020;
    public static long MANAGE_FOLDER = 2501021;
    public static long MOVE_FOLDER = 2501022;
    public static long CREATE_DOCUMENT = 2501023;
    public static long LIST_DOCUMENT = 2501024;
    public static long VIEW_DOCUMENT = 2501025;
    public static long EDIT_DOCUMENT = 2501026;
    public static long DELETE_DOCUMENT = 2501027;
    public static long MANAGE_DOCUMENT = 2501028;
    public static long MOVE_DOCUMENT = 2501029;
    public static long CREATE_INVOICE = 2501045;
    public static long EDIT_INVOICE = 2501046;
    public static long DELETE_INVOICE = 2501047;
    public static long SEND_INVOICE = 2501048;
    public static long VIEW_INVOICE = 2501049;
    public static long ACCEPT_INVOICE = 2501050;
    public static long REJECT_INVOICE = 2501051;
    public static long RETRACT_INVOICE = 2501052;
    public static long MANAGE_PROJECT_MILESTONES = 2501065;
    public static long CUSTOM_EDIT_PROJECT_LEGAL_STATUS = 2501085;
    public static long CUSTOM_EDIT_PROJECT_COMPLIANCE_STATUS = 2501086;
    public static long CREATE_CONTRACT_PRICING = 2501105;
    public static long EDIT_CONTRACT_PRICING = 2501106;
    public static long VIEW_CONTRACT_PRICING = 2501107;
    public static long MANAGE_INVOICE_ADMIN = 2501125;
    public static long OPEN_RFE = 2501126;
    public static long RECALL_RFE = 2501127;
    public static long INVALIDATE_ESTIMATE = 2501128;
    public static long RETRACT_ESTIMATE = 2501129;
    public static long APPROVE_INVOICE = 2501145;
    public static long MANAGE_SOURCING_ADMIN = 2501146;
    public static long EDIT_SOURCING = 2501147;
    public static long VIEW_SOURCING = 2501148;
    public static long EDIT_WORKGROUP_INVENTORY = 2501166;
    public static long DELETE_WORKGROUP_INVENTORY = 2501167;
    public static long ADJUST_WORKGROUP_INVENTORY = 2501168;
    public static long MANAGE_EXCHANGE_RATES = 2501186;
    public static long MANAGE_PROPOSAL_ADMIN = 2501206;
    public static long CREATE_PROPOSAL = 2501207;
    public static long EDIT_PROPOSAL = 2501208;
    public static long DELETE_PROPOSAL = 2501209;
    public static long ACCEPT_PROPOSAL = 2501210;
    public static long REJECT_PROPOSAL = 2501211;
    public static long RETRACT_PROPOSAL = 2501212;
    public static long VIEW_PROPOSAL = 2501226;
    public static long VIEW_SPEC_PRICING = 2501227;
    public static long MANAGE_ORDER_ADMIN = 2501247;
    public static long MANAGE_ESTIMATING_ADMIN = 2501248;
    public static long RESET_USER_PASSWORD = 2501249;
    public static long MANAGE_INVENTORY_ADMIN = 2501250;
    public static long REVIEW_JOB_REQUEST = 2501270;
    public static long CLOSE_QUOTE = 2501290;
    public static long VIEW_WORKGROUP_INVENTORY = 2501310;
    public static long MANAGE_DOCUMENT_SETTINGS = 2501330;
    public static long VIEW_WORKGROUP_ROLE_PRIVILEGES = 2501350;
    public static long VIEW_PROJECT_ROLE_PRIVILEGES = 2501351;
    public static long MANAGE_ITEM_MASTER_ADMIN = 2501370;
    public static long ATTACH_ITEM_MASTER = 2501371;
    public static long SET_PROJECT_HOT_FOR_TEAM = 2501372;
    public static long ROUTE_DOCUMENT = 2501373;
    public static long ROUTE_PROJECT = 2501374;
    public static long EDIT_JOB_DETAILS = 2501393;
    public static long DECLINE_RFQ = 2501413;
    public static long INVITE_SUPPLIER = 2501433;
    public static long VIEW_SYSTEM_ADMIN_ANNOUNCEMENT = 2501439;
    public static long EDIT_SYSTEM_ADMIN_ANNOUNCEMENT = 2501440;
    public static long CANCEL_QUOTE = 2501441;
    public static long RUN_REPORT = 2501450;
}
