package com.noosh.app.commons.constant;

public interface PropertyTypeID
{
    /** The property type used in property engine test cases. */
    public static final long PROPERTY_TYPE_TEST = 1000000;

    /** @deprecated No longer used, kept for matching up to old data. */
    public static final long PROPERTY_TYPE_PROJECT_HEADER = 1000001;

    /** @deprecated No longer used, kept for matching up to old data. */
    public static final long PROPERTY_TYPE_CONTACT = 1000044;

    /** The property type used by user/group preferences. */
    public static final long PROPERTY_TYPE_PREFERENCE = 1000048;

    /** The property type used by the session manager to store persistent
        attributes (such as ui filters and table paging). */
    public static final long SESSION_PERSISTENT_ATTRIBUTE = 1000049;

    /** The property type used by the property engine to store custom
        attributes (extension to base attribute set).*/
    public static final long PROPERTY_TYPE_OXF = 1000050;
}
