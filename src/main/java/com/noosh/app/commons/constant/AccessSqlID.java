package com.noosh.app.commons.constant;

/**
 * Generated on Tue Jul 08 18:50:54 PDT 2003
 * Copyright 1998-2003 Noosh
 */
public interface AccessSqlID
{
    public static long SQL_FOR_GETTING_TEAM_MEMBER_IDS = 1000002;
    public static long SQL_FOR_GETTING_NULL = 1000003;
    public static long SQL_FOR_GETTING_PERSONAL_PROJECTS = 1000020;
    public static long SQL_FOR_GETTING_WORKGROUP_PROJECTS = 1000040;
    public static long SQL_FOR_GETTING_EDITABLE_PROJECTS = 1000060;
    public static long SQL_FOR_GETTING_DELETABLE_PROJECTS = 1000061;
    public static long SQL_FOR_GETTING_ATTACHABLE_PROJECTS = 1000062;
    public static long SQL_FOR_GETTING_DETACHABLE_PROJECTS = 1000063;
    public static long SQL_FOR_GETTING_ACTIVATABLE_PROJECTS = 1000064;
    public static long SQL_FOR_GETTING_ASSIGN_PROJECT_CATEGORY_PROJECTS = 1000068;
    public static long SQL_FOR_GETTING_UPDATEABLE_PROJECT_STATUS_PROJECTS = 1000069;
    public static long SQL_FOR_GETTING_VIEWABLE_ORDERS = 2000000;
    public static long SQL_FOR_GETTING_LIMITED_VIEWABLE_TASKS = 2000010;
    public static long SQL_FOR_GETTING_ALL_VIEWABLE_TASKS = 2000011;
    public static long SQL_FOR_GETTING_VIEWABLE_TASKS_IN_PROJECT = 2000012;
    public static long SQL_FOR_GETTING_VIEWABLE_MESSAGES_IN_A_TEAM = 2000020;
    public static long SQL_FOR_GETTING_ALL_MESSAGES_IN_A_TEAM = 2000040;
    public static long SQL_FOR_GETTING_ALL_VIEWABLE_MESSAGES = 2000060;
    public static long SQL_FOR_GETTING_VIEWABLE_RFES_IN_PROJECT = 2500000;
    public static long SQL_FOR_GETTING_VIEWABLE_ESTIMATES_IN_PROJECT = 2500001;
    public static long SQL_FOR_GETTING_VIEW_SPEC_PROJECTS = 2500100;
    public static long SQL_FOR_GETTING_VIEW_RFE_PROJECTS = 2500110;
    public static long SQL_FOR_GETTING_VIEW_ESTIMATE_PROJECTS = 2500120;
    public static long SQL_FOR_GETTING_VIEW_ORDER_PROJECTS = 2500130;
    public static long SQL_FOR_GETTING_VIEW_PURCHASE_REQUEST_PROJECTS = 2500140;
    public static long SQL_FOR_GETTING_VIEW_SHIPMENT_PROJECTS = 2500431;
    public static long SQL_FOR_GETTING_VIEW_RFQ_PROJECTS = 2500441;
    public static long SQL_FOR_GETTING_VIEW_QUOTE_PROJECTS = 2500442;
    public static long SQL_FOR_GETTING_VIEW_INVOICE_PROJECTS = 2500443;
    public static long SQL_FOR_GETTING_VIEWABLE_DOCUMENTS = 2500444;
    public static long SQL_FOR_GETTING_ALL_VIEWABLE_DOCUMENTS = 2500445;
}
