package com.noosh.app.commons.constant;

/**
 * <AUTHOR>
 * @since 8/13/13
 */
public interface SourceTypeID {
    public static long NOOSH_GROUP_EDITION = 1000000;
    public static long NOOSH_ENTERPRISE = 1000001;
    public static long NOOSH_API = 1000002;
    public static long NOOSH_ONE = 1000003;
    public static long ACCOUNT_RESOURCE = 1000005;
    public static long COLLABORATION_RESOURCE = 1000006;
    public static long ESTIMATE_RESOURCE = 1000007;
    public static long MESSAGE_RESOURCE = 1000008;
    public static long NOOSH_CLOUD_PROCUREMENT = 1000009;
    public static long NOOSH_SEARCH_MANAGER = 1000010;
    public static long ORDER_RESOURCE = 1000011;
    public static long PROJECT_RESOURCE = 1000012;
    public static long QUOTE_RESOURCE = 1000013;
    public static long SPEC_RESOURCE = 1000014;
    public static long SUPPLIER_RATING_RESOURCE = 1000015;
    public static long TASK_RESOURCE = 1000016;
    public static long TRACKING_RESOURCE = 1000017;
    public static long WEB_RESOURCE = 1000018;
    public static long FILE_RESOURCE = 1000019;
    public static long WORKGROUP_RESOURCE = 1000020;
}
