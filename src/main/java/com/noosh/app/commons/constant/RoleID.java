package com.noosh.app.commons.constant;


public interface RoleID {

    public static long WORKGROUP_ADMIN=1000094;
    public static long WORKGROUP_MANAGER=1000095;
	public static long WORKGROUP_MEMBER=1000096;
	public static long PROJECT_OWNER=1000097;
	public static long PROJECT_CLIENT=1000285;
	public static long PROJECT_GUEST=1000286;
	public static long PROJECT_BUYER=1000101;
	public static long PROJECT_SUPPLIER=1000106;
	public static long PROJECT_MEMBER=1000140;
	public static long ADMIN_SUPPORT_MANAGER=2500021;
	public static long ADMIN_CUSTOMER_MANAGER=2500020;
	public static long ADMIN_SUPPORT_MEMBER=2500022;
	public static long ADMIN_PROF_SVCS_MANAGER=2500023;
	public static long ADMIN_MANAGER=2500000;
	public static long ADMIN_PROF_SVCS_MEMBER=2500024;
	public static long PROJECT_EX_SUPPLIER=2500041;
	public static long LIMITED_BUYER=5000103;
	public static long PROJECT_SUPERVISOR=5000104;
	public static long PRIVILEGED_BUYER=5000140;
    public static long PARTNER_ADMIN_MANAGER = 2500092;

}
