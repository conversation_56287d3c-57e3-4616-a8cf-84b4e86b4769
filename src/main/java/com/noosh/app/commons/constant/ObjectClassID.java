package com.noosh.app.commons.constant;

/**
 * Constant interface: ObjectClassID
 * Constant source:    DB
 * Constant query:     select OC_OBJECT_CLASS_ID, constant_token from OC_OBJECT_CLASS order by 1 asc
 *
 * Copyright 1998-2010 Noosh, Inc.
 *
 * <AUTHOR>
 * @since  Thu Dec 23 19:07:05 GMT 2010
 */
public interface ObjectClassID
{
    // roles manager
    public static final long OBJECT_CLASS_TYPE      = 1;
    public static final long OBJECT_CLASS           = 2;
    public static final long OBJECT_CLASS_REL_TYPE  = 3;
    public static final long OBJECT_CLASS_REL       = 4;
    public static final long ACTION_TYPE            = 5;
    public static final long ACCESS_CONTEXT         = 6;
    public static final long ACCESS_RULE            = 7;
    public static final long ACCESS_RULE_REL        = 8;
    public static final long ROLE_CLASS             = 9;
    public static final long ROLE                   = 10;
    public static final long ACCESS_SQL_TYPE        = 11;
    public static final long ACCESS_SQL             = 12;
    public static final long DEFAULT_ROLE           = 13;
    public static final long VALID_ROLE_OBJECT_REL  = 14;
    public static final long OBJECT_STATE           = 15;

    public static final long OBJECT_CLASS_PROJECT = 1000000;
    public static final long PROJECT_CLASS_BUYER = 1000001;
    public static final long PROJECT_CLASS_SUPPLIER = 1000002;
    public static final long OBJECT_CLASS_TRACKING = 1000003;
    public static final long PROJECT_CLASS_BROKER = 1000004;//by default SP is set to this
    public static final long PROJECT_CLASS_CLIENT = 1000005;
    public static final long PROJECT_CLASS_OUTSOURCER = 1000006;
    public static final long PROJECT_LINK = 1000009;
    public static final long RFQ = 1000010;
    public static final long RFQ_ITEM = 1000011;
    public static final long RFQ_SUPPLIER = 1000012;
    public static final long QUOTE = 1000013;
    public static final long QUOTE_ITEM = 1000014;
    public static final long RFQ_QUANTITY = 1000015;
    public static final long QUOTE_PRICE = 1000016;
    public static final long PROPOSAL = 1000017;
    public static final long PROPOSAL_ITEM = 1000018;
    public static final long PROCUREMENT = 1000019;
    public static final long JOB = 1000020;
    public static final long JOB_STATUS = 1000021;
    public static final long PROPOSAL_TEMPLATE = 1000022;
    public static final long PROPOSAL_SPEC_FIELD = 1000023;
    public static final long PROPOSAL_SPEC_SUMMARY = 1000024;
    public static final long PROPOSAL_SPEC_SUMMARY_ITEM = 1000025;
    public static final long PROPOSAL_ITEM_PRICE = 1000026;
    public static final long OBJECT_CLASS_CATEGORY = 1000046;
    public static final long ACCOUNT_MODULE = 1000098;
    public static final long COMPANY = 1000099;
    public static final long WORKGROUP = 1000100;
    public static final long ACCOUNT_USER = 1000101;
    public static final long CLIENT_WORKGROUP = 1000102;
    public static final long PROJECT_MODULE = 1000103;
    public static final long FILE = 1000104;
    public static final long MESSAGE = 1000105;
    public static final long TEAM_MEMBER = 1000106;
    public static final long TRACKING_ITEM = 1000107;
    public static final long TASK = 1000108;
    public static final long COST_CENTER = 1000109;
    public static final long TEAM = 1000110;
    public static final long SCHEDULE = 1000111;
    public static final long SPEC_NODE = 1000112;
    public static final long SPEC_TREE = 1000113;
    public static final long OBJECT_CLASS_SPEC = 1000114;
    public static final long RFE = 1000115;
    public static final long ESTIMATE = 1000116;
    public static final long ORDER = 1000117;
    public static final long CHANGE_ORDER = 1000118;
    public static final long INVOICE = 1000119;
    public static final long INVOICE_ITEM = 1000120;
    public static final long ORDER_ALLOCATION = 1000121;
    public static final long RFE_ALLOCATION = 1000122;
    public static final long OBJECT_CLASS_LONG = 1000140;
    public static final long OBJECT_CLASS_DOUBLE = 1000141;
    public static final long OBJECT_CLASS_DATE = 1000142;
    public static final long OBJECT_CLASS_STRING = 1000143;
    public static final long PFD = 1000150;
    public static final long PSD = 1000151;
    public static final long WORKGROUP_PROJECTS = 1000160;
    public static final long PERSONAL_PROJECTS = 1000161;
    public static final long ALL_RECENT_PROJECTS = 1000162;
    public static final long ALL_HOT_PROJECTS = 1000163;
    public static final long ALL_PROJECTS = 1000164;
    public static final long NON_PROJECT = 1000165;
    public static final long SHARED_CONTACT = 1000185;
    public static final long OBJECT_CLASS_ROW = 1000187;
    public static final long OBJECT_CLASS_COLUMN = 1000188;
    public static final long REPORT_SPEC = 1000200;
    public static final long REPORT_RESULT = 1000201;
    public static final long PERSONAL_CONTACT = 1000202;
    public static final long CONTACT = 1000203;
    public static final long RFE_ITEM = 1000204;
    public static final long ESTIMATE_ITEM = 1000205;
    public static final long ORDER_ITEM = 2000000;
    public static final long NOTIFICATION_ESTIMATE_RECEIVED = 2000071;
    public static final long NOTIFICATION_RFE_SENT = 2000072;
    public static final long NOTIFICATION_RFE_REJECTED = 2000073;
    public static final long NOTIFICATION_ORDER_CREATED = 2000074;
    public static final long NOTIFICATION_ORDER_REJECTED = 2000075;
    public static final long NOTIFICATION_ORDER_ACCEPTED = 2000076;
    public static final long NOTIFICATION_ORDER_CANCELED = 2000077;
    public static final long NOTIFICATION_PROJECT_DELETED = 2000078;
    public static final long NOTIFICATION_PROJECT_DEACTIVATED = 2000079;
    public static final long NOTIFICATION_PROJECT_REACTIVATED = 2000080;
    public static final long NOTIFICATION_ORDER_RETRACTED = 2000106;
    public static final long NOTIFICATION_ORDER_UPDATED = 2000107;
    public static final long PREFERENCE = 2000200;
    public static final long TERMS_AND_CONDITIONS = 2000346;
    public static final long NEW_PROJECT = 2100000;
    public static final long ADMIN_MODULE = 2500000;
    public static final long ADMIN_CUSTOMER = 2500001;
    public static final long ENTITY_CONTAINER = 2500019;
    public static final long ADMIN_ANNOUNCEMENT = 2500020;
    public static final long ADMIN_CUSTOMIZATION = 2500021;
    public static final long ADMIN_USER = 2500022;
    public static final long PRICE_BREAKOUT_TYPE = 2500040;
    public static final long NOTIFICATION_ESTIMATE_REJECTED = 2500041;
    public static final long SHARED_CONTACT_CATEGORY = 2500042;
    public static final long NOTIFICATION_CHANGE_ORDER_CREATED = 2500060;
    public static final long NOTIFICATION_CHANGE_ORDER_REJECTED = 2500061;
    public static final long NOTIFICATION_CHANGE_ORDER_ACCEPTED = 2500062;
    public static final long NOTIFICATION_CHANGE_ORDER_CANCELED = 2500063;
    public static final long NOTIFICATION_CHANGE_ORDER_RETRACTED = 2500064;
    public static final long NOTIFICATION_CHANGE_ORDER_UPDATED = 2500065;
    public static final long RFE_SUPPLIER = 2500067;
    public static final long REPORT_ABOUT_ENTIRE_WORKGROUP = 2500080;
    public static final long COLLABORATION_OBJECT = 2500081;
    public static final long PROJECT_STATUS_OPTIONS = 2500100;
    public static final long PROJECT_STATUS = 2500101;
    public static final long NOTIFICATION_PROJECT_STATUS_UPDATED = 2500102;
    public static final long SUPPLIER = 2500120;
    public static final long NOTIFICATION_SUPPLIER_DISMISSED = 2500121;
    public static final long CONTAINABLE = 2500122;
    public static final long PROJECT_OVERVIEW = 2500180;
    public static final long PROJECT_ACTIVATION = 2500181;
    public static final long PERSON = 2500184;
    public static final long CATEGORY = 2500185;
    public static final long SPEC_TYPE = 2500186;
    public static final long PROPERTY = 2500187;
    public static final long NOTIFICATION_ORDER_STATUS_CHANGED = 2500204;
    public static final long SUPPLIER_RATING_ADMIN = 2500220;
    public static final long SUPPLIER_RATING = 2500221;
    public static final long SUPPORT_TOOLS = 2500240;
    public static final long SUPPORT_TOOLS_TRACKING = 2500241;
    public static final long SUPPORT_TOOLS_USERS = 2500242;
    public static final long SUPPORT_TOOLS_LOGINS = 2500243;
    public static final long TASK_STATUS = 2500260;
    public static final long TASK_TYPE = 2500261;
    public static final long TASK_PLAN = 2500262;
    public static final long SPEC_TEMPLATE = 2500281;
    public static final long TASK_TEMPLATE = 2500282;
    public static final long TASK_TEMPLATE_IN_PROJECT = 2500283;
    public static final long SPEC_FROM_TEMPLATE = 2500300;
    public static final long PROJECT_CATEGORY = 2500320;
    public static final long PROJECT_CATEGORY_FOR_PROJECT = 2500321;
    public static final long SUPPLIER_RATING_REPORT = 2500344;
    public static final long SHIPMENT = 2500345;
    public static final long SHIPMENT_REQUESTED = 2500361;
    public static final long SHIPMENT_ACTUAL = 2500362;
    public static final long SHIPMENT_RECEIVED = 2500363;
    public static final long SHIPMENT_CARRIER = 2500364;
    public static final long DATA_EXCHANGE = 2500380;
    public static final long PURCHASE_REQUEST = 2500390;
    public static final long PURCHASE_REQUEST_ITEM = 2500391;
    public static final long SPEC_REFERENCE_COUNTER = 2500401;
    public static final long TRACKING_TYPE = 2500420;
    public static final long ORDER_COST_CENTER = 2500441;
    public static final long COST_CENTER_ADMIN = 2500442;
    public static final long TIMECARD = 2500462;
    public static final long SUBMITTED_TIMECARD = 2500463;
    public static final long TIMECARD_LINE = 2500464;
    public static final long ALL_ACTIVITY_REPORT = 2500480;
    public static final long LIMITED_ACTIVITY_REPORT = 2500481;
    public static final long SPEC_REFERENCE = 2500482;
    public static final long ADDITIONAL_UNIT_PRICE = 2500502;
    public static final long APPROVED_SUPPLIER = 2500503;
    public static final long NOTIFICATION_CUSTOM_EVENT_POSTED = 2500504;
    public static final long WORKGROUP_ATTRIBUTES = 2500522;
    public static final long ROLE_GROUP = 2500561;
    public static final long ORDER_ACTIVITY_REPORT = 2500591;
    public static final long COST_CENTER_REPORT = 2500592;
    public static final long PROJECT_STATUS_REPORT = 2500593;
    public static final long ORDER_STATUS_REPORT = 2500594;
    public static final long PROJECT_PARTICIPATION_REPORT = 2500601;
    public static final long ESTIMATE_ANALYSIS_REPORT = 2500621;
    public static final long ESTIMATE_WINLOSS_REPORT = 2500622;
    public static final long PREMARKUP_QUOTE = 2500627;
    public static final long CLIENT = 2500646;
    public static final long CUSTOM_FORM = 2500665;
    public static final long CUSTOM_FIELD = 2500666;
    public static final long PROJECT_COST_CENTER = 2500667;
    public static final long OUTSOURCE_PROFIT_REPORT = 2500685;
    public static final long WHERE_USED_REPORT = 2500705;
    public static final long DIRECT_MAIL_MATRIX_REPORT = 2500706;
    public static final long BUDGET_CATEGORY = 2500725;
    public static final long PROJECT_BUDGET = 2500726;
    public static final long PROJECT_COST = 2500727;
    public static final long PROJECT_COST_ITEM = 2500728;
    public static final long RFE_ITEM_OPTION = 2500730;
    public static final long ESTIMATE_ITEM_OPTION = 2500731;
    public static final long ESTIMATE_ITEM_PRICE = 2500732;
    public static final long TASK_PREFERENCES = 2500766;
    public static final long QUICK_ORDER = 2500785;
    public static final long SPEC_QUERY = 2500805;
    public static final long RESOURCES = 2500829;
    public static final long PROJECT_BUDGET_REPORT = 2500866;
    public static final long FOLDER = 2500927;
    public static final long DOCUMENT = 2500928;
    public static final long DOCUMENT_VERSION = 2500929;
    public static final long PROJECT_MILESTONES = 2500945;
    public static final long PROJECT_LEGAL_STATUS = 2500965;
    public static final long PROJECT_COMPLIANCE_STATUS = 2500966;
    public static final long CONTRACT_PRICING = 2500985;
    public static final long INVOICE_ADMIN = 2501005;
    public static final long SUPPLIER_WORKGROUP = 2501006;
    public static final long SOURCING_ADMIN = 2501007;
    public static final long SOURCING = 2501008;
    public static final long DM_CELL = 2501009;
    public static final long WORKGROUP_INVENTORY = 2501047;
    public static final long WORKGROUP_INVENTORY_RECORD = 2501048;
    public static final long EXCHANGE_RATES = 2501067;
    public static final long APPROVAL_ROUTING = 2501068;
    public static final long ROUTING_SLIP = 2501069;
    public static final long PROPOSAL_ADMIN = 2501087;
    public static final long SPEC_PRICING = 2501088;
    public static final long ORDER_ADMIN = 2501108;
    public static final long ESTIMATING_ADMIN = 2501109;
    public static final long WORKGROUP_SPECIALTIES = 2501110;
    public static final long WORKGROUP_EQUIPMENTS = 2501111;
    public static final long WORKGROUP_CAPABILITIES = 2501112;
    public static final long USER_PASSWORD = 2501113;
    public static final long EXTENDED_NOTIFICATION = 2501114;
    public static final long TEAM_OBJECT = 2501115;
    public static final long INVENTORY_ADMIN = 2501116;
    public static final long WORKGROUP_WAREHOUSE_LOCATION = 2501119;
    public static final long WORKGROUP_CUSTOM_DOCUMENT = 2501120;
    public static final long REPOSITORY_REQUEST = 2501121;
    public static final long JOB_REQUEST = 2501141;
    public static final long DOCUMENT_SETTINGS = 2501156;
    public static final long WORKGROUP_ROLE_PRIVILEGES = 2501176;
    public static final long PROJECT_ROLE_PRIVILEGES = 2501177;
    public static final long ITEM_MASTER_ADMIN = 2501196;
    public static final long ITEM_MASTER = 2501197;
    public static final long PROJECT_HOT_FOR_TEAM = 2501198;
    public static final long JOB_DETAILS = 2501199;
    public static final long JOB_SPEC_USER_STATE = 2501200;
    public static final long PSF = 2501201;
    public static final long SYSTEM_ADMIN_ANNOUNCEMENT = 2501240;
    public static final long REPORT = 2501256;
    public static final long SITE = 2501257;
    public static final long PRODUCT = 2501258;
    public static final long SERVICE_PROVIDER = 2501259;
    public static final long OBJECT_CLASS_SPEC_GRID_CONFIG = 2502000;
    public static final long OBJECT_CLASS_SPEC_QTY_KIT = 2502002;
    public static final long OBJECT_CLASS_SP_KIND_CUSTOM_DATA_CONFIG = 2502003;
    public static final long OBJECT_CLASS_SP_KIND = 2502004;
    public static final long OBJECT_CLASS_SP_KIND_CUSTOM_DATA = 2502005;
    public static final long OBJECT_CLASS_SP_KIND_QTY_PER_KIT = 2502006;
    public static final long NL_ACCOUNTUSER_ROLE = 2502007;
    public static final long NL_ROLES = 2502008;
    public static final long NEW_RP_DASHBOARD = 2502009;
    public static final long NEW_RP_ITEM_ROLE = 2502010;
    public static final long NEW_RP_WGSYS_DASHBOARD = 2502011;
    public static final long NEW_RP_WGSYS_ITEM_ROLES = 2502012;


}
// Max ID: 2501256
// Count:  227



