package com.noosh.app.commons.constant;

/**
 * Constant interface: StringID
 * Constant source:    NRF
 * Constant query:     /Users/<USER>/Documents/src/noosh/p4/workspace/ticon/sahara/tools/../modules/resource/i18n/default/en/string.nrf
 *
 * Copyright 1998-2010 Noosh, Inc.
 *
 * <AUTHOR>
 * @since  Sun Dec 19 03:02:24 GMT 2010
 */
public interface StringID
{
    String BUNDLE_NAME = "string";
    long LOGIN_NAME_IN_USE = 1;
    long INACTIVE_ACCOUNT = 2;
    long INVALID_LOGIN = 3;
    long INVALID_PASSWORD = 4;
    long PASSWORDS_DO_NOT_MATCH = 5;
    long DATE_INVALID_CONSTRAINT = 6;
    long EMAIL_INVALID_CONSTRAINT = 7;
    long INTEGER_INVALID_CONSTRAINT = 8;
    long NUMBER_INVALID_CONSTRAINT = 9;
    long RANGE_INVALID_CONSTRAINT = 10;
    long RANGE_TOOSMALL_CONSTRAINT = 11;
    long RANGE_TOOBIG_CONSTRAINT = 12;
    long PASSWORD_TOOSHORT_CONSTRAINT = 13;
    long PASSWORD_TOOLONG_CONSTRAINT = 14;
    long USERNAME_TOOSHORT_CONSTRAINT = 15;
    long USERNAME_TOOLONG_CONSTRAINT = 16;
    long REQUIRED_CONSTRAINT = 17;
    long ZIPCODE_INVALID_CONSTRAINT = 18;
    long MAXLENGTH_TOOBIG_CONSTRAINT = 19;
    long TR_TRACKING_SELECT_EVENT = 20;
    long TR_TRACKING_COMMENTS = 21;
    long TR_TRACKING_EVENT_COLUMN = 22;
    long TR_TRACKING_DESCRIPTION_COLUMN = 23;
    long TR_TRACKING_CREATOR_COLUMN = 24;
    long TR_TRACKING_DATE_COLUMN = 25;
    long TEAM_DEFAULT_TITLE_MESSAGE = 26;
    long REPORT_SPEC_SAVED = 27;
    long REPORT_RESULT_SAVED = 28;
    long MUST_SELECT_ONE_DATA_FIELD = 29;
    long MUST_NOT_HAVE_BREAK = 30;
    long MUST_PROVIDE_FILTER_VALUE = 31;
    long PROJECT_UNCATEGORIZED = 32;
    long AC_ADDRESS_TYPE_DESCRIPTION_MAIN = 33;
    long AC_ADDRESS_TYPE_DESCRIPTION_MAILING = 34;
    long AC_ADDRESS_TYPE_DESCRIPTION_SHIPPING = 35;
    long AC_ADDRESS_TYPE_DESCRIPTION_BRANCH = 36;
    long AC_ADDRESS_TYPE_DESCRIPTION_WAREHOUSE = 37;
    long AC_ADDRESS_TYPE_DESCRIPTION_OTHER = 38;
    long CONSTRAINED = 40;
    long AC_WORKGROUP_TYPE_DESCRIPTION_BUSINESS_UNIT = 51;
    long AC_WORKGROUP_TYPE_DESCRIPTION_BUYER = 52;
    long AC_WORKGROUP_TYPE_DESCRIPTION_SUPPLIER = 53;
    long AC_WORKGROUP_TYPE_DESCRIPTION_AGENT = 54;
    long AC_LOCALE_DESCRIPTION_en_US = 55;
    long CO_SERVICE_DESCRIPTION_PRINT = 56;
    long CO_SERVICE_DESCRIPTION_INSURANCE = 57;
    long CO_SERVICE_DESCRIPTION_MARKETING_SERVICES = 58;
    long SY_STATUS_DESCRIPTION_PM_PROJECT_ACTIVE = 63;
    long SY_STATUS_DESCRIPTION_PM_PROJECT_INACTIVE = 64;
    long SY_STATUS_DESCRIPTION_PM_PROJECT_DELETED = 65;
    long SY_STATUS_DESCRIPTION_RP_REQUEST_REQUESTED = 66;
    long SY_STATUS_DESCRIPTION_RP_REQUEST_RUNNING = 67;
    long SY_STATUS_DESCRIPTION_RP_REQUEST_COMPLETED = 68;
    long SY_STATUS_DESCRIPTION_RP_REQUEST_FAILED = 69;
    long SY_STATUS_DESCRIPTION_RP_SAVED_REQUEST_DELETED = 70;
    long SY_STATUS_DESCRIPTION_RP_SAVED_REQUEST_ACTIVE = 71;
    long TR_TRACKING_TYPE_NAME_CREATE_PROJECT = 72;
    long TR_TRACKING_TYPE_DESCRIPTION_CREATE_PROJECT = 73;
    long TR_TRACKING_TYPE_NAME_CREATE_JOB = 74;
    long TR_TRACKING_TYPE_DESCRIPTION_CREATE_JOB = 75;
    long TR_TRACKING_TYPE_NAME_SHIP_PRODUCTS = 76;
    long TR_TRACKING_TYPE_DESCRIPTION_SHIP_PRODUCTS = 77;
    long TR_TRACKING_TYPE_NAME_CUSTOMER_CANCELED = 78;
    long TR_TRACKING_TYPE_DESCRIPTION_CUSTOMER_CANCELED = 79;
    long TR_TRACKING_TYPE_TEAMMEMBER_ADDED = 80;
    long TR_TRACKING_TYPE_TEAMMEMBER_ADDED_DESC = 81;
    long SY_STATUS_DESCRIPTION_RP_SAVED_RESULT_DELETED = 82;
    long SY_STATUS_DESCRIPTION_RP_SAVED_RESULT_ACTIVE = 83;
    long TM_TEMPLATE_ITEM_MUST_HAVE_USER_OR_ROLE = 86;
    long TM_TEMPLATE_USER_EXISTS = 87;
    long ORDER_TYPE_ORDER = 88;
    long ORDER_TYPE_QUICK_ORDER = 89;
    long ORDER_TYPE_CHANGE_ORDER = 90;
    long INVALID_CLIENT_USER_SELECTED = 91;
    long INVALID_CLIENT_WORKGROUP_SELECTED = 92;
    long INVALID_OUTSORCER_WORKGROUP_SELECTED = 93;
    long ORDER_TYPE_QUOTE_ORDER = 94;
    long ORDER_TYPE_GENERATED = 95;
    long TM_TEMPLATE_ITEM_MUST_HAVE_ROLE = 101;
    long TM_MUST_HAVE_ROLE = 102;
    long EM_OPTION_TYPE_QUANTITY = 103;
    long EM_PRICE_BREAKOUT_TYPE_BINDERY = 104;
    long EM_PRICE_BREAKOUT_TYPE_DATA_PROCESSING = 105;
    long EM_PRICE_BREAKOUT_TYPE_INSERT = 106;
    long EM_PRICE_BREAKOUT_TYPE_LABELAIRE = 107;
    long EM_PRICE_BREAKOUT_TYPE_PAPER = 108;
    long EM_PRICE_BREAKOUT_TYPE_PERSONALIZATION = 109;
    long EM_PRICE_BREAKOUT_TYPE_PREPRESS = 110;
    long EM_PRICE_BREAKOUT_TYPE_PRINTING = 111;
    long EM_PRICE_BREAKOUT_TYPE_SHIPPING = 112;
    long EM_PRICE_BREAKOUT_TYPE_POSTAGE = 113;
    long EM_PRICE_BREAKOUT_TYPE_OTHER = 114;
    long TR_TRACKING_TYPE_NAME_CREATE_RFE = 115;
    long TR_TRACKING_TYPE_DESCRIPTION_CREATE_RFE = 116;
    long TR_TRACKING_TYPE_NAME_UPDATE_RFE = 117;
    long TR_TRACKING_TYPE_DESCRIPTION_UPDATE_RFE = 118;
    long TR_TRACKING_TYPE_NAME_SEND_RFE = 119;
    long TR_TRACKING_TYPE_DESCRIPTION_SEND_RFE = 120;
    long TR_TRACKING_TYPE_NAME_DELETE_RFE = 121;
    long TR_TRACKING_TYPE_DESCRIPTION_DELETE_RFE = 122;
    long TR_TRACKING_TYPE_NAME_REJECT_RFE = 123;
    long TR_TRACKING_TYPE_DESCRIPTION_REJECT_RFE = 124;
    long RP_DATA_FIELD_TYPE_NAME_NUMBER = 134;
    long RP_DATA_FIELD_TYPE_NAME_STRING = 135;
    long RP_DATA_FIELD_TYPE_NAME_DATE = 136;
    long RP_SORT_TYPE_NAME_ASCENDING = 141;
    long RP_SORT_TYPE_NAME_DESCENDING = 142;
    long RP_ALIGNMENT_NAME_LEFT = 143;
    long RP_ALIGNMENT_NAME_RIGHT = 144;
    long RP_ALIGNMENT_NAME_CENTER = 145;
    long RP_RUN_MODE_NAME_ONLINE = 146;
    long RP_RUN_MODE_NAME_BATCH = 147;
    long RP_OUTPUT_FORMAT_NAME_HTML = 148;
    long RP_OUTPUT_FORMAT_NAME_CSV = 149;
    long RP_LOGICAL_OPERATOR_VALUE_AND = 154;
    long RP_LOGICAL_OPERATOR_VALUE_OR = 155;
    long RP_AGGREGATE_FUNCTION_NAME_AVERAGE = 156;
    long RP_AGGREGATE_FUNCTION_NAME_COUNT = 157;
    long RP_AGGREGATE_FUNCTION_NAME_MAXIMUM = 158;
    long RP_AGGREGATE_FUNCTION_NAME_MINIMUM = 159;
    long RP_AGGREGATE_FUNCTION_NAME_SUMMARY = 160;
    long RP_FILTER_OPERATOR_VALUE_EQUAL = 161;
    long RP_FILTER_OPERATOR_VALUE_NOT_EQUAL = 162;
    long RP_FILTER_OPERATOR_VALUE_GREATER = 163;
    long RP_FILTER_OPERATOR_VALUE_GREATER_EQUAL = 164;
    long RP_FILTER_OPERATOR_VALUE_LESS = 165;
    long RP_FILTER_OPERATOR_VALUE_LESS_EQUAL = 166;
    long RP_FILTER_OPERATOR_VALUE_BEGIN_WITH = 167;
    long RP_FILTER_OPERATOR_VALUE_NOT_BEGIN_WITH = 168;
    long RP_FILTER_OPERATOR_VALUE_END_WITH = 169;
    long RP_FILTER_OPERATOR_VALUE_NOT_END_WITH = 170;
    long RP_FILTER_OPERATOR_VALUE_CONTAIN = 171;
    long RP_FILTER_OPERATOR_VALUE_NOT_CONTAIN = 172;
    long RP_FILTER_OPERATOR_VALUE_IS_NULL = 173;
    long RP_FILTER_OPERATOR_VALUE_IS_NOT_NULL = 174;
    long RP_REQUEST_FREQUENCY_NAME_DAILY = 175;
    long RP_REQUEST_FREQUENCY_NAME_WEEKLY = 176;
    long RP_REQUEST_FREQUENCY_NAME_MONTHLY = 177;
    long RP_DATA_CATEGORY_NAME_ACCOUNT_MANAGEMENT = 178;
    long RP_DATA_CATEGORY_NAME_ACCOUNT_MANAGEMENT_WORKGROUP_USER = 179;
    long RP_DATA_CATEGORY_NAME_INSURANCE = 180;
    long RP_DATA_CATEGORY_NAME_INSURANCE_PROJECT = 181;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT = 182;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_ESTIMATE_BUY = 183;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_PROJECT = 184;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_ORDER_BUY = 185;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_SPEC = 186;
    long RFQ_DRAFT = 187;
    long RFQ_OPEN = 188;
    long RFQ_CLOSED = 189;
    long RFQ_CANCELLED = 190;
    long QUOTE_RFQ_CANCELLED = 191;
    long QUOTE_CLOSED = 192;
    long QUOTE_DRAFT = 193;
    long QUOTE_PENDING_CLIENT_ACCEPTANCE = 194;
    long QUOTE_RETRACTED = 195;
    long QUOTE_REJECTED = 196;
    long QUOTE_ACCEPTED = 197;
    long INVALID_QUOTE_PRICE = 198;
    long NOTIFICATION_TYPE_RFQ_SENT = 200;
    long NOTIFICATION_TYPE_RFQ_CLOSED = 201;
    long NOTIFICATION_TYPE_RFQ_CANCELLED = 202;
    long NOTIFICATION_TYPE_QUOTE_SENT = 203;
    long NOTIFICATION_TYPE_QUOTE_REJECTED = 204;
    long NOTIFICATION_TYPE_QUOTE_REVISED = 205;
    long NOTIFICATION_TYPE_QUOTE_ACCEPTED = 206;
    long NOTIFICATION_TYPE_QUOTE_RETRACTED = 207;
    long NOTIFICATION_TYPE_QUOTE_CLOSED = 208;
    long TRACKING_TYPE_RFQ_SENT = 210;
    long TRACKING_TYPE_RFQ_CLOSED = 211;
    long TRACKING_TYPE_RFQ_CANCELLED = 212;
    long TRACKING_TYPE_QUOTE_SENT = 213;
    long TRACKING_TYPE_QUOTE_REJECTED = 214;
    long TRACKING_TYPE_QUOTE_REVISED = 215;
    long TRACKING_TYPE_QUOTE_ACCEPTED = 216;
    long TRACKING_TYPE_QUOTE_RETRACTED = 217;
    long TRACKING_TYPE_QUOTE_CLOSED = 218;
    long TRACKING_TYPE_QUOTE_CLOSED_DESC = 219;
    long TASK_TYPE_QUOTE_SEND_NAME = 220;
    long TASK_TYPE_QUOTE_SEND_DESC = 221;
    long TASK_TYPE_QUOTE_REVIEW_NAME = 222;
    long TASK_TYPE_QUOTE_REVIEW_DESC = 223;
    long TRACKING_TYPE_RFQ_SENT_DESC = 230;
    long TRACKING_TYPE_RFQ_CLOSED_DESC = 231;
    long TRACKING_TYPE_RFQ_CANCELLED_DESC = 232;
    long TRACKING_TYPE_QUOTE_SENT_DESC = 233;
    long TRACKING_TYPE_QUOTE_REJECTED_DESC = 234;
    long TRACKING_TYPE_QUOTE_REVISED_DESC = 235;
    long TRACKING_TYPE_QUOTE_ACCEPTED_DESC = 236;
    long TRACKING_TYPE_QUOTE_RETRACTED_DESC = 237;
    long QUOTE_RECIPIENT_WRONG_WORKGROUP = 238;
    long QUOTE_RECIPIENT_NO_OWNER = 239;
    long QUOTE_RECIPIENT_REQUIRED = 240;
    long USERNAME_RESERVED_CONSTRAINT = 241;
    long CANNOT_SEND_ORDER_TO_CLIENT = 242;
    long CANNOT_SEND_ORDER_TO_NON_OUTSOURCER = 243;
    long OBJECT_STATE_DESC_ORDER_OUTSOURCER_TO_ACCEPT = 244;
    long OBJECT_STATE_DESC_ORDER_CLIENT_TO_ACCEPT = 245;
    long CANNOT_INVITE_MEMBER_AS_SUPPLIER = 246;
    long MEDIATOR_PRINT = 247;
    long MEDIATOR_LASER = 248;
    long MEDIATOR_PREPRESS = 249;
    long JOB_STATUS_NEW = 250;
    long JOB_STATUS_IN_ESTIMATING = 251;
    long JOB_STATUS_IN_QUOTING = 252;
    long JOB_STATUS_ORDER_PENDING = 253;
    long JOB_STATUS_ORDER_ACCEPTED = 254;
    long JOB_STATUS_COMPLETE = 255;
    long JOB_LOST = 256;
    long MEDIATOR_LOGISTICS = 257;
    long APPROVED_SUPPLIER_MISSING_DEFAULT_CSR = 258;
    long CELL_MISSING_REQUIRED_DROPDATE = 259;
    long ORDER_ALLOCATION_ACTIVE = 260;
    long ORDER_ALLOCATION_CANCELLED = 261;
    long NO_PERMISSION_TO_CREATE_ORDER = 286;
    long PROJECT_CANNOT_BE_AGGREGATED = 287;
    long OPTIONS_OF_SAME_SPEC_CANNOT_BE_AGGREGATED = 288;
    long NO_PERMISSION_TO_ATTACH_PROJECT = 289;
    long CANNOT_DELETE_SPEC_RFE_FOREIGN_KEY = 290;
    long CANNOT_DELETE_SPEC_ESTIMATE_FOREIGN_KEY = 291;
    long CANNOT_DELETE_SPEC_QUOTE_FOREIGN_KEY = 292;
    long NO_PROJECTS_SELECTED = 293;
    long CANNOT_DELETE_SPEC_CURRENTSPEC_FOREIGN_KEY = 294;
    long COPY_OF_PATTERN_SPEC_REF = 295;
    long COPY_OF_PATTERN_SPEC_VERSION = 296;
    long COPY_OF_PATTERN_REORDER_VERSION = 297;
    long COPY_OF_PATTERN_REORDER_REF = 298;
    long COPY_OF_PATTERN = 299;
    long NO_CATEGORY = 300;
    long PASSWORD_IN_HISTORY = 301;
    long PASSWORD_CURRENT_DOES_NOT_MATCH = 302;
    long PASSWORD_CANNOT_MATCH = 303;
    long PASSWORD_SAME_AS_LOGIN = 304;
    long PASSWORD_STRONG_SAME_AS_NAME = 305;
    long PASSWORD_STRONG_REPEAT_CHARS = 306;
    long PASSWORD_STRONG_TOO_SHORT = 307;
    long NON_PROJECT = 308;
    long TM_MUST_ENTER_USER = 310;
    long TM_TEMPLATE_EMPTY = 311;
    long DESKOID_TITLE_RFE_ESTIMATE = 312;
    long TM_MUST_ENTER_USER2 = 313;
    long AC_LOCALE_DESCRIPTION_es_SP = 314;
    long DESKOID_TITLE_TRACKING = 315;
    long DESKOID_TITLE_TEAM = 316;
    long DESKOID_TITLE_FILE = 317;
    long DESKOID_TITLE_MESSAGES = 318;
    long TM_MUST_ENTER_SUPPLIERS = 319;
    long OBJECT_STATE_DESC_ORDER_PENDING_SUBMISSION = 350;
    long OBJECT_STATE_DESC_CHANGE_ORDER_PENDING_SUBMISSION = 351;
    long TR_TRACKING_TYPE_NAME_CREATE_ESTIMATE = 364;
    long TR_TRACKING_TYPE_DESCRIPTION_CREATE_ESTIMATE = 365;
    long TR_TRACKING_TYPE_NAME_UPDATE_ESTIMATE = 366;
    long TR_TRACKING_TYPE_DESCRIPTION_UPDATE_ESTIMATE = 367;
    long TR_TRACKING_TYPE_NAME_APPROVE_DRAFT_ESTIMATE = 368;
    long TR_TRACKING_TYPE_DESCRIPTION_APPROVE_DRAFT_ESTIMATE = 369;
    long TR_TRACKING_TYPE_NAME_SEND_ESTIMATE = 370;
    long TR_TRACKING_TYPE_DESCRIPTION_SEND_ESTIMATE = 371;
    long TR_TRACKING_TYPE_NAME_DELETE_ESTIMATE = 372;
    long TR_TRACKING_TYPE_DESCRIPTION_DELETE_ESTIMATE = 373;
    long TR_TRACKING_TYPE_NAME_REJECT_ESTIMATE = 374;
    long TR_TRACKING_TYPE_DESCRIPTION_REJECT_ESTIMATE = 375;
    long TR_TRACKING_TYPE_NAME_CANCEL_ESTIMATE = 376;
    long TR_TRACKING_TYPE_DESCRIPTION_CANCEL_ESTIMATE = 377;
    long TR_TRACKING_TYPE_NAME_REVISE_ESTIMATE = 378;
    long TR_TRACKING_TYPE_DESCRIPTION_REVISE_ESTIMATE = 379;
    long TR_TRACKING_TYPE_NAME_INACTIVATE_ESTIMATE = 380;
    long TR_TRACKING_TYPE_DESCRIPTION_INACTIVATE_ESTIMATE = 381;
    long TR_TRACKING_TYPE_NAME_ACTIVATE_ESTIMATE = 382;
    long TR_TRACKING_TYPE_DESCRIPTION_ACTIVATE_ESTIMATE = 383;
    long TR_TRACKING_TYPE_NAME_APPROVE_ESTIMATE = 384;
    long TR_TRACKING_TYPE_DESCRIPTION_APPROVE_ESTIMATE = 385;
    long FM_MUST_SPECIFY_VALID_FILE = 386;
    long CANNOT_DELETE_SPEC_JOB_FOREIGN_KEY = 387;
    long CANNOT_DELETE_SPEC_PRITEM_FOREIGN_KEY = 388;
    long DM_ACCOUNT_USER_IS_DEFAULT_USER_WRKGRP_NM = 436;
    long DM_ACCOUNT_USER_USER_DEFAULT_ROLE_ID_NM = 437;
    long DM_ACCOUNT_USER_USER_STATUS_ID_NM = 438;
    long DM_ACCOUNT_USER_USER_STATUS_NAME_STR_ID_NM = 439;
    long DM_PERSON_INVITED_BY_FIRST_NAME_NM = 440;
    long DM_PERSON_INVITED_BY_LAST_NAME_NM = 441;
    long DM_PERSON_IS_USER_PUBLIC_NM = 442;
    long DM_PERSON_USER_ACTIVATION_DATE_NM = 443;
    long DM_PERSON_USER_ADDRESS_NM = 444;
    long DM_PERSON_USER_ADDR_CITY_NM = 445;
    long DM_PERSON_USER_ADDR_POSTAL_NM = 446;
    long DM_PERSON_USER_ADDR_STATE_NM = 447;
    long DM_PERSON_USER_FAX_NUMBER_NM = 448;
    long DM_PERSON_USER_FIRST_NAME_NM = 449;
    long DM_PERSON_USER_INVITED_BY_USER_ID_NM = 450;
    long DM_PERSON_USER_LAST_NAME_NM = 451;
    long DM_PERSON_USER_LOCALE_DESC_STR_ID_NM = 452;
    long DM_PERSON_USER_LOCALE_ID_NM = 453;
    long DM_PERSON_USER_LOGIN_NAME_NM = 454;
    long DM_PERSON_USER_MIDDLE_NAME_NM = 455;
    long DM_PERSON_USER_PAGER_EMAIL_NM = 456;
    long DM_PERSON_USER_PAGER_NUMBER_NM = 457;
    long DM_PERSON_USER_PASSWORD_NM = 458;
    long DM_PERSON_USER_PASSWORD_ANSWER_NM = 459;
    long DM_PERSON_USER_PASSWORD_EXPIRED_ON_NM = 460;
    long DM_PERSON_USER_PASSWORD_QUESTION_NM = 461;
    long DM_PERSON_USER_PHONE_NUMBER_NM = 462;
    long DM_PERSON_USER_TIME_ZONE_DESC_STR_ID_NM = 463;
    long DM_PERSON_USER_TIME_ZONE_ID_NM = 464;
    long DM_PERSON_USER_TITLE_NM = 465;
    long DM_PERSON_EMAIL_IS_DEFAULT_USER_EMAIL_NM = 466;
    long DM_PERSON_EMAIL_USER_EMAIL_ADDRESS_NM = 467;
    long DM_WORKGROUP_COMPANY_NAME_NM = 468;
    long DM_WORKGROUP_WORKGROUP_NAME_NM = 469;
    long DM_WORKGROUP_WRKGRP_DEFAULT_CURRENCY_ID_NM = 470;
    long DM_WORKGROUP_WRKGRP_STATUS_ID_NM = 471;
    long DM_WORKGROUP_WRKGRP_STATUS_NAME_STR_ID_NM = 472;
    long DM_WORKGROUP_WRKGRP_TYPE_DESC_STR_ID_NM = 473;
    long DM_WORKGROUP_WRKGRP_TYPE_ID_NM = 474;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDRESS_NM = 475;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_CITY_NM = 476;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_POSTAL_NM = 477;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_STATE_NM = 478;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_TYPE_DESC_STR_ID_NM = 479;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_TYPE_ID_NM = 480;
    long DM_ACCOUNT_USER_IS_DEFAULT_USER_WRKGRP_DC = 481;
    long DM_ACCOUNT_USER_USER_DEFAULT_ROLE_ID_DC = 482;
    long DM_ACCOUNT_USER_USER_STATUS_ID_DC = 483;
    long DM_ACCOUNT_USER_USER_STATUS_NAME_STR_ID_DC = 484;
    long DM_PERSON_INVITED_BY_FIRST_NAME_DC = 485;
    long DM_PERSON_INVITED_BY_LAST_NAME_DC = 486;
    long DM_PERSON_IS_USER_PUBLIC_DC = 487;
    long DM_PERSON_USER_ACTIVATION_DATE_DC = 488;
    long DM_PERSON_USER_ADDRESS_DC = 489;
    long DM_PERSON_USER_ADDR_CITY_DC = 490;
    long DM_PERSON_USER_ADDR_POSTAL_DC = 491;
    long DM_PERSON_USER_ADDR_STATE_DC = 492;
    long DM_PERSON_USER_FAX_NUMBER_DC = 493;
    long DM_PERSON_USER_FIRST_NAME_DC = 494;
    long DM_PERSON_USER_INVITED_BY_USER_ID_DC = 495;
    long DM_PERSON_USER_LAST_NAME_DC = 496;
    long DM_PERSON_USER_LOCALE_DESC_STR_ID_DC = 497;
    long DM_PERSON_USER_LOCALE_ID_DC = 498;
    long DM_PERSON_USER_LOGIN_NAME_DC = 499;
    long DM_PERSON_USER_MIDDLE_NAME_DC = 500;
    long DM_PERSON_USER_PAGER_EMAIL_DC = 501;
    long DM_PERSON_USER_PAGER_NUMBER_DC = 502;
    long DM_PERSON_USER_PASSWORD_DC = 503;
    long DM_PERSON_USER_PASSWORD_ANSWER_DC = 504;
    long DM_PERSON_USER_PASSWORD_EXPIRED_ON_DC = 505;
    long DM_PERSON_USER_PASSWORD_QUESTION_DC = 506;
    long DM_PERSON_USER_PHONE_NUMBER_DC = 507;
    long DM_PERSON_USER_TIME_ZONE_DESC_STR_ID_DC = 508;
    long DM_PERSON_USER_TIME_ZONE_ID_DC = 509;
    long DM_PERSON_USER_TITLE_DC = 510;
    long DM_PERSON_EMAIL_IS_DEFAULT_USER_EMAIL_DC = 511;
    long DM_PERSON_EMAIL_USER_EMAIL_ADDRESS_DC = 512;
    long DM_WORKGROUP_COMPANY_NAME_DC = 513;
    long DM_WORKGROUP_WORKGROUP_NAME_DC = 514;
    long DM_WORKGROUP_WRKGRP_DEFAULT_CURRENCY_ID_DC = 515;
    long DM_WORKGROUP_WRKGRP_STATUS_ID_DC = 516;
    long DM_WORKGROUP_WRKGRP_STATUS_NAME_STR_ID_DC = 517;
    long DM_WORKGROUP_WRKGRP_TYPE_DESC_STR_ID_DC = 518;
    long DM_WORKGROUP_WRKGRP_TYPE_ID_DC = 519;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDRESS_DC = 520;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_CITY_DC = 521;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_POSTAL_DC = 522;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_STATE_DC = 523;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_TYPE_DESC_STR_ID_DC = 524;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_TYPE_ID_DC = 525;
    long DM_ACCOUNT_USER_IS_DEFAULT_USER_WRKGRP_HD = 526;
    long DM_ACCOUNT_USER_USER_DEFAULT_ROLE_ID_HD = 527;
    long DM_ACCOUNT_USER_USER_STATUS_ID_HD = 528;
    long DM_ACCOUNT_USER_USER_STATUS_NAME_STR_ID_HD = 529;
    long DM_PERSON_INVITED_BY_FIRST_NAME_HD = 530;
    long DM_PERSON_INVITED_BY_LAST_NAME_HD = 531;
    long DM_PERSON_IS_USER_PUBLIC_HD = 532;
    long DM_PERSON_USER_ACTIVATION_DATE_HD = 533;
    long DM_PERSON_USER_ADDRESS_HD = 534;
    long DM_PERSON_USER_ADDR_CITY_HD = 535;
    long DM_PERSON_USER_ADDR_POSTAL_HD = 536;
    long DM_PERSON_USER_ADDR_STATE_HD = 537;
    long DM_PERSON_USER_FAX_NUMBER_HD = 538;
    long DM_PERSON_USER_FIRST_NAME_HD = 539;
    long DM_PERSON_USER_INVITED_BY_USER_ID_HD = 540;
    long DM_PERSON_USER_LAST_NAME_HD = 541;
    long DM_PERSON_USER_LOCALE_DESC_STR_ID_HD = 542;
    long DM_PERSON_USER_LOCALE_ID_HD = 543;
    long DM_PERSON_USER_LOGIN_NAME_HD = 544;
    long DM_PERSON_USER_MIDDLE_NAME_HD = 545;
    long DM_PERSON_USER_PAGER_EMAIL_HD = 546;
    long DM_PERSON_USER_PAGER_NUMBER_HD = 547;
    long DM_PERSON_USER_PASSWORD_HD = 548;
    long DM_PERSON_USER_PASSWORD_ANSWER_HD = 549;
    long DM_PERSON_USER_PASSWORD_EXPIRED_ON_HD = 550;
    long DM_PERSON_USER_PASSWORD_QUESTION_HD = 551;
    long DM_PERSON_USER_PHONE_NUMBER_HD = 552;
    long DM_PERSON_USER_TIME_ZONE_DESC_STR_ID_HD = 553;
    long DM_PERSON_USER_TIME_ZONE_ID_HD = 554;
    long DM_PERSON_USER_TITLE_HD = 555;
    long DM_PERSON_EMAIL_IS_DEFAULT_USER_EMAIL_HD = 556;
    long DM_PERSON_EMAIL_USER_EMAIL_ADDRESS_HD = 557;
    long DM_WORKGROUP_COMPANY_NAME_HD = 558;
    long DM_WORKGROUP_WORKGROUP_NAME_HD = 559;
    long DM_WORKGROUP_WRKGRP_DEFAULT_CURRENCY_ID_HD = 560;
    long DM_WORKGROUP_WRKGRP_STATUS_ID_HD = 561;
    long DM_WORKGROUP_WRKGRP_STATUS_NAME_STR_ID_HD = 562;
    long DM_WORKGROUP_WRKGRP_TYPE_DESC_STR_ID_HD = 563;
    long DM_WORKGROUP_WRKGRP_TYPE_ID_HD = 564;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDRESS_HD = 565;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_CITY_HD = 566;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_POSTAL_HD = 567;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_STATE_HD = 568;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_TYPE_DESC_STR_ID_HD = 569;
    long DM_WORKGROUP_ADDRESS_WRKGRP_ADDR_TYPE_ID_HD = 570;
    long DM_PERSON_PERSON_ID_NM = 571;
    long DM_WORKGROUP_WORKGROUP_ID_NM = 572;
    long DM_PERSON_PERSON_ID_DC = 573;
    long DM_WORKGROUP_WORKGROUP_ID_DC = 574;
    long DM_PERSON_PERSON_ID_HD = 575;
    long DM_WORKGROUP_WORKGROUP_ID_HD = 576;
    long SP_REVISION_OF = 577;
    long PROJECT_MGR_MY_JOBS = 578;
    long PROJECT_MGR_ALL_JOBS = 579;
    long PROJECT_MGR_ALL_STATUSES = 580;
    long TR_TRACKING_TYPE_TEAMMEMBER_REMOVED = 581;
    long TR_TRACKING_TYPE_TEAMMEMBER_REMOVED_DESC = 582;
    long TR_TRACKING_TYPE_TEAMMEMBER_ROLE_CHANGE = 583;
    long TR_TRACKING_TYPE_TEAMMEMBER_ROLE_CHANGE_DESC = 584;
    long TR_TRACKING_TYPE_TEAMMEMBER_USER_CHANGE = 585;
    long TR_TRACKING_TYPE_TEAMMEMBER_USER_CHANGE_DESC = 586;
    long RP_FLOATING_FILTER_TYPE_NAME_DAY = 592;
    long RP_FLOATING_FILTER_TYPE_NAME_WEEK = 593;
    long RP_FLOATING_FILTER_TYPE_NAME_MONTH = 594;
    long RP_FLOATING_FILTER_TYPE_NAME_QUARTER = 595;
    long RP_FLOATING_FILTER_TYPE_NAME_YEAR = 596;
    long ALL_CATEGORIES = 601;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_FNAME_NM = 602;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_LNAME_NM = 603;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_USER_ID_NM = 604;
    long DM_PROJECT_PARENT_PROJECT_CLIENT_ACCOUNT_NM = 605;
    long DM_PROJECT_PARENT_PROJECT_COMMENTS_NM = 606;
    long DM_PROJECT_PARENT_PROJECT_CREATE_DATE_NM = 607;
    long DM_PROJECT_PARENT_PROJECT_DESCRIPTION_NM = 608;
    long DM_PROJECT_PARENT_PROJECT_ID_NM = 609;
    long DM_PROJECT_PARENT_PROJECT_NAME_NM = 610;
    long DM_PROJECT_PARENT_PROJECT_NUMBER_NM = 611;
    long DM_PROJECT_PARENT_PROJECT_REFERENCE_NAME_NM = 612;
    long DM_PROJECT_PROJECT_AUTHOR_FNAME_NM = 613;
    long DM_PROJECT_PROJECT_AUTHOR_LNAME_NM = 614;
    long DM_PROJECT_PROJECT_AUTHOR_USER_ID_NM = 615;
    long DM_PROJECT_PROJECT_CLIENT_ACCOUNT_NM = 616;
    long DM_PROJECT_PROJECT_COMMENTS_NM = 617;
    long DM_PROJECT_PROJECT_CREATE_DATE_NM = 618;
    long DM_PROJECT_PROJECT_DESCRIPTION_NM = 619;
    long DM_PROJECT_PROJECT_ID_NM = 620;
    long DM_PROJECT_PROJECT_NAME_NM = 621;
    long DM_PROJECT_PROJECT_NUMBER_NM = 622;
    long DM_PROJECT_PROJECT_REFERENCE_NAME_NM = 623;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_FNAME_DC = 624;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_LNAME_DC = 625;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_USER_ID_DC = 626;
    long DM_PROJECT_PARENT_PROJECT_CLIENT_ACCOUNT_DC = 627;
    long DM_PROJECT_PARENT_PROJECT_COMMENTS_DC = 628;
    long DM_PROJECT_PARENT_PROJECT_CREATE_DATE_DC = 629;
    long DM_PROJECT_PARENT_PROJECT_DESCRIPTION_DC = 630;
    long DM_PROJECT_PARENT_PROJECT_ID_DC = 631;
    long DM_PROJECT_PARENT_PROJECT_NAME_DC = 632;
    long DM_PROJECT_PARENT_PROJECT_NUMBER_DC = 633;
    long DM_PROJECT_PARENT_PROJECT_REFERENCE_NAME_DC = 634;
    long DM_PROJECT_PROJECT_AUTHOR_FNAME_DC = 635;
    long DM_PROJECT_PROJECT_AUTHOR_LNAME_DC = 636;
    long DM_PROJECT_PROJECT_AUTHOR_USER_ID_DC = 637;
    long DM_PROJECT_PROJECT_CLIENT_ACCOUNT_DC = 638;
    long DM_PROJECT_PROJECT_COMMENTS_DC = 639;
    long DM_PROJECT_PROJECT_CREATE_DATE_DC = 640;
    long DM_PROJECT_PROJECT_DESCRIPTION_DC = 641;
    long DM_PROJECT_PROJECT_ID_DC = 642;
    long DM_PROJECT_PROJECT_NAME_DC = 643;
    long DM_PROJECT_PROJECT_NUMBER_DC = 644;
    long DM_PROJECT_PROJECT_REFERENCE_NAME_DC = 645;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_FNAME_HD = 646;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_LNAME_HD = 647;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_USER_ID_HD = 648;
    long DM_PROJECT_PARENT_PROJECT_CLIENT_ACCOUNT_HD = 649;
    long DM_PROJECT_PARENT_PROJECT_COMMENTS_HD = 650;
    long DM_PROJECT_PARENT_PROJECT_CREATE_DATE_HD = 651;
    long DM_PROJECT_PARENT_PROJECT_DESCRIPTION_HD = 652;
    long DM_PROJECT_PARENT_PROJECT_ID_HD = 653;
    long DM_PROJECT_PARENT_PROJECT_NAME_HD = 654;
    long DM_PROJECT_PARENT_PROJECT_NUMBER_HD = 655;
    long DM_PROJECT_PARENT_PROJECT_REFERENCE_NAME_HD = 656;
    long DM_PROJECT_PROJECT_AUTHOR_FNAME_HD = 657;
    long DM_PROJECT_PROJECT_AUTHOR_LNAME_HD = 658;
    long DM_PROJECT_PROJECT_AUTHOR_USER_ID_HD = 659;
    long DM_PROJECT_PROJECT_CLIENT_ACCOUNT_HD = 660;
    long DM_PROJECT_PROJECT_COMMENTS_HD = 661;
    long DM_PROJECT_PROJECT_CREATE_DATE_HD = 662;
    long DM_PROJECT_PROJECT_DESCRIPTION_HD = 663;
    long DM_PROJECT_PROJECT_ID_HD = 664;
    long DM_PROJECT_PROJECT_NAME_HD = 665;
    long DM_PROJECT_PROJECT_NUMBER_HD = 666;
    long DM_PROJECT_PROJECT_REFERENCE_NAME_HD = 667;
    long BATCH_DELETE_PROJECT = 668;
    long BATCH_ASSIGN_CATEGORY = 669;
    long BATCH_INVITE_MEMBER = 670;
    long BATCH_DISMISS_MEMBER = 671;
    long BATCH_CHANGE_STATUS = 672;
    long TM_USER_EXISTS = 673;
    long DM_PROJECT_TEAM_MEMBER_TEAM_MEMBER_FIRST_NAME_NM = 710;
    long DM_PROJECT_TEAM_MEMBER_TEAM_MEMBER_LAST_NAME_NM = 711;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_PERSON_ID_NM = 712;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_USER_FNAME_NM = 713;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_USER_LNAME_NM = 714;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_WRKGRP_ID_NM = 715;
    long DM_PROJECT_TEAM_MEMBER_PERSON_ID_NM = 716;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_WRKGRP_NAME_NM = 717;
    long DM_PROJECT_TRACKING_TRACKING_ID_NM = 718;
    long DM_PROJECT_TRACKING_TRACKING_RECORDED_DATE_NM = 719;
    long DM_PROJECT_TRACKING_TRACKING_TYPE_ID_NM = 720;
    long DM_PROJECT_TRACKING_TRACKING_TYPE_NAME_STR_ID_NM = 721;
    long DM_PROJECT_TEAM_WORKGROUP_WORKGROUP_ID_NM = 722;
    long DM_PROJECT_TEAM_MEMBER_TEAM_MEMBER_FIRST_NAME_DC = 723;
    long DM_PROJECT_TEAM_MEMBER_TEAM_MEMBER_LAST_NAME_DC = 724;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_PERSON_ID_DC = 725;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_USER_FNAME_DC = 726;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_USER_LNAME_DC = 727;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_WRKGRP_ID_DC = 728;
    long DM_PROJECT_TEAM_MEMBER_PERSON_ID_DC = 729;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_WRKGRP_NAME_DC = 730;
    long DM_PROJECT_TRACKING_TRACKING_ID_DC = 731;
    long DM_PROJECT_TRACKING_TRACKING_RECORDED_DATE_DC = 732;
    long DM_PROJECT_TRACKING_TRACKING_TYPE_ID_DC = 733;
    long DM_PROJECT_TRACKING_TRACKING_TYPE_NAME_STR_ID_DC = 734;
    long DM_PROJECT_TEAM_WORKGROUP_WORKGROUP_ID_DC = 735;
    long DM_PROJECT_TEAM_MEMBER_TEAM_MEMBER_FIRST_NAME_HD = 736;
    long DM_PROJECT_TEAM_MEMBER_TEAM_MEMBER_LAST_NAME_HD = 737;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_PERSON_ID_HD = 738;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_USER_FNAME_HD = 739;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_USER_LNAME_HD = 740;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_WRKGRP_ID_HD = 741;
    long DM_PROJECT_TEAM_MEMBER_PERSON_ID_HD = 742;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_WRKGRP_NAME_HD = 743;
    long DM_PROJECT_TRACKING_TRACKING_ID_HD = 744;
    long DM_PROJECT_TRACKING_TRACKING_RECORDED_DATE_HD = 745;
    long DM_PROJECT_TRACKING_TRACKING_TYPE_ID_HD = 746;
    long DM_PROJECT_TRACKING_TRACKING_TYPE_NAME_STR_ID_HD = 747;
    long DM_PROJECT_TEAM_WORKGROUP_WORKGROUP_ID_HD = 748;
    long PROJ_CREATED = 749;
    long PROJ_DELETED = 750;
    long PROJ_ACTIVATED = 751;
    long PROJ_DEACTIVATED = 752;
    long PROJ_AVAILABLE_BID = 753;
    long PROJ_AWARD_YOU = 754;
    long PROJ_AWARD_ELSE = 755;
    long PROJ_SPEC_UPDATED = 756;
    long ROLE_CLASS_CONTEXT_PROJECT_OWNER_DESC = 757;
    long RP_DATA_CATEGORY_NAME_ACCOUNT_MANAGEMENT_DESCN = 758;
    long RP_DATA_CATEGORY_NAME_ACCOUNT_MANAGEMENT_WORKGROUP_USER_DESCN = 759;
    long RP_DATA_CATEGORY_NAME_INSURANCE_DESCN = 760;
    long RP_DATA_CATEGORY_NAME_INSURANCE_PROJECT_DESCN = 761;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_DESCN = 762;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_ESTIMATE_DESCN_BUY = 763;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_PROJECT_DESCN = 764;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_ORDER_DESCN_BUY = 765;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_SPEC_DESCN = 766;
    long ROLE_CHANGE_TYPE_LOST_BIT = 767;
    long ROLE_CHANGE_TYPE_UNINVITED = 768;
    long ROLE_CHANGE_TYPE_JOB_DELETED = 769;
    long ROLE_CHANGE_TYPE_REJECTED = 770;
    long CATEGORY_NAME = 771;
    long DEACTIVATION_REASON_CANCELLED = 772;
    long DEACTIVATION_REASON_ELECTRONIC_FORMS = 773;
    long DEACTIVATION_REASON_INVENTORY_MAINT = 774;
    long DEACTIVATION_REASON_INVENTORY_TRANSFER = 775;
    long DEACTIVATION_REASON_OBSOLETE = 776;
    long DEACTIVATION_REASON_ON_HOLD = 777;
    long DEACTIVATION_REASON_NON_NOOSH = 778;
    long DEACTIVATION_REASON_NON_PRINT = 779;
    long DEACTIVATION_REASON_ARCHIVED = 780;
    long DEACTIVATION_REASON_PRICE_PRODUCT = 781;
    long DEACTIVATION_REASON_OTHER = 782;
    long PROJECT_TYPE_BUYER = 791;
    long PROJECT_TYPE_SUPPLIER = 792;
    long TYPE = 793;
    long BATCH_ACTIVATE = 794;
    long BATCH_DEACTIVATE = 795;
    long STATUS_NAME = 796;
    long ACTIVE = 797;
    long INACTIVE = 798;
    long ALL = 799;
    long LATEST = 800;
    long TA_TASK_TYPE_PROJECT_CREATE_DESC = 801;
    long TA_TASK_TYPE_SEND_RFE_DESC = 804;
    long TA_TASK_TYPE_CREATE_ESTIMATE_DESC = 805;
    long TA_TASK_TYPE_SEND_ESTIMATE_DESC = 806;
    long TA_TASK_TYPE_UNSPECIFIED_DESC = 807;
    long PREFERRED_ROLE_TOKEN_PROJECT_OWNER = 809;
    long TR_TRACKING_TYPE_PROJECT_DELETED = 810;
    long TR_TRACKING_TYPE_PROJECT_DELETED_DESC = 811;
    long PREFERRED_ROLE_TOKEN_PROJECT_SUPPLIER = 812;
    long PREFERRED_ROLE_TOKEN_ESTIMATE_OWNER = 813;
    long PREFERRED_ROLE_TOKEN_RFE_OWNER = 814;
    long TR_TRACKING_TYPE_PROJECT_ACTIVATED = 822;
    long TR_TRACKING_TYPE_PROJECT_ACTIVATED_DESC = 823;
    long TR_TRACKING_TYPE_PROJECT_DEACTIVATED = 824;
    long TR_TRACKING_TYPE_PROJECT_DEACTIVATED_DESC = 825;
    long TR_TRACKING_TYPE_PROJECT_NAME_CHANGED = 826;
    long TR_TRACKING_TYPE_PROJECT_NAME_CHANGED_DESC = 827;
    long PAYMENT_METHOD_PO = 828;
    long PAYMENT_METHOD_CREDIT_CARD = 829;
    long PAYMENT_METHOD_TRANSFER = 830;
    long PAYMENT_METHOD_CASH = 831;
    long CONSTRAINT_SELECT_SPEC = 832;
    long STATE_INVITED = 833;
    long STATE_ACTIVE = 834;
    long STATE_INACTIVE = 835;
    long RP_DATA_FIELD_GROUP_WORKGROUP = 836;
    long RP_DATA_FIELD_GROUP_WORKGROUP_MEMBER = 837;
    long RP_DATA_FIELD_GROUP_PROJECT = 838;
    long RP_DATA_FIELD_GROUP_PARENT_PROJECT = 839;
    long RP_DATA_FIELD_GROUP_CUSTOMIZED_FIELDS = 840;
    long RP_DATA_FIELD_GROUP_PROJECT_TEAM_MEMBER = 841;
    long RP_DATA_FIELD_GROUP_PROJECT_TRACKING = 842;
    long TR_TRACKING_TYPE_QUICK_ORDER_CREATED = 846;
    long TR_TRACKING_TYPE_QUICK_ORDER_CREATED_DESC = 847;
    long CONSTRAINT_SELECT_PROJECT = 849;
    long TR_TRACKING_TYPE_ORDER_CREATED = 850;
    long TR_TRACKING_TYPE_ORDER_CREATED_DESC = 851;
    long CONSTRAINT_SELECT_ESTIMATOR = 852;
    long CONSTRAINT_SPEC = 853;
    long CONSTRAINT_RFE_PERMISSION = 854;
    long TM_UNASSIGNED_MEMBER = 855;
    long TR_TRACKING_SAMPLE_POSTABLE = 856;
    long TR_TRACKING_SAMPLE_POSTABLE_DESC = 857;
    long PREFERRED_ROLE_TOKEN_ORDER_OWNER = 858;
    long AC_WORKGROUP_TYPE_DESCRIPTION_BROKER = 860;
    long PREFERRED_ROLE_TOKEN_RFE_SUPPLIER = 861;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_WRKGRP_ACTVTY_BUY = 862;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_WRKGRP_ACTVTY_DESCN_BUY = 863;
    long REPORT_TITLE_FOR = 864;
    long REPORT_TITLE_REQUEST_BY = 865;
    long REPORT_TITLE_CREATED_ON = 866;
    long PREFERRED_ROLE_TOKEN_ORDER_BUYER = 867;
    long PREFERRED_ROLE_TOKEN_ORDER_SUPPLIER = 868;
    long CONSTRAINT_NO_NONALPHABETIC = 869;
    long CONSTRAINT_INVALID_DATA = 870;
    long UOFM_DAY = 871;
    long UOFM_HOUR = 872;
    long UOFM_EACH = 873;
    long TA_TASK_TYPE_REVIEW_ESTIMATE_DESC = 875;
    long TA_TASK_TYPE_ORDER_REVIEW_DESC = 876;
    long OC_USER_STATUS_CREATED = 878;
    long OC_USER_STATUS_DELETED = 879;
    long OC_USER_STATUS_CANCELLED = 880;
    long OC_USER_STATUS_IN_PROGRESS = 881;
    long OC_USER_STATUS_COMPLETED = 882;
    long OC_USER_STATUS_OBSOLETE = 883;
    long OC_USER_STATUS_ACTIVATED = 884;
    long OC_USER_STATUS_INACTIVATED = 885;
    long OC_USER_STATUS_INVITED = 886;
    long OC_USER_STATUS_DRAFT = 887;
    long OC_USER_STATUS_SENT = 888;
    long OC_USER_STATUS_CLOSED = 889;
    long OC_USER_STATUS_ACCEPTED = 890;
    long OC_USER_STATUS_REJECTED = 891;
    long REPORT_ORDER_TYPE_QUICK_ORDER = 892;
    long REPORT_ORDER_TYPE_NON_QUICK_ORDER = 893;
    long REPORT_ESTIMATE_WIN = 894;
    long REPORT_ESTIMATE_LOSS = 895;
    long REPORT_ESTIMATE_PENDING = 896;
    long OBJECT_STATE_DESC_WORKGROUP_INACTIVATED = 897;
    long OBJECT_STATE_DESC_WORKGROUP_ACTIVATED = 898;
    long OBJECT_STATE_DESC_ACCOUNT_USER_ACTIVATED = 899;
    long OBJECT_STATE_DESC_ACCOUNT_USER_INACTIVATED = 900;
    long OBJECT_STATE_DESC_ACCOUNT_USER_INVITED = 901;
    long OBJECT_STATE_DESC_TASK_CREATED = 902;
    long OBJECT_STATE_DESC_TASK_IN_PROGRESS = 903;
    long OBJECT_STATE_DESC_TASK_OBSOLETE = 904;
    long OBJECT_STATE_DESC_TASK_CANCELLED = 905;
    long OBJECT_STATE_DESC_TASK_COMPLETED = 906;
    long OBJECT_STATE_DESC_PROJECT_ACTIVATED = 907;
    long OBJECT_STATE_DESC_PROJECT_DELETED = 908;
    long OBJECT_STATE_DESC_PROJECT_INACTIVATED = 909;
    long OBJECT_STATE_DESC_RFE_DELETED = 910;
    long OBJECT_STATE_DESC_RFE_SENT = 911;
    long OBJECT_STATE_DESC_RFE_CLOSED = 912;
    long OBJECT_STATE_DESC_RFE_DRAFT = 913;
    long OBJECT_STATE_DESC_RFE_CANCELLED = 914;
    long OBJECT_STATE_DESC_ESTIMATE_CREATED = 915;
    long OBJECT_STATE_DESC_ESTIMATE_DELETED = 916;
    long OBJECT_STATE_DESC_ESTIMATE_DRAFT = 917;
    long OBJECT_STATE_DESC_ESTIMATE_SENT = 918;
    long OBJECT_STATE_DESC_ESTIMATE_ACCEPTED = 919;
    long OBJECT_STATE_DESC_ESTIMATE_REJECTED = 920;
    long OBJECT_STATE_DESC_ORDER_ACCEPTED = 921;
    long OBJECT_STATE_DESC_ORDER_CREATED = 922;
    long OBJECT_STATE_DESC_ORDER_REJECTED = 923;
    long OBJECT_STATE_DESC_ORDER_SUPPLIER_TO_ACCEPT = 924;
    long OBJECT_STATE_DESC_ORDER_BUYER_TO_ACCEPT = 925;
    long OBJECT_STATE_DESC_ORDER_CANCELLED = 926;
    long OBJECT_STATE_DESC_ORDER_RETRACTED = 927;
    long UOFM_UNIT = 928;
    long UOFM_100 = 929;
    long UOFM_1000 = 930;
    long TR_TRACKING_TYPE_ORDER_UPDATED = 931;
    long TR_TRACKING_TYPE_QUICK_ORDER_UPDATED_DESC = 932;
    long TR_TRACKING_TYPE_ORDER_REJECTED = 933;
    long TR_TRACKING_TYPE_QUICK_ORDER_REJECTED_DESC = 934;
    long TR_TRACKING_TYPE_ORDER_RETRACTED = 935;
    long TR_TRACKING_TYPE_QUICK_ORDER_RETRACTED_DESC = 936;
    long TR_TRACKING_TYPE_ORDER_ACCEPTED = 937;
    long TR_TRACKING_TYPE_QUICK_ORDER_ACCEPTED_DESC = 938;
    long TR_TRACKING_TYPE_ORDER_CANCELLED = 939;
    long TR_TRACKING_TYPE_QUICK_ORDER_CANCELLED_DESC = 940;
    long RP_DATA_FIELD_GROUP_ORDER_PROJECT = 941;
    long RP_DATA_FIELD_GROUP_ORDER_ORDER = 942;
    long RP_DATA_FIELD_GROUP_ORDER_ORDER_ITEM = 943;
    long RP_DATA_FIELD_GROUP_RFE_RFE = 944;
    long RP_DATA_FIELD_GROUP_RFE_RFE_ITEM = 945;
    long RP_DATA_FIELD_GROUP_RFE_RFE_TEAM = 946;
    long RP_DATA_FIELD_GROUP_ESTIMATE_ESTIMATE = 947;
    long RP_DATA_FIELD_GROUP_ESTIMATE_ESTIMATE_ITEM = 948;
    long RP_DATA_FIELD_GROUP_ESTIMATE_ESTIMATE_TEAM = 949;
    long RP_DATA_FIELD_GROUP_RFE_PROJECT = 950;
    long DM_RFE_DUE_DATE_NM = 951;
    long DM_RFE_LAST_ACTIVITY_DATE_NM = 952;
    long DM_RFE_PROJECT_CLIENT_ACCOUNT_NM = 953;
    long DM_RFE_PROJECT_CREATE_DATE_NM = 954;
    long DM_RFE_PROJECT_ID_NM = 955;
    long DM_RFE_PROJECT_NAME_NM = 956;
    long DM_RFE_PROJECT_NUMBER_NM = 957;
    long DM_RFE_PROJECT_REFERENCE_NM = 958;
    long DM_RFE_PROJECT_STATUS_DESC_STR_ID_NM = 959;
    long DM_RFE_PROJECT_STATUS_ID_NM = 960;
    long DM_RFE_PROPOSED_COMPLETION_DATE_NM = 961;
    long DM_RFE_RFE_DESCRIPTION_NM = 962;
    long DM_RFE_RFE_ID_NM = 963;
    long DM_RFE_RFE_OWNER_REFERENCE_NM = 964;
    long DM_RFE_RFE_OWNER_WORKGROUP_ID_NM = 965;
    long DM_RFE_RFE_REFERENCE_NM = 966;
    long DM_RFE_RFE_STATUS_DESC_STR_ID_NM = 967;
    long DM_RFE_RFE_STATUS_ID_NM = 968;
    long DM_RFE_RFE_SUBMIT_DATE_NM = 969;
    long DM_RFE_RFE_TITLE_NM = 970;
    long DM_RFE_ITEM_DM_CREATE_DATE_NM = 971;
    long DM_RFE_ITEM_QUANTITY_INDEX_NM = 972;
    long DM_RFE_ITEM_RFE_ID_NM = 973;
    long DM_RFE_ITEM_RFE_ITEM_ID_NM = 974;
    long DM_RFE_ITEM_RFE_ITEM_OPTION_ID_NM = 975;
    long DM_RFE_ITEM_RFE_ITEM_OWNER_REFERENCE_NM = 976;
    long DM_RFE_ITEM_RFE_ITEM_REFERENCE_NM = 977;
    long DM_RFE_ITEM_RFE_QUANTITY_NM = 978;
    long DM_RFE_ITEM_SPEC_ID_NM = 979;
    long DM_RFE_ITEM_SPEC_NAME_NM = 980;
    long DM_RFE_TEAM_DM_CREATE_DATE_NM = 981;
    long DM_RFE_TEAM_FIRST_NAME_NM = 982;
    long DM_RFE_TEAM_LAST_NAME_NM = 983;
    long DM_RFE_TEAM_RFE_ID_NM = 984;
    long DM_RFE_TEAM_ROLE_ID_NM = 985;
    long DM_RFE_TEAM_ROLE_NAME_STR_ID_NM = 986;
    long DM_RFE_TEAM_TEAM_MEMBER_WORKGROUP_NAME_NM = 987;
    long DM_ESTIMATE_DM_CREATE_DATE_NM = 988;
    long DM_ESTIMATE_ESTIMATE_DESCRIPTION_NM = 989;
    long DM_ESTIMATE_ESTIMATE_ID_NM = 990;
    long DM_ESTIMATE_ESTIMATE_OWNER_REFERENCE_NM = 991;
    long DM_ESTIMATE_ESTIMATE_OWNER_WORKGROUP_ID_NM = 992;
    long DM_ESTIMATE_ESTIMATE_REFERENCE_NM = 993;
    long DM_ESTIMATE_ESTIMATE_STATUS_DESC_STR_ID_NM = 994;
    long DM_ESTIMATE_ESTIMATE_STATUS_ID_NM = 995;
    long DM_ESTIMATE_ESTIMATE_SUBMIT_DATE_NM = 996;
    long DM_ESTIMATE_ESTIMATE_TITLE_NM = 997;
    long DM_ESTIMATE_EXPIRATION_DATE_NM = 998;
    long DM_ESTIMATE_LAST_ACTIVITY_DATE_NM = 999;
    long DM_ESTIMATE_RFE_ID_NM = 1000;
    long DM_ESTIMATE_ITEM_DM_CREATE_DATE_NM = 1001;
    long DM_ESTIMATE_ITEM_ESTIMATE_ID_NM = 1002;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_DESCRIPTION_NM = 1003;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_ID_NM = 1004;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_OWNER_REFERENCE_NM = 1005;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_REFERENCE_NM = 1006;
    long DM_ESTIMATE_ITEM_ESTIMATE_PRICE_NM = 1007;
    long DM_ESTIMATE_ITEM_RFE_ITEM_ID_NM = 1008;
    long DM_ESTIMATE_ITEM_RFE_ITEM_OPTION_ID_NM = 1009;
    long DM_ESTIMATE_ITEM_WIN_LOSE_NAME_STR_ID_NM = 1010;
    long DM_ESTIMATE_ITEM_WIN_LOSE_TYPE_ID_NM = 1011;
    long DM_RFE_DM_CREATE_DATE_NM = 1012;
    long DM_RFE_DUE_DATE_DC = 1013;
    long DM_RFE_LAST_ACTIVITY_DATE_DC = 1014;
    long DM_RFE_PROJECT_CLIENT_ACCOUNT_DC = 1015;
    long DM_RFE_PROJECT_CREATE_DATE_DC = 1016;
    long DM_RFE_PROJECT_ID_DC = 1017;
    long DM_RFE_PROJECT_NAME_DC = 1018;
    long DM_RFE_PROJECT_NUMBER_DC = 1019;
    long DM_RFE_PROJECT_REFERENCE_DC = 1020;
    long DM_RFE_PROJECT_STATUS_DESC_STR_ID_DC = 1021;
    long DM_RFE_PROJECT_STATUS_ID_DC = 1022;
    long DM_RFE_PROPOSED_COMPLETION_DATE_DC = 1023;
    long DM_RFE_RFE_DESCRIPTION_DC = 1024;
    long DM_RFE_RFE_ID_DC = 1025;
    long DM_RFE_RFE_OWNER_REFERENCE_DC = 1026;
    long DM_RFE_RFE_OWNER_WORKGROUP_ID_DC = 1027;
    long DM_RFE_RFE_REFERENCE_DC = 1028;
    long DM_RFE_RFE_STATUS_DESC_STR_ID_DC = 1029;
    long DM_RFE_RFE_STATUS_ID_DC = 1030;
    long DM_RFE_RFE_SUBMIT_DATE_DC = 1031;
    long DM_RFE_RFE_TITLE_DC = 1032;
    long DM_RFE_ITEM_DM_CREATE_DATE_DC = 1033;
    long DM_RFE_ITEM_QUANTITY_INDEX_DC = 1034;
    long DM_RFE_ITEM_RFE_ID_DC = 1035;
    long DM_RFE_ITEM_RFE_ITEM_ID_DC = 1036;
    long DM_RFE_ITEM_RFE_ITEM_OPTION_ID_DC = 1037;
    long DM_RFE_ITEM_RFE_ITEM_OWNER_REFERENCE_DC = 1038;
    long DM_RFE_ITEM_RFE_ITEM_REFERENCE_DC = 1039;
    long DM_RFE_ITEM_RFE_QUANTITY_DC = 1040;
    long DM_RFE_ITEM_SPEC_ID_DC = 1041;
    long DM_RFE_ITEM_SPEC_NAME_DC = 1042;
    long DM_RFE_TEAM_DM_CREATE_DATE_DC = 1043;
    long DM_RFE_TEAM_FIRST_NAME_DC = 1044;
    long DM_RFE_TEAM_LAST_NAME_DC = 1045;
    long DM_RFE_TEAM_RFE_ID_DC = 1046;
    long DM_RFE_TEAM_ROLE_ID_DC = 1047;
    long DM_RFE_TEAM_ROLE_NAME_STR_ID_DC = 1048;
    long DM_RFE_TEAM_TEAM_MEMBER_WORKGROUP_NAME_DC = 1049;
    long DM_ESTIMATE_DM_CREATE_DATE_DC = 1050;
    long DM_ESTIMATE_ESTIMATE_DESCRIPTION_DC = 1051;
    long DM_ESTIMATE_ESTIMATE_ID_DC = 1052;
    long DM_ESTIMATE_ESTIMATE_OWNER_REFERENCE_DC = 1053;
    long DM_ESTIMATE_ESTIMATE_OWNER_WORKGROUP_ID_DC = 1054;
    long DM_ESTIMATE_ESTIMATE_REFERENCE_DC = 1055;
    long DM_ESTIMATE_ESTIMATE_STATUS_DESC_STR_ID_DC = 1056;
    long DM_ESTIMATE_ESTIMATE_STATUS_ID_DC = 1057;
    long DM_ESTIMATE_ESTIMATE_SUBMIT_DATE_DC = 1058;
    long DM_ESTIMATE_ESTIMATE_TITLE_DC = 1059;
    long DM_ESTIMATE_EXPIRATION_DATE_DC = 1060;
    long DM_ESTIMATE_LAST_ACTIVITY_DATE_DC = 1061;
    long DM_ESTIMATE_RFE_ID_DC = 1062;
    long DM_ESTIMATE_ITEM_DM_CREATE_DATE_DC = 1063;
    long DM_ESTIMATE_ITEM_ESTIMATE_ID_DC = 1064;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_DESCRIPTION_DC = 1065;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_ID_DC = 1066;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_OWNER_REFERENCE_DC = 1067;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_REFERENCE_DC = 1068;
    long DM_ESTIMATE_ITEM_ESTIMATE_PRICE_DC = 1069;
    long DM_ESTIMATE_ITEM_RFE_ITEM_ID_DC = 1070;
    long DM_ESTIMATE_ITEM_RFE_ITEM_OPTION_ID_DC = 1071;
    long DM_ESTIMATE_ITEM_WIN_LOSE_NAME_STR_ID_DC = 1072;
    long DM_ESTIMATE_ITEM_WIN_LOSE_TYPE_ID_DC = 1073;
    long DM_RFE_DM_CREATE_DATE_DC = 1074;
    long DM_RFE_DUE_DATE_HD = 1075;
    long DM_RFE_LAST_ACTIVITY_DATE_HD = 1076;
    long DM_RFE_PROJECT_CLIENT_ACCOUNT_HD = 1077;
    long DM_RFE_PROJECT_CREATE_DATE_HD = 1078;
    long DM_RFE_PROJECT_ID_HD = 1079;
    long DM_RFE_PROJECT_NAME_HD = 1080;
    long DM_RFE_PROJECT_NUMBER_HD = 1081;
    long DM_RFE_PROJECT_REFERENCE_HD = 1082;
    long DM_RFE_PROJECT_STATUS_DESC_STR_ID_HD = 1083;
    long DM_RFE_PROJECT_STATUS_ID_HD = 1084;
    long DM_RFE_PROPOSED_COMPLETION_DATE_HD = 1085;
    long DM_RFE_RFE_DESCRIPTION_HD = 1086;
    long DM_RFE_RFE_ID_HD = 1087;
    long DM_RFE_RFE_OWNER_REFERENCE_HD = 1088;
    long DM_RFE_RFE_OWNER_WORKGROUP_ID_HD = 1089;
    long DM_RFE_RFE_REFERENCE_HD = 1090;
    long DM_RFE_RFE_STATUS_DESC_STR_ID_HD = 1091;
    long DM_RFE_RFE_STATUS_ID_HD = 1092;
    long DM_RFE_RFE_SUBMIT_DATE_HD = 1093;
    long DM_RFE_RFE_TITLE_HD = 1094;
    long DM_RFE_ITEM_DM_CREATE_DATE_HD = 1095;
    long DM_RFE_ITEM_QUANTITY_INDEX_HD = 1096;
    long DM_RFE_ITEM_RFE_ID_HD = 1097;
    long DM_RFE_ITEM_RFE_ITEM_ID_HD = 1098;
    long DM_RFE_ITEM_RFE_ITEM_OPTION_ID_HD = 1099;
    long DM_RFE_ITEM_RFE_ITEM_OWNER_REFERENCE_HD = 1100;
    long DM_RFE_ITEM_RFE_ITEM_REFERENCE_HD = 1101;
    long DM_RFE_ITEM_RFE_QUANTITY_HD = 1102;
    long DM_RFE_ITEM_SPEC_ID_HD = 1103;
    long DM_RFE_ITEM_SPEC_NAME_HD = 1104;
    long DM_RFE_TEAM_DM_CREATE_DATE_HD = 1105;
    long DM_RFE_TEAM_FIRST_NAME_HD = 1106;
    long DM_RFE_TEAM_LAST_NAME_HD = 1107;
    long DM_RFE_TEAM_RFE_ID_HD = 1108;
    long DM_RFE_TEAM_ROLE_ID_HD = 1109;
    long DM_RFE_TEAM_ROLE_NAME_STR_ID_HD = 1110;
    long DM_RFE_TEAM_TEAM_MEMBER_WORKGROUP_NAME_HD = 1111;
    long DM_ESTIMATE_DM_CREATE_DATE_HD = 1112;
    long DM_ESTIMATE_ESTIMATE_DESCRIPTION_HD = 1113;
    long DM_ESTIMATE_ESTIMATE_ID_HD = 1114;
    long DM_ESTIMATE_ESTIMATE_OWNER_REFERENCE_HD = 1115;
    long DM_ESTIMATE_ESTIMATE_OWNER_WORKGROUP_ID_HD = 1116;
    long DM_ESTIMATE_ESTIMATE_REFERENCE_HD = 1117;
    long DM_ESTIMATE_ESTIMATE_STATUS_DESC_STR_ID_HD = 1118;
    long DM_ESTIMATE_ESTIMATE_STATUS_ID_HD = 1119;
    long DM_ESTIMATE_ESTIMATE_SUBMIT_DATE_HD = 1120;
    long DM_ESTIMATE_ESTIMATE_TITLE_HD = 1121;
    long DM_ESTIMATE_EXPIRATION_DATE_HD = 1122;
    long DM_ESTIMATE_LAST_ACTIVITY_DATE_HD = 1123;
    long DM_ESTIMATE_RFE_ID_HD = 1124;
    long DM_ESTIMATE_ITEM_DM_CREATE_DATE_HD = 1125;
    long DM_ESTIMATE_ITEM_ESTIMATE_ID_HD = 1126;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_DESCRIPTION_HD = 1127;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_ID_HD = 1128;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_OWNER_REFERENCE_HD = 1129;
    long DM_ESTIMATE_ITEM_ESTIMATE_ITEM_REFERENCE_HD = 1130;
    long DM_ESTIMATE_ITEM_ESTIMATE_PRICE_HD = 1131;
    long DM_ESTIMATE_ITEM_RFE_ITEM_ID_HD = 1132;
    long DM_ESTIMATE_ITEM_RFE_ITEM_OPTION_ID_HD = 1133;
    long DM_ESTIMATE_ITEM_WIN_LOSE_NAME_STR_ID_HD = 1134;
    long DM_ESTIMATE_ITEM_WIN_LOSE_TYPE_ID_HD = 1135;
    long DM_RFE_DM_CREATE_DATE_HD = 1136;
    long DM_ORDER_BUYER_WORKGROUP_ID_NM = 1137;
    long DM_ORDER_BUYER_WORKGROUP_NAME_NM = 1138;
    long DM_ORDER_DM_CREATE_DATE_NM = 1139;
    long DM_ORDER_IS_QUICK_ORDER_NM = 1140;
    long DM_ORDER_LAST_ACTIVITY_DATE_NM = 1141;
    long DM_ORDER_ORDER_COMPLETION_DATE_NM = 1142;
    long DM_ORDER_ORDER_ID_NM = 1143;
    long DM_ORDER_ORDER_REFERENCE_NM = 1144;
    long DM_ORDER_ORDER_STATUS_DESC_STR_ID_NM = 1145;
    long DM_ORDER_ORDER_STATUS_ID_NM = 1146;
    long DM_ORDER_ORDER_TYPE_NAME_STR_ID_NM = 1147;
    long DM_ORDER_PAYMENT_METHOD_DESC_STR_ID_NM = 1148;
    long DM_ORDER_PAYMENT_METHOD_ID_NM = 1149;
    long DM_ORDER_PO_NUMBER_NM = 1150;
    long DM_ORDER_PROJECT_CLIENT_ACCOUNT_NM = 1151;
    long DM_ORDER_PROJECT_CREATE_DATE_NM = 1152;
    long DM_ORDER_PROJECT_DESC_STR_ID_NM = 1153;
    long DM_ORDER_PROJECT_ID_NM = 1154;
    long DM_ORDER_PROJECT_NAME_NM = 1155;
    long DM_ORDER_PROJECT_NUMBER_NM = 1156;
    long DM_ORDER_PROJECT_REFERENCE_NM = 1157;
    long DM_ORDER_PROJECT_STATUS_ID_NM = 1158;
    long DM_ORDER_SERVICE_ID_NM = 1159;
    long DM_ORDER_SHIPPING_NM = 1160;
    long DM_ORDER_SHIPPING_CURRENCY_NM = 1161;
    long DM_ORDER_SUPPLIER_TRACKING_NUMBER_NM = 1162;
    long DM_ORDER_SUPPLIER_WORKGROUP_ID_NM = 1163;
    long DM_ORDER_SUPPLIER_WORKGROUP_NAME_NM = 1164;
    long DM_ORDER_TAX_NM = 1165;
    long DM_ORDER_TAX_CURRENCY_NM = 1166;
    long DM_ORDER_ITEM_AVERAGE_ESTIMATE_PRICE_NM = 1167;
    long DM_ORDER_ITEM_DELIVERABLE_ID_NM = 1168;
    long DM_ORDER_ITEM_DM_CREATE_DATE_NM = 1169;
    long DM_ORDER_ITEM_ESTIMATE_COUNT_NM = 1170;
    long DM_ORDER_ITEM_ESTIMATE_ITEM_OPTION_ID_NM = 1171;
    long DM_ORDER_ITEM_ESTIMATE_ITEM_PRICE_ID_NM = 1172;
    long DM_ORDER_ITEM_ESTIMATE_PRICE_NM = 1173;
    long DM_ORDER_ITEM_ESTIMATE_QUANTITY_NM = 1174;
    long DM_ORDER_ITEM_MAXIMUM_ESTIMATE_PRICE_NM = 1175;
    long DM_ORDER_ITEM_MINIMUM_ESTIMATE_PRICE_NM = 1176;
    long DM_ORDER_ITEM_ORDER_ID_NM = 1177;
    long DM_ORDER_ITEM_ORDER_ITEM_COMPLETION_DATE_NM = 1178;
    long DM_ORDER_ITEM_ORDER_ITEM_ID_NM = 1179;
    long DM_ORDER_ITEM_ORDER_VALUE_NM = 1180;
    long DM_ORDER_ITEM_ORDER_VALUE_CURRENCY_NM = 1181;
    long DM_ORDER_ITEM_QUANTITY_NM = 1182;
    long DM_ORDER_ITEM_SPEC_ID_NM = 1183;
    long DM_ORDER_ITEM_SPEC_NAME_NM = 1184;
    long DM_ORDER_ITEM_UNIT_PRICE_NM = 1185;
    long DM_ORDER_ITEM_UNIT_PRICE_CURRENCY_NM = 1186;
    long DM_ORDER_ITEM_UOFM_DESC_STR_ID_NM = 1187;
    long DM_ORDER_ITEM_UOFM_ID_NM = 1188;
    long DM_ORDER_BUYER_WORKGROUP_ID_DC = 1189;
    long DM_ORDER_BUYER_WORKGROUP_NAME_DC = 1190;
    long DM_ORDER_DM_CREATE_DATE_DC = 1191;
    long DM_ORDER_IS_QUICK_ORDER_DC = 1192;
    long DM_ORDER_LAST_ACTIVITY_DATE_DC = 1193;
    long DM_ORDER_ORDER_COMPLETION_DATE_DC = 1194;
    long DM_ORDER_ORDER_ID_DC = 1195;
    long DM_ORDER_ORDER_REFERENCE_DC = 1196;
    long DM_ORDER_ORDER_STATUS_DESC_STR_ID_DC = 1197;
    long DM_ORDER_ORDER_STATUS_ID_DC = 1198;
    long DM_ORDER_ORDER_TYPE_NAME_STR_ID_DC = 1199;
    long DM_ORDER_PAYMENT_METHOD_DESC_STR_ID_DC = 1200;
    long DM_ORDER_PAYMENT_METHOD_ID_DC = 1201;
    long DM_ORDER_PO_NUMBER_DC = 1202;
    long DM_ORDER_PROJECT_CLIENT_ACCOUNT_DC = 1203;
    long DM_ORDER_PROJECT_CREATE_DATE_DC = 1204;
    long DM_ORDER_PROJECT_DESC_STR_ID_DC = 1205;
    long DM_ORDER_PROJECT_ID_DC = 1206;
    long DM_ORDER_PROJECT_NAME_DC = 1207;
    long DM_ORDER_PROJECT_NUMBER_DC = 1208;
    long DM_ORDER_PROJECT_REFERENCE_DC = 1209;
    long DM_ORDER_PROJECT_STATUS_ID_DC = 1210;
    long DM_ORDER_SERVICE_ID_DC = 1211;
    long DM_ORDER_SHIPPING_DC = 1212;
    long DM_ORDER_SHIPPING_CURRENCY_DC = 1213;
    long DM_ORDER_SUPPLIER_TRACKING_NUMBER_DC = 1214;
    long DM_ORDER_SUPPLIER_WORKGROUP_ID_DC = 1215;
    long DM_ORDER_SUPPLIER_WORKGROUP_NAME_DC = 1216;
    long DM_ORDER_TAX_DC = 1217;
    long DM_ORDER_TAX_CURRENCY_DC = 1218;
    long DM_ORDER_ITEM_AVERAGE_ESTIMATE_PRICE_DC = 1219;
    long DM_ORDER_ITEM_DELIVERABLE_ID_DC = 1220;
    long DM_ORDER_ITEM_DM_CREATE_DATE_DC = 1221;
    long DM_ORDER_ITEM_ESTIMATE_COUNT_DC = 1222;
    long DM_ORDER_ITEM_ESTIMATE_ITEM_OPTION_ID_DC = 1223;
    long DM_ORDER_ITEM_ESTIMATE_ITEM_PRICE_ID_DC = 1224;
    long DM_ORDER_ITEM_ESTIMATE_PRICE_DC = 1225;
    long DM_ORDER_ITEM_ESTIMATE_QUANTITY_DC = 1226;
    long DM_ORDER_ITEM_MAXIMUM_ESTIMATE_PRICE_DC = 1227;
    long DM_ORDER_ITEM_MINIMUM_ESTIMATE_PRICE_DC = 1228;
    long DM_ORDER_ITEM_ORDER_ID_DC = 1229;
    long DM_ORDER_ITEM_ORDER_ITEM_COMPLETION_DATE_DC = 1230;
    long DM_ORDER_ITEM_ORDER_ITEM_ID_DC = 1231;
    long DM_ORDER_ITEM_ORDER_VALUE_DC = 1232;
    long DM_ORDER_ITEM_ORDER_VALUE_CURRENCY_DC = 1233;
    long DM_ORDER_ITEM_QUANTITY_DC = 1234;
    long DM_ORDER_ITEM_SPEC_ID_DC = 1235;
    long DM_ORDER_ITEM_SPEC_NAME_DC = 1236;
    long DM_ORDER_ITEM_UNIT_PRICE_DC = 1237;
    long DM_ORDER_ITEM_UNIT_PRICE_CURRENCY_DC = 1238;
    long DM_ORDER_ITEM_UOFM_DESC_STR_ID_DC = 1239;
    long DM_ORDER_ITEM_UOFM_ID_DC = 1240;
    long DM_ORDER_BUYER_WORKGROUP_ID_HD = 1241;
    long DM_ORDER_BUYER_WORKGROUP_NAME_HD = 1242;
    long DM_ORDER_DM_CREATE_DATE_HD = 1243;
    long DM_ORDER_IS_QUICK_ORDER_HD = 1244;
    long DM_ORDER_LAST_ACTIVITY_DATE_HD = 1245;
    long DM_ORDER_ORDER_COMPLETION_DATE_HD = 1246;
    long DM_ORDER_ORDER_ID_HD = 1247;
    long DM_ORDER_ORDER_REFERENCE_HD = 1248;
    long DM_ORDER_ORDER_STATUS_DESC_STR_ID_HD = 1249;
    long DM_ORDER_ORDER_STATUS_ID_HD = 1250;
    long DM_ORDER_ORDER_TYPE_NAME_STR_ID_HD = 1251;
    long DM_ORDER_PAYMENT_METHOD_DESC_STR_ID_HD = 1252;
    long DM_ORDER_PAYMENT_METHOD_ID_HD = 1253;
    long DM_ORDER_PO_NUMBER_HD = 1254;
    long DM_ORDER_PROJECT_CLIENT_ACCOUNT_HD = 1255;
    long DM_ORDER_PROJECT_CREATE_DATE_HD = 1256;
    long DM_ORDER_PROJECT_DESC_STR_ID_HD = 1257;
    long DM_ORDER_PROJECT_ID_HD = 1258;
    long DM_ORDER_PROJECT_NAME_HD = 1259;
    long DM_ORDER_PROJECT_NUMBER_HD = 1260;
    long DM_ORDER_PROJECT_REFERENCE_HD = 1261;
    long DM_ORDER_PROJECT_STATUS_ID_HD = 1262;
    long DM_ORDER_SERVICE_ID_HD = 1263;
    long DM_ORDER_SHIPPING_HD = 1264;
    long DM_ORDER_SHIPPING_CURRENCY_HD = 1265;
    long DM_ORDER_SUPPLIER_TRACKING_NUMBER_HD = 1266;
    long DM_ORDER_SUPPLIER_WORKGROUP_ID_HD = 1267;
    long DM_ORDER_SUPPLIER_WORKGROUP_NAME_HD = 1268;
    long DM_ORDER_TAX_HD = 1269;
    long DM_ORDER_TAX_CURRENCY_HD = 1270;
    long DM_ORDER_ITEM_AVERAGE_ESTIMATE_PRICE_HD = 1271;
    long DM_ORDER_ITEM_DELIVERABLE_ID_HD = 1272;
    long DM_ORDER_ITEM_DM_CREATE_DATE_HD = 1273;
    long DM_ORDER_ITEM_ESTIMATE_COUNT_HD = 1274;
    long DM_ORDER_ITEM_ESTIMATE_ITEM_OPTION_ID_HD = 1275;
    long DM_ORDER_ITEM_ESTIMATE_ITEM_PRICE_ID_HD = 1276;
    long DM_ORDER_ITEM_ESTIMATE_PRICE_HD = 1277;
    long DM_ORDER_ITEM_ESTIMATE_QUANTITY_HD = 1278;
    long DM_ORDER_ITEM_MAXIMUM_ESTIMATE_PRICE_HD = 1279;
    long DM_ORDER_ITEM_MINIMUM_ESTIMATE_PRICE_HD = 1280;
    long DM_ORDER_ITEM_ORDER_ID_HD = 1281;
    long DM_ORDER_ITEM_ORDER_ITEM_COMPLETION_DATE_HD = 1282;
    long DM_ORDER_ITEM_ORDER_ITEM_ID_HD = 1283;
    long DM_ORDER_ITEM_ORDER_VALUE_HD = 1284;
    long DM_ORDER_ITEM_ORDER_VALUE_CURRENCY_HD = 1285;
    long DM_ORDER_ITEM_QUANTITY_HD = 1286;
    long DM_ORDER_ITEM_SPEC_ID_HD = 1287;
    long DM_ORDER_ITEM_SPEC_NAME_HD = 1288;
    long DM_ORDER_ITEM_UNIT_PRICE_HD = 1289;
    long DM_ORDER_ITEM_UNIT_PRICE_CURRENCY_HD = 1290;
    long DM_ORDER_ITEM_UOFM_DESC_STR_ID_HD = 1291;
    long DM_ORDER_ITEM_UOFM_ID_HD = 1292;
    long PREFERRED_ROLE_TOKEN_ESTIMATE_BUYER = 1293;
    long NOTIFICATION_TYPE_USER_CREATED = 1294;
    long NOTIFICATION_TYPE_USER_ACTIVATED = 1295;
    long NOTIFICATION_TYPE_USER_DEACTIVATED = 1296;
    long NOTIFICATION_TYPE_RFE_SENT = 1297;
    long NOTIFICATION_TYPE_RFE_DECLINED = 1298;
    long NOTIFICATION_TYPE_ESTIMATE_SENT = 1299;
    long NOTIFICATION_TYPE_PROJECT_MEMBER_INVITED = 1300;
    long NOTIFICATION_TYPE_PROJECT_MEMBER_REVOKED = 1301;
    long NOTIFICATION_TYPE_PROJECT_MEMBER_ROLE_UPDATED = 1302;
    long DM_RFE_ITEM_QUANTITY_1_NM = 1400;
    long DM_RFE_ITEM_QUANTITY_1_DC = 1401;
    long DM_RFE_ITEM_QUANTITY_1_HD = 1402;
    long DM_RFE_ITEM_QUANTITY_2_NM = 1403;
    long DM_RFE_ITEM_QUANTITY_2_DC = 1404;
    long DM_RFE_ITEM_QUANTITY_2_HD = 1405;
    long DM_RFE_ITEM_QUANTITY_3_NM = 1406;
    long DM_RFE_ITEM_QUANTITY_3_DC = 1407;
    long DM_RFE_ITEM_QUANTITY_3_HD = 1408;
    long DM_RFE_ITEM_QUANTITY_4_NM = 1409;
    long DM_RFE_ITEM_QUANTITY_4_DC = 1410;
    long DM_RFE_ITEM_QUANTITY_4_HD = 1411;
    long DM_RFE_ITEM_QUANTITY_5_NM = 1412;
    long DM_RFE_ITEM_QUANTITY_5_DC = 1413;
    long DM_RFE_ITEM_QUANTITY_5_HD = 1414;
    long DM_ESTIMATE_ITEM_PRICE_1_NM = 1415;
    long DM_ESTIMATE_ITEM_PRICE_1_DC = 1416;
    long DM_ESTIMATE_ITEM_PRICE_1_HD = 1417;
    long DM_ESTIMATE_ITEM_PRICE_2_NM = 1418;
    long DM_ESTIMATE_ITEM_PRICE_2_DC = 1419;
    long DM_ESTIMATE_ITEM_PRICE_2_HD = 1420;
    long DM_ESTIMATE_ITEM_PRICE_3_NM = 1421;
    long DM_ESTIMATE_ITEM_PRICE_3_DC = 1422;
    long DM_ESTIMATE_ITEM_PRICE_3_HD = 1423;
    long DM_ESTIMATE_ITEM_PRICE_4_NM = 1424;
    long DM_ESTIMATE_ITEM_PRICE_4_DC = 1425;
    long DM_ESTIMATE_ITEM_PRICE_4_HD = 1426;
    long DM_ESTIMATE_ITEM_PRICE_5_NM = 1427;
    long DM_ESTIMATE_ITEM_PRICE_5_DC = 1428;
    long DM_ESTIMATE_ITEM_PRICE_5_HD = 1429;
    long DM_RFE_RFE_OWNER_WORKGROUP_NAME_NM = 1430;
    long DM_RFE_RFE_OWNER_WORKGROUP_NAME_DC = 1431;
    long DM_RFE_RFE_OWNER_WORKGROUP_NAME_HD = 1432;
    long CONSTRAINT_SELECT_SPEC_SERVICE_TYPE = 1433;
    long NOTIFICATION_TYPE_ORDER_CREATED = 1434;
    long NOTIFICATION_TYPE_ORDER_REJECTED = 1435;
    long NOTIFICATION_TYPE_ORDER_RETRACTED = 1436;
    long NOTIFICATION_TYPE_ORDER_UPDATED = 1437;
    long NOTIFICATION_TYPE_ORDER_ACCEPTED = 1438;
    long NOTIFICATION_TYPE_ORDER_CANCELLED = 1439;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_ESTIMATE_SELL = 1440;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_ORDER_SELL = 1441;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_WRKGRP_ACTVTY_SELL = 1442;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_ESTIMATE_DESCN_SELL = 1443;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_ORDER_DESCN_SELL = 1444;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_WRKGRP_ACTVTY_DESCN_SELL = 1445;
    long ME_LOW_PRIORITY = 1446;
    long ME_MEDIUM_PRIORITY = 1447;
    long ME_HIGH_PRIORITY = 1448;
    long TA_TASK_TYPE_SEND_RFE_NAME = 1449;
    long TA_TASK_TYPE_CREATE_ESTIMATE_NAME = 1450;
    long TA_TASK_TYPE_SEND_ESTIMATE_NAME = 1451;
    long TA_TASK_TYPE_UNSPECIFIED_NAME = 1452;
    long TA_TASK_TYPE_REVIEW_ESTIMATE_NAME = 1453;
    long TA_TASK_TYPE_ORDER_REVIEW_NAME = 1454;
    long CONSTRAINT_SPEC_SERVICE_TYPE = 1455;
    long ME_NO_RECIPIENT = 1456;
    long DM_ESTIMATE_RFE_OWNER_WORKGROUP_NAME_NM = 1457;
    long DM_ESTIMATE_RFE_OWNER_WORKGROUP_NAME_DC = 1458;
    long DM_ESTIMATE_RFE_OWNER_WORKGROUP_NAME_HD = 1459;
    long VIEW = 1460;
    long EDIT = 1461;
    long CREATE = 1462;
    long REJECT = 1463;
    long RETRACT = 1464;
    long CANCEL = 1465;
    long ACCEPT = 1466;
    long FULL = 1467;
    long TA_TASK_TYPE_REVIEW_JOB_REQUEST_NAME = 1468;
    long TA_TASK_TYPE_REVIEW_JOB_REQUEST_DESC = 1469;
    long DM_WORKGROUP_ACTIVITY_BUYER_WORKGROUP_ID_NM = 1472;
    long DM_WORKGROUP_ACTIVITY_BUYER_WORKGROUP_NAME_NM = 1473;
    long DM_WORKGROUP_ACTIVITY_DM_CREATE_DATE_NM = 1474;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_DELETED_NM = 1475;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_DRAFT_NM = 1476;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_RECEIVED_NM = 1477;
    long DM_WORKGROUP_ACTIVITY_LAST_ACTIVITY_DATE_NM = 1478;
    long DM_WORKGROUP_ACTIVITY_ORDER_ACCEPTED_NM = 1479;
    long DM_WORKGROUP_ACTIVITY_ORDER_BUYER_TO_ACCEPT_NM = 1480;
    long DM_WORKGROUP_ACTIVITY_ORDER_CANCELLED_NM = 1481;
    long DM_WORKGROUP_ACTIVITY_ORDER_REJECTED_NM = 1482;
    long DM_WORKGROUP_ACTIVITY_ORDER_RETRACTED_NM = 1483;
    long DM_WORKGROUP_ACTIVITY_ORDER_SUPPLIER_TO_ACCEPT_NM = 1484;
    long DM_WORKGROUP_ACTIVITY_RFE_DELETED_NM = 1485;
    long DM_WORKGROUP_ACTIVITY_RFE_DRAFT_NM = 1486;
    long DM_WORKGROUP_ACTIVITY_RFE_SENT_NM = 1487;
    long DM_WORKGROUP_ACTIVITY_SUPPLIER_WORKGROUP_ID_NM = 1488;
    long DM_WORKGROUP_ACTIVITY_SUPPLIER_WORKGROUP_NAME_NM = 1489;
    long DM_WORKGROUP_ACTIVITY_VALUE_ACCEPTED_NM = 1490;
    long DM_WORKGROUP_ACTIVITY_VALUE_BUYER_TO_ACCEPT_NM = 1491;
    long DM_WORKGROUP_ACTIVITY_VALUE_CANCELLED_NM = 1492;
    long DM_WORKGROUP_ACTIVITY_VALUE_REJECTED_NM = 1493;
    long DM_WORKGROUP_ACTIVITY_VALUE_RETRACTED_NM = 1494;
    long DM_WORKGROUP_ACTIVITY_VALUE_SUPPLIER_TO_ACCEPT_NM = 1495;
    long DM_WORKGROUP_ACTIVITY_BUYER_WORKGROUP_ID_DC = 1496;
    long DM_WORKGROUP_ACTIVITY_BUYER_WORKGROUP_NAME_DC = 1497;
    long DM_WORKGROUP_ACTIVITY_DM_CREATE_DATE_DC = 1498;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_DELETED_DC = 1499;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_DRAFT_DC = 1500;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_RECEIVED_DC = 1501;
    long DM_WORKGROUP_ACTIVITY_LAST_ACTIVITY_DATE_DC = 1502;
    long DM_WORKGROUP_ACTIVITY_ORDER_ACCEPTED_DC = 1503;
    long DM_WORKGROUP_ACTIVITY_ORDER_BUYER_TO_ACCEPT_DC = 1504;
    long DM_WORKGROUP_ACTIVITY_ORDER_CANCELLED_DC = 1505;
    long DM_WORKGROUP_ACTIVITY_ORDER_REJECTED_DC = 1506;
    long DM_WORKGROUP_ACTIVITY_ORDER_RETRACTED_DC = 1507;
    long DM_WORKGROUP_ACTIVITY_ORDER_SUPPLIER_TO_ACCEPT_DC = 1508;
    long DM_WORKGROUP_ACTIVITY_RFE_DELETED_DC = 1509;
    long DM_WORKGROUP_ACTIVITY_RFE_DRAFT_DC = 1510;
    long DM_WORKGROUP_ACTIVITY_RFE_SENT_DC = 1511;
    long DM_WORKGROUP_ACTIVITY_SUPPLIER_WORKGROUP_ID_DC = 1512;
    long DM_WORKGROUP_ACTIVITY_SUPPLIER_WORKGROUP_NAME_DC = 1513;
    long DM_WORKGROUP_ACTIVITY_VALUE_ACCEPTED_DC = 1514;
    long DM_WORKGROUP_ACTIVITY_VALUE_BUYER_TO_ACCEPT_DC = 1515;
    long DM_WORKGROUP_ACTIVITY_VALUE_CANCELLED_DC = 1516;
    long DM_WORKGROUP_ACTIVITY_VALUE_REJECTED_DC = 1517;
    long DM_WORKGROUP_ACTIVITY_VALUE_RETRACTED_DC = 1518;
    long DM_WORKGROUP_ACTIVITY_VALUE_SUPPLIER_TO_ACCEPT_DC = 1519;
    long DM_WORKGROUP_ACTIVITY_BUYER_WORKGROUP_ID_HD = 1520;
    long DM_WORKGROUP_ACTIVITY_BUYER_WORKGROUP_NAME_HD = 1521;
    long DM_WORKGROUP_ACTIVITY_DM_CREATE_DATE_HD = 1522;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_DELETED_HD = 1523;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_DRAFT_HD = 1524;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_RECEIVED_HD = 1525;
    long DM_WORKGROUP_ACTIVITY_LAST_ACTIVITY_DATE_HD = 1526;
    long DM_WORKGROUP_ACTIVITY_ORDER_ACCEPTED_HD = 1527;
    long DM_WORKGROUP_ACTIVITY_ORDER_BUYER_TO_ACCEPT_HD = 1528;
    long DM_WORKGROUP_ACTIVITY_ORDER_CANCELLED_HD = 1529;
    long DM_WORKGROUP_ACTIVITY_ORDER_REJECTED_HD = 1530;
    long DM_WORKGROUP_ACTIVITY_ORDER_RETRACTED_HD = 1531;
    long DM_WORKGROUP_ACTIVITY_ORDER_SUPPLIER_TO_ACCEPT_HD = 1532;
    long DM_WORKGROUP_ACTIVITY_RFE_DELETED_HD = 1533;
    long DM_WORKGROUP_ACTIVITY_RFE_DRAFT_HD = 1534;
    long DM_WORKGROUP_ACTIVITY_RFE_SENT_HD = 1535;
    long DM_WORKGROUP_ACTIVITY_SUPPLIER_WORKGROUP_ID_HD = 1536;
    long DM_WORKGROUP_ACTIVITY_SUPPLIER_WORKGROUP_NAME_HD = 1537;
    long DM_WORKGROUP_ACTIVITY_VALUE_ACCEPTED_HD = 1538;
    long DM_WORKGROUP_ACTIVITY_VALUE_BUYER_TO_ACCEPT_HD = 1539;
    long DM_WORKGROUP_ACTIVITY_VALUE_CANCELLED_HD = 1540;
    long DM_WORKGROUP_ACTIVITY_VALUE_REJECTED_HD = 1541;
    long DM_WORKGROUP_ACTIVITY_VALUE_RETRACTED_HD = 1542;
    long DM_WORKGROUP_ACTIVITY_VALUE_SUPPLIER_TO_ACCEPT_HD = 1543;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_SENT_NM = 1544;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_SENT_DC = 1545;
    long DM_WORKGROUP_ACTIVITY_ESTIMATE_SENT_HD = 1546;
    long DM_WORKGROUP_ACTIVITY_RFE_RECEIVED_NM = 1547;
    long DM_WORKGROUP_ACTIVITY_RFE_RECEIVED_DC = 1548;
    long DM_WORKGROUP_ACTIVITY_RFE_RECEIVED_HD = 1549;
    long RP_DATA_FIELD_GROUP_WORKGROUP_ACTIVITY_MEASUREMENT = 1550;
    long DM_ORDER_ITEM_SPEC_TYPE_ID_NM = 1551;
    long DM_ORDER_ITEM_SPEC_TYPE_ID_DC = 1552;
    long DM_ORDER_ITEM_SPEC_TYPE_ID_HD = 1553;
    long DM_ORDER_ITEM_SPEC_TYPE_NM = 1554;
    long DM_ORDER_ITEM_SPEC_TYPE_DC = 1555;
    long DM_ORDER_ITEM_SPEC_TYPE_HD = 1556;
    long CONSTRAINT_SELECTING_ONESELF = 1557;
    long FILENAME_INVALID_CONSTRAINT = 1558;
    long OBJECT_STATE_DESC_MESSAGE_UNREAD = 1559;
    long OBJECT_STATE_DESC_MESSAGE_READ = 1560;
    long OBJECT_STATE_DESC_MESSAGE_FOLLOWUP = 1561;
    long OBJECT_STATE_DESC_MESSAGE_DELETED = 1562;
    long RP_DATA_FIELD_GROUP_SPEC = 1563;
    long CONSTRAINT_INVITE_SUPPLIER_ONLY = 1564;
    long AC_TERMS_TYPE_DESCRIPTION_RFE = 1565;
    long AC_TERMS_TYPE_DESCRIPTION_PURCHASE = 1566;
    long AC_TERMS_TYPE_DESCRIPTION_ESTIMATE = 1567;
    long AC_TERMS_TYPE_DESCRIPTION_SALES = 1568;
    long WORKGROUP_TAB_INFO = 1569;
    long WORKGROUP_TAB_MEMBERS = 1570;
    long WORKGROUP_TAB_PREF = 1571;
    long NOTIFICATION_TYPE_RFE_MEMBER_INVITED = 1572;
    long NOTIFICATION_TYPE_RFE_MEMBER_REVOKED = 1573;
    long CONSTRAINT_ILLEGAL_CHARACTER = 1574;
    long OBJECT_STATE_DESC_ACCOUNT_USER_UNINVITED = 1575;
    long TR_TRACKING_TYPE_PROJECT_EVENT = 1600;
    long TR_TRACKING_TYPE_FILES_RECEIVED = 1601;
    long TR_TRACKING_TYPE_FILES_SENT = 1602;
    long REPORT_HIDDEN_FIELD_MUST_NOT_HAVE_SORTED = 1603;
    long REPORT_FIELD_MUST_NOT_ALL_HIDDEN = 1604;
    long REPORT_SAVED_TITLE_EXISTS = 1605;
    long REPORT_ESTIMATE_ANALYSIS_VIEW_REPORT_BY_MONTH = 1606;
    long REPORT_ESTIMATE_ANALYSIS_VIEW_REPORT_BY_BUYER = 1607;
    long REPORT_ESTIMATE_ANALYSIS_COMPARE_PREMIUM = 1608;
    long REPORT_ESTIMATE_ANALYSIS_COMPARE_COST = 1609;
    long REPORT_ESTIMATE_ANALYSIS_INCLUDE_ALL_JOBS_WITH_LT = 1610;
    long REPORT_ESTIMATE_ANALYSIS_INCLUDE_ALL_JOBS_WITH_LE = 1611;
    long REPORT_ESTIMATE_ANALYSIS_INCLUDE_ALL_JOBS_WITH_EQ = 1612;
    long REPORT_ESTIMATE_ANALYSIS_INCLUDE_ALL_JOBS_WITH_GT = 1613;
    long REPORT_ESTIMATE_ANALYSIS_INCLUDE_ALL_JOBS_WITH_GE = 1614;
    long REPORT_ESTIMATE_ANALYSIS_ESTIMATES = 1615;
    long REPORT_ESTIMATE_ANALYSIS_NOTES = 1616;
    long NOTIFICATION_TYPE_ESTIMATE_REJECTED = 1617;
    long RE_ROLE_WORKGROUP_ADMIN = 1624;
    long RE_ROLE_WORKGROUP_MANAGER = 1625;
    long RE_ROLE_WORKGROUP_MEMBER = 1626;
    long RE_ROLE_PROJECT_OWNER = 1627;
    long RE_ROLE_PROJECT_BUYER = 1628;
    long RE_ROLE_PROJECT_SUPPLIER = 1629;
    long RE_ROLE_RFE_OWNER = 1630;
    long RE_ROLE_ESTIMATE_OWNER = 1631;
    long RE_ROLE_ORDER_OWNER = 1632;
    long RE_ROLE_PROJECT_MEMBER = 1633;
    long RE_ROLE_RFE_EXSUPPLIER = 1634;
    long RE_ROLE_RFE_SUPPLIER = 1635;
    long RE_ROLE_PROJECT_CLIENT = 1636;
    long RE_ROLE_PROJECT_GUEST = 1637;
    long RE_ROLE_ESTIMATE_SUPPLIER = 1638;
    long RE_ROLE_ORDER_SUPPLIER = 1639;
    long RE_ROLE_RFE_BUYER = 1640;
    long RE_ROLE_ESTIMATE_BUYER = 1641;
    long RE_ROLE_ORDER_BUYER = 1642;
    long RE_ROLE_ADMIN_MANAGER = 1643;
    long RE_ROLE_ADMIN_CUSTOMER_MANAGER = 1644;
    long RE_ROLE_ADMIN_SUPPORT_MANAGER = 1645;
    long RE_ROLE_ADMIN_SUPPORT_MEMBER = 1646;
    long RE_ROLE_ADMIN_PROF_SVCS_MANAGER = 1647;
    long RE_ROLE_ADMIN_PROF_SVCS_MEMBER = 1648;
    long YES = 1649;
    long NO = 1650;
    long SP_NO_ITEMS = 1651;
    long REPORT_ESTIMATE_WIN_LOSS_VIEW_CUSTOMER = 1652;
    long REPORT_ESTIMATE_WIN_LOSS_VIEW_SALES_REP = 1653;
    long WORKGROUP_TAB_BREAKOUTS = 1654;
    long AC_LOCALE_DESCRIPTION_DE_DE = 1655;
    long TR_TRACKING_TYPE_CHANGE_ORDER_CREATED_DESC = 1656;
    long TR_TRACKING_TYPE_CHANGE_ORDER_CREATED = 1657;
    long TR_TRACKING_TYPE_CHANGE_ORDER_UPDATED_DESC = 1658;
    long TR_TRACKING_TYPE_CHANGE_ORDER_UPDATED = 1659;
    long TR_TRACKING_TYPE_CHANGE_ORDER_REJECTED_DESC = 1660;
    long TR_TRACKING_TYPE_CHANGE_ORDER_REJECTED = 1661;
    long TR_TRACKING_TYPE_CHANGE_ORDER_RETRACTED_DESC = 1662;
    long TR_TRACKING_TYPE_CHANGE_ORDER_RETRACTED = 1663;
    long TR_TRACKING_TYPE_CHANGE_ORDER_ACCEPTED_DESC = 1664;
    long TR_TRACKING_TYPE_CHANGE_ORDER_ACCEPTED = 1665;
    long TR_TRACKING_TYPE_CHANGE_ORDER_CANCELLED_DESC = 1666;
    long TR_TRACKING_TYPE_CHANGE_ORDER_CANCELLED = 1667;
    long OBJECT_STATE_DESC_ORDER_TEMPORARY = 1669;
    long TA_TASK_TYPE_CHANGE_ORDER_REVIEW_NAME = 1670;
    long TA_TASK_TYPE_CHANGE_ORDER_REVIEW_DESC = 1671;
    long CM_SHARED_CATEGORY = 1675;
    long CM_PRIVATE_CATEGORY = 1676;
    long CM_FILTER_WORKGROUP_MEMBERS = 1677;
    long CM_LIVE_CONTACTS = 1678;
    long CM_CONTACTS = 1679;
    long ERROR_MESSAGE_NO_PERMISSION = 1680;
    long ERROR_MESSAGE_NO_OWNERSHIP_BREAKOUT = 1681;
    long ERROR_MESSAGE_REFERENCE_BREAKOUT = 1682;
    long CANDO_MESSAGE_DISASSOCIATE_BREAKOUT = 1683;
    long MYDESK_FILTER_A_DUE_TODAY = 1684;
    long MYDESK_FILTER_A_DUE_THIS_WEEK = 1685;
    long MYDESK_FILTER_A_DUE_WITHIN_SEVEN_DAYS = 1686;
    long MYDESK_FILTER_A_OVERDUE_SEVEN_DAYS = 1687;
    long MYDESK_FILTER_A_ALL_OVERDUE = 1688;
    long MYDESK_FILTER_A_ALL = 1689;
    long MYDESK_FILTER_A_RECEIVED_TODAY = 1690;
    long MYDESK_FILTER_A_RECEIVED_PAST_TWO_DAYS = 1691;
    long MYDESK_FILTER_A_RECEIVED_PAST_SEVEN_DAYS = 1692;
    long MYDESK_FILTER_B_TODAY = 1693;
    long MYDESK_FILTER_B_PAST_TWO_DAYS = 1694;
    long MYDESK_FILTER_B_PAST_SEVEN_DAYS = 1695;
    long MYDESK_FILTER_B_PAST_THIRTY_DAYS = 1696;
    long MYDESK_FILTER_B_PAST_NINETY_DAYS = 1697;
    long MYDESK_FILTER_C_ALL_UNREAD = 1698;
    long MYDESK_FILTER_C_SIX_MOST_RECENT = 1699;
    long MYDESK_FILTER_C_TWELVE_MOST_RECENT = 1700;
    long MYDESK_FILTER_C_TWENTY_MOST_RECENT = 1701;
    long MYDESK_FILTER_C_RECEIVED_TODAY = 1702;
    long MYDESK_FILTER_C_RECEIVED_THIS_WEEK = 1703;
    long MYDESK_FILTER_D_ALL_EVENTS_LAST_HUNDRED = 1704;
    long MYDESK_FILTER_D_ALL_EVENTS_LAST_TEN = 1705;
    long MYDESK_FILTER_D_PROJECT_EVENTS_LAST_HUNDRED = 1706;
    long MYDESK_FILTER_D_PROJECT_EVENTS_LAST_TEN = 1707;
    long MYDESK_FILTER_D_TEAM_EVENTS_LAST_HUNDRED = 1708;
    long MYDESK_FILTER_D_TEAM_EVENTS_LAST_TEN = 1709;
    long MYDESK_FILTER_D_FILE_EVENTS_LAST_HUNDRED = 1710;
    long MYDESK_FILTER_D_FILE_EVENTS_LAST_TEN = 1711;
    long MYDESK_FILTER_D_ESTIMATE_EVENTS_LAST_HUNDRED = 1712;
    long MYDESK_FILTER_D_ESTIMATE_EVENTS_LAST_TEN = 1713;
    long MYDESK_FILTER_D_ORDER_EVENTS_LAST_HUNDRED = 1714;
    long MYDESK_FILTER_D_ORDER_EVENTS_LAST_TEN = 1715;
    long MYDESK_FILTER_E_NEXT_TASK = 1716;
    long MYDESK_FILTER_E_LATEST_EVENT = 1717;
    long MYDESK_FILTER_E_LATEST_MESSAGE = 1718;
    long MYDESK_FILTER_E_ESTIMATES = 1719;
    long MYDESK_FILTER_E_ORDERS = 1720;
    long MYDESK_FILTER_E_SHIP_DELIVERY = 1721;
    long MYDESK_FILTER_E_NONE = 1722;
    long CONSTRAINT_TOTAL_PRICE_BREAKOUT_TOO_BIG = 1723;
    long MYDESK_FILTER_B_SIX_MOST_RECENT = 1724;
    long MYDESK_FILTER_B_TWELVE_MOST_RECENT = 1725;
    long MYDESK_FILTER_B_TWENTY_MOST_RECENT = 1726;
    long REPORT_NO_DATA_FOUND = 1729;
    long REPORT_ESTIMATE_ANALYSIS_PROJECT_NUMBER = 1730;
    long REPORT_ESTIMATE_ANALYSIS_PROJECT_NAME = 1731;
    long REPORT_ESTIMATE_ANALYSIS_ORDER_REFERENCE = 1732;
    long REPORT_ESTIMATE_ANALYSIS_ORDER_ITEM_ID = 1733;
    long REPORT_ESTIMATE_ANALYSIS_AWARDED_ESTIMATE_AMOUNT = 1734;
    long REPORT_ESTIMATE_ANALYSIS_COST_SAVINGS = 1735;
    long REPORT_ESTIMATE_ANALYSIS_MINIMUM_ESTIMATE_AMOUNT = 1736;
    long REPORT_ESTIMATE_ANALYSIS_AVERAGE_ESTIMATE_AMOUNT = 1737;
    long REPORT_ESTIMATE_ANALYSIS_MAXIMUM_ESTIMATE_AMOUNT = 1738;
    long REPORT_ESTIMATE_ANALYSIS_ESTIMATES_EVALUATED = 1739;
    long REPORT_ESTIMATE_ANALYSIS_PREMIUM_PAID = 1740;
    long REPORT_ACTIVITY_FOR_PERIOD = 1741;
    long REPORT_TOTALS = 1742;
    long REPORT_ESTIMATE_ANALYSIS_MONTH = 1743;
    long REPORT_ESTIMATE_ANALYSIS_COST_SAVINGS_PCT = 1745;
    long REPORT_ESTIMATE_ANALYSIS_RFES_SEND = 1746;
    long REPORT_ESTIMATE_ANALYSIS_BUYER_NAME = 1747;
    long REPORT_ESTIMATE_ANALYSIS_PREMIUM_PAID_PCT = 1749;
    long REPORT_ESTIMATE_ANALYSIS_MINIMUM_ESTIMATE = 1750;
    long REPORT_ESTIMATE_ANALYSIS_AMOUNT = 1751;
    long REPORT_JANUARY = 1752;
    long REPORT_FEBRUARY = 1753;
    long REPORT_MARCH = 1754;
    long REPORT_APRIL = 1755;
    long REPORT_MAY = 1756;
    long REPORT_JUNE = 1757;
    long REPORT_JULY = 1758;
    long REPORT_AUGUST = 1759;
    long REPORT_SEPTEMBER = 1760;
    long REPORT_OCTOBER = 1761;
    long REPORT_NOVEMBER = 1762;
    long REPORT_DECEMBER = 1763;
    long REPORT_FOR = 1764;
    long REPORT_ALL = 1765;
    long REPORT_ESTIMATE_WIN_LOSS_WINNING_ESTIMATE_DETAIL = 1766;
    long REPORT_ESTIMATE_WIN_LOSS_LOSING_ESTIMATE_DETAIL = 1767;
    long REPORT_ESTIMATE_WIN_LOSS_PENDING_ESTIMATE_DETAIL = 1768;
    long REPORT_ESTIMATE_WIN_LOSS_RFE_DETAIL = 1769;
    long REPORT_ESTIMATE_WIN_LOSS_ESTIMATE_WIN_LOSS_BY = 1770;
    long REPORT_SALES_REP = 1771;
    long REPORT_CUSTOMER = 1772;
    long REPORT_ESTIMATE_WIN_LOSS_WON = 1773;
    long REPORT_ESTIMATE_WIN_LOSS_LOSS = 1774;
    long REPORT_ESTIMATE_WIN_LOSS_PENDING = 1775;
    long CLOSED_BID = 1776;
    long OPEN_BID = 1777;
    long PRICE_DECREMENT_PERCENT = 1778;
    long PRICE_DECREMENT_VALUE = 1779;
    long BIDDING_RULE_INDIVIDUAL = 1780;
    long BIDDING_RULE_LOWEST_BID = 1781;
    long CM_ADDRESS_BOOK_TABS = 1799;
    long CM_ADDRESS = 1800;
    long CM_ADDRESS_BOOK = 1801;
    long CM_ADD_CATEGORY = 1802;
    long CM_A_CONTACT_CAN_REPRESENT_A_NOOSH_MEMBER_RETRIEVED_VIA_THEIR_EMAIL_ADDRESS = 1803;
    long CM_CATEGORIES = 1804;
    long CM_CATEGORY = 1805;
    long CM_CITY = 1806;
    long CM_CLICK_B_CREATE_LIVE_CONTACT_B_BELOW_TO_ADD_THIS_MEMBER_TO_YOU_CONTACT_LIST = 1807;
    long CM_COMPANY = 1808;
    long CM_COMPANY_NAME = 1809;
    long CM_CONFIRM_DELETE = 1810;
    long CM_CONTACTS_IN_CATEGORY = 1812;
    long CM_CONTACT_INFORMATION = 1813;
    long CM_COUNTRY = 1814;
    long CM_DELETE_THESE_CATEGORIES = 1815;
    long CM_EMAIL = 1816;
    long CM_EMAIL_ADDRESS = 1817;
    long CM_ENTER_AN_EMAIL_ADDRESS_BELOW_TO_FIND_IN_THE_NOOSH_MEMBER_LIST = 1818;
    long CM_FAX = 1819;
    long CM_FAX_NUMBER = 1820;
    long CM_FILENAME = 1821;
    long CM_FIND_NOOSH_MEMBER = 1822;
    long CM_FIND_MEMBER = 1823;
    long CM_FIRST_M_LAST_NAME = 1824;
    long CM_LAST_NAME_FIRST = 1825;
    long CM_MEMBERS_SUMMARY = 1826;
    long CM_MEMBER_SUMMARY = 1827;
    long CM_MOBILE = 1828;
    long CM_NAME = 1829;
    long CM_NO = 1830;
    long CM_NOOSH_HAS_LOCATED_A_MATCH_BASED_ON_THE_EMAIL_ADDRESS_YOU_VE_ENTERED = 1831;
    long CM_NOOSH_HAS_LOCATED_MORE_THAN_ONE_MATCH_BASED_ON_THE_EMAIL_ADDRESS_YOU_VE_ENTERED = 1832;
    long CM_NOTES = 1833;
    long CM_NO_NOOSH_MEMBERS_WERE_FOUND_WITH_THIS_EMAIL_ADDRESS = 1834;
    long CM_PAGER = 1835;
    long CM_PHONE_NUMBER = 1836;
    long CM_POSTAL_CODE = 1837;
    long CM_PRIMARY_PHONE = 1838;
    long CM_RESULT = 1839;
    long CM_SECONDARY_PHONE = 1840;
    long CM_SELECT_THE_CHECKBOX_FOR_EACH_OF_THE_CORRESPONDING_MEMBERS_FOR_WHOM_YOU_WOULD_LIKE_TO_CREATE_A_LIVE_CONTACT_AND_CLICK_B_CREATE_LIVE_CONTACT_B_BELOW_TO_ADD_THOSE_MEMBERS_TO_YOUR_CONTACT_LIST = 1841;
    long CM_SHARED_WITHIN_MY_GROUP = 1843;
    long CM_STATE_PROVINCE = 1844;
    long CM_THIS_CONTACT_IS_B_PERSONAL_B = 1845;
    long CM_THIS_CONTACT_IS_B_SHARED_B_WITHIN_MY_GROUP = 1846;
    long CM_TITLE = 1847;
    long CM_UPLOAD_YOUR_ADDRESS_BOOK = 1848;
    long CM_VIEW_CATEGORY = 1849;
    long CM_VIEW_CONTACT = 1850;
    long CM_WEBSITE = 1851;
    long CM_YES = 1852;
    long CM_SHARED_CONTACT = 1853;
    long CM_LIVE_CONTACT = 1854;
    long OBJECT_STATE_DESC_CHANGE_ORDER_CREATED_BY_BUYER = 1855;
    long OBJECT_STATE_DESC_CHANGE_ORDER_CREATED_BY_SUPPLIER = 1856;
    long OBJECT_STATE_DESC_CHANGE_ORDER_ACCEPTED = 1857;
    long OBJECT_STATE_DESC_CHANGE_ORDER_REJECTED = 1858;
    long OBJECT_STATE_DESC_CHANGE_ORDER_SUPPLIER_TO_ACCEPT = 1859;
    long OBJECT_STATE_DESC_CHANGE_ORDER_BUYER_TO_ACCEPT = 1860;
    long OBJECT_STATE_DESC_CHANGE_ORDER_CANCELLED = 1861;
    long OBJECT_STATE_DESC_CHANGE_ORDER_RETRACTED = 1862;
    long CREATE_SPEC_ITEM_TYPE_DUPLICATION_ERROR = 1863;
    long REGISTER_SPEC_PARAMS_ERROR = 1864;
    long ACCESS_RESULT_CODE_USER_NOT_IN_TEAM = 1865;
    long ACCESS_RESULT_CODE_NO_PERMISSION_FOR_WORKGROUP_ROLE = 1866;
    long ACCESS_RESULT_CODE_NO_PERMISSION_FOR_TEAM_ROLE = 1867;
    long ACCESS_RESULT_CODE_ACCESS_EXPLICITLY_DENIED_IN_PROJECT = 1868;
    long ACCESS_RESULT_CODE_ACCESS_NOT_EXPLICITLY_GRANTED_IN_PROJECT = 1869;
    long ACCESS_RESULT_CODE_OBJECT_IN_DIFFERENT_STATE = 1870;
    long DM_IS_CURRENT = 1871;
    long DM_IS_CURRENT_RECORD = 1872;
    long DM_ORDER_ORDER_TYPE_ID = 1873;
    long DM_ORDER_ORDER_TYPE_STR_ID = 1874;
    long DM_ORDER_BUYER_USER_ID = 1875;
    long DM_ORDER_BUYER_FIRST_NAME = 1876;
    long DM_ORDER_BUYER_LAST_NAME = 1877;
    long DM_ORDER_SUPPLIER_USER_ID = 1878;
    long DM_ORDER_SUPPLIER_FIRST_NAME = 1879;
    long DM_ORDER_SUPPLIER_LAST_NAME = 1880;
    long NOTIFICATION_TYPE_CHANGE_ORDER_CREATED = 1881;
    long NOTIFICATION_TYPE_CHANGE_ORDER_REJECTED = 1882;
    long NOTIFICATION_TYPE_CHANGE_ORDER_RETRACTED = 1883;
    long NOTIFICATION_TYPE_CHANGE_ORDER_UPDATED = 1884;
    long NOTIFICATION_TYPE_CHANGE_ORDER_ACCEPTED = 1885;
    long NOTIFICATION_TYPE_CHANGE_ORDER_CANCELLED = 1886;
    long PM_COPIED_SPEC_PREFIX = 1887;
    long PM_HOME_DESKOID_STRUCTURE = 1888;
    long PM_HOME_DESKOID_ESTIMATES = 1889;
    long PM_HOME_DESKOID_ORDERS = 1890;
    long PM_HOME_DESKOID_TASKS = 1891;
    long PM_HOME_DESKOID_TRACKING = 1892;
    long PM_HOME_DESKOID_OVERVIEW = 1893;
    long PM_HOME_DESKOID_TEAMS = 1894;
    long PM_HOME_DESKOID_FILES = 1895;
    long PM_HOME_DESKOID_MESSAGES = 1896;
    long TA_TASK_TYPE_CREATE_RFE_NAME = 1897;
    long NOTIFICATION_RFE_DATES_REVISED = 1898;
    long CONSTRAINT_ESTIMATING_TIME_EXPIRED = 1899;
    long PM_TASKS_FILTER_DUE_TODAY = 1900;
    long PM_TASKS_FILTER_DUE_THIS_WEEK = 1901;
    long PM_TASKS_FILTER_DUE_WITHIN_SEVEN_DAYS = 1902;
    long PM_TASKS_FILTER_ALL_OVERDUE = 1903;
    long PM_TASKS_FILTER_ALL = 1904;
    long PM_TASKS_FILTER_RECEIVED_TODAY = 1905;
    long PM_TASKS_FILTER_RECEIVED_PAST_TWO_DAYS = 1906;
    long PM_TASKS_FILTER_RECEIVED_PAST_SEVEN_DAYS = 1907;
    long RFE_SUPPLIER_ESTIMATING = 1908;
    long RFE_SUPPLIER_REJECTED = 1909;
    long RFE_SUPPLIER_DISMISSED = 1910;
    long PM_TRACKING_FILTER_ALL = 1911;
    long PM_TRACKING_FILTER_TODAY = 1912;
    long PM_TRACKING_FILTER_PAST_TWO_DAYS = 1913;
    long PM_TRACKING_FILTER_PAST_SEVEN_DAYS = 1914;
    long PM_TRACKING_FILTER_PAST_THIRTY_DAYS = 1915;
    long PM_TRACKING_FILTER_MOST_RECENT_SIX = 1916;
    long PM_TRACKING_FILTER_MOST_RECENT_TWELVE = 1917;
    long PM_TRACKING_FILTER_MOST_RECENT_TWENTY = 1918;
    long PM_FILES_FILTER_ALL = 1919;
    long PM_FILES_FILTER_TODAY = 1920;
    long PM_FILES_FILTER_PAST_TWO_DAYS = 1921;
    long PM_FILES_FILTER_PAST_SEVEN_DAYS = 1922;
    long PM_FILES_FILTER_PAST_THIRTY_DAYS = 1923;
    long PM_FILES_FILTER_MOST_RECENT_SIX = 1924;
    long PM_FILES_FILTER_MOST_RECENT_TWELVE = 1925;
    long PM_FILES_FILTER_MOST_RECENT_TWENTY = 1926;
    long PM_MESSAGES_FILTER_ALL_UNREAD = 1927;
    long PM_MESSAGES_FILTER_MOST_RECENT_SIX = 1928;
    long PM_MESSAGES_FILTER_MOST_RECENT_TWELVE = 1929;
    long PM_MESSAGES_FILTER_MOST_RECENT_TWENTY = 1930;
    long PM_MESSAGES_FILTER_RECEIVED_TODAY = 1931;
    long PM_MESSAGES_FILTER_RECEIVED_THIS_WEEK = 1932;
    long REPORT_ESTIMATE_ANALYSIS_REPORT = 1933;
    long DM_ESTIMATE_BREAKOUT_USER_DEFINED_NAME = 1934;
    long DM_ESTIMATE_BREAKOUT_TYPE_ID = 1935;
    long DM_ESTIMATE_BREAKOUT_TYPE = 1936;
    long DM_ESTIMATE_BREAKOUT_PRICE_1 = 1937;
    long DM_ESTIMATE_BREAKOUT_PRICE_2 = 1938;
    long DM_ESTIMATE_BREAKOUT_PRICE_3 = 1939;
    long DM_ESTIMATE_BREAKOUT_PRICE_4 = 1940;
    long DM_ESTIMATE_BREAKOUT_PRICE_5 = 1941;
    long PR_UOM_INH = 1942;
    long PR_UOM_FOT = 1943;
    long PR_UOM_MMT = 1944;
    long PR_UOM_CMT = 1945;
    long PR_UOM_MTR = 1946;
    long PR_UOM_LBR = 1947;
    long PR_UOM_NOOSH_PNT = 1948;
    long PR_UOM_GM = 1949;
    long PR_UOM_NOOSH_LPC = 1950;
    long PR_UOM_NOOSH_LPI = 1951;
    long PR_UOM_KGM = 1952;
    long SUPPLIER_STRING = 1953;
    long PM_STATUS_DEFAULT_NEW = 1954;
    long PM_STATUS_DEFAULT_ARCHIVED = 1955;
    long TM_INVITATION_TYPE_PROJECT_COLLABORATOR = 1956;
    long TM_INVITATION_TYPE_RFE_COLLABORATOR = 1957;
    long TM_INVITATION_TYPE_ORDER_COLLABORATOR = 1958;
    long TM_INVITATION_TYPE_ESTIMATOR = 1959;
    long TM_INVITATION_TYPE_SUPPLIER_STAFF = 1960;
    long AC_WORKGROUP_PROJECT_STATUS_TAB = 1961;
    long RE_ROLE_PROJECT_EX_SUPPLIER = 1962;
    long WORKGROUP_TAB_TERMS = 1963;
    long NOTIFICATION_TYPE_HOME_LOGIN_RETRIEVAL = 1964;
    long TM_ROLE_OPTION = 1965;
    long TM_TEMPLATE_OPTION = 1966;
    long RFE_DUEDATE_INVALID_CONSTRAINT = 1967;
    long RFE_COMPLETION_DATE_INVALID_CONSTRAINT = 1968;
    long RFE_OPENBID_DUEDATE_INVALID_CONSTRAINT = 1969;
    long DROPDOWN_OTHER_OPTION = 1970;
    long RFE_STARTDATE_INVALID_CONSTRAINT = 1971;
    long CM_ADD_NEW_CONTACT = 1972;
    long CM_EDIT_CONTACT = 1973;
    long CM_ADD_LIVE_CONTACT = 1974;
    long CHECK_ALL = 1975;
    long CLEAR_ALL = 1976;
    long CM_DELETE_THESE_CONTACTS = 1977;
    long CM_DELETE_CONTACT_CONFIRM_MESSAGE = 1978;
    long COLLABORATION_OBJECT_ACTIVE = 1979;
    long TRACKING_TYPE_NAME_PROJECT_STATUS_UPDATED = 1980;
    long TRACKING_TYPE_DESC_PROJECT_STATUS_UPDATED = 1981;
    long NOTIFICATION_PROJECT_STATUS_CHANGED = 1992;
    long RFE_BREAKOUT_PRICE_CONSTRAINT = 1993;
    long IN_CREATE_WORKGROUP = 1994;
    long IN_EDIT_WORKGROUP = 1995;
    long NOTIFICATION_TYPE_DISMISS_SUPPLIER = 1996;
    long CM_CONTACTS_LIST = 1997;
    long TR_TRACKING_TYPE_NAME_DISMISS_SUPPLIER = 1998;
    long TR_TRACKING_TYPE_DESCRIPTION_DISMISS_SUPPLIER = 1999;
    long TR_TRACKING_TYPE_NAME_RFE_DATE_CHANGE = 2000;
    long TR_TRACKING_TYPE_DESCRIPTION_RFE_DATE_CHANGE = 2001;
    long CONTAINABLE_ACTIVE = 2002;
    long SPEC_REGISTER_UOFM_ERROR = 2003;
    long SP_NEW_SPEC_LABEL = 2004;
    long COLLABORATION_OBJECT_INACTIVE = 2005;
    long INVALID_LOGIN_OR_PASSWORD = 2006;
    long REPORT_ADHOC_RECORD_NO = 2007;
    long REPORT_TITLE_DATA_ASOF = 2008;
    long REPORT_NOT_AVAILABLE = 2009;
    long ESTIMATE_OPENBID_INITIAL_PRICE_CONSTRAINT = 2010;
    long ESTIMATE_OPENBID_PRICE_CONSTRAINT = 2011;
    long PR_PARAM_NAME_TOO_LONG = 2012;
    long PR_PARAM_NAME_ILLEGAL = 2013;
    long RFE_DUEDATE_CONSTRAINT = 2014;
    long CM_WORKGROUP = 2015;
    long PR_UOM_PPI = 2016;
    long PR_UOM_PIXEL_MM = 2017;
    long FM_FILE_CHECKED_OUT_BY = 2018;
    long FM_COPY_OF = 2019;
    long FM_COPY_NUM_OF = 2020;
    long FM_COPY_NUM_OF_PREFIX = 2021;
    long FM_COPY_NUM_OF_SUFFIX = 2022;
    long AC_DUPLICATE_COMPANY_ERROR = 2023;
    long AC_DUPLICATE_WORKGROUP_ERROR = 2024;
    long CONSTRAINT_ESTIMATOR_ALREADY_IN_TEAM = 2025;
    long CM_DELETE_TEAM_MEMBER_CONFIRM = 2026;
    long CM_DELETE_TEAM_TEMPLATE_CONFIRM = 2027;
    long OPEN_BID_NOTE = 2028;
    long ERROR_MESSAGE_DELETE_REFERENCE_BREAKOUT = 2029;
    long PM_INVALID_NUM_PROJECT_COPIES = 2030;
    long CM_UNASSIGNED = 2031;
    long PERSONALIZE_THIS_PAGE = 2032;
    long PM_TASKS_FILTER_OVERDUE_SEVEN_DAYS = 2033;
    long ME_MY_DESK = 2034;
    long CM_SELECT_CONTACT_TYPE = 2035;
    long CM_SELECT_CONTACT_TYPE_YOU_WHISH_TO_CREATE = 2036;
    long CM_MY_OWN_CONTACT = 2037;
    long CM_NOOSH_MEMBER_CONTACT = 2038;
    long CM_ADD_NOOSH_MEMBER_DESCRIPTION = 2039;
    long CM_FIRST = 2040;
    long CM_LAST = 2041;
    long CM_OR = 2042;
    long CM_EDIT_CATEGORY = 2043;
    long CM_ADD_A_CONTACT_TO_CATEGORY_DESCRIPTION = 2044;
    long CM_REMOVE_A_CONTACT_TO_CATEGORY_DESCRIPTION = 2045;
    long CM_ADD_CONTACT = 2046;
    long CM_MEMBERS_FOUND = 2047;
    long CM_LISTED_BELOW_NOOSH_MEMBERS_DESCRIPTION = 2048;
    long ESTIMATE_OWNER_REFERENCE_CONSTRAINT = 2049;
    long ESTIMATE_TITLE_CONSTRAINT = 2050;
    long ESTIMATE_ITEM_CONSTRAINT = 2051;
    long CONTAINABLE_DEACTIVATED = 2052;
    long CM_FILTER_MY_WORKGROUP = 2053;
    long CM_FILTER_OUTSIDE_MY_WORKGROUP = 2054;
    long CM_NOOSH_MEMBERS = 2055;
    long CM_CONTACTS_CATEGORIES = 2056;
    long CM_CONTACTS_SELECT_LIST = 2057;
    long RE_ROLE_PROJECT_DISMISSED_SUPPLIER = 2058;
    long BUYER_AND_SUPPLIER_MUST_USE_SAME_CURRENCY = 2059;
    long OBJECT_STATE_DESC_ORDER_SHIPPED = 2060;
    long OBJECT_STATE_DESC_ORDER_DELIVERED = 2061;
    long OBJECT_STATE_DESC_ORDER_COMPLETED = 2062;
    long TR_TRACKING_TYPE_ORDER_MARKED_ACCEPTED = 2063;
    long TR_TRACKING_TYPE_ORDER_MARKED_ACCEPTED_DESC = 2064;
    long TR_TRACKING_TYPE_ORDER_MARKED_SHIPPED = 2065;
    long TR_TRACKING_TYPE_ORDER_MARKED_SHIPPED_DESC = 2066;
    long TR_TRACKING_TYPE_ORDER_MARKED_DELIVERED = 2067;
    long TR_TRACKING_TYPE_ORDER_MARKED_DELIVERED_DESC = 2068;
    long TR_TRACKING_TYPE_ORDER_MARKED_COMPLETED = 2069;
    long TR_TRACKING_TYPE_ORDER_MARKED_COMPLETED_DESC = 2070;
    long NOTIFICATION_TYPE_ORDER_STATUS_CHANGED = 2071;
    long CANNOT_COMPLETE_HAS_PENDING_CHANGE_ORDERS = 2072;
    long TR_TRACKING_TYPE_NAME_FILE_CREATED = 2073;
    long TR_TRACKING_TYPE_DESCRIPTION_FILE_CREATED = 2074;
    long TR_TRACKING_TYPE_NAME_FILE_EDITED = 2075;
    long TR_TRACKING_TYPE_DESCRIPTION_FILE_EDITED = 2076;
    long TR_TRACKING_TYPE_NAME_FILE_DELETED = 2077;
    long TR_TRACKING_TYPE_DESCRIPTION_FILE_DELETED = 2078;
    long TR_TRACKING_TYPE_NAME_FILE_VERSION_CREATED = 2079;
    long TR_TRACKING_TYPE_DESCRIPTION_FILE_VERSION_CREATED = 2080;
    long TR_TRACKING_TYPE_NAME_FILE_MOVED = 2081;
    long TR_TRACKING_TYPE_DESCRIPTION_FILE_MOVED = 2082;
    long TR_TRACKING_TYPE_NAME_FILE_PRIVILEGES_CHANGED = 2083;
    long TR_TRACKING_TYPE_DESCRIPTION_FILE_PRIVILEGES_CHANGED = 2084;
    long TR_TRACKING_TYPE_NAME_FILE_DOWNLOADED = 2085;
    long TR_TRACKING_TYPE_DESCRIPTION_FILE_DOWNLOADED = 2086;
    long OR_ORDER_COMPLETE_WARNING = 2087;
    long OBJECT_STATE_DESC_ORDER_PARTIALLY_SHIPPED = 2088;
    long TR_TRACKING_TYPE_ORDER_MARKED_PARTIALLY_SHIPPED = 2089;
    long TR_TRACKING_TYPE_ORDER_MARKED_PARTIALLY_SHIPPED_DESC = 2090;
    long PM_ORDER_ACCEPTED = 2091;
    long PM_ORDERS_ACCEPTED = 2092;
    long PM_ORDER_CREATED = 2093;
    long PM_ORDERS_CREATED = 2094;
    long PM_ESTIMATE_SENT = 2095;
    long PM_ESTIMATES_SENT = 2096;
    long PM_ESTIMATE_RECEIVED = 2097;
    long PM_ESTIMATES_RECEIVED = 2098;
    long PM_RFE_SENT = 2099;
    long PM_RFES_SENT = 2100;
    long PM_RFE_RECEIVED = 2101;
    long PM_RFES_RECEIVED = 2102;
    long SPEC_REGISTER_UOFM_ERROR_SAME_GROUP = 2103;
    long SP_SPEC_TYPE_BUSINESS_FORM_LASER = 2104;
    long SP_SPEC_TYPE_BUSINESS_FORM_CONTINUOUS = 2105;
    long SP_SPEC_TYPE_BUSINESS_FORM_UNITSET = 2106;
    long SP_SPEC_TYPE_PRINTED_PRODUCT = 2107;
    long SP_SPEC_TYPE_ENVELOPE = 2108;
    long SP_SPEC_TYPE_CGS = 2109;
    long RFE_CURRENCY_CONSTRAINT = 2110;
    long PM_DUPLICATE_STATUS_NAME = 2111;
    long TRACKING_TYPE_USER_PRINTING_STARTED = 2112;
    long TRACKING_TYPE_USER_PRINTING_COMPLETED = 2113;
    long TRACKING_TYPE_USER_POSTPRESS_COMPLETED = 2114;
    long TRACKING_TYPE_USER_PROOFS_SENT = 2115;
    long TRACKING_TYPE_USER_PROOFS_RECEIVED = 2116;
    long TRACKING_TYPE_USER_PROOFS_OK = 2117;
    long TRACKING_TYPE_USER_PRESS_CHECK_SCHEDULED = 2118;
    long TRACKING_TYPE_USER_PRESS_CHECK_OK = 2119;
    long TRACKING_TYPE_USER_SAMPLES_SENT = 2120;
    long TRACKING_TYPE_USER_POSTAGE_REVISED = 2121;
    long TRACKING_TYPE_USER_MATERIALS_RECEIVED = 2122;
    long TRACKING_TYPE_USER_MATERIALS_SENT = 2123;
    long RFE_SUPPLIER_DRAFT = 2124;
    long RFE_SUPPLIER_SEND_PENDING = 2125;
    long RFE_SUPPLIER_SEND_FAILED = 2126;
    long OBJECT_STATE_DESC_ORDER_ACCEPTED_NOT_YET_SHIPPED = 2127;
    long TR_TRACKING_TYPE_ORDER_MARKED_ACCEPTED_NOT_YET_SHIPPED = 2128;
    long TR_TRACKING_TYPE_ORDER_MARKED_ACCEPTED_NOT_YET_SHIPPED_DESC = 2129;
    long SR_SUPPLIER_RATING_ACTIVE = 2130;
    long SR_SUPPLIER_RATING_INACTIVE = 2131;
    long SR_VISIBLE_TO_SUPPLIERS = 2132;
    long SR_NOT_VISIBLE_TO_SUPPLIERS = 2133;
    long NOTIFICATION_TYPE_MESSAGE_SENT = 2134;
    long DAY_PLURAL = 2135;
    long HOUR_PLURAL = 2136;
    long UNIT_PLURAL = 2137;
    long HUNDRED_PLURAL = 2138;
    long THOUSAND_PLURAL = 2139;
    long REPORT_PROJECT_HREF = 2140;
    long AC_WORKGROUP_SUPPLIER_RATING_TAB = 2141;
    long PM_COPY_PROJECT_PLEASE_WAIT = 2142;
    long NOTIFICATION_TYPE_FILE_CREATED = 2143;
    long NOTIFICATION_TYPE_FILE_EDITED = 2144;
    long NOTIFICATION_TYPE_FILE_DELETED = 2145;
    long NOTIFICATION_TYPE_FILE_VERSION_CREATED = 2146;
    long NOTIFICATION_TYPE_FILE_ACCESS_GRANTED = 2147;
    long NOTIFICATION_TYPE_FILE_ACCESS_REVOKED = 2148;
    long RP_OUTPUT_FORMAT_NAME_EXCEL = 2149;
    long RP_DATA_CATEGORY_NAME2_PROCUREMENT_ESTIMATE = 2150;
    long RP_DATA_CATEGORY_NAME2_PROCUREMENT_ORDER = 2151;
    long RP_DATA_CATEGORY_NAME2_PROCUREMENT_WRKGRP_ACTVTY = 2152;
    long RFE_SUPPLIER_ALREADY_EXISTED_CONSTRAINT = 2153;
    long SR_YES = 2155;
    long SR_PARTIAL = 2156;
    long SR_NO = 2157;
    long WORKGROUP_TAB_TASKS = 2158;
    long NOTIFICATION_TYPE_COMPLETE_SUPPLIER_RATING = 2159;
    long TA_TASK_TYPE_CREATE_RATING_NAME = 2160;
    long TA_TASK_TYPE_CREATE_RATING_DESC = 2161;
    long TR_TRACKING_TYPE_NAME_SEND_RFE_ADDITIONAL_SUPPLIER = 2162;
    long TR_TRACKING_TYPE_DESCRIPTION_SEND_RFE_ADDITIONAL_SUPPLIER = 2163;
    long CO_SERVICE_DESCRIPTION_PEOPLE_SERVICES = 2164;
    long CO_SERVICE_DESCRIPTION_CUSTOM_PRODUCTS_AND_SERVICES = 2165;
    long CO_SERVICE_DESCRIPTION_OTHER_JOBS = 2166;
    long CONTAINABLE_DELETED = 2167;
    long TR_TRACKING_TYPE_TASK_ASSIGNED = 2168;
    long TR_TRACKING_TYPE_TASK_ASSIGNED_DESC = 2169;
    long TR_TRACKING_TYPE_TASK_REASSIGNED = 2170;
    long TR_TRACKING_TYPE_TASK_REASSIGNED_DESC = 2171;
    long TR_TRACKING_TYPE_TASK_DELETED = 2172;
    long TR_TRACKING_TYPE_TASK_DELETED_DESC = 2173;
    long TR_TRACKING_TYPE_TASK_COMPLETED = 2174;
    long TR_TRACKING_TYPE_TASK_COMPLETED_DESC = 2175;
    long TR_TRACKING_TYPE_TASK_UPDATED = 2176;
    long TR_TRACKING_TYPE_TASK_UPDATED_DESC = 2177;
    long TR_TRACKING_TYPE_TASK_READY_TO_COMPLETE = 2178;
    long TR_TRACKING_TYPE_TASK_READY_TO_COMPLETE_DESC = 2179;
    long TR_TRACKING_TYPE_TASK_READY_TO_START = 2180;
    long TR_TRACKING_TYPE_TASK_READY_TO_START_DESC = 2181;
    long NOTIFICATION_TYPE_TASK_ASSIGNED = 2182;
    long NOTIFICATION_TYPE_TASK_REASSIGNED = 2183;
    long NOTIFICATION_TYPE_TASK_DELETED = 2184;
    long NOTIFICATION_TYPE_TASK_COMPLETED = 2185;
    long NOTIFICATION_TYPE_TASK_UPDATED = 2186;
    long NOTIFICATION_TYPE_TASK_READY_TO_START = 2187;
    long NOTIFICATION_TYPE_TASK_READY_TO_COMPLETE = 2188;
    long OBJECT_STATE_DESC_TASK_ON_HOLD = 2189;
    long WORKGROUP_TAB_SPEC_TEMPLATES = 2190;
    long TA_TASK_STARTING_DEPENDENCY = 2191;
    long TA_TASK_COMPLETION_DEPENDENCY = 2192;
    long TASK_NAME_CONSTRAINT = 2193;
    long DM_ORDER_MISC_COST = 2194;
    long DM_ORDER_CHANGE_ORDER_STATUS = 2195;
    long SPEC_TAB_SPECS = 2196;
    long SPEC_TAB_TEMPLATE = 2197;
    long DM_PROJECT_PROJECT_STATUS = 2198;
    long DM_PROJECT_DEACTIVATION_REASON = 2199;
    long DM_PROJECT_WORKGROUP_ID = 2200;
    long DM_PROJECT_USER_DEFINED_PROJECT_STATUS = 2201;
    long AC_LOCALE_DESCRIPTION_en_UK = 2202;
    long NOTIFICATION_TYPE_TASK_ASSIGNED_NO_DUE_DATE = 2203;
    long RP_DATA_FIELD_GROUP_TASK = 2296;
    long RP_DATA_FIELD_GROUP_TASK_PROJECT = 2297;
    long RP_DATA_FIELD_GROUP_TASK_DEPENDENCY = 2298;
    long RP_DATA_FIELD_GROUP_TASK_CONTRIBUTOR = 2299;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_TASK = 2300;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_TASK_DESCN = 2301;
    long DM_TASK_ACTUAL_DURATION = 2302;
    long DM_TASK_ACTUAL_END_DATE = 2303;
    long DM_TASK_ACTUAL_START_DATE = 2304;
    long DM_TASK_ASSIGNED_BY_USER_FIRST_NAME = 2305;
    long DM_TASK_ASSIGNED_BY_USER_ID = 2306;
    long DM_TASK_ASSIGNED_BY_USER_LAST_NAME = 2307;
    long DM_TASK_ASSIGNED_TO_USER_FIRST_NAME = 2308;
    long DM_TASK_ASSIGNED_TO_USER_ID = 2309;
    long DM_TASK_ASSIGNED_TO_USER_LAST_NAME = 2310;
    long DM_TASK_CREATOR_FIRST_NAME = 2311;
    long DM_TASK_CREATOR_LAST_NAME = 2312;
    long DM_TASK_CREATOR_USER_ID = 2313;
    long DM_TASK_CREATOR_WORKGROUP_ID = 2314;
    long DM_TASK_CREATOR_WORKGROUP_NAME = 2315;
    long DM_TASK_CUSTOM_STATUS_ID = 2316;
    long DM_TASK_CUSTOM_STATUS_NAME = 2317;
    long DM_TASK_DM_CREATE_DATE = 2318;
    long DM_TASK_IS_ACTIVE_CUSTOM_STATUS = 2319;
    long DM_TASK_IS_ACTIVE_TASK_PLAN_TEMPLATE = 2320;
    long DM_TASK_IS_ACTIVE_TASK_TYPE = 2321;
    long DM_TASK_IS_DEFAULT_CUSTOM_STATUS = 2322;
    long DM_TASK_IS_READY_TO_COMPLETE = 2323;
    long DM_TASK_IS_READY_TO_START = 2324;
    long DM_TASK_PERCENTAGE_COMPLETE = 2325;
    long DM_TASK_PRIORITY = 2326;
    long DM_TASK_REVISED_DURATION = 2327;
    long DM_TASK_REVISED_END_DATE = 2328;
    long DM_TASK_REVISED_START_DATE = 2329;
    long DM_TASK_SYSTEM_STATUS_ID = 2330;
    long DM_TASK_SYSTEM_STATUS_STR_ID = 2331;
    long DM_TASK_TASK_COMMENTS = 2332;
    long DM_TASK_TASK_CREATE_DATE = 2333;
    long DM_TASK_TASK_DESCRIPTION = 2334;
    long DM_TASK_TASK_ID = 2335;
    long DM_TASK_TASK_NAME = 2336;
    long DM_TASK_TASK_PLAN_TEMPLATE_ID = 2337;
    long DM_TASK_TASK_PLAN_TEMPLATE_NAME = 2338;
    long DM_TASK_TASK_TYPE_ID = 2339;
    long DM_TASK_TASK_TYPE_NAME_STR_ID = 2340;
    long DM_TASK_USER_DEFINE_TASK_TYPE_NAME = 2341;
    long DM_TASK_CONTRIBUTOR_CONTRIBUTOR_FIRST_NAME = 2342;
    long DM_TASK_CONTRIBUTOR_CONTRIBUTOR_LAST_NAME = 2343;
    long DM_TASK_CONTRIBUTOR_CONTRIBUTOR_USER_ID = 2344;
    long DM_TASK_CONTRIBUTOR_TASK_CONTRIBUTOR_ID = 2345;
    long DM_TASK_DEPENDENCY_DEPENDS_ON_TASK_ID = 2346;
    long DM_TASK_DEPENDENCY_IS_ENFORCED = 2347;
    long DM_TASK_DEPENDENCY_IS_MET = 2348;
    long DM_TASK_DEPENDENCY_TASK_DEPENDENCY_ID = 2349;
    long DM_TASK_DEPENDENCY_TASK_DEPENDENCY_TYPE_ID = 2350;
    long DM_TASK_DEPENDENCY_TASK_DEPENDENCY_TYPE_STR_ID = 2351;
    long DM_TASK_PROJECT_OWNER_WORKGROUP_ID = 2352;
    long DM_TASK_PROJECT_PRJECT_CLIENT_ACCOUNT = 2353;
    long DM_TASK_PROJECT_PROJECT_CREATE_DATE = 2354;
    long DM_TASK_PROJECT_PROJECT_ID = 2355;
    long DM_TASK_PROJECT_PROJECT_NAME = 2356;
    long DM_TASK_PROJECT_PROJECT_NUMBER = 2357;
    long DM_TASK_PROJECT_PROJECT_REFERENCE = 2358;
    long WORKGROUP_TAB_ROLES = 2359;
    long WORKGROUP_TAB_SPECS = 2360;
    long WORKGROUP_TAB_WORKFLOWS = 2361;
    long DM_TASK_BASELINE_DURATION = 2362;
    long DM_TASK_BASELINE_END_DATE = 2363;
    long DM_TASK_BASELINE_START_DATE = 2364;
    long DM_TASK_IS_OVER_DUE = 2365;
    long DM_TASK_TASK_MOD_DATE = 2366;
    long TASK_PLAN_TEMPLATE_CONSTRAINT = 2367;
    long BLANK_TEMPLATE = 2368;
    long NOTIFICATION_TYPE_PROJECT_ANOTHER_MEMBER_INVITED = 2369;
    long TR_TRACKING_TYPE_SPEC_CREATED = 2370;
    long TR_TRACKING_TYPE_SPEC_EDITED = 2371;
    long TR_TRACKING_TYPE_SPEC_DELETED = 2372;
    long TR_TRACKING_TYPE_SPEC_CREATED_DESCR = 2373;
    long TR_TRACKING_TYPE_SPEC_EDITED_DESCR = 2374;
    long MY_DESKS = 2376;
    long TR_TRACKING_TYPE_RATING_QUESTIONNAIRE_COMPLETED = 2375;
    long TR_TRACKING_TYPE_RATING_QUESTIONNAIRE_COMPLETED_DESC = 2377;
    long TR_TRACKING_TYPE_RATING_QUESTIONNAIRE_REVISED = 2378;
    long TR_TRACKING_TYPE_RATING_QUESTIONNAIRE_REVISED_DESC = 2379;
    long TR_TRACKING_TYPE_SPEC_DELETED_DESCR = 2380;
    long TM_USER_EXISTS_IN_COLLABORATION_TEAM = 2381;
    long PM_ORDER_COMPLETED = 2382;
    long PM_ORDERS_COMPLETED = 2383;
    long FM_VIEW_LOG_FILE = 2386;
    long SR_REPORT_TOTAL_INCLUDES_OTHER_COSTS = 2387;
    long FM_VIEW_FILE = 2388;
    long TR_TRACKING_TYPE_TASK_RENAMED = 2389;
    long TR_TRACKING_TYPE_TASK_RENAMED_DESC = 2390;
    long NOTIFICATION_TYPE_TASK_RENAMED = 2391;
    long NOT_APPLICABLE = 2392;
    long WORKGROUP_TAB_SUPPLIERS = 2397;
    long TA_UNASSIGNED = 2398;
    long ASSIGNED_TO_WORKGROUP_MEMBERS = 2399;
    long ASSIGNED_TO_NON_WORKGROUP_MEMBERS = 2400;
    long UNASSIGNED = 2401;
    long INVALID_OPTION = 2402;
    long URL_PROTOCOL_CONSTRAINT = 2403;
    long WORKGROUP_TAB_AUP = 2404;
    long CONSTRAINT_AUP_REQUIRED = 2405;
    long DM_PROJECT_PROJECT_COMPLETION_DATE = 2410;
    long STRUCTURE_DESKOID_LABEL = 2411;
    long ESTIMATES_DESKOID_LABEL = 2412;
    long ORDERS_DESKOID_LABEL = 2413;
    long TASKS_DESKOID_LABEL = 2414;
    long TRACKING_DESKOID_LABEL = 2415;
    long OVERVIEW_DESKOID_LABEL = 2416;
    long TEAMS_DESKOID_LABEL = 2417;
    long FILES_DESKOID_LABEL = 2418;
    long MESSAGES_DESKOID_LABEL = 2419;
    long MYINFO_TAB_USER_INFO = 2420;
    long MYINFO_TAB_PREFERENCES = 2421;
    long MYINFO_TAB_TEAM_TEMPLATES = 2422;
    long MYINFO_TAB_AUTOMATIC_INVITATIONS = 2423;
    long TASKS_ALL_OPEN = 2424;
    long PM_SPEC_EDITABLE = 2425;
    long SR_SECTION_DELIVERY_PERFORMANCE = 2426;
    long SR_SECTION_COST = 2427;
    long SR_SECTION_CUSTOMER_SERVICE = 2428;
    long SR_SECTION_QUALITY = 2429;
    long SR_SECTION_PROCESS_IMPROVEMENT_INNOVATION = 2430;
    long SR_SECTION_OTHER = 2431;
    long SH_SAME_DAY = 2432;
    long SH_OVERNIGHT = 2433;
    long SH_24HOURS = 2434;
    long SH_2ND_DAY = 2435;
    long SH_PRIORITY = 2436;
    long SH_GROUND = 2437;
    long SH_OTHER = 2438;
    long SH_BOXES = 2439;
    long SH_CARTONS = 2440;
    long SH_PALLETS = 2441;
    long WORKGROUP_TAB_SHIPPING_CARRIER = 2442;
    long ERROR_CARRIER_CANNOT_BE_DELETED = 2443;
    long TR_TRACKING_TYPE_SHIPMENT_UPDATED = 2444;
    long TR_TRACKING_TYPE_SHIPMENT_UPDATED_DESC = 2445;
    long NOTIFICATION_TYPE_SHIPMENT_CREATED = 2446;
    long NOTIFICATION_TYPE_SHIPMENT_UPDATED = 2447;
    long NOTIFICATION_TYPE_SHIPMENT_DELETED = 2448;
    long RECIPIENTS = 2449;
    long OBJECT_CLASS_PROJECT = 2450;
    long OBJECT_CLASS_TRACKING = 2451;
    long OBJECT_CLASS_CATEGORY = 2452;
    long OBJECT_CLASS_FILE = 2453;
    long OBJECT_CLASS_MESSAGE = 2454;
    long OBJECT_CLASS_TASK = 2455;
    long OBJECT_CLASS_SPEC = 2456;
    long OBJECT_CLASS_RFE = 2457;
    long OBJECT_CLASS_ESTIMATE = 2458;
    long OBJECT_CLASS_ORDER = 2459;
    long OBJECT_CLASS_CHANGE_ORDER = 2460;
    long OBJECT_CLASS_SHIPMENT = 2461;
    long OBJECT_CLASS_DONT_CARE = 2462;
    long OBJECT_CLASS_RFE_ALLOCATION = 2463;
    long ERROR_DX_SENDER_ACCOUNT_CANNOT_BE_DELETED = 2464;
    long MYINFO_TAB_EXTERNAL_APPLICATIONS = 2465;
    long SHIPMENT_FILTER_RFE_SPEC = 2466;
    long SHIPMENT_FILTER_PROJECT_SPEC = 2467;
    long SHIPMENT_FILTER_ORDER_SPEC = 2468;
    long WORKGROUP_TAB_COST_CENTERS = 2469;
    long SH_LOCAL_CARRIER = 2470;
    long SH_FEDEX = 2471;
    long SH_UPS = 2472;
    long SH_CUSTOMER_PICKUP = 2473;
    long SH_AIRBORN_EXPRESS = 2474;
    long SH_US_POSTAL = 2475;
    long SH_COMMON_CARRIER = 2476;
    long SH_SUPPLIER_TRUCK = 2477;
    long SH_HDL = 2479;
    long NO_PERMISSION_TO_DELETE_THIS_SHIPMENT = 2480;
    long NO_PERMISSION_TO_CREATE_SHIPMENT = 2481;
    long NO_PERMISSION_TO_EDIT_THIS_SHIPMENT = 2482;
    long NO_PERMISSION_TO_DELETE_SHIPMENT_FOR_RFE = 2483;
    long NO_PERMISSION_TO_CREATE_SHIPMENT_FOR_RFE = 2484;
    long NO_PERMISSION_TO_EDIT_SHIPMENT_FOR_RFE = 2485;
    long NO_PERMISSION_TO_DELETE_SHIPMENT_FOR_ORDER = 2486;
    long NO_PERMISSION_TO_CREATE_SHIPMENT_FOR_ORDER = 2487;
    long NO_PERMISSION_TO_EDIT_SHIPMENT_FOR_ORDER = 2488;
    long SHIPMENT_LOCATION_CONSTRAINT = 2489;
    long WORKGROUP_TAB_SCHEDULE_TEMPLATES = 2490;
    long NOTIFICATION_TYPE_TASK_REMINDER = 2491;
    long PURCHASE_REQUEST_CREATED = 2492;
    long PURCHASE_REQUEST_SENT = 2493;
    long PURCHASE_REQUEST_APPROVED = 2494;
    long WORKGROUP_TAB_COUNTERS = 2495;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CREATED = 2496;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CREATED_DESC = 2497;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_SENT = 2498;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_SENT_DESC = 2499;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_APPROVED = 2500;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_APPROVED_DESC = 2501;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_DELETED = 2502;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_DELETED_DESC = 2503;
    long TR_TRACKING_TYPE_TRANSFER_PROJECT = 2504;
    long TR_TRACKING_TYPE_TRANSFER_PROJECT_DESC = 2505;
    long NOTIFICATION_TYPE_PROJECT_TRANSFERED = 2506;
    long WORKGROUP_TAB_TRACKING = 2507;
    long NOTIFICATION_TYPE_CUSTOM_TRACKING_POSTED = 2508;
    long TRANSFER_PROJECT_SAME_GROUP_CONSTRAINT = 2509;
    long TIMECARD_INVALID_DATE = 2510;
    long TIMECARD_DATE_ALREADY_SUBMITTED = 2511;
    long DM_TASK_PLAN_HOURS = 2512;
    long DM_TASK_ACTUAL_HOURS = 2513;
    long TR_TRACKING_TYPE_COST_CENTER_UPDATED = 2514;
    long COST_CENTER_EXISTS = 2515;
    long ACTIVITY_REPORT_SUMMARY = 2516;
    long EVENT_ACTIVITY_REPORT = 2517;
    long PROJECT_ACTIVITY_REPORT = 2518;
    long RFE_ACTIVITY_REPORT = 2519;
    long ESTIMATE_ACTIVITY_REPORT = 2520;
    long AWARDED_ORDERS_ACTIVITY_REPORT = 2521;
    long ACCEPTED_ORDERS_ACTIVITY_REPORT = 2522;
    long REPORT_ACTIVITY_PROJECT = 2523;
    long REPORT_ACTIVITY_PROJECT_NUMBER = 2524;
    long REPORT_ACTIVITY_PROJECT_NAME = 2525;
    long REPORT_ACTIVITY_EVENT = 2526;
    long REPORT_ACTIVITY_DESCRIPTION = 2527;
    long REPORT_ACTIVITY_TIME_RECORDED = 2528;
    long REPORT_ACTIVITY_CREATED_BY_PERSON = 2529;
    long REPORT_ACTIVITY_CREATED_BY_WORKGROUP = 2530;
    long REPORT_ACTIVITY_PARENT_PROJECT = 2531;
    long REPORT_ACTIVITY_PROJECT_CATEGORY = 2532;
    long REPORT_ACTIVITY_ACTION = 2533;
    long REPORT_ACTIVITY_CREATED_DATE = 2534;
    long REPORT_ACTIVITY_DELETED_DATE = 2535;
    long REPORT_ACTIVITY_RFE_REFERENCE = 2536;
    long REPORT_ACTIVITY_RFE_TITLE = 2537;
    long REPORT_ACTIVITY_RFE_SENT_DATE = 2538;
    long REPORT_ACTIVITY_ESTIMATE_DUE_DATE = 2539;
    long REPORT_ACTIVITY_ITEMS = 2540;
    long REPORT_ACTIVITY_BUYER_WORKGROUP = 2541;
    long REPORT_ACTIVITY_BUYER = 2542;
    long REPORT_ACTIVITY_SUPPLIER_WORKGROUP = 2543;
    long REPORT_ACTIVITY_SUPPLIERS = 2544;
    long REPORT_ACTIVITY_ESTIMATE_REFERENCE = 2545;
    long REPORT_ACTIVITY_ESTIMATE_TITLE = 2546;
    long REPORT_ACTIVITY_ESTIMATE_SUBMIT_DATE = 2547;
    long REPORT_ACTIVITY_SUPPLIER = 2548;
    long REPORT_ACTIVITY_ORDER_REFERENCE = 2549;
    long REPORT_ACTIVITY_ORDER_TITLE = 2550;
    long REPORT_ACTIVITY_DATE = 2551;
    long REPORT_ACTIVITY_ORDER_TYPE = 2552;
    long REPORT_ACTIVITY_AMOUNT = 2553;
    long TR_TRACKING_TYPE_COST_CENTER_UPDATED_DESCR = 2554;
    long REPORT_ACTIVITY_EVENTS = 2555;
    long REPORT_ACTIVITY_PROJECTS = 2556;
    long REPORT_ACTIVITY_RFES_SENT = 2557;
    long REPORT_ACTIVITY_RFES_RECEIVED = 2558;
    long REPORT_ACTIVITY_ESTIMATES_SENT = 2559;
    long REPORT_ACTIVITY_ESTIMATES_RECEIVED = 2560;
    long REPORT_ACTIVITY_AWARDED_ORDERS_COUNT = 2561;
    long REPORT_ACTIVITY_AWARDED_ORDERS_AMOUNT = 2562;
    long REPORT_ACTIVITY_ACCEPTED_ORDERS_COUNT = 2563;
    long REPORT_ACTIVITY_ACCEPTED_ORDERS_AMOUNT = 2564;
    long REPORT_ACTIVITY_MEMBER_NAME = 2565;
    long CONSTRAINT_NEW_OWNER = 2566;
    long MYDESK_FILTER_D_TODAYS_EVENTS = 2567;
    long RP_DATA_CATEGORY_NAME_PROCESS = 2568;
    long RP_DATA_CATEGORY_NAME_PROCESS_DESCN = 2569;
    long RP_DATA_CATEGORY_NAME_PROCESS_TIMECARD = 2570;
    long RP_DATA_CATEGORY_NAME_PROCESS_TIMECARD_DESCN = 2571;
    long RP_DATA_FIELD_GROUP_TIMECARD = 2572;
    long DM_TIMECARD_TA_TIMECARD_LINE_ENTRY_ID = 2573;
    long DM_TIMECARD_USER_ID = 2574;
    long DM_TIMECARD_USER_FIRST_NAME = 2575;
    long DM_TIMECARD_USER_LAST_NAME = 2576;
    long DM_TIMECARD_WORKGROUP_ID = 2577;
    long DM_TIMECARD_WORKGROUP_NAME = 2578;
    long DM_TIMECARD_WEEK_BEGINING = 2579;
    long DM_TIMECARD_DATE_WORKED = 2580;
    long DM_TIMECARD_PROJECT_NUMBER = 2581;
    long DM_TIMECARD_PROJECT_NAME = 2582;
    long DM_TIMECARD_TA_TASK_ID = 2583;
    long DM_TIMECARD_TASK_NAME = 2584;
    long DM_TIMECARD_BILLABLE_HOURS = 2585;
    long DM_TIMECARD_ACCOUNT_NUMBER = 2586;
    long DM_TIMECARD_ACTIVITY_NAME = 2587;
    long DM_TIMECARD_NON_BILLABLE_HOURS = 2588;
    long DM_TIMECARD_TOTAL_HOURS = 2589;
    long DM_TIMECARD_IS_SUBMITTED = 2590;
    long DM_TIMECARD_CREATE_DATE = 2591;
    long DM_TIMECARD_MOD_DATE = 2592;
    long DM_TIMECARD_DM_CREATE_DATE = 2593;
    long TA_TASK_CANNOT_DELETE_TIMECARD_TASK = 2594;
    long TA_TASK_ADD_TIMECARD_ENTRY_DATE_REQUIRED = 2595;
    long DATE_NULL_CONSTRAINT = 2596;
    long INAVLID_DATE_CONSTRAINT = 2597;
    long DM_SPEC_PROJECT_SPEC = 2598;
    long DM_SPEC_RFE_SPEC = 2599;
    long DM_SPEC_ESTIMATE_SPEC = 2600;
    long DM_SPEC_ORDER_SPEC = 2601;
    long DM_SPEC_CHANGE_ORDER_SPEC = 2602;
    long DM_ORDER_COSTCENTER_COSTCENTER_ALLOCATION_ID = 2603;
    long DM_ORDER_COSTCENTER_COSTCENTER_NUMBER = 2604;
    long DM_ORDER_COSTCENTER_DESCRIPTION = 2605;
    long DM_ORDER_COSTCENTER_ALLOCATION_PERCENT = 2606;
    long DM_ORDER_COSTCENTER_ALLOCATED_COST = 2607;
    long DM_ORDER_COSTCENTER_ORDER_ID  = 2608;
    long DM_ORDER_COSTCENTER_DM_CREATE_DATE = 2609;
    long RP_DATA_FIELD_GROUP_ORDER_ORDER_COSTCENTER = 2610;
    long RP_DATA_FIELD_GROUP_ORDER_ORDER_ITEM_COSTCENTER = 2611;
    long DM_ORDER_ITEM_COSTCENTER_COSTCENTER_ALLOCATION_ID = 2612;
    long DM_ORDER_ITEM_COSTCENTER_COSTCENTER_NUMBER = 2613;
    long DM_ORDER_ITEM_COSTCENTER_DESCRIPTION = 2614;
    long DM_ORDER_ITEM_COSTCENTER_ALLOCATION_PERCENT = 2615;
    long DM_ORDER_ITEM_COSTCENTER_ALLOCATED_COST = 2616;
    long DM_ORDER_ITEM_COSTCENTER_ORDER_ITEM_ID = 2617;
    long DM_ORDER_ITEM_COSTCENTER_DM_CREATE_DATE = 2618;
    long APPROVED = 2619;
    long CONTACTS_DO_CREATE_LIVE_CONTACT_ALERT = 2621;
    long DM_PROJECT_OBJECT_STATE_ID = 2622;
    long DM_PROJECT_ACTIVATION_STATUS = 2623;
    long DM_ORDER_COSTCENTER_ORDER_ITEM_ID  = 2625;
    long DM_SPEC_SPEC_NAME = 2626;
    long TR_PUBLIC_EVENT = 2627;
    long SP_SPEC_TYPE_NOT_SELECTED = 2628;
    long DM_ORDER_COSTCENTER_GL_ACCOUNT = 2629;
    long DM_ORDER_BUYER_V_ACCEPT_DATE = 2630;
    long DM_ORDER_SUPPLIER_V_ACCEPT_DATE = 2631;
    long SHIPMENT_SELECT_LOCATIONS = 2632;
    long SP_SPEC_TYPE_DIRECTMAIL = 2633;
    long SP_SPEC_TYPE_PREPRESS = 2634;
    long WORKGROUP_TAB_ATTRIBUTES = 2635;
    long WORKGROUP_ATTRIBUTE_TIMECARD_ACCOUNT_NUMBER = 2636;
    long WORKGROUP_ATTRIBUTE_TIMECARD_ACTIVITY = 2637;
    long SPEC_PRODUCT_TYPE = 2638;
    long SPEC_UNSPSC_TYPE = 2639;
    long UNSPSC_TYPE_44121505 = 2640;
    long UNSPSC_TYPE_44121506 = 2641;
    long UNSPSC_TYPE_55101501 = 2642;
    long UNSPSC_TYPE_55101502 = 2643;
    long UNSPSC_TYPE_55101503 = 2644;
    long UNSPSC_TYPE_55101504 = 2645;
    long UNSPSC_TYPE_55101509 = 2646;
    long UNSPSC_TYPE_55101510 = 2647;
    long UNSPSC_TYPE_55101515 = 2648;
    long UNSPSC_TYPE_UNSPECIFIED = 2649;
    long PRODUCT_TYPE_AD_REPRINT = 2650;
    long PRODUCT_TYPE_ANNUAL_REPORT = 2651;
    long PRODUCT_TYPE_BANNER = 2652;
    long PRODUCT_TYPE_BINDER = 2653;
    long PRODUCT_TYPE_BOOK = 2654;
    long PRODUCT_TYPE_BOOKLET = 2655;
    long PRODUCT_TYPE_BOX = 2656;
    long PRODUCT_TYPE_BROCHURE = 2657;
    long PRODUCT_TYPE_BUCKSLIP = 2658;
    long PRODUCT_TYPE_BUSINESS_FORM = 2659;
    long PRODUCT_TYPE_BUSINESS_FORM_LASER = 2660;
    long PRODUCT_TYPE_BUSINESS_FORM_CONT = 2661;
    long PRODUCT_TYPE_BUSINESS_FORM_UNIT = 2662;
    long PRODUCT_TYPE_BUS_REPLY_CARD = 2663;
    long PRODUCT_TYPE_CARD = 2664;
    long PRODUCT_TYPE_CARTON = 2665;
    long PRODUCT_TYPE_CATALOG = 2666;
    long PRODUCT_TYPE_CD_PACKAGING = 2667;
    long PRODUCT_TYPE_CERTIFICATE = 2668;
    long PRODUCT_TYPE_COUPON = 2669;
    long PRODUCT_TYPE_CUSTOM = 2670;
    long PRODUCT_TYPE_DATA_SHEET = 2671;
    long PRODUCT_TYPE_DIRECTORY = 2672;
    long PRODUCT_TYPE_DIRECT_MAIL = 2673;
    long PRODUCT_TYPE_DIVIDER = 2674;
    long PRODUCT_TYPE_DOUBLE_POSTCARD = 2675;
    long PRODUCT_TYPE_EASEL_CARD = 2676;
    long PRODUCT_TYPE_ENVELOPE = 2677;
    long PRODUCT_TYPE_FLAT_DOCUMENT = 2678;
    long PRODUCT_TYPE_FLYER = 2679;
    long PRODUCT_TYPE_FLOOR_STAND = 2680;
    long PRODUCT_TYPE_FORM = 2681;
    long PRODUCT_TYPE_HANG_TAG = 2682;
    long PRODUCT_TYPE_INSERT = 2683;
    long PRODUCT_TYPE_INVITATION = 2684;
    long PRODUCT_TYPE_LABEL = 2685;
    long PRODUCT_TYPE_LETTER = 2686;
    long PRODUCT_TYPE_LETTERHEAD = 2687;
    long PRODUCT_TYPE_MANUAL = 2688;
    long PRODUCT_TYPE_MAPS = 2689;
    long PRODUCT_TYPE_MEMO_PAD = 2690;
    long PRODUCT_TYPE_NEWSLETTER = 2691;
    long PRODUCT_TYPE_PAPER = 2692;
    long PRODUCT_TYPE_POP_DISPLAY = 2693;
    long PRODUCT_TYPE_POSTCARD = 2694;
    long PRODUCT_TYPE_POSTER = 2695;
    long PRODUCT_TYPE_PRESENTATION_FOLDER = 2696;
    long PRODUCT_TYPE_SCENT_STRIP = 2697;
    long PRODUCT_TYPE_SELF_MAILER = 2698;
    long PRODUCT_TYPE_SLIPSHEET = 2699;
    long PRODUCT_TYPE_TABS = 2700;
    long PRODUCT_TYPE_KIT = 2701;
    long PRODUCT_TYPE_OUTER_ENVELOPE = 2702;
    long PRODUCT_TYPE_BUSINESS_REPLY_ENVELOPE = 2703;
    long PRODUCT_TYPE_CRE = 2704;
    long PRODUCT_TYPE_LIFT_NOTE = 2705;
    long PRODUCT_TYPE_SAVINGS_CALCULATOR = 2706;
    long PRODUCT_TYPE_TAKE_ONE = 2707;
    long PRODUCT_TYPE_POST_IT_NOTE = 2708;
    long PRODUCT_TYPE_LABEL_INSERT = 2709;
    long WARNING_REPORT_TRUNCATED = 2710;
    long REPORT_TYPE_ORDER_STATUS = 2711;
    long REPORT_TYPE_ORDER_ACTIVITY = 2712;
    long REPORT_TYPE_COST_CENTER = 2713;
    long WORKGROUP_TAB_PRAG = 2714;
    long PRODUCT_TYPE_STICKER = 2715;
    long PRODUCT_TYPE_PLASTIC_CARD = 2716;
    long PRODUCT_TYPE_TEMP_CARD = 2717;
    long UNSPSC_TYPE_5510 = 2718;
    long ER_CREATE_ROLE_GROUP_FIRST = 2719;
    long PRODUCT_TYPE_BUSINESS_CARD = 2720;
    long PRODUCT_TYPE_E_CONTENT = 2721;
    long PRODUCT_TYPE_MAGAZINE = 2722;
    long INVALID_PROJECT_OWNER = 2723;
    long NO_PERMISSION_TO_OWN_PROJECT = 2724;
    long PRAG_ALREADY_EXISTING_MEMBERS = 2725;
    long UNSPSC_TYPE_BUSINESS_FORMS = 2726;
    long DEFAULT_PRAG_NAME_FOR_WG = 2727;
    long DEFAULT_PRAG_NAME_FOR_NON_WG = 2728;
    long SPEC_TITLE_DATA_ASOF = 2729;
    long WORKGROUP_ATTRIBUTE_ERROR_MESSAGE = 2730;
    long TM_TEMPLATE_INVALID_ROLE = 2731;
    long DM_ORDER_ITEM_PRODUCT_TYPE = 2732;
    long DM_ORDER_ITEM_UNSPSC = 2733;
    long DM_ORDER_ITEM_SUPPLIER_V_PRODUCT_TYPE = 2734;
    long DM_ORDER_ITEM_SUPPLIER_V_UNSPSC = 2735;
    long DM_SPEC_PRODUCT_TYPE = 2736;
    long DM_SPEC_UNSPSC = 2737;
    long WORKGROUP_TAB_WORKGROUP_PREF = 2738;
    long DM_ORDER_ITEM_AC_ATTR_PRODUCT_TYPE_ID = 2739;
    long DM_ORDER_ITEM_AC_ATTR_UNSPSC_TYPE_ID = 2740;
    long DM_ORDER_ITEM_SUPPLIER_V_AC_ATTR_PRODUCT_TYPE_ID = 2741;
    long DM_ORDER_ITEM_SUPPLIER_V_AC_ATTR_UNSPSC_TYPE_ID = 2742;
    long DM_SPEC_AC_ATTR_PRODUCT_TYPE_ID = 2743;
    long DM_SPEC_AC_ATTR_UNSPSC_TYPE_ID = 2744;
    long DM_PROJECT_PROJECT_CATEGORY = 2745;
    long DM_ORDER_BUYER_V_PROJECT_CATEGORY = 2746;
    long DM_ORDER_SUPPLIER_V_PROJECT_CATEGORY = 2747;
    long DM_RFE_BUYER_V_PROJECT_CATEGORY = 2748;
    long DM_RFE_SUPPLIER_V_PROJECT_CATEGORY = 2749;
    long DM_ORDER_BUYER_V_USER_DEFINED_PROJECT_STATUS = 2750;
    long DM_ORDER_SUPPLIER_V_USER_DEFINED_PROJECT_STATUS = 2751;
    long DM_ORDER_BUYER_V_OBJECT_STATE_ID = 2752;
    long DM_ORDER_BUYER_V_ACTIVATION_STATUS = 2753;
    long DM_ORDER_SUPPLIER_V_OBJECT_STATE_ID = 2754;
    long DM_ORDER_SUPPLIER_V_ACTIVATION_STATUS = 2755;
    long PRODUCT_TYPE_AD = 2756;
    long PRODUCT_TYPE_POINT_OF_SALE_KIT = 2757;
    long TR_TRACKING_TYPE_SCHEDULE_CREATED = 2758;
    long TR_TRACKING_TYPE_SCHEDULE_CREATED_DESC = 2759;
    long TR_TRACKING_TYPE_SCHEDULE_UPDATED = 2760;
    long TR_TRACKING_TYPE_SCHEDULE_UPDATED_DESC = 2761;
    long PRODUCT_TYPE_PHOTOGRAPH = 2762;
    long DEFAULT_ROLE_MUST_BE_SELECTED = 2763;
    long PR_UOM_MIL = 2764;
    long DM_RFE_BUYER_V_USER_DEFINED_PROJECT_STATUS = 2765;
    long DM_RFE_SUPPLIER_V_USER_DEFINED_PROJECT_STATUS = 2766;
    long DM_RFE_BUYER_V_OBJECT_STATE_ID = 2767;
    long DM_RFE_BUYER_V_ACTIVATION_STATUS = 2768;
    long DM_RFE_SUPPLIER_V_OBJECT_STATE_ID = 2769;
    long DM_RFE_SUPPLIER_V_ACTIVATION_STATUS = 2770;
    long SELECT_A_ROLE = 2771;
    long REPORT_TYPE_PROJECT_STATUS = 2772;
    long PRODUCT_TYPE_CD_ROM = 2773;
    long PRODUCT_TYPE_TAX_SHIPPING = 2774;
    long PRODUCT_TYPE_SHELF_TALKER = 2775;
    long PRODUCT_TYPE_RISER_CARD = 2776;
    long PRODUCT_TYPE_PAD = 2777;
    long PRODUCT_TYPE_POCKET_FOLDER = 2778;
    long PRODUCT_TYPE_AD_SLICKS_EXAMPLES = 2779;
    long PRODUCT_TYPE_FLIP_BOOK = 2780;
    long PRODUCT_TYPE_INSTRUCTION_SHEET = 2781;
    long PRODUCT_TYPE_NOTEPAD = 2782;
    long PRODUCT_TYPE_RADIO_SCRIPT = 2783;
    long PRODUCT_TYPE_SALES_SHEET = 2784;
    long PRODUCT_TYPE_TABBED_DIVIDERS = 2785;
    long PRODUCT_TYPE_DIE_CUT_ITEM = 2786;
    long REPORT_TYPE_PROJECT_PARTICIPATION = 2800;
    long SHIPMENT_REQUEST_TYPE_NORMAL = 2849;
    long SHIPMENT_REQUEST_TYPE_SAMPLE = 2850;
    long SHIPMENT_REQUEST_TYPE_PROOF = 2851;
    long DM_ORDER_BUYER_V_TITLE = 2852;
    long DM_ORDER_SUPPLIER_V_TITLE = 2853;
    long TM_INVITE_PERMISSION_CHANGED = 2854;
    long TM_UNINVITE_PERMISSION_CHANGED = 2855;
    long SH_BATCH_DELETE = 2856;
    long SH_BATCH_ADD_DELIVERY_LOCATION = 2857;
    long SH_CARRIER_ADD_NEW = 2858;
    long SH_CARRIER_ADD_DEFAULT = 2859;
    long SH_CARRIER_SELECT_CONSTRAINT = 2860;
    long BATCH_DELETE_TASK = 2861;
    long BATCH_EDIT_TASK = 2862;
    long DM_PERSON_USER_FULL_NAME = 2863;
    long DM_PERSON_INVITED_BY_FULL_NAME = 2864;
    long DM_PROJECT_PROJECT_AUTHOR_FULL_NAME = 2865;
    long DM_ORDER_BUYER_V_BUYER_FULL_NAME = 2866;
    long DM_ORDER_SUPPLIER_V_BUYER_FULL_NAME = 2867;
    long DM_ORDER_BUYER_V_SUPPLIER_FULL_NAME = 2868;
    long DM_ORDER_SUPPLIER_V_SUPPLIER_FULL_NAME = 2869;
    long DM_PROJECT_PARENT_PROJECT_AUTHOR_FUNAME = 2870;
    long DM_PROJECT_TEAM_MEMBER_TEAM_MEMBER_FULL_NAME = 2871;
    long DM_PROJECT_TRACKING_TRACKING_ENABLE_USER_FUNAME = 2872;
    long DM_TASK_ASSIGNED_BY_USER_FULL_NAME = 2873;
    long DM_TASK_ASSIGNED_TO_USER_FULL_NAME = 2874;
    long DM_TASK_CREATOR_FULL_NAME = 2875;
    long DM_TASK_CONTRIBUTOR_CONTRIBUTOR_FULL_NAME = 2876;
    long DM_TIMECARD_USER_FULL_NAME = 2877;
    long WORKGROUP_TAB_CLIENTS = 2878;
    long CLIENT_NOT_FOUND = 2879;
    long CLIENT_SELECT_CONSTRAINT = 2880;
    long DM_ORDER_BUYER_V_SUPPLIER_REFERENCE = 2881;
    long DM_ORDER_SUPPLIER_V_SUPPLIER_REFERENCE = 2882;
    long TR_TRACKING_TYPE_NAME_SEND_ESTIMATE_FAILED = 2883;
    long TR_TRACKING_TYPE_DESCRIPTION_SEND_ESTIMATE_FAILED = 2884;
    long RE_ROLE_PROJECT_TASK_OWNER = 2885;
    long CLIENT_OPTION_PREFIX = 2886;
    long SUPPLIER_SELECT_CONSTRAINT = 2887;
    long CONSTRAINT_ESTIMATING_TIME_NOT_BEGIN = 2888;
    long SUBMITTED_AFTER_TIME_EXPIRED = 2889;
    long CUSTOM_CONTROL_INT = 2890;
    long CUSTOM_CONTROL_TEXT = 2891;
    long CUSTOM_CONTROL_TEXTAREA = 2892;
    long CUSTOM_CONTROL_DROPDOWN = 2893;
    long CUSTOM_CONTROL_DATE = 2894;
    long CUSTOM_CONTROL_MONEY = 2895;
    long TR_TRACKING_TYPE_NAME_RFE_DATES_REVISED = 2896;
    long TR_TRACKING_TYPE_DESCRIPTION_RFE_DATES_REVISED = 2897;
    long DM_PROJECT_TEAM_MEMBER_ROLE_ID = 2898;
    long DM_PROJECT_TEAM_MEMBER_ROLE_NAME_STR_ID = 2899;
    long DM_PROJECT_TEAM_MEMBER_ROLE_NAME_STR = 2900;
    long DM_ACCOUNT_USER_ROLE_ID = 2901;
    long DM_ACCOUNT_USER_ROLE_NAME_STR_ID = 2902;
    long DM_ACCOUNT_USER_ROLE_NAME_STR = 2903;
    long TR_TRACKING_TYPE_ADD_NEW = 2904;
    long TR_TRACKING_TYPE_ADD_DEFAULT = 2905;
    long TR_TRACKING_SELECT_CONSTRAINT = 2906;
    long EXTEND_FORM_COST_CENTER = 2907;
    long EXTEND_FORM_PROJECT_HOME = 2908;
    long WORKGROUP_TAB_FORM_FIELDS = 2909;
    long CUSTOM_CONTROL_DATETIME = 2910;
    long CUSTOM_CONTROL_NUMBER = 2911;
    long CUSTOM_CONTROL_CHECKBOX = 2912;
    long COST_CENTER_ALLOCATION = 2913;
    long TR_TRACKING_TYPE_PROJECT_COST_CENTER_UPDATED = 2914;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_OUTSOURCING = 2915;
    long RP_DATA_CATEGORY_NAME_PROCUREMENT_OUTSOURCING_DESCN = 2916;
    long RP_DATA_FIELD_GROUP_OUTSOURCING_RFQ = 2917;
    long RP_DATA_FIELD_GROUP_OUTSOURCING_RFQ_ITEM = 2918;
    long RP_DATA_FIELD_GROUP_OUTSOURCING_QUOTE = 2919;
    long RP_DATA_FIELD_GROUP_OUTSOURCING_QUOTE_ITEM = 2920;
    long RP_DATA_FIELD_GROUP_OUTSOURCING_QUOTE_PRICING_OPTIONS = 2921;
    long DM_RFQ_RFQ_ID = 2922;
    long DM_RFQ_RFQ_REFERENCE = 2923;
    long DM_RFQ_RFQ_TITLE = 2924;
    long DM_RFQ_RFQ_DESCRIPTION = 2925;
    long DM_RFQ_RFQ_COMMENTS = 2926;
    long DM_RFQ_RFQ_SUBMIT_DATE = 2927;
    long DM_RFQ_RFQ_QUOTE_DUE_DATE = 2928;
    long DM_RFQ_RFQ_PROPOSED_COMPLETION_DATE = 2929;
    long DM_RFQ_BUYER_AC_WORKGROUP_ID = 2930;
    long DM_RFQ_RFQ_CLIENT_WORKGROUP_NAME  = 2931;
    long DM_RFQ_RFQ_CLIENT_PERSON_FIRST_NAME = 2932;
    long DM_RFQ_RFQ_CLIENT_PERSON_LAST_NAME = 2933;
    long DM_RFQ_OUTSOURCER_WORKGROUP_ID = 2934;
    long DM_RFQ_RFQ_OUTSOURCER_WORKGROUP_NAME = 2935;
    long DM_RFQ_OBJECT_STATE_ID = 2936;
    long DM_RFQ_RFQ_DESCRIPTION_STR_ID = 2937;
    long DM_RFQ_PROJECT_ID = 2938;
    long DM_RFQ_ITEM_RFQ_ITEM_ID = 2939;
    long DM_RFQ_ITEM_RFQ_ID = 2940;
    long DM_RFQ_ITEM_SPEC_ID = 2941;
    long DM_RFQ_ITEM_RFQ_SPEC_NAME = 2942;
    long DM_RFQ_ITEM_QUANTITY_1 = 2943;
    long DM_RFQ_ITEM_QUANTITY_2 = 2944;
    long DM_RFQ_ITEM_QUANTITY_3 = 2945;
    long DM_RFQ_ITEM_QUANTITY_4 = 2946;
    long DM_RFQ_ITEM_QUANTITY_5 = 2947;
    long DM_QUOTE_QUOTE_ID = 2948;
    long DM_QUOTE_RFQ_ID = 2949;
    long DM_QUOTE_QUOTE_REFERENCE = 2950;
    long DM_QUOTE_QUOTE_TITLE = 2951;
    long DM_QUOTE_QUOTE_COMPLETION_DATE = 2952;
    long DM_QUOTE_QUOTE_DESCRIPTION = 2953;
    long DM_QUOTE_OUTSOURCER_WORKGROUP_ID = 2954;
    long DM_QUOTE_QUOTE_OUTSOURCER_WORKGROUP_NM = 2955;
    long DM_QUOTE_CLIENT_WORKGROUP_ID = 2956;
    long DM_QUOTE_QUOTE_CLIENT_WORKGROUP_NAME = 2957;
    long DM_QUOTE_OUTSOURCER_USER_ID = 2958;
    long DM_QUOTE_QUOTE_PERSON_FIRST_NAME = 2959;
    long DM_QUOTE_QUOTE_PERSON_LAST_NAME = 2960;
    long DM_QUOTE_STATUS_ID = 2961;
    long DM_QUOTE_QUOTE_DESCRIPTION_STR_ID = 2962;
    long DM_QUOTE_PROJECT_ID = 2963;
    long DM_QUOTE_ITEM_QUOTE_ITEM_ID = 2964;
    long DM_QUOTE_ITEM_QUOTE_ID = 2965;
    long DM_QUOTE_ITEM_SPEC_ID = 2966;
    long DM_QUOTE_ITEM_QUOTE_ITEM_SPEC_NAME = 2967;
    long DM_QUOTE_ITEM_IS_PRE_MARKUP_VISIBLE  = 2968;
    long DM_QUOTE_ITEM_QUOTE_ITEM_COMPLETION_DATE = 2969;
    long DM_QUOTE_ITEM_QUOTE_ITEM_COMMENTS = 2970;
    long DM_QUOTE_PRICE_QUOTE_PRICE_ID = 2971;
    long DM_QUOTE_PRICE_QUOTE_ITEM_ID = 2972;
    long DM_QUOTE_PRICE_QUOTE_PRICE_IS_CHOSEN  = 2973;
    long DM_QUOTE_PRICE_IS_VISIBLE_TO_BUYER = 2974;
    long DM_QUOTE_PRICE_QUOTE_PRICE_QUANTITY = 2975;
    long DM_QUOTE_PRICE_QUOTE_PRICE_ESTIMATED_PRICE = 2976;
    long DM_QUOTE_PRICE_QUOTE_PRICE_PRE_MARKUP = 2977;
    long DM_QUOTE_PRICE_QUOTE_PRICE_MARKUP_PERCENT = 2978;
    long DM_QUOTE_PRICE_MARKUP_PERCENT_AMOUNT = 2979;
    long DM_QUOTE_PRICE_QUOTE_PRICE_MARKUP_FIXED = 2980;
    long DM_QUOTE_PRICE_QUOTE_PRICE_PRICE = 2981;
    long DM_QUOTE_PRICE_EM_ESTIMATE_ITEM_PRICE_ID = 2982;
    long DM_QUOTE_PRICE_QUOTE_PRICE_SUPPLIER_NAME = 2982;
    long DM_QUOTE_PRICE_QUOTE_PRICE_SUPPLIER_CODE = 2983;
    long DM_QUOTE_PRICE_QUOTE_PRICE_SUPPLIER_ALIAS = 2984;
    long BATCH_TASK_ERROR_NOT_READY_TO_START = 2985;
    long BATCH_TASK_ERROR_NOT_READY_TO_COMPLETE = 2986;
    long CONSTRAINT_SEND_ESTIMATE_PERMISSION = 2987;
    long CONSTRAINT_EDIT_ESTIMATE_PERMISSION = 2988;
    long DM_WHERE_USED_REPORT = 2989;
    long DM_WUR_SPEC_NAME = 2991;
    long DM_WUR_PROJECT = 2992;
    long DM_WUR_DIRECTMAIL_SPEC = 2993;
    long DM_WUR_DIRECTMAIL_PROJECT = 2994;
    long DM_WUR_CELL_NO = 2995;
    long DM_WUR_QUANTITY = 2996;
    long DM_WUR_DROP_DATE = 2997;
    long SPEC_NAME_DIRECTMAIL_MATRIX = 2998;
    long AUTOMATIC_OPENBID_EXTENSION_CONSTRAINT = 2999;
    long UNAPPROVED_SUPPLIER_CONSTRAINT = 3000;
    long CM_LIVE_CONTACT_INACTIVE = 3001;
    long RFQ = 3002;
    long QUOTE = 3003;
    long UOFM_GROUP_CHANGE_CONSTRAINT = 3004;
    long CM_FILTER_THEIR_WORKGROUP = 3005;
    long CM_FILTER_OUTSIDE_THEIR_WORKGROUP = 3006;
    long DEACTIVATION_REASON_DELIVERED = 3007;
    long PRODUCT_TYPE_AUDIO_VISUAL_CLEARED_FOR_BROADCAST = 3008;
    long PRODUCT_TYPE_AUDIO_VISUAL_NOT_CLEARED_FOR_BROADCAST = 3009;
    long PRODUCT_TYPE_AUDIOCASSETTE = 3010;
    long PRODUCT_TYPE_BADGE_BADGE_HOLDER = 3011;
    long PRODUCT_TYPE_BANNER_FLAG = 3012;
    long PRODUCT_TYPE_BOOK_TEXTBOOK_MANUAL = 3013;
    long PRODUCT_TYPE_BOOKMARK = 3014;
    long PRODUCT_TYPE_DVD = 3015;
    long PRODUCT_TYPE_EXHIBIT = 3016;
    long PRODUCT_TYPE_FOLDER = 3017;
    long PRODUCT_TYPE_FORMS_RECEIPTS = 3018;
    long PRODUCT_TYPE_LEAFLET_STUFFER = 3019;
    long PRODUCT_TYPE_MISCELLANEOUS = 3020;
    long PRODUCT_TYPE_REPRO_SHEETS = 3021;
    long PRODUCT_TYPE_SIGN_BILLBOARD = 3022;
    long PRODUCT_TYPE_SLIDES = 3023;
    long PRODUCT_TYPE_SPECIALTY_ITEM = 3024;
    long PRODUCT_TYPE_STATEMENT = 3025;
    long DMM_JOB_NUMBER = 3026;
    long DMM_LINE_OF_BUSINESS = 3027;
    long DMM_PRODUCT_CELL = 3028;
    long DMM_PACKAGE_NAME = 3029;
    long DMM_INLINE = 3030;
    long DMM_ESTIMATED_QUANTITY = 3031;
    long DMM_PRINT_QUANTITY = 3032;
    long DMM_ACTUAL_QUANTITY = 3033;
    long DMM_FORM = 3034;
    long DMM_OE = 3035;
    long DMM_BRE = 3036;
    long DMM_INSERT1 = 3037;
    long DMM_INSERT2 = 3038;
    long DMM_INSERT3 = 3039;
    long DMM_INSERT4 = 3040;
    long DMM_INSERT5 = 3041;
    long DMM_STICKER_AFFIX = 3042;
    long DMM_STICKER_INSERT = 3043;
    long DMM_CARD = 3044;
    long DMM_LASER_CODE = 3045;
    long DMM_CLASS = 3046;
    long DMM_POSTAGE_TYPE = 3047;
    long DMM_DROP_DATE = 3048;
    long DMM_EXPIRATION_DATE = 3049;
    long DMM_WHITE_PAPER_PROOF_REQUESTED = 3050;
    long DMM_LASER_SIGNOFF_REQUIRED = 3051;
    long DMM_SAMPLES_REQUIRED = 3052;
    long DMM_OE_VENDOR = 3053;
    long DMM_OE_PRESS_VENDOR = 3054;
    long DMM_BRE_VENDOR = 3055;
    long DMM_BRE_PREPRESS_VENDOR = 3056;
    long DMM_INSERT1_VENDOR = 3057;
    long DMM_INSERT1_PREPRESS_VENDOR = 3058;
    long DMM_INSERT2_VENDOR = 3059;
    long DMM_INSERT2_PREPRESS_VENDOR = 3060;
    long DMM_INSERT3_VENDOR = 3061;
    long DMM_INSERT3_PREPRESS_VENDOR = 3062;
    long DMM_INSERT4_VENDOR = 3063;
    long DMM_INSERT4_PREPRESS_VENDOR = 3064;
    long DMM_INSERT5_VENDOR = 3065;
    long DMM_INSERT5_PREPRESS_VENDOR = 3066;
    long DMM_STICKER_AFFIXED_VENDOR = 3067;
    long DMM_STICKER_AFFIXED_PREPRESS_VENDOR = 3068;
    long DMM_STICKER_INSERT_VENDOR = 3069;
    long DMM_STICKER_INSERT_PREPRESS_VENDOR = 3070;
    long DMM_CARD_VENDOR = 3071;
    long DMM_CARD_PREPRESS_VENDOR = 3072;
    long DMM_FORM_VENDOR = 3073;
    long DMM_FORM_PREPRESS_VENDOR = 3074;
    long DMM_LASER_VENDOR = 3075;
    long DMM_IMAGING_FORM_1 = 3076;
    long DMM_IMAGING_FORM_2 = 3077;
    long DMM_IMAGING_FORM_3 = 3078;
    long DMM_DIRECT_MAIL_MATRIX = 3079;
    long DMM_YES = 3080;
    long DMM_NO = 3081;
    long PERM_INFO_CREATE_ACCOUNT_USER = 3082;
    long PERM_INFO_EDIT_ACCOUNT_USER = 3083;
    long PERM_INFO_VIEW_ACCOUNT_USER = 3084;
    long PERM_INFO_MANAGE_ADDITIONAL_UNIT_PRICE = 3085;
    long PERM_INFO_RUN_ALL_ACTIVITY_REPORT = 3086;
    long PERM_INFO_MANAGE_APPROVED_SUPPLIER = 3087;
    long PERM_INFO_MANAGE_CLIENT = 3088;
    long PERM_INFO_CREATE_COMPANY = 3089;
    long PERM_INFO_EDIT_COMPANY = 3090;
    long PERM_INFO_VIEW_COMPANY = 3091;
    long PERM_INFO_MANAGE_COST_CENTER_ADMIN = 3092;
    long PERM_INFO_VIEW_COST_CENTER_ADMIN = 3093;
    long PERM_INFO_RUN_COST_CENTER_REPORT = 3094;
    long PERM_INFO_VIEW_CUSTOM_FIELD = 3095;
    long PERM_INFO_MANAGE_CUSTOM_FIELD = 3096;
    long PERM_INFO_RUN_ESTIMATE_ANALYSIS_REPORT = 3097;
    long PERM_INFO_RUN_ESTIMATE_WINLOSS_REPORT = 3098;
    long PERM_INFO_RUN_LIMITED_ACTIVITY_REPORT = 3099;
    long PERM_INFO_CREATE_NEW_PROJECT = 3100;
    long PERM_INFO_RUN_ORDER_ACTIVITY_REPORT = 3101;
    long PERM_INFO_RUN_ORDER_STATUS_REPORT = 3102;
    long PERM_INFO_RUN_OUTSOURCE_PROFIT_REPORT = 3103;
    long PERM_INFO_VIEW_PERSONAL_PROJECTS = 3104;
    long PERM_INFO_VIEW_PRICE_BREAKOUT_TYPE = 3105;
    long PERM_INFO_MANAGE_PRICE_BREAKOUT_TYPE = 3106;
    long PERM_INFO_RUN_PRODUCTION_MATRIX_REPORT = 3107;
    long PERM_INFO_VIEW_PROJECT_CATEGORY = 3108;
    long PERM_INFO_MANAGE_PROJECT_CATEGORY = 3109;
    long PERM_INFO_RUN_PROJECT_PARTICIPATION_REPORT = 3110;
    long PERM_INFO_VIEW_PROJECT_STATUS_OPTIONS = 3111;
    long PERM_INFO_CREATE_PROJECT_STATUS_OPTIONS = 3112;
    long PERM_INFO_EDIT_PROJECT_STATUS_OPTIONS = 3113;
    long PERM_INFO_DELETE_PROJECT_STATUS_OPTIONS = 3114;
    long PERM_INFO_RUN_PROJECT_STATUS_REPORT = 3115;
    long PERM_INFO_VIEW_REPORT_ABOUT_ENTIRE_WORKGROUP = 3116;
    long PERM_INFO_CREATE_REPORT_RESULT = 3117;
    long PERM_INFO_EDIT_REPORT_RESULT = 3118;
    long PERM_INFO_VIEW_REPORT_RESULT = 3119;
    long PERM_INFO_DELETE_REPORT_RESULT = 3120;
    long PERM_INFO_CREATE_REPORT_SPEC = 3121;
    long PERM_INFO_EDIT_REPORT_SPEC = 3122;
    long PERM_INFO_VIEW_REPORT_SPEC = 3123;
    long PERM_INFO_DELETE_REPORT_SPEC = 3124;
    long PERM_INFO_MANAGE_ROLE_GROUP = 3125;
    long PERM_INFO_VIEW_SHARED_CONTACT = 3126;
    long PERM_INFO_EDIT_SHARED_CONTACT = 3127;
    long PERM_INFO_DELETE_SHARED_CONTACT = 3128;
    long PERM_INFO_CREATE_SHARED_CONTACT = 3129;
    long PERM_INFO_CREATE_SHARED_CONTACT_CATEGORY = 3130;
    long PERM_INFO_VIEW_SHARED_CONTACT_CATEGORY = 3131;
    long PERM_INFO_EDIT_SHARED_CONTACT_CATEGORY = 3132;
    long PERM_INFO_DELETE_SHARED_CONTACT_CATEGORY = 3133;
    long PERM_INFO_MANAGE_SHIPMENT_CARRIER = 3134;
    long PERM_INFO_VIEW_SHIPMENT_CARRIER = 3135;
    long PERM_INFO_MANAGE_SPEC_REFERENCE_COUNTER = 3136;
    long PERM_INFO_VIEW_SPEC_TEMPLATE = 3137;
    long PERM_INFO_MANAGE_SPEC_TEMPLATE = 3138;
    long PERM_INFO_EDIT_SUBMITTED_TIMECARD = 3139;
    long PERM_INFO_VIEW_SUPPLIER_RATING_ADMIN = 3140;
    long PERM_INFO_MANAGE_SUPPLIER_RATING_ADMIN = 3141;
    long PERM_INFO_RUN_SUPPLIER_RATING_REPORT = 3142;
    long PERM_INFO_MANAGE_TASK_STATUS = 3143;
    long PERM_INFO_VIEW_TASK_STATUS = 3144;
    long PERM_INFO_VIEW_TASK_TEMPLATE = 3145;
    long PERM_INFO_MANAGE_TASK_TEMPLATE = 3146;
    long PERM_INFO_MANAGE_TASK_TYPE = 3147;
    long PERM_INFO_VIEW_TASK_TYPE = 3148;
    long PERM_INFO_VIEW_TERMS_AND_CONDITIONS = 3149;
    long PERM_INFO_EDIT_TERMS_AND_CONDITIONS = 3150;
    long PERM_INFO_CREATE_TIMECARD = 3151;
    long PERM_INFO_MANAGE_TRACKING_TYPE = 3152;
    long PERM_INFO_RUN_WHERE_USED_REPORT = 3153;
    long PERM_INFO_CREATE_WORKGROUP = 3154;
    long PERM_INFO_EDIT_WORKGROUP = 3155;
    long PERM_INFO_VIEW_WORKGROUP = 3156;
    long PERM_INFO_MANAGE_WORKGROUP_ATTRIBUTES = 3157;
    long PERM_INFO_VIEW_WORKGROUP_PROJECTS = 3158;
    long PERM_INFO_CREATE_CHANGE_ORDER = 3159;
    long PERM_INFO_EDIT_CHANGE_ORDER = 3160;
    long PERM_INFO_VIEW_CHANGE_ORDER = 3161;
    long PERM_INFO_ACCEPT_CHANGE_ORDER = 3162;
    long PERM_INFO_REJECT_CHANGE_ORDER = 3163;
    long PERM_INFO_CANCEL_CHANGE_ORDER = 3164;
    long PERM_INFO_RETRACT_CHANGE_ORDER = 3165;
    long PERM_INFO_SHARE_COLLABORATION_OBJECT = 3166;
    long PERM_INFO_CREATE_ESTIMATE = 3167;
    long PERM_INFO_EDIT_ESTIMATE = 3168;
    long PERM_INFO_VIEW_ESTIMATE = 3169;
    long PERM_INFO_DELETE_ESTIMATE = 3170;
    long PERM_INFO_SEND_ESTIMATE = 3171;
    long PERM_INFO_ACCEPT_ESTIMATE = 3172;
    long PERM_INFO_REJECT_ESTIMATE = 3173;
    long PERM_INFO_CREATE_FILE = 3174;
    long PERM_INFO_EDIT_FILE = 3175;
    long PERM_INFO_VIEW_FILE = 3176;
    long PERM_INFO_DELETE_FILE = 3177;
    long PERM_INFO_ANNOTATE_FILE = 3178;
    long PERM_INFO_CREATE_MESSAGE = 3179;
    long PERM_INFO_EDIT_MESSAGE = 3180;
    long PERM_INFO_VIEW_MESSAGE = 3181;
    long PERM_INFO_DELETE_MESSAGE = 3182;
    long PERM_INFO_RECEIVE_NOTIFICATION_CHANGE_ORDER_ACCEPTED = 3183;
    long PERM_INFO_RECEIVE_NOTIFICATION_CHANGE_ORDER_CANCELED = 3184;
    long PERM_INFO_RECEIVE_NOTIFICATION_CHANGE_ORDER_CREATED = 3185;
    long PERM_INFO_RECEIVE_NOTIFICATION_CHANGE_ORDER_REJECTED = 3186;
    long PERM_INFO_RECEIVE_NOTIFICATION_CHANGE_ORDER_RETRACTED = 3187;
    long PERM_INFO_RECEIVE_NOTIFICATION_CHANGE_ORDER_UPDATED = 3188;
    long PERM_INFO_RECEIVE_NOTIFICATION_CUSTOM_EVENT_POSTED = 3189;
    long PERM_INFO_RECEIVE_NOTIFICATION_ESTIMATE_RECEIVED = 3190;
    long PERM_INFO_RECEIVE_NOTIFICATION_ESTIMATE_REJECTED = 3191;
    long PERM_INFO_RECEIVE_NOTIFICATION_ORDER_ACCEPTED = 3192;
    long PERM_INFO_RECEIVE_NOTIFICATION_ORDER_CANCELED = 3193;
    long PERM_INFO_RECEIVE_NOTIFICATION_ORDER_CREATED = 3194;
    long PERM_INFO_RECEIVE_NOTIFICATION_ORDER_REJECTED = 3195;
    long PERM_INFO_RECEIVE_NOTIFICATION_ORDER_RETRACTED = 3196;
    long PERM_INFO_RECEIVE_NOTIFICATION_ORDER_STATUS_CHANGED = 3197;
    long PERM_INFO_RECEIVE_NOTIFICATION_ORDER_UPDATED = 3198;
    long PERM_INFO_RECEIVE_NOTIFICATION_PROJECT_DEACTIVATED = 3199;
    long PERM_INFO_RECEIVE_NOTIFICATION_PROJECT_DELETED = 3200;
    long PERM_INFO_RECEIVE_NOTIFICATION_PROJECT_REACTIVATED = 3201;
    long PERM_INFO_RECEIVE_NOTIFICATION_PROJECT_STATUS_UPDATED = 3202;
    long PERM_INFO_RECEIVE_NOTIFICATION_RFE_REJECTED = 3203;
    long PERM_INFO_RECEIVE_NOTIFICATION_RFE_SENT = 3204;
    long PERM_INFO_RECEIVE_NOTIFICATION_SUPPLIER_DISMISSED = 3205;
    long PERM_INFO_EDIT_PROJECT = 3206;
    long PERM_INFO_DELETE_PROJECT = 3207;
    long PERM_INFO_ATTACH_PROJECT = 3208;
    long PERM_INFO_DETACH_PROJECT = 3209;
    long PERM_INFO_COPY_PROJECT = 3210;
    long PERM_INFO_TRANSFER_PROJECT = 3211;
    long PERM_INFO_CREATE_SPEC = 3212;
    long PERM_INFO_EDIT_SPEC = 3213;
    long PERM_INFO_VIEW_SPEC = 3214;
    long PERM_INFO_DELETE_SPEC = 3215;
    long PERM_INFO_SHARE_SPEC = 3216;
    long PERM_INFO_CREATE_ORDER = 3217;
    long PERM_INFO_EDIT_ORDER = 3218;
    long PERM_INFO_VIEW_ORDER = 3219;
    long PERM_INFO_ACCEPT_ORDER = 3220;
    long PERM_INFO_REJECT_ORDER = 3221;
    long PERM_INFO_CANCEL_ORDER = 3222;
    long PERM_INFO_RETRACT_ORDER = 3223;
    long PERM_INFO_MARK_ORDER_ACCEPTED = 3224;
    long PERM_INFO_MARK_ORDER_SHIPPED = 3225;
    long PERM_INFO_MARK_ORDER_DELIVERED = 3226;
    long PERM_INFO_MARK_ORDER_COMPLETED = 3227;
    long PERM_INFO_ADD_ORDER_COST_CENTER = 3228;
    long PERM_INFO_EDIT_ORDER_COST_CENTER = 3229;
    long PERM_INFO_VIEW_ORDER_COST_CENTER = 3230;
    long PERM_INFO_VIEW_PREMARKUP_QUOTE = 3231;
    long PERM_INFO_EDIT_PROJECT_ACTIVATION = 3232;
    long PERM_INFO_ASSIGN_PROJECT_CATEGORY_FOR_PROJECT = 3233;
    long PERM_INFO_ADD_PROJECT_COST_CENTER = 3234;
    long PERM_INFO_VIEW_PROJECT_COST_CENTER = 3235;
    long PERM_INFO_EDIT_PROJECT_OVERVIEW = 3236;
    long PERM_INFO_EDIT_PROJECT_STATUS = 3237;
    long PERM_INFO_CREATE_PURCHASE_REQUEST = 3238;
    long PERM_INFO_EDIT_PURCHASE_REQUEST = 3239;
    long PERM_INFO_VIEW_PURCHASE_REQUEST = 3240;
    long PERM_INFO_APPROVE_PURCHASE_REQUEST = 3241;
    long PERM_INFO_SEND_QUOTE = 3242;
    long PERM_INFO_VIEW_QUOTE = 3243;
    long PERM_INFO_REVISE_QUOTE = 3244;
    long PERM_INFO_REJECT_QUOTE = 3245;
    long PERM_INFO_ACCEPT_QUOTE = 3246;
    long PERM_INFO_RETRACT_QUOTE = 3247;
    long PERM_INFO_CREATE_RFE = 3248;
    long PERM_INFO_EDIT_RFE = 3249;
    long PERM_INFO_VIEW_RFE = 3250;
    long PERM_INFO_DELETE_RFE = 3251;
    long PERM_INFO_SEND_RFE = 3252;
    long PERM_INFO_REJECT_RFE = 3253;
    long PERM_INFO_CLOSE_RFE = 3254;
    long PERM_INFO_CANCEL_RFE = 3255;
    long PERM_INFO_RESPOND_RFE = 3256;
    long PERM_INFO_CREATE_OPEN_BID_RFE = 3257;
    long PERM_INFO_VIEW_RFQ = 3258;
    long PERM_INFO_SEND_RFQ = 3259;
    long PERM_INFO_CLOSE_RFQ = 3260;
    long PERM_INFO_MANAGE_SHIPMENT = 3261;
    long PERM_INFO_UPDATE_SHIPMENT_ACTUAL = 3262;
    long PERM_INFO_UPDATE_SHIPMENT_RECEIVED = 3263;
    long PERM_INFO_UPDATE_SHIPMENT_REQUESTED = 3264;
    long PERM_INFO_CREATE_SPEC_FROM_TEMPLATE = 3265;
    long PERM_INFO_DISMISS_SUPPLIER = 3266;
    long PERM_INFO_CREATE_SUPPLIER_RATING = 3267;
    long PERM_INFO_EDIT_SUPPLIER_RATING = 3268;
    long PERM_INFO_VIEW_SUPPLIER_RATING = 3269;
    long PERM_INFO_CREATE_TASK = 3270;
    long PERM_INFO_EDIT_TASK = 3271;
    long PERM_INFO_VIEW_TASK = 3272;
    long PERM_INFO_DELETE_TASK = 3273;
    long PERM_INFO_MANAGE_TASK_PLAN = 3274;
    long PERM_INFO_VIEW_TASK_PLAN = 3275;
    long PERM_INFO_SHARE_TASK_PLAN = 3276;
    long PERM_INFO_APPLY_TASK_TEMPLATE_IN_PROJECT = 3277;
    long PERM_INFO_CREATE_TEAM_MEMBER = 3278;
    long PERM_INFO_EDIT_TEAM_MEMBER = 3279;
    long PERM_INFO_VIEW_TEAM_MEMBER = 3280;
    long PERM_INFO_DELETE_TEAM_MEMBER = 3281;
    long PERM_INFO_INVITE_TEAM_MEMBER = 3282;
    long PERM_INFO_CREATE_TRACKING_ITEM = 3283;
    long PERM_INFO_VIEW_TRACKING_ITEM = 3284;
    long PERM_INFO_DELETE_TRACKING_ITEM = 3285;
    long PERM_INFO_UNUSED_PERMISSION = 3286;
    long PERM_INFO_INTERNAL_PERMISSION = 3287;
    long DM_RFE_BUYER_V_BID_TYPE_ID = 3288;
    long DM_RFE_BUYER_V_NAME_STR_ID = 3289;
    long DM_RFE_SUPPLIER_V_BID_TYPE_ID = 3290;
    long DM_RFE_SUPPLIER_V_NAME_STR_ID = 3291;
    long TR_TRACKING_TYPE_PROJECT_COST_CENTER_UPDATED_DESCR = 3292;
    long SP_REG_TEMPLATE_FILE_NAME = 3293;
    long PRODUCT_TYPE_DISKETTES = 3294;
    long PRODUCT_TYPE_VIDEO = 3295;
    long CUSTOM_FIELD_DUPLICATE_LABEL = 3296;
    long UNIQUE_PROJECT_NAME_CONSTRAINT = 3297;
    long CUSTOM_FIELD_INVALID_CHARACTER = 3298;
    long DM_SPEC_SPEC_REFERENCE_ID = 3299;
    long DM_ORDER_ITEM_SPEC_REFERENCE_ID = 3300;
    long DM_ORDER_ITEM_SUPPLIER_V_SPEC_REFERENCE_ID = 3301;
    long MULTI_CLICK_MSG = 3302;
    long TR_TRACKING_TYPE_SPEC_ITEM_CREATED_DESCR = 3303;
    long TR_TRACKING_TYPE_SPEC_ITEM_CREATED = 3304;
    long TR_TRACKING_TYPE_SPEC_ITEM_EDITED_DESCR = 3305;
    long TR_TRACKING_TYPE_SPEC_ITEM_EDITED = 3306;
    long PRODUCT_TYPE_LASER = 3307;
    long CLIENT_EXIST_CONSTRAINT = 3308;
    long DUPLICATE_ATTRIBUTE = 3309;
    long DM_ORDER_SPEC1_EXT_PROPERTY_ID = 3310;
    long DM_PROJECT_TRACKING_TRACKING_DESCRIPTION = 3311;
    long DM_TASK_SCHEDULE_CODE = 3312;
    long NO_SPECS_SELECTED = 3313;
    long DM_SPEC_OBJECT_STATE_ID = 3314;
    long DM_SPEC_DESCRIPTION_STR_ID = 3315;
    long DM_ORDER_BUYER_V_SUPPLIER_CODE = 3316;
    long DM_ORDER_SUPPLIER_V_SUPPLIER_CODE = 3317;
    long DM_ESTIMATE_ESTIMATE_SUPPLIER_CODE = 3318;
    long DM_ESTIMATE_SUPPLIER_V_ESTIMATE_SUPPLIER_CODE = 3319;
    long PR_UOM_BLANK = 3320;
    long TEMPLATE_OF = 3321;
    long COPY_OF = 3322;
    long NOTIFICATION_TYPE_CLOSING_CHANGE_ORDER_CREATED = 3323;
    long NOTIFICATION_TYPE_CLOSING_CHANGE_ORDER_UPDATED = 3324;
    long NOTIFICATION_TYPE_CLOSING_CHANGE_ORDER_ACCEPTED = 3325;
    long NOTIFICATION_TYPE_CLOSING_CHANGE_ORDER_CANCELLED = 3326;
    long NOTIFICATION_TYPE_CLOSING_CHANGE_ORDER_REJECTED = 3327;
    long NOTIFICATION_TYPE_CLOSING_CHANGE_ORDER_RETRACTED = 3328;
    long NOTIFICATION_TYPE_RFE_ACCEPTED = 3329;
    long PROJECT_BUDGET = 3500;
    long PROJECT_BUDGET_CATEGORY = 3501;
    long PRODUCTION_MATRIX = 3502;
    long PO_PROJECT_NAME = 3503;
    long PO_PROJECT_CATEGORY = 3504;
    long PO_PROJECT_ID = 3505;
    long PO_PROJECT_NUMBER = 3506;
    long PO_OWNER_WORKGROUP = 3507;
    long PO_CLIENT_ACCOUNT = 3508;
    long PO_PROJECT_CREATE_DATE = 3509;
    long PO_PROJECT_COMPLETION_DATE = 3510;
    long PO_DESCRIPTION = 3511;
    long PO_COMMENTS = 3512;
    long PO_PROJECT_STATE = 3513;
    long PO_PROJECT_STATUS = 3514;
    long CUSTOM_VIEW_PREFIX_PO = 3515;
    long CUSTOM_VIEW_PREFIX_MI = 3516;
    long PERM_INFO_VIEW_BUDGET_CATEGORY = 3520;
    long PERM_INFO_EDIT_BUDGET_CATEGORY = 3521;
    long PERM_INFO_VIEW_PROJECT_BUDGET = 3522;
    long PERM_INFO_EDIT_PROJECT_BUDGET = 3523;
    long SHIPMENT_DEFAULT_NAME = 3524;
    long SHIPMENT_FOR_CURRENT_PROJECT = 3525;
    long SHIPMENT_FOR_CURRENT_N_SUB_PROJECT = 3526;
    long OBJECT_STATE_DESC_SHIPMENT_NOT_SHIPPED = 3527;
    long OBJECT_STATE_DESC_SHIPMENT_PARTIALLY_SHIPPED = 3528;
    long OBJECT_STATE_DESC_SHIPMENT_SHIPPED = 3529;
    long OBJECT_STATE_DESC_SHIPMENT_DELIVERED = 3530;
    long INCOMPATIBLE_SHIPMENT_STATUS_INFO = 3531;
    long SHIPMENT_NOT_FOUND = 3532;
    long SH_BATCH_VIEW_SHIPMENTS_DETAIL = 3533;
    long PROJECT_COST = 3534;
    long UNCLASSIFIED = 3535;
    long TRACKING_TYPE_PROJECT_BUDGET_UPDATED = 3536;
    long NOTIFICATION_TYPE_COST_EXCEED_BUDGET = 3537;
    long WORKGROUP_TAB_SHIPPING_METHOD = 3538;
    long SH_METHOD_ADD_NEW = 3539;
    long SH_METHOD_ADD_DEFAULT = 3540;
    long SH_METHOD_SELECT_CONSTRAINT = 3541;
    long PROJECT_COST_ITEM = 3542;
    long SH_BATCH_COPY = 3543;
    long SH_BATCH_UPDATE_REQUESTS = 3544;
    long SH_BATCH_CHANGE_DELIVERY_LOCATIONS = 3545;
    long SH_BATCH_DELETE_REQUESTS = 3546;
    long SH_BATCH_UPLOAD_FILES = 3547;
    long SH_BATCH_CHANGE_ADDRESS_SOURCE_CONSTRAINT = 3548;
    long SH_BATCH_CHANGE_ADDRESS_SOURCE_REQUIRED = 3549;
    long AC_COUNTRY_NAME_AF = 4000;
    long AC_COUNTRY_NAME_AL = 4001;
    long AC_COUNTRY_NAME_DZ = 4002;
    long AC_COUNTRY_NAME_AS = 4003;
    long AC_COUNTRY_NAME_AD = 4004;
    long AC_COUNTRY_NAME_AO = 4005;
    long AC_COUNTRY_NAME_AI = 4006;
    long AC_COUNTRY_NAME_AQ = 4007;
    long AC_COUNTRY_NAME_AG = 4008;
    long AC_COUNTRY_NAME_AR = 4009;
    long AC_COUNTRY_NAME_AM = 4010;
    long AC_COUNTRY_NAME_AW = 4011;
    long AC_COUNTRY_NAME_AU = 4012;
    long AC_COUNTRY_NAME_AT = 4013;
    long AC_COUNTRY_NAME_AZ = 4014;
    long AC_COUNTRY_NAME_BS = 4015;
    long AC_COUNTRY_NAME_BH = 4016;
    long AC_COUNTRY_NAME_BD = 4017;
    long AC_COUNTRY_NAME_BB = 4018;
    long AC_COUNTRY_NAME_BY = 4019;
    long AC_COUNTRY_NAME_BE = 4020;
    long AC_COUNTRY_NAME_BZ = 4021;
    long AC_COUNTRY_NAME_BJ = 4022;
    long AC_COUNTRY_NAME_BM = 4023;
    long AC_COUNTRY_NAME_BT = 4024;
    long AC_COUNTRY_NAME_BO = 4025;
    long AC_COUNTRY_NAME_BA = 4026;
    long AC_COUNTRY_NAME_BW = 4027;
    long AC_COUNTRY_NAME_BV = 4028;
    long AC_COUNTRY_NAME_BR = 4029;
    long AC_COUNTRY_NAME_IO = 4030;
    long AC_COUNTRY_NAME_BN = 4031;
    long AC_COUNTRY_NAME_BG = 4032;
    long AC_COUNTRY_NAME_BF = 4033;
    long AC_COUNTRY_NAME_BI = 4034;
    long AC_COUNTRY_NAME_KH = 4035;
    long AC_COUNTRY_NAME_CM = 4036;
    long AC_COUNTRY_NAME_CA = 4037;
    long AC_COUNTRY_NAME_CV = 4038;
    long AC_COUNTRY_NAME_KY = 4039;
    long AC_COUNTRY_NAME_CF = 4040;
    long AC_COUNTRY_NAME_TD = 4041;
    long AC_COUNTRY_NAME_CL = 4042;
    long AC_COUNTRY_NAME_CN = 4043;
    long AC_COUNTRY_NAME_CX = 4044;
    long AC_COUNTRY_NAME_CC = 4045;
    long AC_COUNTRY_NAME_CO = 4046;
    long AC_COUNTRY_NAME_KM = 4047;
    long AC_COUNTRY_NAME_CD = 4048;
    long AC_COUNTRY_NAME_CG = 4049;
    long AC_COUNTRY_NAME_CK = 4050;
    long AC_COUNTRY_NAME_CR = 4051;
    long AC_COUNTRY_NAME_CI = 4052;
    long AC_COUNTRY_NAME_HR = 4053;
    long AC_COUNTRY_NAME_CU = 4054;
    long AC_COUNTRY_NAME_CY = 4055;
    long AC_COUNTRY_NAME_CZ = 4056;
    long AC_COUNTRY_NAME_DK = 4057;
    long AC_COUNTRY_NAME_DJ = 4058;
    long AC_COUNTRY_NAME_DM = 4059;
    long AC_COUNTRY_NAME_DO = 4060;
    long AC_COUNTRY_NAME_TL = 4061;
    long AC_COUNTRY_NAME_EC = 4062;
    long AC_COUNTRY_NAME_EG = 4063;
    long AC_COUNTRY_NAME_SV = 4064;
    long AC_COUNTRY_NAME_GQ = 4065;
    long AC_COUNTRY_NAME_ER = 4066;
    long AC_COUNTRY_NAME_EE = 4067;
    long AC_COUNTRY_NAME_ET = 4068;
    long AC_COUNTRY_NAME_FK = 4069;
    long AC_COUNTRY_NAME_FO = 4070;
    long AC_COUNTRY_NAME_FJ = 4071;
    long AC_COUNTRY_NAME_FI = 4072;
    long AC_COUNTRY_NAME_FR = 4073;
    long AC_COUNTRY_NAME_FX = 4074;
    long AC_COUNTRY_NAME_GF = 4075;
    long AC_COUNTRY_NAME_PF = 4076;
    long AC_COUNTRY_NAME_TF = 4077;
    long AC_COUNTRY_NAME_GA = 4078;
    long AC_COUNTRY_NAME_GM = 4079;
    long AC_COUNTRY_NAME_GE = 4080;
    long AC_COUNTRY_NAME_DE = 4081;
    long AC_COUNTRY_NAME_GH = 4082;
    long AC_COUNTRY_NAME_GI = 4083;
    long AC_COUNTRY_NAME_GR = 4084;
    long AC_COUNTRY_NAME_GL = 4085;
    long AC_COUNTRY_NAME_GD = 4086;
    long AC_COUNTRY_NAME_GP = 4087;
    long AC_COUNTRY_NAME_GU = 4088;
    long AC_COUNTRY_NAME_GT = 4089;
    long AC_COUNTRY_NAME_GN = 4090;
    long AC_COUNTRY_NAME_GW = 4091;
    long AC_COUNTRY_NAME_GY = 4092;
    long AC_COUNTRY_NAME_HT = 4093;
    long AC_COUNTRY_NAME_HM = 4094;
    long AC_COUNTRY_NAME_HN = 4095;
    long AC_COUNTRY_NAME_HK = 4096;
    long AC_COUNTRY_NAME_HU = 4097;
    long AC_COUNTRY_NAME_IS = 4098;
    long AC_COUNTRY_NAME_IN = 4099;
    long AC_COUNTRY_NAME_ID = 4100;
    long AC_COUNTRY_NAME_IR = 4101;
    long AC_COUNTRY_NAME_IQ = 4102;
    long AC_COUNTRY_NAME_IE = 4103;
    long AC_COUNTRY_NAME_IL = 4104;
    long AC_COUNTRY_NAME_IT = 4105;
    long AC_COUNTRY_NAME_JM = 4106;
    long AC_COUNTRY_NAME_JP = 4107;
    long AC_COUNTRY_NAME_JO = 4108;
    long AC_COUNTRY_NAME_KZ = 4109;
    long AC_COUNTRY_NAME_KE = 4110;
    long AC_COUNTRY_NAME_KI = 4111;
    long AC_COUNTRY_NAME_KP = 4112;
    long AC_COUNTRY_NAME_KR = 4113;
    long AC_COUNTRY_NAME_KW = 4114;
    long AC_COUNTRY_NAME_KG = 4115;
    long AC_COUNTRY_NAME_LA = 4116;
    long AC_COUNTRY_NAME_LV = 4117;
    long AC_COUNTRY_NAME_LB = 4118;
    long AC_COUNTRY_NAME_LS = 4119;
    long AC_COUNTRY_NAME_LR = 4120;
    long AC_COUNTRY_NAME_LY = 4121;
    long AC_COUNTRY_NAME_LI = 4122;
    long AC_COUNTRY_NAME_LT = 4123;
    long AC_COUNTRY_NAME_LU = 4124;
    long AC_COUNTRY_NAME_MO = 4125;
    long AC_COUNTRY_NAME_MK = 4126;
    long AC_COUNTRY_NAME_MG = 4127;
    long AC_COUNTRY_NAME_MW = 4128;
    long AC_COUNTRY_NAME_MY = 4129;
    long AC_COUNTRY_NAME_MV = 4130;
    long AC_COUNTRY_NAME_ML = 4131;
    long AC_COUNTRY_NAME_MT = 4132;
    long AC_COUNTRY_NAME_MH = 4133;
    long AC_COUNTRY_NAME_MQ = 4134;
    long AC_COUNTRY_NAME_MR = 4135;
    long AC_COUNTRY_NAME_MU = 4136;
    long AC_COUNTRY_NAME_YT = 4137;
    long AC_COUNTRY_NAME_MX = 4138;
    long AC_COUNTRY_NAME_FM = 4139;
    long AC_COUNTRY_NAME_MD = 4140;
    long AC_COUNTRY_NAME_MC = 4141;
    long AC_COUNTRY_NAME_MN = 4142;
    long AC_COUNTRY_NAME_MS = 4143;
    long AC_COUNTRY_NAME_MA = 4144;
    long AC_COUNTRY_NAME_MZ = 4145;
    long AC_COUNTRY_NAME_MM = 4146;
    long AC_COUNTRY_NAME_NA = 4147;
    long AC_COUNTRY_NAME_NR = 4148;
    long AC_COUNTRY_NAME_NP = 4149;
    long AC_COUNTRY_NAME_NL = 4150;
    long AC_COUNTRY_NAME_AN = 4151;
    long AC_COUNTRY_NAME_NC = 4152;
    long AC_COUNTRY_NAME_NZ = 4153;
    long AC_COUNTRY_NAME_NI = 4154;
    long AC_COUNTRY_NAME_NE = 4155;
    long AC_COUNTRY_NAME_NG = 4156;
    long AC_COUNTRY_NAME_NU = 4157;
    long AC_COUNTRY_NAME_NF = 4158;
    long AC_COUNTRY_NAME_MP = 4159;
    long AC_COUNTRY_NAME_NO = 4160;
    long AC_COUNTRY_NAME_OM = 4161;
    long AC_COUNTRY_NAME_PK = 4162;
    long AC_COUNTRY_NAME_PW = 4163;
    long AC_COUNTRY_NAME_PS = 4164;
    long AC_COUNTRY_NAME_PA = 4165;
    long AC_COUNTRY_NAME_PG = 4166;
    long AC_COUNTRY_NAME_PY = 4167;
    long AC_COUNTRY_NAME_PE = 4168;
    long AC_COUNTRY_NAME_PH = 4169;
    long AC_COUNTRY_NAME_PN = 4170;
    long AC_COUNTRY_NAME_PL = 4171;
    long AC_COUNTRY_NAME_PT = 4172;
    long AC_COUNTRY_NAME_PR = 4173;
    long AC_COUNTRY_NAME_QA = 4174;
    long AC_COUNTRY_NAME_RE = 4175;
    long AC_COUNTRY_NAME_RO = 4176;
    long AC_COUNTRY_NAME_RU = 4177;
    long AC_COUNTRY_NAME_RW = 4178;
    long AC_COUNTRY_NAME_KN = 4179;
    long AC_COUNTRY_NAME_LC = 4180;
    long AC_COUNTRY_NAME_VC = 4181;
    long AC_COUNTRY_NAME_WS = 4182;
    long AC_COUNTRY_NAME_SM = 4183;
    long AC_COUNTRY_NAME_ST = 4184;
    long AC_COUNTRY_NAME_SA = 4185;
    long AC_COUNTRY_NAME_SN = 4186;
    long AC_COUNTRY_NAME_SC = 4187;
    long AC_COUNTRY_NAME_SL = 4188;
    long AC_COUNTRY_NAME_SG = 4189;
    long AC_COUNTRY_NAME_SK = 4190;
    long AC_COUNTRY_NAME_SI = 4191;
    long AC_COUNTRY_NAME_SB = 4192;
    long AC_COUNTRY_NAME_SO = 4193;
    long AC_COUNTRY_NAME_ZA = 4194;
    long AC_COUNTRY_NAME_GS = 4195;
    long AC_COUNTRY_NAME_ES = 4196;
    long AC_COUNTRY_NAME_LK = 4197;
    long AC_COUNTRY_NAME_SH = 4198;
    long AC_COUNTRY_NAME_PM = 4199;
    long AC_COUNTRY_NAME_SD = 4200;
    long AC_COUNTRY_NAME_SR = 4201;
    long AC_COUNTRY_NAME_SJ = 4202;
    long AC_COUNTRY_NAME_SZ = 4203;
    long AC_COUNTRY_NAME_SE = 4204;
    long AC_COUNTRY_NAME_CH = 4205;
    long AC_COUNTRY_NAME_SY = 4206;
    long AC_COUNTRY_NAME_TW = 4207;
    long AC_COUNTRY_NAME_TJ = 4208;
    long AC_COUNTRY_NAME_TZ = 4209;
    long AC_COUNTRY_NAME_TH = 4210;
    long AC_COUNTRY_NAME_TG = 4211;
    long AC_COUNTRY_NAME_TK = 4212;
    long AC_COUNTRY_NAME_TO = 4213;
    long AC_COUNTRY_NAME_TT = 4214;
    long AC_COUNTRY_NAME_TN = 4215;
    long AC_COUNTRY_NAME_TR = 4216;
    long AC_COUNTRY_NAME_TM = 4217;
    long AC_COUNTRY_NAME_TC = 4218;
    long AC_COUNTRY_NAME_TV = 4219;
    long AC_COUNTRY_NAME_UG = 4220;
    long AC_COUNTRY_NAME_UA = 4221;
    long AC_COUNTRY_NAME_AE = 4222;
    long AC_COUNTRY_NAME_GB = 4223;
    long AC_COUNTRY_NAME_US = 4224;
    long AC_COUNTRY_NAME_UM = 4225;
    long AC_COUNTRY_NAME_UY = 4226;
    long AC_COUNTRY_NAME_UZ = 4227;
    long AC_COUNTRY_NAME_VU = 4228;
    long AC_COUNTRY_NAME_VA = 4229;
    long AC_COUNTRY_NAME_VE = 4230;
    long AC_COUNTRY_NAME_VN = 4231;
    long AC_COUNTRY_NAME_VG = 4232;
    long AC_COUNTRY_NAME_VI = 4233;
    long AC_COUNTRY_NAME_WF = 4234;
    long AC_COUNTRY_NAME_EH = 4235;
    long AC_COUNTRY_NAME_YE = 4236;
    long AC_COUNTRY_NAME_YU = 4237;
    long AC_COUNTRY_NAME_ZM = 4238;
    long AC_COUNTRY_NAME_ZW = 4239;
    long AC_TIME_ZONE_DESCRIPTION_000 = 4300;
    long AC_TIME_ZONE_DESCRIPTION_001 = 4301;
    long AC_TIME_ZONE_DESCRIPTION_002 = 4302;
    long AC_TIME_ZONE_DESCRIPTION_003 = 4303;
    long AC_TIME_ZONE_DESCRIPTION_004 = 4304;
    long AC_TIME_ZONE_DESCRIPTION_015 = 4305;
    long AC_TIME_ZONE_DESCRIPTION_013 = 4306;
    long AC_TIME_ZONE_DESCRIPTION_010 = 4307;
    long AC_TIME_ZONE_DESCRIPTION_033 = 4308;
    long AC_TIME_ZONE_DESCRIPTION_020 = 4309;
    long AC_TIME_ZONE_DESCRIPTION_030 = 4310;
    long AC_TIME_ZONE_DESCRIPTION_025 = 4311;
    long AC_TIME_ZONE_DESCRIPTION_045 = 4312;
    long AC_TIME_ZONE_DESCRIPTION_035 = 4313;
    long AC_TIME_ZONE_DESCRIPTION_040 = 4314;
    long AC_TIME_ZONE_DESCRIPTION_050 = 4315;
    long AC_TIME_ZONE_DESCRIPTION_055 = 4316;
    long AC_TIME_ZONE_DESCRIPTION_056 = 4317;
    long AC_TIME_ZONE_DESCRIPTION_060 = 4318;
    long AC_TIME_ZONE_DESCRIPTION_065 = 4319;
    long AC_TIME_ZONE_DESCRIPTION_070 = 4320;
    long AC_TIME_ZONE_DESCRIPTION_073 = 4321;
    long AC_TIME_ZONE_DESCRIPTION_075 = 4322;
    long AC_TIME_ZONE_DESCRIPTION_080 = 4323;
    long AC_TIME_ZONE_DESCRIPTION_083 = 4324;
    long AC_TIME_ZONE_DESCRIPTION_090 = 4325;
    long AC_TIME_ZONE_DESCRIPTION_085 = 4326;
    long AC_TIME_ZONE_DESCRIPTION_110 = 4327;
    long AC_TIME_ZONE_DESCRIPTION_095 = 4328;
    long AC_TIME_ZONE_DESCRIPTION_105 = 4329;
    long AC_TIME_ZONE_DESCRIPTION_100 = 4330;
    long AC_TIME_ZONE_DESCRIPTION_113 = 4331;
    long AC_TIME_ZONE_DESCRIPTION_130 = 4332;
    long AC_TIME_ZONE_DESCRIPTION_115 = 4333;
    long AC_TIME_ZONE_DESCRIPTION_120 = 4334;
    long AC_TIME_ZONE_DESCRIPTION_140 = 4335;
    long AC_TIME_ZONE_DESCRIPTION_125 = 4336;
    long AC_TIME_ZONE_DESCRIPTION_135 = 4337;
    long AC_TIME_ZONE_DESCRIPTION_158 = 4338;
    long AC_TIME_ZONE_DESCRIPTION_150 = 4339;
    long AC_TIME_ZONE_DESCRIPTION_145 = 4340;
    long AC_TIME_ZONE_DESCRIPTION_155 = 4341;
    long AC_TIME_ZONE_DESCRIPTION_160 = 4342;
    long AC_TIME_ZONE_DESCRIPTION_165 = 4343;
    long AC_TIME_ZONE_DESCRIPTION_170 = 4344;
    long AC_TIME_ZONE_DESCRIPTION_175 = 4345;
    long AC_TIME_ZONE_DESCRIPTION_180 = 4346;
    long AC_TIME_ZONE_DESCRIPTION_185 = 4347;
    long AC_TIME_ZONE_DESCRIPTION_190 = 4348;
    long AC_TIME_ZONE_DESCRIPTION_193 = 4349;
    long AC_TIME_ZONE_DESCRIPTION_201 = 4350;
    long AC_TIME_ZONE_DESCRIPTION_195 = 4351;
    long AC_TIME_ZONE_DESCRIPTION_200 = 4352;
    long AC_TIME_ZONE_DESCRIPTION_203 = 4353;
    long AC_TIME_ZONE_DESCRIPTION_205 = 4354;
    long AC_TIME_ZONE_DESCRIPTION_207 = 4355;
    long AC_TIME_ZONE_DESCRIPTION_210 = 4356;
    long AC_TIME_ZONE_DESCRIPTION_227 = 4357;
    long AC_TIME_ZONE_DESCRIPTION_215 = 4358;
    long AC_TIME_ZONE_DESCRIPTION_225 = 4359;
    long AC_TIME_ZONE_DESCRIPTION_220 = 4360;
    long AC_TIME_ZONE_DESCRIPTION_235 = 4361;
    long AC_TIME_ZONE_DESCRIPTION_230 = 4362;
    long AC_TIME_ZONE_DESCRIPTION_240 = 4363;
    long AC_TIME_ZONE_DESCRIPTION_250 = 4364;
    long AC_TIME_ZONE_DESCRIPTION_245 = 4365;
    long AC_TIME_ZONE_DESCRIPTION_260 = 4366;
    long AC_TIME_ZONE_DESCRIPTION_255 = 4367;
    long AC_TIME_ZONE_DESCRIPTION_275 = 4368;
    long AC_TIME_ZONE_DESCRIPTION_265 = 4369;
    long AC_TIME_ZONE_DESCRIPTION_270 = 4370;
    long AC_TIME_ZONE_DESCRIPTION_280 = 4371;
    long AC_TIME_ZONE_DESCRIPTION_290 = 4372;
    long AC_TIME_ZONE_DESCRIPTION_285 = 4373;
    long AC_TIME_ZONE_DESCRIPTION_300 = 4374;
    long PC_BREAKOUT_SELF_REFERENCING_PARENT = 4500;
    long PERM_INFO_VIEW_SHIPMENT = 4501;
    long PERM_INFO_CREATE_SPEC_REFERENCE = 4502;
    long PC_BREAKOUT_SUBQUANTITY_LIMIT = 4503;
    long PC_BREAKOUT_SUBQUANTITIES_REQUIRED = 4504;
    long PC_BREAKOUT_PRICE_REQUIRED = 4505;
    long REPORT_PIVOT_TOTAL = 4506;
    long REPORT_PIVOT_GRAND_TOTAL = 4507;
    long CONTRACT_PRICING_INVALID_CONTRACT_DATA = 4508;
    long CONTRACT_PRICING_TIME_OVERLAP = 4509;
    long CONTRACT_PRICING_ONE_DEFAULT = 4510;
    long CONTRACT_PRICING_INVALID_PRODUCT_TYPE_REG = 4511;
    long CONTRACT_PRICING_SUPPLIER_REQUIRED = 4512;
    long XML_NOT_WELL_FORMED = 4513;
    long XML_NOT_VALID = 4514;
    long NO_DATA_SHEET = 4515;
    long NO_PRICE_SHEET = 4516;
    long NO_PRICING_FORMULAE = 4517;
    long CALC_NO_KEY_IN_DATA_SHEET = 4518;
    long CALC_NO_KEY_IN_PRICE_SHEET = 4519;
    long CALC_NO_KEY_IN_SET = 4520;
    long CALC_LOOKUP_FAILED = 4521;
    long CALC_LOOKUP_NEED_NUMBER = 4522;
    long CALC_LOOKUP_NUMBER_BELOW_BINS = 4523;
    long CALC_TYPE_MISMATCH = 4524;
    long CALC_DIVIDE_BY_ZERO = 4525;
    long CALC_FUNCTION_ERROR = 4526;
    long PERM_INFO_MANAGE_TASK_PREFERENCES = 4527;
    long RFE_ALL_QTY_CONSTRAINT = 4528;
    long PERM_INFO_CREATE_QUICK_ORDER = 4529;
    long PERM_INFO_RUN_SPEC_QUERY = 4530;
    long EXCEED_SELF_INVITED_LIMIT = 4531;
    long EXCEED_SELF_INVITED_LIMIT_ERROR = 4532;
    long EXCEED_SELF_INVITED_ERROR = 4533;
    long SELF_INVITING_NOT_ALLOW = 4534;
    long SUPPLIER_ALREADY_EXISTED = 4535;
    long RFE_HAS_CLOSED = 4536;
    long RFE_NOT_AVAILABLE_FOR_ESTIMATING = 4537;
    long DM_WORKGROUP_ACTIVITY_SUPPLIER_CODE = 4538;
    long PC_BREAKOUT_ROOT_QUANTITY = 4539;
    long BOOKLET_PLUS_COVER = 4540;
    long BOOKLET_SELF_COVER = 4541;
    long BRIEFS = 4542;
    long BROCHURE_FOLDED = 4543;
    long BROCHURE_PLUS_COVER = 4544;
    long BROCHURE_SELF_COVER = 4545;
    long CATALOG_PLUS_COVER = 4546;
    long CATALOG_SELF_COVER = 4547;
    long C_CHANNEL_CARD = 4548;
    long CD_SLEEVE = 4549;
    long COVER_WRAP = 4550;
    long DISPLAY_STAND = 4551;
    long FSI = 4552;
    long GUIDE = 4553;
    long HAND_OUT_COUPON = 4554;
    long INFO_CARD = 4555;
    long IRG_INSTANT_REFERENCE_GUIDES = 4556;
    long MAGAZINE_INSERTS = 4557;
    long MERCHANDISING_IN_STORE = 4558;
    long NEWSPAPER_INSERTS = 4559;
    long OTHER_FOLDER = 4560;
    long REFERENCE_GUIDE = 4561;
    long REPLY_CARD = 4562;
    long SHEET_BRIEF_BOUND = 4563;
    long SHEET_BRIEF_FLAT_OR_FOLDED = 4564;
    long TEAR_PADS = 4565;
    long VIDPRO_CARD = 4566;
    long WHITE_PAPERS = 4567;
    long TENT_CARD = 4568;
    long TRACKING_TYPE_ESTIMATE_ACTIVATED = 4570;
    long TRACKING_TYPE_ESTIMATE_ACTIVATED_DESC = 4571;
    long NOTIFICATION_TYPE_ESTIMATE_ACTIVATED = 4572;
    long AUTO_GENERATED_ESTIMATE_CONSTRAINT = 4573;
    long PERM_INFO_MANAGE_RESOURCES = 4575;
    long DM_ESTIMATE_BUYER_V_ESTIMATE_AUTO_GENERATED = 4576;
    long DM_ESTIMATE_SUPPLIER_V_ESTIMATE_AUTO_GENERATED = 4577;
    long DM_ESTIMATE_BUYER_V_ESTIMATE_BEHALF_USER_ID = 4578;
    long DM_ESTIMATE_SUPPLIER_V_ESTIMATE_BEHALF_USER_ID = 4579;
    long EXPIRED_REFRESHED_FORM = 4580;
    long EXPIRED_FORM = 4581;
    long PRODUCT_TYPE_WALLET_GUIDE = 4582;
    long PRODUCT_TYPE_USER_GUIDE = 4583;
    long PERM_INFO_RUN_PROJECT_BUDGET_REPORT = 4590;
    long DM_PROJECT_PROJECT_MOD_DATE = 4591;
    long CUSTOM_PRICING_ERROR = 4592;
    long SPEC_ITEM_DELETE = 4595;
    long REGEX_SYNTAX_CONSTRAINT = 4597;
    long REGEX_ERROR_CONSTRAINT = 4598;
    long REGEX_UNMATCHED_CONSTRAINT = 4599;
    long STRING_INVALID_EQUALS_CONSTRAINT = 4600;
    long STRING_INVALID_EQUALS_IGNORE_CASE_CONSTRAINT = 4601;
    long STRING_INVALID_STARTS_WITH_CONSTRAINT = 4602;
    long STRING_INVALID_ENDS_WITH_CONSTRAINT = 4603;
    long STRING_INVALID_INDEX_OF_CONSTRAINT = 4604;
    long STRING_INVALID_LAST_INDEX_OF_CONSTRAINT = 4605;
    long STRING_INVALID_REGEX_CONSTRAINT = 4606;
    long OBJECT_COUNTER_TYPE_LABEL = 4607;
    long PROJECT_COUNTER_TYPE_LABEL = 4608;
    long PRODUCT_TYPE_SINGLE_SHEET = 4610;
    long PRODUCT_TYPE_WOBBLER = 4611;
    long PRODUCT_TYPE_WRAP_MM = 4612;
    long PRODUCT_TYPE_BUNDLE_HANGER = 4613;
    long PRODUCT_TYPE_CONSUMER_EXTERNAL = 4614;
    long PRODUCT_TYPE_DISTRIBUTION_ANALYSIS = 4615;
    long PRODUCT_TYPE_EMAIL_EXTERNAL = 4616;
    long PRODUCT_TYPE_GROWTH_DECLINE = 4617;
    long PRODUCT_TYPE_IN_MARKET = 4618;
    long PRODUCT_TYPE_IN_MARKET_PRIZM = 4619;
    long PRODUCT_TYPE_MODEL = 4620;
    long PRODUCT_TYPE_NONE = 4621;
    long PRODUCT_TYPE_MAPPING = 4622;
    long PRODUCT_TYPE_OCCUPANT = 4623;
    long PRODUCT_TYPE_OUT_OF_MARKET = 4624;
    long PRODUCT_TYPE_OUT_OF_MARKET_PRIZM = 4625;
    long PRODUCT_TYPE_PROFILE = 4626;
    long PRODUCT_TYPE_RESPONSE_ANALYSIS = 4627;
    long PRODUCT_TYPE_RFM = 4628;
    long PRODUCT_TYPE_SCORING = 4629;
    long PRODUCT_TYPE_BUSINESS_EXTERNAL = 4630;
    long PRODUCT_TYPE_CUSTOM_ANALYTICS = 4631;
    long PRODUCT_TYPE_CUSTOM_REPORTS = 4632;
    long PRODUCT_TYPE_CUSTOMIZED_MARKET_MAIL = 4633;
    long PRODUCT_TYPE_DAC_MM = 4634;
    long PRODUCT_TYPE_DATA_CLEANSING = 4635;
    long PRODUCT_TYPE_DATA_CLEANSING_NCOA = 4636;
    long PRODUCT_TYPE_DATA_ENTRY = 4637;
    long PRODUCT_TYPE_DATABASE_BUILDS = 4638;
    long PRODUCT_TYPE_DATABASE_UPDATES = 4639;
    long PRODUCT_TYPE_MAILER = 4640;
    long PRODUCT_TYPE_MERGE_PURGE = 4641;
    long PRODUCT_TYPE_NCOA = 4642;
    long PRODUCT_TYPE_OUTER_ENVELOPE_WINDOW = 4643;
    long PRODUCT_TYPE_OUTER_ENVELOPE_NO_WINDOW = 4644;
    long PRODUCT_TYPE_PAID_PLUS_FSI = 4645;
    long PRODUCT_TYPE_PRINT_AND_MAIL_MM = 4646;
    long PRODUCT_TYPE_RACK_CARD = 4647;
    long PRODUCT_TYPE_SHARED_MAIL = 4648;
    long DM_ORDER_BUYER_V_ACCEPTED_TAX_AMOUNT = 4649;
    long DM_ORDER_BUYER_V_ACCEPTED_SHIPPING_AMOUNT = 4650;
    long DM_ORDER_BUYER_V_ACCEPTED_MISC_AMOUNT = 4651;
    long DM_ORDER_ITEM_ACCEPTED_ORDER_VALUE = 4652;
    long INVALID_WG_GUID = 4653;
    long PRODUCT_TYPE_LETTER_PACKAGE = 4660;
    long DM_ORDER_ITEM_SUPPLIER_V_ACCEPTED_ORDER_VALUE = 4661;
    long DM_ORDER_BUYER_V_GRAND_TOTAL = 4662;
    long DM_ORDER_SUPPLIER_V_GRAND_TOTAL = 4663;
    long DM_ORDER_ITEM_GRAND_ORDER_VALUE = 4664;
    long DM_ORDER_ITEM_SUPPLIER_V_GRAND_ORDER_VALUE = 4665;
    long DM_NEXT_REFRESH_TIME = 4666;
    long WORKGROUP_TAB_PRODUCT_TYPES = 4667;
    long WORKGROUP_TAB_CUSTOM_DATA = 4668;
    long WORKGROUP_TAB_TIME_CARD = 4669;
    long WORKGROUP_TAB_USER_FIELDS = 4670;
    long WORKGROUP_TAB_SPEC_PRICING = 4671;
    long OBJECT_CLASS_INVOICE = 4679;
    long OBJECT_CLASS_INVOICE_ITEM = 4680;
    long TRACKING_TYPE_INVOICE_CREATED = 4681;
    long TRACKING_TYPE_INVOICE_CREATED_DESC = 4682;
    long TRACKING_TYPE_INVOICE_DELETED = 4683;
    long TRACKING_TYPE_INVOICE_DELETED_DESC = 4684;
    long TRACKING_TYPE_INVOICE_UPDATED = 4685;
    long TRACKING_TYPE_INVOICE_UPDATED_DESC = 4686;
    long TRACKING_TYPE_INVOICE_SENT = 4687;
    long TRACKING_TYPE_INVOICE_SENT_DESC = 4688;
    long TRACKING_TYPE_INVOICE_REVISED = 4689;
    long TRACKING_TYPE_INVOICE_REVISED_DESC = 4690;
    long TRACKING_TYPE_INVOICE_ACCEPTED = 4691;
    long TRACKING_TYPE_INVOICE_ACCEPTED_DESC = 4692;
    long TRACKING_TYPE_INVOICE_REJECTED = 4693;
    long TRACKING_TYPE_INVOICE_REJECTED_DESC = 4694;
    long TRACKING_TYPE_INVOICE_RETRACTED = 4695;
    long TRACKING_TYPE_INVOICE_RETRACTED_DESC = 4696;
    long OBJECT_STATE_DESC_INVOICE_DRAFT = 4697;
    long OBJECT_STATE_DESC_INVOICE_PENDING_APPROVAL = 4698;
    long OBJECT_STATE_DESC_INVOICE_ACCEPTED = 4699;
    long OBJECT_STATE_DESC_INVOICE_REJECTED = 4700;
    long OBJECT_STATE_DESC_INVOICE_RETRACTED = 4701;
    long TASK_TYPE_INVOICE_SEND_NAME = 4702;
    long TASK_TYPE_INVOICE_SEND_DESC = 4703;
    long TASK_TYPE_INVOICE_REVIEW_NAME = 4704;
    long TASK_TYPE_INVOICE_REVIEW_DESC = 4705;
    long NOTIFICATION_TYPE_INVOICE_SENT = 4706;
    long NOTIFICATION_TYPE_INVOICE_REVISED = 4707;
    long NOTIFICATION_TYPE_INVOICE_ACCEPTED = 4708;
    long NOTIFICATION_TYPE_INVOICE_REJECTED = 4709;
    long NOTIFICATION_TYPE_INVOICE_RETRACTED = 4710;
    long WORKGROUP_TAB_ORDER_INVOICING = 4711;
    long NO_PERMISSION_TO_UPDATE_INVOICE_PREF = 4712;
    long INVOICE_CONSTRAINT_ORDER_REQUIRED = 4713;
    long INVOICE_CONSTRAINT_NO_PERMISSION_ON_ORDER = 4714;
    long INVOICE_CONSTRAINT_NO_UPDATE_AT_STATE = 4715;
    long INVOICE_CONSTRAINT_NO_SEND_AT_STATE = 4716;
    long INVOICE_CONSTRAINT_NO_ACCEPT_AT_STATE = 4717;
    long INVOICE_CONSTRAINT_NO_REJECT_AT_STATE = 4718;
    long INVOICE_CONSTRAINT_NO_RETRACT_AT_STATE = 4719;
    long INVOICE_CONSTRAINT_DELETE_PERMISSION = 4720;
    long INVOICE_CONSTRAINT_SEND_PERMISSION = 4721;
    long INVOICE_CONSTRAINT_ACCEPT_PERMISSION = 4722;
    long INVOICE_CONSTRAINT_REJECT_PERMISSION = 4723;
    long INVOICE_CONSTRAINT_RETRACT_PERMISSION = 4724;
    long INVOICE_CONSTRAINT_CREATE_PERMISSION = 4725;
    long INVOICE_CONSTRAINT_EDIT_PERMISSION = 4726;
    long INVOICE_CONSTRAINT_UPDATE_STALE_DATA = 4727;
    long INVOICE_CONSTRAINT_PERMISSION = 4728;
    long INVOICE_CONSTRAINT_APPROVE_PERMISSION = 4729;
    long PERM_INFO_CREATE_INVOICE = 4730;
    long PERM_INFO_EDIT_INVOICE = 4731;
    long PERM_INFO_DELETE_INVOICE = 4732;
    long PERM_INFO_SEND_INVOICE = 4733;
    long PERM_INFO_VIEW_INVOICE = 4734;
    long PERM_INFO_ACCEPT_INVOICE = 4735;
    long PERM_INFO_REJECT_INVOICE = 4736;
    long PERM_INFO_RETRACT_INVOICE = 4737;
    long WORKGROUP_TAB_SHIPMENT = 4738;
    long OBJECT_CLASS_FOLDER = 4800;
    long OBJECT_CLASS_DOCUMENT = 4801;
    long OBJECT_CLASS_DOCUMENT_VERSION = 4802;
    long PERM_INFO_CREATE_FOLDER = 4803;
    long PERM_INFO_VIEW_FOLDER = 4804;
    long PERM_INFO_EDIT_FOLDER = 4805;
    long PERM_INFO_DELETE_FOLDER = 4806;
    long PERM_INFO_MANAGE_FOLDER = 4807;
    long PERM_INFO_MOVE_FOLDER = 4808;
    long PERM_INFO_CREATE_DOCUMENT = 4809;
    long PERM_INFO_LIST_DOCUMENT = 4810;
    long PERM_INFO_VIEW_DOCUMENT = 4811;
    long PERM_INFO_EDIT_DOCUMENT = 4812;
    long PERM_INFO_DELETE_DOCUMENT = 4813;
    long PERM_INFO_MANAGE_DOCUMENT = 4814;
    long PERM_INFO_MOVE_DOCUMENT = 4815;
    long DMS_PROJECT_FILES_FOLDER_TITLE = 4816;
    long DMS_PROJECT_FILES_FOLDER_DESCRIPTION = 4817;
    long DMS_FOLDER_VIEW_PANEL_TITLE = 4818;
    long DMS_DOCUMENT_VIEW_PANEL_TITLE = 4819;
    long DMS_DOCUMENT_VERSION_VIEW_PANEL_TITLE = 4820;
    long DMS_FOLDER_EXISTS = 4821;
    long DMS_ERROR = 4822;
    long DMS_DELETE_ACCESS_ERROR = 4823;
    long DMS_UPDATE_ACCESS_ERROR = 4824;
    long DMS_CREATE_VERSION_ACCESS_ERROR = 4825;
    long DMS_FILE_UNTITLED = 4826;
    long DMS_DO_UPLOAD_NOW_ALERT = 4827;
    long INVOICE_VALIDATION_CONSTRAINT_QUANTITY_TOO_BIG = 4828;
    long INVOICE_VALIDATION_CONSTRAINT_PRICE_TOO_BIG = 4829;
    long INVOICE_VALIDATION_CONSTRAINT_BREAKOUT_QUANTITY = 4830;
    long INVOICE_VALIDATION_CONSTRAINT_BREAKOUT_PRICE = 4831;
    long INVOICE_VALIDATION_CONSTRAINT_TAX = 4832;
    long INVOICE_VALIDATION_CONSTRAINT_SHIPPING = 4833;
    long INVOICE_VALIDATION_CONSTRAINT_MISC_COST = 4834;
    long DMS_FILE_UPLOAD_ERROR = 4835;
    long DMS_URL_UPLOAD_ERROR = 4836;
    long DMS_FILE_UPLOAD_EXCEEDS_MAXIMUM_SIZE = 4837;
    long DMS_STORAGE_QUOTA_EXCEEDED = 4838;
    long DMS_FILE_UPLOAD_PANEL_TITLE = 4839;
    long DMS_FILE_VERSION_UPLOAD_PANEL_TITLE = 4840;
    long DMS_URL_UPLOAD_PANEL_TITLE = 4841;
    long DMS_URL_VERSION_UPLOAD_PANEL_TITLE = 4842;
    long DMS_FOLDER_CREATE_PANEL_TITLE = 4843;
    long DMS_FOLDER_EDIT_PANEL_TITLE = 4844;
    long DMS_DOCUMENT_CREATE_PANEL_TITLE = 4845;
    long DMS_DOCUMENT_EDIT_PANEL_TITLE = 4846;
    long DMS_DOCUMENT_VERSION_CREATE_PANEL_TITLE = 4847;
    long DMS_DOCUMENT_VERSION_EDIT_PANEL_TITLE = 4848;
    long DMS_ATTACHED_FILES_FOLDER_TITLE = 4849;
    long DMS_ATTACHED_FILES_FOLDER_DESCRIPTION = 4850;
    long DMS_URL_INVALID_ERROR = 4851;
    long URI_PARSE_CONSTRAINT = 4852;
    long URL_PARSE_CONSTRAINT = 4853;
    long CANNOT_UPDATE_SHIPMENT_STATUS_DUE_TO_DEPENDENT_ORDER = 4854;
    long CANNOT_TRANSFER_PROJECT_DUE_TO_JOB_STATUS = 4855;
    long DIRECT_MAIL_ITEM_NOT_FOUND = 4856;
    long DIRECT_MAIL_COMPONENT_NOT_FOUND = 4857;
    long DMS_DOCUMENT_OBJECT_ERROR_PANEL_TITLE = 4858;
    long DMS_FOLDER_NO_DELETE_PERMISSION = 4859;
    long DMS_DOCUMENT_NO_DELETE_PERMISSION = 4860;
    long DMS_DOCUMENT_OBJECT_NOT_FOUND = 4861;
    long TR_TRACKING_TYPE_NAME_FILE_VERSION_DELETED = 4862;
    long TR_TRACKING_TYPE_DESCRIPTION_FILE_VERSION_DELETED = 4863;
    long NOTIFICATION_TYPE_FILE_VERSION_DELETED = 4864;
    long OBSOLETE_SPEC_NO_EDITING_ALLOWED = 4865;
    long PREF_REUSE_SPEC_ON_REORDER_MUST_BE_ENABLED = 4866;
    long NOTIFICATION_TYPE_FILE_BATCH_ACTION_FAILED = 4867;
    long DIRECT_MAIL_DATE_RANGE_INVALID = 4868;
    long MIME_TYPE_EML = 4998;
    long MIME_TYPE_MICROSOFT_OUTLOOK = 4999;
    long MIME_TYPE_AUTO_DETECT = 5000;
    long MIME_TYPE_TEXT = 5001;
    long MIME_TYPE_BINARY = 5002;
    long MIME_TYPE_HTML = 5003;
    long MIME_TYPE_XML = 5004;
    long MIME_TYPE_JPEG = 5005;
    long MIME_TYPE_GIF = 5006;
    long MIME_TYPE_TIFF = 5007;
    long MIME_TYPE_PNG = 5008;
    long MIME_TYPE_BMP = 5009;
    long MIME_TYPE_PDF = 5010;
    long MIME_TYPE_FDF = 5011;
    long MIME_TYPE_ZIP = 5012;
    long MIME_TYPE_MICROSOFT_WORD = 5013;
    long MIME_TYPE_MICROSOFT_EXCEL = 5014;
    long MIME_TYPE_MICROSOFT_POWERPOINT = 5015;
    long MIME_TYPE_ADOBE_PHOTOSHOP = 5016;
    long DM_RFE_BUYER_V_RFE_OWNER_FULL_NAME = 5017;
    long DM_RFE_SUPPLIER_V_RFE_OWNER_FULL_NAME = 5018;
    long DM_ORDER_BUYER_V_ORDER_CREATE_DATE = 5019;
    long DM_ORDER_BUYER_V_ANNULMENT_DATE = 5020;
    long DM_ORDER_SUPPLIER_V_ORDER_CREATE_DATE = 5021;
    long DM_ORDER_SUPPLIER_V_ANNULMENT_DATE = 5022;
    long DM_ORDER_BUYER_V_AWARDED_DATE = 5023;
    long DM_ORDER_SUPPLIER_V_AWARDED_DATE = 5024;
    long DM_ESTIMATE_ITEM_COMMENTS = 5025;
    long DM_ESTIMATE_ITEM_SUPPLIER_V_COMMENTS = 5026;
    long DM_ESTIMATE_ITEM_IS_ESTIMATED = 5027;
    long DM_ESTIMATE_ITEM_SUPPLIER_V_IS_ESTIMATED = 5028;
    long DM_TIMECARD_CODE = 5029;
    long CONTRACT_PRICING_NAME_REQUIRED = 5030;
    long INVOICE_CONSTRAINT_ACCEPT_STALE_DATA = 5031;
    long INVOICE_CONSTRAINT_RETRACT_STALE_DATA = 5032;
    long INVOICE_CONSTRAINT_REJECT_STALE_DATA = 5033;
    long AC_LOCALE_DESCRIPTION_en_EU = 5034;
    long REPORT_ESTIMATE_ANALYSIS_FILTER_BY_ACTIVITY_DATE = 5035;
    long REPORT_ESTIMATE_ANALYSIS_FILTER_BY_CREATED_DATE = 5036;
    long PERM_INFO_MANAGE_PROJECT_MILESTONES = 5037;
    long INVOICE_VALIDATION_CONSTRAINT_QUANTITY_PRICE = 5038;
    long INVOICE_CONSTRAINT_STALE_DATA = 5039;
    long INVALID_SUBMISSION = 5040;
    long PRODUCT_TYPE_THUMB_EDGE_CATALOG = 5100;
    long DM_PROJECT_TEAM_MEMBER_USER_DEFINED_TRACKING_NAME = 5101;
    long CUSTOM_EDIT_PROJECT_LEGAL_STATUS = 5102;
    long CUSTOM_EDIT_PROJECT_COMPLIANCE_STATUS = 5103;
    long NOTIFICATION_TYPE_SYSTEM_ACCOUNT_ACTIVATION = 5104;
    long NOTIFICATION_TYPE_SYSTEM_ACCOUNT_ACTIVATION_AGREE_ACCEPTED = 5105;
    long RP_DATA_FIELD_TYPE_NAME_DAY = 5106;
    long DM_NEXT_REFRESH_COMMENTS = 5107;
    long CM_CONTACT_TYPE_CONTACT = 5108;
    long CM_CONTACT_TYPE_INVOICE = 5109;
    long CM_ERROR_LOCKED_CONTACT = 5110;
    long WORKGROUP_TAB_INVOICE_BILLING_CONTACTS = 5111;
    long CONTACT_SELECT_CONSTRAINT = 5112;
    long CONTACT_NOT_FOUND_CONSTRAINT = 5113;
    long NO_PERMISSION_FOR_ACTION_CONSTRAINT = 5114;
    long PERM_INFO_CREATE_CONTRACT_PRICING = 5115;
    long PERM_INFO_EDIT_CONTRACT_PRICING = 5116;
    long PERM_INFO_VIEW_CONTRACT_PRICING = 5117;
    long OBJECT_STATE_DESC_RFE_RECALLED = 5119;
    long OBJECT_STATE_DESC_ESTIMATE_RETRACTED = 5120;
    long OBJECT_STATE_DESC_ESTIMATE_INVALIDATED = 5121;
    long NOTIFICATION_TYPE_RFE_RECALLED = 5122;
    long NOTIFICATION_TYPE_RFE_CLOSED = 5123;
    long NOTIFICATION_TYPE_RFE_REOPENED = 5124;
    long NOTIFICATION_TYPE_ESTIMATE_RETRACTED = 5125;
    long NOTIFICATION_TYPE_ESTIMATE_INVALIDATED = 5126;
    long TRACKING_TYPE_RFE_RECALLED = 5127;
    long TRACKING_TYPE_RFE_RECALLED_DESC = 5128;
    long TRACKING_TYPE_RFE_REOPENED = 5129;
    long TRACKING_TYPE_RFE_REOPENED_DESC = 5130;
    long TRACKING_TYPE_RFE_CLOSED = 5131;
    long TRACKING_TYPE_RFE_CLOSED_DESC = 5132;
    long TRACKING_TYPE_ESTIMATE_RETRACTED = 5133;
    long TRACKING_TYPE_ESTIMATE_RETRACTED_DESC = 5134;
    long TRACKING_TYPE_ESTIMATE_INVALIDATED = 5135;
    long TRACKING_TYPE_ESTIMATE_INVALIDATED_DESC = 5136;
    long RFE_SUPPLIER_RECALLED = 5137;
    long RFE_SUPPLIER_CLOSED = 5138;
    long PERM_INFO_MANAGE_INVOICE_ADMIN = 5139;
    long PERM_INFO_OPEN_RFE = 5140;
    long PERM_INFO_RECALL_RFE = 5141;
    long PERM_INFO_INVALIDATE_ESTIMATE = 5142;
    long PERM_INFO_RETRACT_ESTIMATE = 5143;
    long ESTIMATE_NOT_FOUND = 5144;
    long COPY_FROM_ESTIMATE = 5145;
    long SELECT_AN_ESTIMATE = 5146;
    long CANNOT_DELETE_THE_ONLY_ITEM = 5147;
    long CANNOT_CLOSE_RFE_STATE_CONSTRAINT = 5148;
    long CANNOT_REOPEN_RFE_STATE_CONSTRAINT = 5149;
    long RFE_NOT_FOUND = 5150;
    long OBJECT_NOT_FOUND = 5151;
    long RFE_RECALL_NOT_ALLOW_ORDER_EXISTED = 5152;
    long CANNOT_RECALL_RFE_STATE_CONSTRAINT = 5153;
    long NO_ESTIMATE_TO_INVALIDATE = 5154;
    long INVOICE_NON_BILLABLE_REASON_PREPAYMENT = 5155;
    long INVOICE_NON_BILLABLE_REASON_CHANGE_ORDERS = 5156;
    long INVOICE_NON_BILLABLE_REASON_OTHER = 5157;
    long INVOICE_VALIDATION_CONSTRAINT_NON_BILLABLE_REASON = 5158;
    long UNLOCKING_NOT_SUPPORTED = 5159;
    long COPY_OF_TITLE = 5160;
    long INVALID_DUE_TO_RFE_RECALL = 5161;
    long INVOICE_RECIPIENT_REQUIRED = 5162;
    long CANNOT_DELETE_BILLING_RECIPIENT_FOREIGN_KEY = 5163;
    long REPLACE_INVOICE_STRING = 5164;
    long REPLACE_FOR = 5165;
    long PERM_INFO_APPROVE_INVOICE = 5166;
    long TRACKING_TYPE_INVOICE_APPROVED = 5167;
    long TRACKING_TYPE_INVOICE_APPROVED_DESC = 5168;
    long NOTIFICATION_TYPE_INVOICE_APPROVED = 5169;
    long INVOICE_CONSTRAINT_NO_ACCEPT_WITH_APPROVAL = 5170;
    long INVOICE_CONSTRAINT_NO_APPROVE_AT_STATE = 5171;
    long NO_PERMISSION_TO_PROCESS_CHANGE_ORDER = 5172;
    long RFE_RECALL_FOR_REVISING = 5173;
    long PC_BREAKOUT_SUBQUANTITY_REQUIRED = 5174;
    long TM_INVITATION_TYPE_SUPPLIER_INVITE = 5175;
    long NOTIFICATION_TYPE_SUPPLIER_INVITED = 5176;
    long TRACKING_TYPE_SUPPLIER_INVITED = 5177;
    long TRACKING_TYPE_SUPPLIER_INVITED_DESC = 5178;
    long NOTIFICATION_TYPE_SUPPLIER_UNINVITED = 5179;
    long TRACKING_TYPE_SUPPLIER_UNINVITED = 5180;
    long TRACKING_TYPE_SUPPLIER_UNINVITED_DESC = 5181;
    long INVALID_TEAM = 5182;
    long AC_DATA_SET_INVALID_IDS_REMOVE = 5183;
    long AC_DATA_SET_INVALID_IDS_UPDATE = 5184;
    long AC_DATA_SET_DUPLICATED_IDS = 5185;
    long AC_DATA_SET_TOO_MANY_ATTRIBUTES = 5186;
    long AC_DATA_SET_ID_REQUIRED = 5187;
    long AC_DATA_SET_INVALID_ID = 5188;
    long OBSOLETED_ORDER_DATA = 5189;
    long PC_BREAKOUT_PRICE_PER_OUT_OF_SYNC = 5190;
    long NO_PERMISSION_TO_REVISE_RFE_DATES = 5191;
    long RFE_CONSTRAINT_EXCEEDING_MAX_EXTENSIONS = 5192;
    long OBSOLETE_SPEC_TEMPLATE = 5193;
    long WORKGROUP_TAB_ORDER = 5194;
    long UNSPECIFIED = 5195;
    long OBSOLETED_RFE_DATA = 5196;
    long TASK_TYPE_CLOSING_CHANGE_ORDER_REVIEW_NAME = 5200;
    long TASK_TYPE_CLOSING_CHANGE_ORDER_REVIEW_DESC = 5201;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_CREATED = 5202;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_CREATED_DESC = 5203;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_UPDATED = 5204;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_UPDATED_DESC = 5205;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_REJECTED = 5206;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_REJECTED_DESC = 5207;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_RETRACTED = 5208;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_RETRACTED_DESC = 5209;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_ACCEPTED = 5210;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_ACCEPTED_DESC = 5211;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_CANCELLED = 5212;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_CANCELLED_DESC = 5213;
    long TR_TRACKING_TYPE_ORDER_COMPLETED = 5214;
    long TR_TRACKING_TYPE_ORDER_COMPLETED_DESC = 5215;
    long NOTIFICATION_TYPE_ORDER_COMPLETED = 5216;
    long PENDING_CHANGE_ORDERS_CONSTRAINT = 5217;
    long PENDING_COMPLETE_ORDER_CONSTRAINT = 5218;
    long NO_PERMISSION_CLOSE_ORDER_CONSTRAINT = 5219;
    long ORDER_ESTIMATE_INVALID_CONSTRAINT = 5220;
    long MARK_REQUESTS_SHIPPED = 5221;
    long MARK_REQUESTS_RECEIVED = 5222;
    long EDIT_REQUESTS = 5223;
    long DELETE_REQUESTS = 5224;
    long ADD_REQUESTS = 5225;
    long SELECT_REQUESTS_CONSTRAINT = 5226;
    long INVENTORY_ALERT_TYPE_SAFETY_LEVEL = 5250;
    long INVENTORY_ALERT_TYPE_REORDER_LEVEL = 5251;
    long INVENTORY_ALERT_TYPE_MAXIMUM_LEVEL = 5252;
    long NOTIFICATION_TYPE_INVENTORY_BELOW_SAFETY = 5253;
    long NOTIFICATION_TYPE_INVENTORY_BELOW_REORDER = 5254;
    long NOTIFICATION_TYPE_INVENTORY_ABOVE_MAXIMUM = 5255;
    long INVENTORY_SKU_NONUNIQUE = 5256;
    long INVENTORY_SKU_NONUNIQUE_CHANGE_VERSION = 5257;
    long INVENTORY_ADJUSTMENT_TYPE_1 = 5260;
    long INVENTORY_ADJUSTMENT_TYPE_2 = 5261;
    long INVENTORY_ADJUSTMENT_TYPE_3 = 5262;
    long PC_STRATEGY_PAPER_SPECIFICATION = 5276;
    long PC_STRATEGY_INK_SPECIFICATION = 5277;
    long PC_STRATEGY_BARTER_CREDITS = 5278;
    long PC_STRATEGY_OTHER = 5279;
    long PC_STRATEGY_MULTIPLE_BIDDING = 5280;
    long PC_STRATEGY_OPEN_BIDDING = 5281;
    long PC_STRATEGY_RATE_CARD = 5282;
    long PC_STRATEGY_CONTRACT_PRICING = 5283;
    long PC_STRATEGY_AGGREGATION = 5284;
    long PC_STRATEGY_CREATIVE_CHANGE = 5285;
    long PC_STRATEGY_STANDARDIZATION = 5286;
    long PC_STRATEGY_DELIVERY_EFFICIENCY = 5287;
    long PC_STRATEGY_FULFILLMENT = 5288;
    long PC_STRATEGY_RATING_PERCENT_DONT_ADDUP = 5289;
    long WORKGROUP_TAB_SOURCING = 5290;
    long PC_STRATEGY_ADD_NEW = 5291;
    long PC_STRATEGY_ADD_DEFAULT = 5292;
    long PC_STRATEGY_SELECT_CONSTRAINT = 5293;
    long PERM_INFO_MANAGE_SOURCING_ADMIN = 5294;
    long PERM_INFO_EDIT_SOURCING = 5295;
    long PERM_INFO_VIEW_SOURCING = 5296;
    long PRICING_QUICK_ORDER_CONSTRAINT = 5297;
    long NO_PERMISSION_EDIT_RFE_INFO = 5298;
    long OBJECT_CLASS_DM_CELL = 5299;
    long WORKGROUP_TAB_ESTIMATING = 5300;
    long EDIT_WORKGROUP_INVENTORY = 5301;
    long DELETE_WORKGROUP_INVENTORY = 5302;
    long ADJUST_WORKGROUP_INVENTORY = 5303;
    long PRODUCT_TYPE_APPLICATION = 5304;
    long PRODUCT_TYPE_RIPPLE = 5305;
    long PRODUCT_TYPE_SERVICE = 5306;
    long WORKGROUP_ATTRIBUTE_HOLIDAY = 5310;
    long PROJECT_OWNER_REQUIRED = 5311;
    long PROJECT_OWNER_INVALID = 5312;
    long NO_PERMISSION_REMOVE_TEAM_MEMBER = 5313;
    long CANNOT_REMOVE_SELF_FROM_PROJECT_TEAM = 5314;
    long NO_TEAM_MEMBERS_SELECTED = 5315;
    long CANNOT_EDIT_TEAM_MEMBERS = 5316;
    long CANNOT_EDIT_TEAM_MEMBER_ONLY_OWNER = 5317;
    long CANNOT_INVITE_TEAM_MEMBERS = 5318;
    long CANNOT_PERFORM_ACTION_NO_OWNERS = 5319;
    long WORKGROUP_TAB_CURRENCY = 5320;
    long CONSTRAINT_INVALID_EXCHANGE_RATE = 5321;
    long OBJECT_STATE_DESC_PENDING_APPROVAL = 5322;
    long OBJECT_STATE_DESC_APPROVED = 5323;
    long OBJECT_STATE_DESC_DISAPPROVED = 5324;
    long OBJECT_STATE_DESC_APPROVED_WITH_CHANGES = 5325;
    long ERROR_NON_NOOSH_OPERATION_ONLY = 5400;
    long ERROR_SELECT_QUOTE_STATUS_REQUIRED = 5401;
    long PERM_INFO_MANAGE_EXCHANGE_RATES = 5402;
    long AC_TERMS_TYPE_DESCRIPTION_PROPOSAL = 5403;
    long WORKGROUP_TAB_PROPOSAL_TEMPLATES = 5404;
    long FILESIZE_CONTSTRAINT = 5405;
    long OBJECT_CLASS_PROPOSAL = 5406;
    long OBJECT_CLASS_PROPOSAL_ITEM = 5407;
    long OBJECT_STATE_DESC_PROPOSAL_DRAFT = 5408;
    long OBJECT_STATE_DESC_PROPOSAL_PENDING = 5409;
    long OBJECT_STATE_DESC_PROPOSAL_ACCEPTED = 5410;
    long OBJECT_STATE_DESC_PROPOSAL_REJECTED = 5411;
    long OBJECT_STATE_DESC_PROPOSAL_RETRACTED = 5412;
    long TRACKING_TYPE_PROPOSAL_CREATED = 5413;
    long TRACKING_TYPE_PROPOSAL_CREATED_DESC = 5414;
    long TRACKING_TYPE_PROPOSAL_UPDATED = 5415;
    long TRACKING_TYPE_PROPOSAL_UPDATED_DESC = 5416;
    long TRACKING_TYPE_PROPOSAL_DELETED = 5417;
    long TRACKING_TYPE_PROPOSAL_DELETED_DESC = 5418;
    long TRACKING_TYPE_PROPOSAL_SENT = 5419;
    long TRACKING_TYPE_PROPOSAL_SENT_DESC = 5420;
    long TRACKING_TYPE_PROPOSAL_REJECTED = 5421;
    long TRACKING_TYPE_PROPOSAL_REJECTED_DESC = 5422;
    long TRACKING_TYPE_PROPOSAL_RETRACTED = 5423;
    long TRACKING_TYPE_PROPOSAL_RETRACTED_DESC = 5424;
    long TRACKING_TYPE_PROPOSAL_ACCEPTED = 5425;
    long TRACKING_TYPE_PROPOSAL_ACCEPTED_DESC = 5426;
    long INVALID_PROPOSAL_STATUS = 5427;
    long NOTIFICATION_TYPE_ROUTING_OBJECT_ROUTED = 5428;
    long NOTIFICATION_TYPE_ROUTING_OBJECT_APPROVED = 5429;
    long NOTIFICATION_TYPE_ROUTING_OBJECT_DISAPPROVED = 5430;
    long TASK_TYPE_ROUTING_OBJECT_APPROVAL_NAME = 5431;
    long TASK_TYPE_ROUTING_OBJECT_APPROVAL_DESC = 5432;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_ROUTED = 5433;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_ROUTED_DESC = 5434;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_APPROVED = 5435;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_APPROVED_DESC = 5436;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_DISAPPROVED = 5437;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_DISAPPROVED_DESC = 5438;
    long OBJECT_CLASS_PROCUREMENT = 5439;
    long CATEGORIES_NOT_FOUND = 5440;
    long OBJECT_CLASS_PROPOSAL_TEMPLATE = 5441;
    long PERM_INFO_MANAGE_PROPOSAL_ADMIN = 5442;
    long PERM_INFO_CREATE_PROPOSAL = 5443;
    long PERM_INFO_EDIT_PROPOSAL = 5444;
    long PERM_INFO_DELETE_PROPOSAL = 5445;
    long PERM_INFO_ACCEPT_PROPOSAL = 5446;
    long PERM_INFO_REJECT_PROPOSAL = 5447;
    long PERM_INFO_RETRACT_PROPOSAL = 5448;
    long PERM_INFO_VIEW_PROPOSAL = 5449;
    long LOGO_NAME_CONSTRAINT = 5450;
    long LOGO_FILENAME_CONSTRAINT = 5451;
    long PROJECT_DEFAULT_SEARCH_FILTER = 5452;
    long PROJECT_DESCRIPTION = 5453;
    long PROJECT_COMMENTS = 5454;
    long PROJECT_COMPLETION_DATE = 5455;
    long PROJECT_CREATION_DATE = 5456;
    long NOTIFICATION_TYPE_ROUTING_OBJECT_COMPLETE = 5457;
    long NOTIFICATION_TYPE_ROUTING_OBJECT_TIMEOUT = 5458;
    long PROJECTS_IN_EXCEL = 5459;
    long OBJECT_STATE_DESC_ROUTING_SLIP_ACTIVE = 5460;
    long OBJECT_STATE_DESC_ROUTING_SLIP_COMPLETED = 5461;
    long OBJECT_STATE_DESC_ROUTING_SLIP_CANCELLED = 5462;
    long NOTIFICATION_TYPE_ROUTING_OBJECT_CANCELLED = 5463;
    long NOTIFICATION_TYPE_ROUTING_OBJECT_UPDATED = 5464;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_COMPLETED = 5465;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_COMPLETED_DESC = 5466;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_CANCELLED = 5467;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_CANCELLED_DESC = 5468;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_UPDATED = 5469;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_UPDATED_DESC = 5470;
    long NO_SHIPMENT_DELIVERY_SELECTED = 5471;
    long INVALID_SUPPLIERS = 5472;
    long INACTIVE_SUPPLIER = 5473;
    long OPTION_ALL = 5474;
    long RATING_ALREADY_EXISTS = 5475;
    long UNABLE_TO_PROCESS = 5476;
    long REPORT_ERROR_SHORT = 5477;
    long REPORT_ERROR_LONG = 5478;
    long REPORT_ERROR_UNKNOWN_MESSAGE = 5479;
    long REPORT_ERROR_CONTACT_SUPPORT = 5480;
    long BREAKOUT_PRICE_REQUIRED_FOR_QUOTED_PRICE = 5481;
    long REASON_DESC_LOWEST_PRICE = 5482;
    long REASON_DESC_LOWEST_PRICE_NOT_MEETING_DELIVERY_REQUIREMENTS = 5483;
    long REASON_DESC_LOWEST_PRICE_NOT_MEETING_QUALITY_REQUIREMENTS = 5484;
    long REASON_DESC_CLIENT_DICTATED_SUPPLIER = 5485;
    long REASON_DESC_AGENCY_DICATED_SUPPLIER = 5486;
    long REASON_DESC_MWOB = 5487;
    long REASON_DESC_UNDER_CONTRACT = 5488;
    long REASON_DESC_REPRINT_TO_PREVIOUS_SUPPLIER = 5489;
    long REASON_DESC_OTHER = 5490;
    long PRODUCT_TYPE_FREIGHT = 5491;
    long PRODUCT_TYPE_HANDBOOK = 5492;
    long PRODUCT_TYPE_PRE_PRINTED_SHELL = 5493;
    long INACTIVE_USER = 5494;
    long COPYPROJECT_NO_ACTIVE_USERS = 5495;
    long TM_USER_EXISTS_IN_TEAM = 5496;
    long TM_USER_INVALID_ROLE = 5497;
    long TRACKING_TYPE_PROPOSAL_STATUS = 5498;
    long TRACKING_TYPE_PROPOSAL_STATUS_DESC = 5499;
    long QUOTE_WAS_REVISED = 5500;
    long QUOTE_WAS_REJECTED = 5501;
    long QUOTE_WAS_RETRACTED = 5502;
    long PROPOSAL_WAS_ACCEPTED = 5503;
    long TRACKING_TYPE_QUOTE_CREATED = 5504;
    long TRACKING_TYPE_QUOTE_CREATED_DESC = 5505;
    long TRACKING_TYPE_QUOTE_UPDATED = 5506;
    long TRACKING_TYPE_QUOTE_UPDATED_DESC = 5507;
    long TRACKING_TYPE_QUOTE_DELETED = 5508;
    long TRACKING_TYPE_QUOTE_DELETED_DESC = 5509;
    long CANNOT_ADD_QUOTE_ITEM = 5510;
    long CANNOT_DELETE_QUOTE_ITEM = 5511;
    long PERM_INFO_VIEW_SPEC_PRICING = 5512;
    long ERROR_USER_ALREADY_ACTIVATED = 5513;
    long ERROR_USER_ALREADY_DEACTIVATED = 5514;
    long PERM_INFO_MANAGE_ORDER_ADMIN = 5515;
    long PERM_INFO_MANAGE_ESTIMATING_ADMIN = 5516;
    long CURRENT_PROJECT = 5517;
    long PARENT_PROJECT = 5518;
    long TIMECARD_LINE = 5519;
    long PRODUCT_TYPE_BINDING_FINISHING = 5520;
    long PRODUCT_TYPE_BLUE_BADGE = 5521;
    long PRODUCT_TYPE_BLUE_BADGE_POUCH = 5522;
    long PRODUCT_TYPE_BRAILLE = 5523;
    long PRODUCT_TYPE_CLOCK_CARD = 5524;
    long PRODUCT_TYPE_COMMAND_PAPER = 5525;
    long PRODUCT_TYPE_DESIGN = 5526;
    long PRODUCT_TYPE_DIGITAL_MONO = 5527;
    long PRODUCT_TYPE_DIGITAL_COLOR = 5528;
    long PRODUCT_TYPE_DVD_PACKAGING = 5529;
    long PRODUCT_TYPE_DVD_SLEEVE = 5530;
    long PRODUCT_TYPE_EDITORIAL = 5531;
    long PRODUCT_TYPE_GAZETTE = 5532;
    long PRODUCT_TYPE_HOUSE_PAPER = 5533;
    long PRODUCT_TYPE_LARGE_PRINT = 5534;
    long PRODUCT_TYPE_LEGISLATION = 5535;
    long PRODUCT_TYPE_LOOSE_LEAF = 5536;
    long PRODUCT_TYPE_LOOSE_LEAF_UPDATE = 5537;
    long PRODUCT_TYPE_METAL_PLAQUE = 5538;
    long PRODUCT_TYPE_SLATE_PLAQUE = 5539;
    long PRODUCT_TYPE_TRANSLATION = 5540;
    long TRACKING_TYPE_QUOTE_SELECTION_CHANGED = 5541;
    long TRACKING_TYPE_QUOTE_SELECTION_CHANGED_DESC = 5542;
    long PRODUCT_TYPE_FULFILLMENT = 5543;
    long PRODUCT_TYPE_BLOW_UPS = 5544;
    long PRODUCT_TYPE_COASTERS = 5545;
    long PRODUCT_TYPE_NCR_FORMS = 5546;
    long PRODUCT_TYPE_PAMPHLETS = 5547;
    long PRODUCT_TYPE_FLIP_CHART = 5548;
    long PRODUCT_TYPE_OUTDOOR = 5549;
    long PRODUCT_TYPE_PERMANENT_UNIT = 5550;
    long PRODUCT_TYPE_CATALOGS_WITH_NOTEBOOKS = 5551;
    long PRODUCT_TYPE_CD_WITH_HOLDER_CASE = 5552;
    long PRODUCT_TYPE_DVD_WITH_HOLDER_CASE = 5553;
    long PRODUCT_TYPE_MAGNETS = 5554;
    long PRODUCT_TYPE_PAMPHLETS_SELF_COVER = 5555;
    long PRODUCT_TYPE_PAMPHLETS_COVER_OR_SEPARATE_COVER = 5556;
    long PRODUCT_TYPE_TWO_PANEL_SELF_MAILER = 5557;
    long PRODUCT_TYPE_THREE_PANEL_SELF_MAILER = 5558;
    long PRODUCT_TYPE_MULTI_PANEL_SELF_MAILER = 5559;
    long PRODUCT_TYPE_CUSTOM_ENVELOPE = 5560;
    long PRODUCT_TYPE_CONVERTED_ENVELOPE = 5561;
    long PRODUCT_TYPE_ENVELOPE_PACKAGE = 5562;
    long PRODUCT_TYPE_MATERIALS_AND_LABOR = 5563;
    long PRODUCT_TYPE_STOP_LIGHT_PAPER = 5564;
    long PRODUCT_TYPE_SCRATCH_OFFS = 5565;
    long PRODUCT_TYPE_ADJUNCT = 5566;
    long PRODUCT_TYPE_BACK_LIT_POSTER = 5567;
    long PRODUCT_TYPE_BAR_MATS = 5568;
    long PRODUCT_TYPE_BIN_CARD = 5569;
    long PRODUCT_TYPE_BLACK_BOARD = 5570;
    long PRODUCT_TYPE_BLISTER_CARD = 5571;
    long PRODUCT_TYPE_BRAND_BOARD = 5572;
    long PRODUCT_TYPE_BRAND_GUIDES = 5573;
    long PRODUCT_TYPE_CAR_WRAPS = 5574;
    long PRODUCT_TYPE_CHART = 5575;
    long PRODUCT_TYPE_CORFLUTE = 5576;
    long PRODUCT_TYPE_COUNTER_CARD = 5577;
    long PRODUCT_TYPE_COUNTER_UNIT = 5578;
    long PRODUCT_TYPE_DECAL = 5579;
    long PRODUCT_TYPE_ENTRY_FORMS = 5580;
    long PRODUCT_TYPE_ENTRY_PAD = 5581;
    long PRODUCT_TYPE_FEATURE_PIECE = 5582;
    long PRODUCT_TYPE_FLAG = 5583;
    long PRODUCT_TYPE_FLOW_WRAP = 5584;
    long PRODUCT_TYPE_FLYSHEET = 5585;
    long PRODUCT_TYPE_FRIDGE_STRIPS = 5586;
    long PRODUCT_TYPE_GATE_COVER = 5587;
    long PRODUCT_TYPE_GIFT_CARDS = 5588;
    long PRODUCT_TYPE_LAMA = 5589;
    long PRODUCT_TYPE_LANYARD = 5590;
    long PRODUCT_TYPE_LIGHT_BOX_TRANSPARENCIES = 5591;
    long PRODUCT_TYPE_MENU = 5592;
    long PRODUCT_TYPE_MOBILE = 5593;
    long PRODUCT_TYPE_MOOD_BOARD = 5594;
    long PRODUCT_TYPE_NAME_TAG = 5595;
    long PRODUCT_TYPE_NECK_TAGS = 5596;
    long PRODUCT_TYPE_ONSERT = 5597;
    long PRODUCT_TYPE_PACKAGING = 5598;
    long PRODUCT_TYPE_PACKAGING_FOILS = 5599;
    long PRODUCT_TYPE_PALLET_STRIPS = 5600;
    long PRODUCT_TYPE_PALLET_TOPPER = 5601;
    long PRODUCT_TYPE_PALLET_WRAP = 5602;
    long PRODUCT_TYPE_PASSPORT = 5603;
    long PRODUCT_TYPE_PRICE_BOARDS = 5604;
    long PRODUCT_TYPE_PRICING_CARDS = 5605;
    long PRODUCT_TYPE_PULL_UP_BANNER = 5606;
    long PRODUCT_TYPE_PULL_UP_DISPLAY = 5607;
    long PRODUCT_TYPE_SPECTACULAR = 5608;
    long PRODUCT_TYPE_STANDEE = 5609;
    long PRODUCT_TYPE_STRUTTED_CARD = 5610;
    long PRODUCT_TYPE_TABLE_CARD = 5611;
    long PRODUCT_TYPE_TAP_MATERIAL = 5612;
    long PRODUCT_TYPE_TICKETS = 5613;
    long PRODUCT_TYPE_TOWER_UNIT = 5614;
    long PRODUCT_TYPE_TRADE_PRESENTER = 5615;
    long PRODUCT_TYPE_TRUCK_CURTAINS = 5616;
    long PRODUCT_TYPE_TSHIRTS = 5617;
    long PRODUCT_TYPE_USB = 5618;
    long PRODUCT_TYPE_VOUCHERS = 5619;
    long PRODUCT_TYPE_WINE_BAR = 5620;
    long PRODUCT_TYPE_WORKBOOK = 5621;
    long PRODUCT_TYPE_CARRIERS = 5622;
    long PRODUCT_TYPE_GRAPHIC_SERVICES = 5623;
    long PRODUCT_TYPE_FACTSHEET = 5624;
    long PRODUCT_TYPE_PREPRESS = 5625;
    long PRODUCT_TYPE_TAPWOBBLER = 5626;
    long PRODUCT_TYPE_FRIDGEWOBBLER = 5627;
    long PRODUCT_TYPE_PREMIUMS = 5628;
    long PRODUCT_TYPE_LOGISTICS = 5629;
    long PRODUCT_TYPE_ARTWORK = 5630;
    long PRODUCT_TYPE_CASESTACKER = 5631;
    long PRODUCT_TYPE_FRIDGECADDY = 5632;
    long PRODUCT_TYPE_GLORIFIER = 5633;
    long PRODUCT_TYPE_WEBTOPRINT = 5634;
    long PRODUCT_TYPE_CIRCULAR = 5635;
    long PRODUCT_TYPE_COLLATERAL = 5636;
    long PRODUCT_TYPE_DIRECTMAILER = 5637;
    long PRODUCT_TYPE_SIGNAGE = 5638;
    long PRODUCT_TYPE_WINDOWS = 5639;
    long PRODUCT_TYPE_FILM = 5640;
    long PRODUCT_TYPE_SPONSORSHIPBOARD = 5641;
    long PRODUCT_TYPE_SELLSHEETS = 5642;
    long PRODUCT_TYPE_SLIMJIM_BROCHURES = 5643;
    long PRODUCT_TYPE_TECHBULLETIN = 5644;
    long PRODUCT_TYPE_FASTFACTS = 5645;
    long PRODUCT_TYPE_PRODGUIDES = 5646;
    long PRODUCT_TYPE_REMINDERCARDS = 5647;
    long PRODUCT_TYPE_SAMPLE_STARTERKITS = 5648;
    long PRODUCT_TYPE_CLIENT_INFOPADS = 5649;
    long PRODUCT_TYPE_PI_SHEETS = 5650;
    long PRODUCT_TYPE_BOX_STUFFERS = 5651;
    long PRODUCT_TYPE_CALENDARS = 5652;
    long PRODUCT_TYPE_COUNTER_MATS = 5653;
    long PRODUCT_TYPE_CLINGFLOOR_GRAPHICS = 5654;
    long PRODUCT_TYPE_CDDVD_DUP = 5655;
    long PRODUCT_TYPE_CREATIVE_SERVICES = 5656;
    long PRODUCT_TYPE_DM_SELFMAILER = 5657;
    long PRODUCT_TYPE_DM_ENVELOPE = 5658;
    long PRODUCT_TYPE_POSTAGE = 5659;
    long PRODUCT_TYPE_PROMOTIONAL = 5660;
    long PRODUCT_TYPE_COUPON_REBATE = 5661;
    long PRODUCT_TYPE_PACKOUTS = 5662;
    long PRODUCT_TYPE_CASE_CARD = 5663;
    long PRODUCT_TYPE_NECKERS = 5664;
    long PRODUCT_TYPE_CASE_WRAP = 5665;
    long PRODUCT_TYPE_BANNERHARDWARE = 5666;
    long PRODUCT_TYPE_ENTRYBOX = 5667;
    // long PRODUCT_TYPE_FREIGHT = 5668; // already exists
    long PRODUCT_TYPE_STATIONERY = 5669;
    long PRODUCT_TYPE_TASTINGMATS = 5670;
    long PRODUCT_TYPE_WOBBLERBARFLY = 5671;
    long PRODUCT_TYPE_CUBE = 5672;
    long PRODUCT_TYPE_SHELFFIN = 5673;
    long PRODUCT_TYPE_SEMIPERMDISPLAY = 5674;
    long PRODUCT_TYPE_SHELFFLAG = 5675;
    long PRODUCT_TYPE_SHELFREADYPACK = 5676;
    long PRODUCT_TYPE_DISTRIBUTION = 5677;
    long PRODUCT_TYPE_BELLYBAND = 5678;
    long PRODUCT_TYPE_BILLBOARD = 5679;
    long PRODUCT_TYPE_BRAND_SPOTTER = 5680;
    long PRODUCT_TYPE_BS_POSTER = 5681;
    long PRODUCT_TYPE_COFFRET_SLEEVE = 5682;
    long PRODUCT_TYPE_COUNTER_UNITS = 5683;
    long PRODUCT_TYPE_DROP_BOX = 5684;
    long PRODUCT_TYPE_ENTRY_BOX = 5685;
    long PRODUCT_TYPE_EXHIBITION_STAND = 5686;
    long PRODUCT_TYPE_FABRIC_BANNER = 5687;
    long PRODUCT_TYPE_FILES = 5688;
    long PRODUCT_TYPE_FLAG_POLES = 5689;
    long PRODUCT_TYPE_FLEXISTEEL_POSTERS = 5690;
    long PRODUCT_TYPE_FSU_PERMANENT = 5691;
    long PRODUCT_TYPE_FSU_TEMPORARY = 5692;
    long PRODUCT_TYPE_GIFT_BAG = 5693;
    long PRODUCT_TYPE_GONDOLAS = 5694;
    long PRODUCT_TYPE_HEADER_BOARD = 5695;
    long PRODUCT_TYPE_INFO_GUIDE = 5696;
    long PRODUCT_TYPE_JOB_AID = 5697;
    long PRODUCT_TYPE_LEAFLET = 5698;
    long PRODUCT_TYPE_LICENCE_DISK_HOLDER = 5699;
    long PRODUCT_TYPE_ONE_TOUCH_PACKS = 5700;
    long PRODUCT_TYPE_PLINTH_SLEEVE = 5701;
    long PRODUCT_TYPE_POP_UP_STAND = 5702;
    long PRODUCT_TYPE_SAMPLES = 5703;
    long PRODUCT_TYPE_SHELF_DIVIDER = 5704;
    long PRODUCT_TYPE_SHELF_HEADER = 5705;
    long PRODUCT_TYPE_SHELF_LIP = 5706;
    long PRODUCT_TYPE_SHELF_STOPPER = 5707;
    long PRODUCT_TYPE_SHELF_TRAY = 5708;
    long PRODUCT_TYPE_STRUT_CARDS = 5709;
    long PRODUCT_TYPE_TEST_PRINTS = 5710;
    long PRODUCT_TYPE_TRAY = 5711;
    long PRODUCT_TYPE_WALL_PAPER = 5712;
    long PRODUCT_TYPE_Z_CARD = 5713;
    long WORKGROUP_TAB_PROFILE = 6000;
    long WORKGROUP_TAB_CAPABILITIES = 6001;
    long CAPABILITY_PROFILE = 6002;
    long SPECIALTY_PRODUCT_AD = 6010;
    long SPECIALTY_PRODUCT_ANNOUNCEMENT = 6011;
    long SPECIALTY_PRODUCT_ANNUAL_REPORT = 6012;
    long SPECIALTY_PRODUCT_BAG = 6013;
    long SPECIALTY_PRODUCT_BANNER = 6014;
    long SPECIALTY_PRODUCT_BINDER = 6015;
    long SPECIALTY_PRODUCT_BOOK = 6016;
    long SPECIALTY_PRODUCT_BOOKLET = 6017;
    long SPECIALTY_PRODUCT_BROCHURE = 6018;
    long SPECIALTY_PRODUCT_CARD = 6019;
    long SPECIALTY_PRODUCT_CARTON = 6020;
    long SPECIALTY_PRODUCT_CATALOG = 6021;
    long SPECIALTY_PRODUCT_CD = 6022;
    long SPECIALTY_PRODUCT_ENV = 6023;
    long SPECIALTY_PRODUCT_INSERT = 6024;
    long SPECIALTY_PRODUCT_FLYER = 6025;
    long SPECIALTY_PRODUCT_FOLDER = 6026;
    long SPECIALTY_PRODUCT_FORM = 6027;
    long SPECIALTY_PRODUCT_GRAPHIC = 6028;
    long SPECIALTY_PRODUCT_LABEL = 6029;
    long SPECIALTY_PRODUCT_LETTERHEAD = 6030;
    long SPECIALTY_PRODUCT_MAGAZINE = 6031;
    long SPECIALTY_PRODUCT_MANUAL = 6032;
    long SPECIALTY_PRODUCT_MULTIMEDIA = 6033;
    long SPECIALTY_PRODUCT_NEWSLETTER = 6034;
    long SPECIALTY_PRODUCT_NCR = 6035;
    long SPECIALTY_PRODUCT_SELFMAILER = 6036;
    long SPECIALTY_PRODUCT_SELL_SHEET = 6037;
    long SPECIALTY_PRODUCT_PACKAGING = 6038;
    long SPECIALTY_PRODUCT_PLASTIC_CARD = 6039;
    long SPECIALTY_PRODUCT_POP = 6040;
    long SPECIALTY_PRODUCT_POSTER = 6041;
    long SPECIALTY_PRODUCT_USER_GUIDE = 6042;
    long SPECIALTY_SERVICE_BINDING_WIRE_O = 6043;
    long SPECIALTY_SERVICE_BINDING_SADDLE_STITCH = 6044;
    long SPECIALTY_SERVICE_BINDING_PERFECT = 6045;
    long SPECIALTY_SERVICE_BINDING_GLUE = 6046;
    long SPECIALTY_SERVICE_BINDING_GBC = 6047;
    long SPECIALTY_SERVICE_DIE_CUTTING = 6048;
    long SPECIALTY_SERVICE_AQUEOUS_COATING = 6049;
    long SPECIALTY_SERVICE_DESIGN = 6050;
    long SPECIALTY_SERVICE_DIGITAL_PREPRESS = 6051;
    long SPECIALTY_SERVICE_DIGITAL_PROOFING = 6052;
    long SPECIALTY_SERVICE_DATA_PROCESSING = 6053;
    long SPECIALTY_SERVICE_UV_COATING = 6054;
    long SPECIALTY_SERVICE_INSERTING = 6055;
    long SPECIALTY_SERVICE_PERSONALIZATION = 6056;
    long SPECIALTY_SERVICE_MAILING = 6057;
    long SPECIALTY_SERVICE_FOIL_STAMPING = 6058;
    long SPECIALTY_SERVICE_EMBOSSING = 6059;
    long SPECIALTY_SERVICE_NUMBERING = 6060;
    long SPECIALTY_SERVICE_PADDING = 6061;
    long SPECIALTY_SERVICE_LAMINATION = 6062;
    long SPECIALTY_SERVICE_BINDING = 6063;
    long SPECIALTY_SERVICE_INLINE_FINISHING = 6064;
    long SPECIALTY_SERVICE_FULFILLMENT = 6065;
    long SPECIALTY_SERVICE_WAREHOUSING = 6066;
    long SPECIALTY_PRODUCT = 6070;
    long SPECIALTY_SERVICE = 6071;
    long EQUIPMENT_MAKER_ABDICK = 6100;
    long EQUIPMENT_MAKER_ADAST = 6101;
    long EQUIPMENT_MAKER_AKIYAMA = 6102;
    long EQUIPMENT_MAKER_BAUM = 6103;
    long EQUIPMENT_MAKER_CHAMPLAIN = 6104;
    long EQUIPMENT_MAKER_DELPHAX = 6105;
    long EQUIPMENT_MAKER_DIDDE = 6106;
    long EQUIPMENT_MAKER_GESTETNER = 6107;
    long EQUIPMENT_MAKER_HAMADA = 6108;
    long EQUIPMENT_MAKER_HASHIMOTO = 6109;
    long EQUIPMENT_MAKER_HAMILTON = 6110;
    long EQUIPMENT_MAKER_HARRIS = 6111;
    long EQUIPMENT_MAKER_HEATH = 6112;
    long EQUIPMENT_MAKER_HEIDELBERG = 6113;
    long EQUIPMENT_MAKER_ITEK = 6114;
    long EQUIPMENT_MAKER_KBA = 6115;
    long EQUIPMENT_MAKER_KOMORI = 6116;
    long EQUIPMENT_MAKER_MAN = 6117;
    long EQUIPMENT_MAKER_MAN_ROLAND = 6118;
    long EQUIPMENT_MAKER_MIEHLE_ROLAND = 6119;
    long EQUIPMENT_MAKER_MILLER = 6120;
    long EQUIPMENT_MAKER_MITSUBISHI = 6121;
    long EQUIPMENT_MAKER_MULTILITH = 6122;
    long EQUIPMENT_MAKER_RYOBI = 6123;
    long EQUIPMENT_MAKER_SAKURAI = 6124;
    long EQUIPMENT_MAKER_STEVENS = 6125;
    long EQUIPMENT_MAKER_XEROX = 6126;
    long EQUIPMENT_MAKER_WATKISS = 6127;
    long EQUIPMENT_CLASS_PREPRESS = 6130;
    long EQUIPMENT_CLASS_PRESS = 6131;
    long EQUIPMENT_CLASS_FINISHING = 6132;
    long EQUIPMENT_TYPE_PREPRESS_DIGITAL = 6140;
    long EQUIPMENT_TYPE_PREPRESS_PROOFS = 6141;
    long EQUIPMENT_TYPE_PREPRESS_PLATEMAKING = 6142;
    long EQUIPMENT_TYPE_PRESS_SHEETFED = 6143;
    long EQUIPMENT_TYPE_PRESS_WEBFED = 6144;
    long EQUIPMENT_TYPE_PRESS_WEB_HEATSET = 6145;
    long EQUIPMENT_TYPE_PRESS_DIGIAL = 6146;
    long EQUIPMENT_TYPE_PRESS_FLEXO = 6147;
    long EQUIPMENT_TYPE_PRESS_GRAVURE = 6148;
    long EQUIPMENT_TYPE_PRESS_FORMS = 6149;
    long EQUIPMENT_TYPE_PRESS_LETTERPRESS = 6150;
    long EQUIPMENT_TYPE_PRESS_SILK_SCREEN = 6151;
    long EQUIPMENT_TYPE_PRESS_ENVELOPE = 6152;
    long EQUIPMENT_TYPE_FINISHING_FOLDER = 6153;
    long EQUIPMENT_TYPE_FINISHING_BINDER = 6154;
    long EQUIPMENT_TYPE_FINISHING_INSERTING = 6155;
    long EQUIPMENT_TYPE_FINISHING_DIE_CUT_FOIL_LAMINATE = 6156;
    long EQUIPMENT_TYPE_FINISHING_CUTTER = 6157;
    long EQUIPMENT_TYPE_FINISHING_MISC = 6158;
    long UOFM_IN = 6160;
    long UOFM_FT = 6161;
    long UOFM_MM = 6162;
    long UOFM_CM = 6163;
    long UV_COATING = 6164;
    long INLINE_FINISHING = 6165;
    long SUPPLIER_NOT_FOUND = 6170;
    long INCOMPLETED_SEARCH_CRITERIA = 6171;
    long SEARCH_RESULT_HAS_TOO_MANY_ENTRIES = 6172;
    long INVALID_KEYWORD_SEARCH_STRING = 6173;
    long INVALID_EQUIPMENT_KEYWORD_SEARCH_STRING = 6174;
    long INVALID_USER_WORKGROUP = 6175;
    long WORKGROUP_TAB_LOGO = 6176;
    long CANNOT_DELETE_LOGO_PROFILE_FOREIGN_KEY = 6177;
    long CANNOT_DELETE_LOGO_PROPOSAL_FOREIGN_KEY = 6178;
    long CANNOT_DELETE_LOGO_PROPOSAL_TEMPLATE_FOREIGN_KEY = 6179;
    long MYDESK_FILTER_E_ACTIVE_PROJECT = 6180;
    long MYDESK_FILTER_E_ALL_PROJECT = 6181;
    long NO_PERMISSION_TO_REJECT_ORDER = 6182;
    long NO_PERMISSION_TO_REJECT_CHANGE_ORDER = 6183;
    long WORKGROUP_SPECIALTIES = 6184;
    long WORKGROUP_EQUIPMENTS = 6185;
    long WORKGROUP_CAPABILITIES = 6186;
    long TASK_DEPENDENCIES_CONSTRAINT = 6187;
    long INVALID_TASK_ACTUAL_COMPLETE_DATE = 6188;
    long INVALID_LOGO_FILE = 6189;
    long SEARCH_STRING_TOO_SHORT = 6190;
    long SEARCH_CANNOT_BE_PROCESSED = 6191;
    long CHOOSE_ONE = 6192;
    long TR_TRACKING_TYPE_PROJECT_COMPLETION_DATE_CHANGED = 6300;
    long TR_TRACKING_TYPE_PROJECT_COMPLETION_DATE_CHANGED_DESC = 6301;
    long NOTIFICATION_TYPE_PASSWORD_RESET = 6302;
    long INVALID_LOGIN_ACCOUNT_LOCKED = 6303;
    long INVALID_PASSWORD_LOCKOUT_WARNING = 6304;
    long INVALID_PASSWORD_LOCKOUT = 6305;
    long PERM_INFO_RESET_USER_PASSWORD = 6306;
    long SCHEDULE_DESKOID_LABEL = 6307;
    long ALL_RECENT_PROJECTS = 6308;
    long ALL_HOT_PROJECTS = 6309;
    long RELATIVE_DATE_RANGE_LAST_24_HOURS = 6310;
    long RELATIVE_DATE_RANGE_LAST_7_DAYS = 6311;
    long RELATIVE_DATE_RANGE_LAST_14_DAYS = 6312;
    long RELATIVE_DATE_RANGE_LAST_30_DAYS = 6313;
    long RELATIVE_DATE_RANGE_LAST_60_DAYS = 6314;
    long RELATIVE_DATE_RANGE_LAST_90_DAYS = 6315;
    long RELATIVE_DATE_RANGE_NEXT_24_HOURS = 6316;
    long RELATIVE_DATE_RANGE_NEXT_7_DAYS = 6317;
    long RELATIVE_DATE_RANGE_NEXT_14_DAYS = 6318;
    long RELATIVE_DATE_RANGE_NEXT_30_DAYS = 6319;
    long RELATIVE_DATE_RANGE_NEXT_60_DAYS = 6320;
    long RELATIVE_DATE_RANGE_NEXT_90_DAYS = 6321;
    long RELATIVE_DATE_RANGE_LAST_180_DAYS = 6322;
    long RELATIVE_DATE_RANGE_NEXT_180_DAYS = 6323;
    long RELATIVE_DATE_RANGE_LAST_1_YEAR = 6324;
    long RELATIVE_DATE_RANGE_NEXT_1_YEAR = 6325;
    long SPECIALTY_SERVICE_ANIMATION = 6326;
    long SPECIALTY_SERVICE_AUDIO_MIXING = 6327;
    long SPECIALTY_SERVICE_DVD_AUTHORING = 6328;
    long SPECIALTY_SERVICE_E_LEARNING = 6329;
    long SPECIALTY_SERVICE_HD_EDITING = 6330;
    long SPECIALTY_SERVICE_INTERACTIVE_PROG = 6331;
    long SPECIALTY_SERVICE_INTERNET_DESIGN = 6332;
    long SPECIALTY_SERVICE_LEARNING_MANAGEMENT = 6333;
    long SPECIALTY_SERVICE_ORIGINAL_MUSIC = 6334;
    long SPECIALTY_SERVICE_PHOTOGRAPHY = 6335;
    long SPECIALTY_SERVICE_SCRIPT_WRITING = 6336;
    long SPECIALTY_SERVICE_TV_COMMERCIAL = 6337;
    long SPECIALTY_SERVICE_VIDEO_PROD = 6338;
    long SPECIALTY_SERVICE_VIDEO_EDITING = 6339;
    long SPECIALTY_SERVICE_VISUAL_EFFECT = 6340;
    long SPECIALTY_SERVICE_WEB_DESIGN = 6341;
    long SPECIALTY_SERVICE_WEB_DB = 6342;
    long RFE_SUPPLIER_AUTO_PRICING = 6500;
    long ROUTING_USER_NOT_ACTIVE = 6501;
    long PROCUREMENT_RANGE_PAST_WEEK = 6502;
    long PROCUREMENT_RANGE_PAST_2_WEEK = 6503;
    long PROCUREMENT_RANGE_PAST_30_DAYS = 6504;
    long PROCUREMENT_RANGE_PAST_60_DAYS = 6505;
    long PROCUREMENT_RANGE_PAST_90_DAYS = 6506;
    long PROCUREMENT_RANGE_PAST_120_DAYS = 6507;
    long SEARCHFIELD_ORDER = 6508;
    long SEARCHFIELD_PROJECT = 6509;
    long DATEFIELD_ORDER_CREATION_DATE = 6510;
    long DATEFIELD_ORDER_COMPLETION_DATE = 6511;
    long MY_ORDERS = 6512;
    long ALL_ORDERS = 6513;
    long ALL_TRANSACTIONS = 6514;
    long BUY_TRANSACTIONS = 6515;
    long SELL_TRANSACTIONS = 6516;
    long MY_RFES = 6520;
    long ALL_RFES = 6521;
    long DATEFIELD_RFE_CREATION_DATE = 6522;
    long DATEFIELD_RFE_SUBMIT_DATE = 6523;
    long DATEFIELD_RFE_DUE_DATE = 6524;
    long SEARCHFIELD_RFE = 6525;
    long MY_ESTIMATES = 6526;
    long ALL_ESTIMATES = 6527;
    long DATEFIELD_EST_CREATION_DATE = 6528;
    long DATEFIELD_EST_SUBMIT_DATE = 6529;
    long DATEFIELD_EST_EXPIRED_DATE = 6530;
    long SEARCHFIELD_EST = 6531;
    long PROJECT_CATEGORY = 6532;
    long WORKSPACE_TAB_PROJECTS = 6534;
    long WORKSPACE_TAB_RFES = 6535;
    long WORKSPACE_TAB_ESTIMATES = 6536;
    long WORKSPACE_TAB_ORDERS = 6537;
    long WORKSPACE_TAB_SPECS = 6538;
    long WORKSPACE_TAB_INVENTORIES = 6539;
    long WORKSPACE_TAB_RFQS = 6540;
    long WORKSPACE_TAB_QUOTES = 6541;
    long MY_RFQS = 6542;
    long ALL_RFQS = 6543;
    long DATEFIELD_RFQ_CREATION_DATE = 6544;
    long DATEFIELD_RFQ_SUBMIT_DATE = 6545;
    long DATEFIELD_RFQ_DUE_DATE = 6546;
    long SEARCHFIELD_RFQ = 6545;
    long SEARCHFIELD_QUOTE = 6546;
    long MY_QUOTES = 6547;
    long ALL_QUOTES = 6548;
    long OBJECT_STATE_DESC_TEAMOBJECT_ACTIVATED = 6549;
    long OBJECT_STATE_DESC_TEAMOBJECT_DELETED = 6550;
    long OBJECT_STATE_DESC_TEAMOBJECT_INACTIVATED = 6551;
    long WORKGROUP_TAB_EXTENDED_NOTIFICATION = 6552;
    long MYINFO_TAB_EXTENDED_NOTIFICATION = 6553;
    long OBJECT_CLASS_EXTENDED_NOTIFICATION = 6554;
    long EXTENDED_NOTIFICATION_NOT_FOUND = 6555;
    long NO_PERMISSION_TO_DELETE_THIS_EXTENDED_NOTIFICATION = 6556;
    long NO_PERMISSION_TO_UPDATE_THIS_EXTENDED_NOTIFICATION = 6557;
    long INVALID_ASSIGN_TO = 6558;
    long SEARCHFIELD_SHIPMENT = 6559;
    long SEARCHFIELD_SPEC = 6560;
    long MY_SHIPMENTS = 6561;
    long ALL_SHIPMENTS = 6562;
    long COPY_SHIPMENT_REQUIRED_SOURCE_SHIPMENT = 6563;
    long COPY_SHIPMENT_REQUIRED_ONE_SOURCE = 6564;
    long COPY_SHIPMENT_REQUIRED_DESTINATION = 6565;
    long TR_TRACKING_TYPE_SHIPMENT_COPIED = 6566;
    long TR_TRACKING_TYPE_SHIPMENT_COPIED_DESC = 6567;
    long SHIPMENT_REQUIRED = 6568;
    long SHIPMENT_TEMPLATE = 6569;
    long COPY_SHIPMENT_AMBIGUOUS_DESTINATION = 6570;
    long COPY_SHIPMENT_JOB_EXISTED=6571;

    long DIGEST_ALGORITHM_MD5 = 6600;
    long DIGEST_ALGORITHM_SHA1 = 6601;
    long COMPRESSION_ALGORITHM_DEFLATE = 6610;
    long COMPRESSION_ALGORITHM_GZIP = 6611;
    long COMPRESSION_ALGORITHM_ZIP = 6612;
    long CHECKSUM_ALGORITHM_CRC32 = 6620;
    long CHECKSUM_ALGORITHM_ADLER32 = 6621;
    long CELL_MISSING_REQUIRED_QUANTITY = 6622;
    long AUTH_METHOD_SSO_SIMPLE_HTTPS_POST = 6699; 
    long AUTH_METHOD_PASSWORD = 6700;
    long AUTH_METHOD_SSO_SAML_11 = 6701;
    long OCI_AUTH_DOMAIN_REQUIRED = 6702;
    long OCI_AUTH_DOMAIN_SECRET_REQUIRED = 6703;
    long OCI_AUTH_IDENTITY_REQUIRED = 6704;
    long OCI_AUTH_RETURN_URL_REQUIRED = 6705;
    long OCI_AUTH_FAILED = 6706;
    long OCI_VERSION_NOT_SUPPORTED = 6707;
    long OCI_OPI_VERSION_NOT_SUPPORTED = 6708;

    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CHANGE_CREATED = 7000;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CHANGE_CREATED_DESC = 7001;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CHANGE_DELETED = 7002;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CHANGE_DELETED_DESC = 7003;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CHANGE_SENT = 7004;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CHANGE_SENT_DESC = 7005;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CHANGE_APPROVED = 7006;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_CHANGE_APPROVED_DESC = 7007;
    long WORKSPACE_TAB_PURCHASE_REQUESTS = 7008;
    long DATEFIELD_PURCHASE_REQUEST_CREATION_DATE = 7009;
    long DATEFIELD_PURCHASE_REQUEST_COMPLETION_DATE = 7010;
    long SEARCHFIELD_PURCHASE_REQUEST = 7011;
    long MY_PURCHASE_REQUESTS = 7012;
    long ALL_PURCHASE_REQUESTS = 7013;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_REVISED = 7014;
    long TR_TRACKING_TYPE_PURCHASE_REQUEST_REVISED_DESC = 7015;
    long CONSTRAINT_TOTAL_PRICE_BREAKOUT_TOO_BIG_FOR_QUOTED_PRICE = 7016;
    long NOTIFICATION_TYPE_ROUTING_OBJECT_APPROVED_WITH_CHANGES = 7020;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_APPROVED_WITH_CHANGES = 7021;
    long TR_TRACKING_TYPE_ROUTING_OBJECT_APPROVED_WITH_CHANGES_DESC = 7022;
    long NOTIFICATION_TYPE_JOB_REQUEST_UPDATED = 7400;
    long NOTIFICATION_TYPE_JOB_REQUEST_CREATED = 7401;
    long NOTIFICATION_TYPE_JOB_REQUEST_DELETED = 7402;
    long NOTIFICATION_TYPE_JOB_REQUEST_ACCEPTED = 7403;
    long NOTIFICATION_TYPE_JOB_REQUEST_REJECTED = 7404;
    long NOTIFICATION_TYPE_JOB_REQUEST_PRICING_UPDATED = 7405;
    long NOTIFICATION_TYPE_JOB_REQUEST_IN_PRICING = 7406;
    long WORKGROUP_WAREHOUSE_LOCATION = 7500;
    long OBJECT_STATE_DESC_WAREHOUSE_LOCATION_ACTIVE = 7501;
    long OBJECT_STATE_DESC_WAREHOUSE_LOCATION_INACTIVE = 7502;
    long OBJECT_STATE_DESC_INVENTORY_STATUS_INBOUND = 7503;
    long OBJECT_STATE_DESC_INVENTORY_STATUS_AVAILABLE = 7504;
    long OBJECT_STATE_DESC_INVENTORY_STATUS_RESERVED = 7505;
    long OBJECT_STATE_DESC_INVENTORY_STATUS_RELEASED = 7506;
    long OBJECT_STATE_DESC_INVENTORY_STATUS_QUARANTINED = 7507;
    long OBJECT_STATE_DESC_INVENTORY_STATUS_DESTROYED = 7508;
    long WORKGROUP_TAB_INVENTORY = 7509;
    long CANNOT_DELETE_INVENTORY_CATEGORY_NOT_FOUND_ERROR = 7510;
    long CANNOT_DELETE_INVENTORY_CLASSIFICATION_NOT_FOUND_ERROR = 7511;
    long CANNOT_DELETE_INVENTORY_ACTIVITY_TYPE_NOT_FOUND_ERROR = 7512;
    long CANNOT_ADD_SUPPLIER_WHLOCATION_FOREIGN_KEY = 7513;
    long INVENTORY_INVALID_STATE_TRANSITION = 7515;
    long INVENTORY_INVALID_STATE_TYPE_COMBINATION = 7516;
    long CANNOT_DELETE_SHIPMENT_REQUEST_ACTIVITY = 7517;
    long NO_PERMISSION_TO_DELETE_UPLOAD_HISTORY = 7518;
    long UPLOAD_HISTORY_NOT_FOUND = 7519;
    long UNABLE_TO_UPLOAD = 7520;
    long INVALID_FORMAT_SOURCE_TRANSACTION_ID = 7521;
    long INVALID_FORMAT_LOCATION = 7522;
    long INVALID_FORMAT_ITEM_CODE_NULL = 7523;
    long INVALID_FORMAT_TRANSACTION_DATE = 7524;
    long INVALID_FORMAT_TRANSACTION_TYPE = 7525;
    long INVALID_FORMAT_TRANSACTION_GROUP = 7526;
    long INVALID_FORMAT_TRANSACTION_QTY = 7527;
    long IVENTORY_LOCATION_INVALID = 7528;
    long INVENTORY_ITEM_CODE_NOT_EXISTED = 7529;
    long INVENTORY_INVALID_QTY_CAPACITY = 7530;
    long INVENTORY_TRANSACTION_DATE_CANNOT_BE_FUTURE = 7531;
    long INVENTORY_TRANSACTION_GROUP_REQUIRED = 7532;
    long INVENTORY_TRANSACTION_GROUP_INVALID = 7533;
    long INVENTORY_LOCATION_NOT_EXISTED = 7534;
    long INVENTORY_QUANTITY_TOO_BIG = 7535;
    long INVENTORY_TYPE_NOT_EXISTED = 7536;
    long INVALID_FORMAT_QUANTITY_NULL = 7537;
    long UNIQUE_WAREHOUSE_LOCATION_CONSTRAINT = 7538;
    long CANNOT_DELETE_SKU_UT_FOREIGN_KEY = 7539;
    long TOO_MANY_ACTIVITIES_FOUND_FOR_GIVEN_REFERENCE = 7540;
    long NO_ACTIVITY_FOUND_FOR_GIVEN_REFERENCE = 7541;
    long INVALID_INVENTORY_STATE = 7542;
    long INVENTORY_RECORD_TERMINAL_STATE = 7543;
    long INVENTORY_QUANTITY_CANNOT_BE_CHANGED = 7544;
    long TOO_MANY_ACTIVITY_FOR_SKU_REFERENCE = 7545;
    long FOR_WORKGROUP_REQUIRED = 7546;
    long INVALID_UPLOAD_FILE = 7547;
    long UNABLE_TO_PROCESS_FILE = 7548;
    long IDCODE_CANNOT_BE_NULL = 7549;
    long DATE_MUST_BE_BEFORE_TODAY = 7550;
    long PERM_INFO_MANAGE_INVENTORY_ADMIN = 7551;
    long TRANSACTIONDATETIME_TIME_REQUIRED = 7552;
    long TRANSACTIONDATETIME_DATE_REQUIRED = 7553;
    long INVALID_FORMAT_TRANSACTION_TIME = 7554;
    long CANNOT_PROCESS_TRANSACTION_DATETIME = 7555;
    long INVALID_UOM = 7556;
    long INVALID_QUANTITY = 7557;
    long INVENTORY_ACTIVITY_TYPES_REQUIRED = 7558;
    long INVENTORY_TYPE_REQUIRED = 7559;
    long INVALID_TRANSACTION_INDICATOR = 7560;
    long INCOMING_SHIPMENT_DEFAULT_IS_REQUIRED = 7561;
    long RECEIVED_SHIPMENT_DEFAULT_IS_REQUIRED = 7562;
    long CANNOT_DELETE_INCOMING_SHIPMENT_DEFAULT = 7563;
    long CANNOT_DELETE_RECEIVED_SHIPMENT_DEFAULT = 7564;
    long NO_CATEGORY_WAS_SELECTED = 7565;
    long NO_CLASSIFICATION_WAS_SELECTED = 7566;
    long CATEGORY_NOT_FOUND = 7567;
    long CLASSIFICATION_NOT_FOUND = 7568;
    long PERM_INFO_REVIEW_JOB_REQUEST = 7569;
    long WAREHOUSE_LOCATION_IS_REQUIRED = 7570;
    long INVENTORY_SETTTING_INCOMING_DEFAULT_REQUIRED = 7571;
    long INVENTORY_SETTTING_RECEIVED_DEFAULT_REQUIRED = 7572;
    long CLIENT_IS_BEING_USED = 7573;
    long PERM_INFO_CLOSE_QUOTE = 7574;
    long TR_TRACKING_TYPE_RATING_QUESTIONNAIRE_EDITED = 7575;
    long TR_TRACKING_TYPE_RATING_QUESTIONNAIRE_EDITED_DESC = 7576;
    long BATCH_UPDATE_GENERAL_INFO = 7577;
    long RFQ_QUOTE_SUBMITTED = 7578;
    long VIEW_WORKGROUP_INVENTORY = 7579;
    long INVALID_LOGIN_WORKGROUP_LOCKED = 7580;
    long WORKGROUP_TAB_TAX = 7581;
    long TAX_DEFAULT_LABEL_STR = 7582;
    long FILE_PREFS = 7583;
    long PERM_INFO_MANAGE_DOCUMENT_SETTINGS = 7584;
    long PERM_INFO_VIEW_WORKGROUP_ROLE_PRIVILEGES = 7585;
    long PERM_INFO_VIEW_PROJECT_ROLE_PRIVILEGES = 7586;
    long PRODUCT_TYPE_DATA_PROCESSING = 7587;
    long INVALID_EXTERNAL_TRANSACTION_TYPE = 7588;
    long BACKING_SHEET = 7589;
    long FSU = 7590;
    long SHELF_STRIP = 7591;
    long BUNTING = 7592;
    long SCRATCH_CARD = 7593;
    long STREAMER = 7594;
    long DANGLER = 7595;
    long BAGS = 7596;
    long VOUCHER = 7597;
    long SACHET_HANGER = 7598;
    long DISPENSER = 7599;
    long TAGS = 7600;
    long TRAY = 7601;
    long HEADER_CARD = 7602;
    long TRADE_BRIEFING_FOLDER = 7603;
    long CATEGORY_BANNER = 7604;
    long CATEGORY_DIVIDER = 7605;
    long CARRY_TALKER = 7606;
    long EYE_CATCHER = 7607;
    long FLOOR_GRAPHICS = 7608;
    long COMIC_BOOK = 7609;
    long CUT_SIZE = 7610;
    long FINANCIAL = 7611;
    long GIFT_WRAP = 7612;
    long GREETING_CARD = 7613;
    long RFQ_NOT_ACTIVE_CONSTRAINT = 7614;
    long DMS_CUSTOM_FILES_FOLDER_TITLE = 7700;
    long DMS_CUSTOM_FILES_FOLDER_DESCRIPTION = 7701;
    long OBJECT_CLASS_WORKGROUP_CUSTOM_DOCUMENT = 7702;
    long OBJECT_CLASS_REPOSITORY_REQUEST = 7703;
    long OBJECT_STATE_REPOSITORY_REQUEST_QUEUED = 7710;
    long OBJECT_STATE_REPOSITORY_REQUEST_PROCESSING = 7711;
    long OBJECT_STATE_REPOSITORY_REQUEST_SUCCESSFUL = 7712;
    long OBJECT_STATE_REPOSITORY_REQUEST_RETRYING = 7713;
    long OBJECT_STATE_REPOSITORY_REQUEST_FAILED = 7714;
    long OBJECT_STATE_REPOSITORY_REQUEST_ERROR_PROCESSOR = 7715;
    long OBJECT_STATE_REPOSITORY_REQUEST_ERROR_PROCESSOR_NOT_AVAILABLE = 7716;
    long OBJECT_STATE_REPOSITORY_REQUEST_ERROR_RESOURCE = 7717;
    long OBJECT_STATE_REPOSITORY_REQUEST_ERROR_RESOURCE_NOT_AVAILABLE = 7718;
    long OBJECT_STATE_REPOSITORY_REQUEST_ERROR_RESOURCE_ACCESS_DENIED = 7719;
    long CLOSING_CHANGE_ORDER_ALL_ITEMS_CONSTRAINT = 7720;
    long PERM_INFO_MANAGE_ITEM_MASTER_ADMIN = 7721;
    long PERM_INFO_ATTACH_ITEM_MASTER = 7722;
    long PERM_INFO_SET_PROJECT_HOT_FOR_TEAM = 7723;
    long PERM_INFO_ROUTE_DOCUMENT = 7724;
    long PERM_INFO_ROUTE_PROJECT = 7725;
    long USE_TEAM_SETTING = 7726;
    long OBJECT_CLASS_ESTIMATE_ITEM_PRICE = 7727;
    long ITEM_MASTER_INCONSISTENT_SPEC_TYPE = 7728;
    long SPEC_TYPE_INCONSISTENT = 7729;
    long ITEM_NUMBER_REQUIRED = 7730;
    long ITEM_VERSION_PRODUCT_NUMBER_REQUIRED = 7731;
    long ITEM_MASTER_SPEC_TYPE_REQUIRED = 7732;
    long ITEM_VERSION_NEW = 8000;
    long ITEM_VERSION_ACTIVE = 8001;
    long ITEM_VERSION_OBSOLETE = 8002;
    long WORKSPACE_TAB_ITEM_MASTER = 8003;
    long ITEM_VERSION_NO_EDIT_ALLOWED = 8004;
    long LOCKED_VERSION_NO_EDIT_ALLOWED = 8005;
    long OBSOLETE_VERSION_NO_EDIT_ALLOWED = 8006;
    long MASTER_NOT_SPECIFIED = 8007;
    long MASTER_NOT_FOUND = 8008;
    long REFERENCE_OBJECT_IS_NOT_MASTER = 8009;
    long ITEM_MASTER_NOT_FOUND = 8010;
    long ITEM_MASTER_DELETE_CONSTRAINT = 8011;
    long ITEM_VERSION_DELETE_CONSTRAINT = 8012;
    long ITEM_REFERENCE_UNIQUE_CONSTRAINT = 8013;
    long ITEM_VERSION_UNIQUE_CONSTRAINT = 8014;
    long MANAGE_ITEM_MASTER_PERMISSION_CONSTRAINT = 8015;
    long BATCH_DELETE_ITEM_VERSION = 8016;
    long BATCH_MAKR_ITEM_VERSION_CURRENT = 8017;
    long BATCH_MARK_ITEM_VERSION_OBSOLETE = 8018;
    long ITEM_VERSION_OBSOLETE_CANNOT_BE_CURRENT = 8019;
    long ITEM_VERSION_INVALID_NEXT_STATE = 8020;
    long ITEM_MASTER_PERMISSION_CONSTRAINT = 8021;
    long ITEM_VERSION_CURRENT_CONSTRAINT = 8022;
    long ITEM_VERSION_OBSOLETE_CONSTRAINT = 8023;
    long ITEM_VERSION_NONE_SELECTED = 8024;
    long ITEM_MASTER_SKU_PATTERN = 8025;
    long INVENTORY_DELETE_CONSTRAINT = 8026;
    long CONSTRAINT_SELECT_ITEM_MASTER = 8027;
    long TR_TRACKING_TYPE_ITEM_ATTACHED = 8028;
    long TR_TRACKING_TYPE_ITEM_ATTACHED_DESC = 8029;
    long TR_TRACKING_TYPE_ITEM_DETACHED = 8030;
    long TR_TRACKING_TYPE_ITEM_DETACHED_DESC = 8031;
    long ATTACHED_ITEM_CONSTRAINT = 8032;
    long ITEM_MASTER = 8033;
    long ITEM_MASTER_PROCURE_CONSTRAINTS = 8034;
    long CANNOT_TRANSFER_PROJECT_DUE_TO_ITEM_MASTER = 8035;
    long USER_DEFINED_UOM_PATTERN = 8036;
    long USER_DEFINED_UOM = 8037;
    long DELETE_UOM_CONSTRAINT = 8038;
    long ITEM_VERSION_ACTIVE_WARNING = 8039;
    long MYDESK_FILTER_E_QUOTES = 8040;
    long INVALID_SINGLE_MODE_SPEC_SELECTION = 8041;
    long SPEC_TYPE_DOES_NOT_SUPPORT_ITEM_MASTER = 8042;
    long NO_ITEM_MASTER_SPEC_TYPE_AVAILABLE = 8043;
    long BATCH_HOT = 8044;
    long BATCH_NOT_HOT = 8045;
    long NO_PERMISSION_TO_ATTACH_ITEM = 8046;
    long PR_UOM_OZ = 8047;
    long ITEM_VERSION_CURRENT_WARNING = 8048;
    long OBJECT_CLASS_CLIENT_WORKGROUP = 8050;
    long OBJECT_CLASS_TEAM = 8051;
    long OBJECT_CLASS_PFD = 8052;
    long OBJECT_CLASS_PSD = 8053;
    long OBJECT_STATE_DESC_ORDER_DRAFT = 8100;
    long TR_TRACKING_TYPE_ORDER_DRAFT_CREATED = 8101;
    long TR_TRACKING_TYPE_ORDER_DRAFT_CREATED_DESC = 8102;
    long TR_TRACKING_TYPE_ORDER_DRAFT_UPDATED = 8103;
    long TR_TRACKING_TYPE_ORDER_DRAFT_UPDATED_DESC = 8104;
    long TR_TRACKING_TYPE_ORDER_DRAFT_DELETED = 8105;
    long TR_TRACKING_TYPE_ORDER_DRAFT_DELETED_DESC = 8106;
    long CANNOT_DRAFT_ORDER_BLASTER = 8107;
    long ORDER_CANNOT_BE_DELETED = 8108;
    long INVALID_ACTION = 8109;
    long ORDER_SUPPLIER_REQUIRED_DUE_TO_BREAKOUT = 8110;
    long WORKGROUP_REQUIRED_FOR_ADDING_SUPPLIER = 8111;
    long PERM_INFO_EDIT_JOB_DETAILS = 8112;
    long MYSELF = 8115;
    long PROJECT_TEAM = 8116;
    long EVERYONE = 8117;
    long OBJECT_STATE_DESC_JOB_SPEC_USER_STATE_ACTIVE = 8118;
    long OBJECT_STATE_DESC_JOB_SPEC_USER_STATE_ARCHIVED = 8119;
    long JOB_SPEC_USER_STATE_ACTION_DESC = 8120;
    long JOB_SPEC_REQUIRED_FOR_UPDATING_JOB_SPEC_USER_STATUS = 8121;
    long QUOTES_DESKOID_LABEL = 8122;
    long PRODUCTIZE_ACTION_DESC = 8123;
    long QUOTE_BUY_ORDERS_CONSTRAINT = 8500;
    long NO_ORDERS_TO_BUY = 8501;
    long ORDER_WIZ_NO_KEY = 8502;
    long ORDER_WIZ_NO_DATA = 8503;
    long ORDER_WIZ_ORDER_NOT_FOUND = 8504;
    long ORDER_WIZ_ORDER_JOB_CONSTRAINT = 8505;
    long ORDER_WIZ_ORDER_ITEM_NOT_FOUND = 8506;
    long INVALID_SELECTION = 8507;
    long NO_ORDERS_AVAILABLE = 8508;
    long CREATE_BUY_CHANGE_ORDERS_CONSTRAINT = 8509;
    long CREATE_SELL_CHANGE_ORDERS_CONSTRAINT = 8510;
    long JOB_SELECTION_CONSTRAINT = 8511;
    long OBJECT_STATE_DESC_PSF_CANCELLED = 8512;
    long NOTIFICATION_TYPE_PSF_PERSON_CREATED = 8513;
    long NO_USER_FOUND = 8514;
    long EMAIL_DOES_NOT_MATCH = 8515;
    long INVALID_SECURITY_CODE = 8516;
    long LOGIN_NAME_DOES_NOT_MATCH = 8517;
    long PASSWORD_RESET_LINK_EXPIRED = 8518;
    long USER_NOT_ACTIVATED = 8519;
    long OBJECT_STATE_DESC_PSF_DRAFT = 8520;
    long OBJECT_STATE_DESC_PSF_SUBMITTED = 8521;
    long OBJECT_STATE_DESC_PSF_ACCEPTED = 8522;
    long OBJECT_CLASS_PSF = 8523;
    long OBJECT_STATE_DESC_PSF_DELETED = 8524;
    long TASK_TYPE_BUY_ORDER_FOR_QUOTE_NAME = 8525;
    long TASK_TYPE_BUY_ORDER_FOR_QUOTE_DESC = 8526;
    long NO_ACTIVE_ORDERS = 8527;
    long NO_PERMISSION_TO_REJECT_QUOTE = 8528;
    long UNSUPPORT_IMAGE_TYPE = 8529;
    long PSF_DEFAULT_SEARCH_FILTER = 8530;
    long PSF_TASK_ID = 8531;
    long PSF_PROJECT_ID = 8532;
    long PSF_CREATE_DATE = 8533;
    long PSF_MOD_DATE = 8534;
    long PSF_SUBMIT_DATE = 8535;
    long PSF_RESPONSE_DATE = 8536;
    long PSF_DELETE_ACTION_DESC = 8537;
    long RFQ_DECLINED = 8538;
    long TRACKING_TYPE_RFQ_DECLINED = 8539;
    long TRACKING_TYPE_RFQ_DECLINED_DESC = 8540;
    long NOTIFICATION_TYPE_RFQ_DECLINED = 8541;
    long PERM_INFO_DECLINE_RFQ = 8542;
    long NOTIFICATION_TYPE_CUSTOM_NOTIFICATION_GENERATED = 8543;
    long APPLYING_CLOSING_CHANGE_ORDERS_CONSTRAINT = 8544;
    long APPLYING_CLOSING_CHANGE_ORDER_PENDING_CONSTRAINT = 8545;
    long PERM_INFO_INVITE_SUPPLIER = 8546;
    long OBJECT_STATE_DESC_PSF_IN_PRICING = 8547;
    long OBJECT_STATE_DESC_PSF_PENDING_ACCEPTANCE = 8548;
    long OBJECT_STATE_DESC_PSF_REJECTED = 8549;
    long PAYMENT_METHOD_IN_CONTRACT = 8550;
    long PO_NUMBER_CONSTRAINTS = 8551;
    long PSF_WAS_REJECTED = 8552;
    long PLEASE_SELECT = 8553;
    long COMPLETION_DATE_CONSTRAINT = 8554;
    long COMPLETION_DATE_REQUIRED_CONSTRAINT = 8555;
    long CLIENT_CODE_REQUIRED_CONSTRAINT = 8556;
    long CLIENT_CODE_INVALID = 8557;
    long CLIENT_CODE_NOT_UNIQUE = 8558;
    long COMPLETION_DATE_EQUAL_OR_GREATER = 8559;
    long DEFAULT_CONTRACT_PRICING_TIME_OVERLAP = 8561;
    long DEFAULT_CONTRACT_PRICING_ONE_DEFAULT = 8562;
    long PFD_REQUIRED_FOR_ACTIVE_CONTRACT = 8563;
    long ACTIVE_PSD_REQUIRED_FOR_ACTIVE_CONTRACT = 8564;
    long SUPPLIER_REQUIRED_FOR_ACTIVE_CONTRACT = 8565;
    long CLIENT_REQUIRED_FOR_ACTIVE_CONTRACT = 8566;
    long DEFAULT_CLIENT_CONTRACT_CONSTRAINT = 8567;
    long SELECTED_SUPPLIER_WITHOUT_CSR = 8568;
    long WORKGROUP_TAB_PC_REASON = 8600;
    long SELECTION_REASON_SYSTEM = 8601;
    long SELECTION_REASON_CUSTOM = 8602;
    long SELECTION_REASON_REQUIRED = 8603;
    long AC_LOCALE_DESCRIPTION_zh_CN = 8604;
    long AC_LOCALE_DESCRIPTION_fr_FR = 8605;
    long AC_LOCALE_DESCRIPTION_ja_JP = 8606;
    long AC_LOCALE_DESCRIPTION_en_AU = 8607;
    long AC_LOCALE_DESCRIPTION_fr_CA = 8608;
    long AC_LOCALE_DESCRIPTION_es_LA = 8609;
    long RFE_AUTO_DISMISS_SUPPLIER = 8610;
    long SPEC_NAME = 8625;
    long REFERENCE_NUMBER = 8626;
    long SKU = 8627;
    long JOB_ID = 8628;
    long SPEC_DATE_CREATED = 8629;
    long SPEC_LAST_UPDATED = 8630;
    long CREATED_BY_USER_REQUIRED = 8631;
    long CONTACT_LAST_NAME = 8634;
    long CONTACT_FIRST_NAME = 8635;
    long CONTACT_EMAIL_ADDRESS = 8636;
    long CONTACT_COMPANY = 8637;
    long CONTACT_WORKGROUP = 8638;
    long CONTACT_TAB_LABEL_LAST_NAME = 8639;
    long A = 8640;
    long E = 8641;
    long I = 8642;
    long O = 8643;
    long U = 8644;
    long JOB_STATUS_IN_PR = 8700;
    long PURCHASE_REQUEST_EDIT_PERMISSION = 8701;
    long QUOTE_PENDING_APPROVAL = 8702;
    long TRACKING_TYPE_QUOTE_SUBMIT_FOR_APPROVAL = 8703;
    long TRACKING_TYPE_QUOTE_SUBMIT_FOR_APPROVAL_DESC = 8704;
    long AC_WORKGROUP_TYPE_DESCRIPTION_OUTSOURCER = 8705;
    long QUOTE_CANCELLED = 8706;
    long TRACKING_TYPE_QUOTE_CANCELLED = 8707;
    long TRACKING_TYPE_QUOTE_CANCELLED_DESC = 8708;
    long QUOTE_WAS_CANCELLED = 8709;
    long PROPOSAL_CANCELLED = 8710;
    long TRACKING_TYPE_PROPOSAL_CANCELLED = 8711;
    long TRACKING_TYPE_PROPOSAL_CANCELLED_DESC = 8712;
    long QUOTE_REVISE_SUPPLIER_CONSTRAINT = 8713;
    long INVALID_QUOTE_SELECTION_MODE = 8714;
    long OBSOLETE_QUOTE_PRICE = 8715;
    long WORKGROUP_TAB_QUOTE = 8716;
    long AC_WORKGROUP_TYPE_DESCRIPTION_PARTNER = 8725;
    long PARTNER_ADMIN_MANAGEMENT = 8726;
    long PARTNER_MANAGED_SPECS = 8727;
    long PARTNER_MANAGED_ROLES = 8728;
    long PARTNER_ASSIGNMENT = 8729;
    long ACCOUNTS = 8748;
    long MANAGEMENT = 8749;
    long WORKGROUP = 8750;
    long COMPANY = 8751;
    long ACTIVATION = 8752;
    long CONTRACT = 8753;
    long PRODUCTION_ACCOUNTS = 8754;
    long INTERNAL_ACCOUNTS = 8755;
    long ALL_ACCOUNTS = 8756;
    long CREATE_DATE = 8757;
    long MOD_DATE = 8758;
    long SYSTEM_ANNOUNCEMENTS = 8759;
    long ANNOUNCEMENTS = 8760;
    long USERS = 8761;
    long EVENTS = 8762;
    long SESSIONS = 8763;
    long SPECS = 8764;
    long ROLES = 8765;
    long CUSTOM_FIELDS = 8766;
    long CUSTOM_RESOURCES = 8767;
    long DX_ACCOUNTS = 8768;
    long DX_GROUPS = 8769;
    long DX_REQUESTS = 8770;
    long ROLE_NAME_INVALID_CONSTRAINT = 8771;
    long ROLE_FILTER_ROLE_NAME = 8772;
    long ROLE_CLASS_ACCOUNT_ROLES = 8773;
    long ROLE_CLASS_PROJECT_BUYER_ROLES = 8774;
    long ROLE_CLASS_PROJECT_SUPPLIER_ROLES = 8775;
    long SELECTION_OF_ROLE_REQUIRED = 8776;
    long SPEC_REGISTRATION_FILTER_SPEC_NAME = 8777;
    long SPEC_REGISTRATION_FILTER_SPEC_FORM_ID = 8778;
    long SPEC_REGISTRATION_FILTER_DESCRIPTION = 8779;
    long SPEC_REGISTRATION_FILTER_TEMPLATE_NAME = 8780;
    long SELECTION_OF_SPEC_REQUIRED = 8781;
    long SELECTION_OF_WORKGROUP_REQUIRED = 8782;
    long ROLE_NAME_DUPLICATE_FOR_WORKGROUP = 8783;
    long BATCH_UNREGISTRATION_ACTION_DESC = 8784;
    long SELECTION_OF_COMPANY_REQUIRED = 8785;
    long PROJECT_ORDER_DESKOID_ALL_ORDERS = 8786;
    long PROJECT_ORDER_DESKOID_BUY_ORDERS = 8787;
    long PROJECT_ORDER_DESKOID_SELL_ORDERS = 8788;
    long PERM_INFO_VIEW_SYSTEM_ANNOUNCEMENT = 8800;
    long PERM_INFO_EDIT_SYSTEM_ANNOUNCEMENT = 8801;
    long RE_ROLE_PARTNER_ADMIN_MANAGER = 8802;
    long PERM_INFO_CANCEL_QUOTE = 8803;
    long OBJECT_STATE_DESC_RFE_IN_AUCTION = 8850;
    long OBJECT_STATE_DESC_RFE_SUPPLIER_IN_AUCTION = 8851;
    long TA_TASK_TYPE_SEND_BID_NAME = 8852;
    long TA_TASK_TYPE_SEND_BID_DESC = 8853;
    long TR_TRACKING_TYPE_NAME_AUCTION_RFE_SENT = 8854;
    long TR_TRACKING_TYPE_DESCRIPTION_AUCTION_RFE_SENT = 8855;
    long NOTIFICATION_TYPE_AUCTION_RFE_SENT = 8856;
    long INVALID_BID_AMOUNT = 8857;
    long INVALID_BID_SUBMIT_DATE = 8858;
    long RFE_ESTIMATEDUEDATE_BEFORE_BIDENDDATE_INVALID_CONSTRAINT = 8859;
    long RFE_REVERSE_BID_SUPPLIER_DISMISSED_COMMENTS = 8860;
    long RFE_OPENBID_EARLY_CLOSE_SUPPLIER_DISMISSED_NOT_LOWEST_BID = 8861;
    long OBJECT_STATE_DESC_RFE_AWARD_AUCTION = 8862;
    long OBJECT_STATE_DESC_RFE_SUPPLIER_AWARD_AUCTION = 8863;
    long TR_TRACKING_TYPE_NAME_END_AUCTION_RFE = 8864;
    long TR_TRACKING_TYPE_DESCRIPTION_END_AUCTION_RFE = 8865;
    long RFE_STARTDATE_AFTER_BIDENDDATE_INVALID_CONSTRAINT = 8866;
    long RFE_BIDENDDATE_BEFORE_BIDSTARTDATE_INVALID_CONTRAINT = 8867;
    long RFE_BIDENDDATE_AFTER_DUEDATE_INVALID_CONSTRAINT = 8868;
    long RFE_BIDENDDATE_INVALID_CONSTRAINT = 8869;
    long REVERSE_AUCTION = 8870;
    long TA_TASK_TYPE_SELECT_WINNING_NAME = 8871;
    long TA_TASK_TYPE_SELECT_WINNING_DESC = 8872;
    long NOTIFICATION_TYPE_AUCTION_WINNING_BID = 8873;
    long INVITE_SUPPLIERS_CONSTRAINTS = 8874;
    long RFE_TIME_PERIOD_BETWEEN_BIDENDDATE_AND_DUEDATE_INVALID_CONSTRAINT = 8875;
    long RFE_REVERSE_BID_OTHER_SUPPLIER_NAME = 8876;
    long INVALID_BID_PERMISSION = 8881;
    long BID_WINNER_REQUIRED = 8882;
    long INVALID_CLOSE_AUCTION_PERMISSION = 8883;
    long INACTIVE_AUCTION = 8884;
    long INVALID_START_AUCTION_PERMISSION = 8885;
    long INVALID_AWARD_AUCTION_PERMISSION = 8886;
    long INVALID_ESTIMATE_PRICE_ENTERED = 8887;
    long EXTEND_BID_TIME_FROM_LAST_SUPPLIER_BID = 8888;
    long EXTEND_BID_TIME_FROM_BID_END_TIME = 8889;
    long INVALID_BUCKET_NAME = 8900;
    long BUCK_ALREADY_EXISTED = 8901;
    long PM_COPY_PROJECT_SPEC_NOT_COPIED = 8902;
    long MY_ORDERS_FROM_HOT_PROJECT = 8910;
    long SELECT_YOUR_SUPPLIERS = 8915;
    long CALCULATE_LOWEST_THREE_SUPPLIERS = 8916;
    long COMPARE_LOWEST_THREE_TO_THIS_SUPPLIER = 8917;
    long COMPARE_LOWEST_THREE_TO_THESE_SUPPLIERS = 8918;
    long PERM_INFO_RUN_REPORT = 8920;
    long SPEC_SUMMARY_ITEM_EXISTS = 8925;
    long SPEC_SUMMARY_NAME_REQUIRED = 8928;
    long SPEC_SUMMARY_DEFAULT_CONSTRAINT = 8929;
    long CALC_LOOKUP_NUMBER_ABOVE_MAX = 9000;
    long CONTROL_INVALID_DATE = 9001;
    long CONTROL_INVALID_NUMBER = 9002;
    long FORM_ALREADYSUBMITTED = 9003;
    long DUPLICATES_NOT_ALLOWED = 9004;
    long DAY_NAMES = 9005;
    long SYSTEM_ERRORMSG = 9006;
    long SYSTEM_APOLOGY = 9007;
    long SYSTEM_ANONYMOUS_ACCESS_DENIED = 9008;
    long SYSTEM_ACCESS_DENIED = 9009;
    long FAULT_JSON = 9010;
    long FAULT_UNRECOGNIZED = 9011;
    long HTTP_INTERNAL_SERVER_ERROR = 9012;
    long HTTP_NOT_FOUND = 9013;
    long HTTP_FORBIDDEN = 9014;
    long ERROR_CAPTION = 9015;
    long ERROR_BULLET = 9016;
    long CONTACT_SELECTOR_CATEGORY = 9017;
    long CONTACT_SELECTOR_IN_CATEGORY = 9018;
    long CONTACT_SELECTOR_REMOVE_CONTACT = 9019;
    long CONTACT_SELECTOR_EXPAND_CATEGORY = 9020;
    long CONTACT_SELECTOR_LINK = 9021;
    long CONTACT_SELECTOR_LINK_TITLE = 9022;
    long INVALID_RFQ_RECIPIENT = 9023;
    long PAPER_ORDER_DIRECT_ALLOW_BY_BUYER_ONLY = 9025;
    long PAPER_ORDER_DIRECT_ALLOW_BY_BUYER_OR_SUPPLIER = 9026;
    long PAPER_ORDER_DIRECTED = 9027;
    long PAPER_ORDER_DIRECT = 9028;
    long NO_PAPER_SUPPLIERS = 9029;
    long PAPER_FLOW_FULL_CATALOG_TAB_NAME = 9030;
    long PAPER_FLOW_CUSTOMER_PROGRAM_TAB_NAME = 9031;
    long PAPER_PRODUCT_REQUIRED = 9032;
    long PAPER = 9033;
    long PAPER_PRODUCT_PRICING_REQUIRED = 9034;
    long PENDING_SUPPLIER_ACCEPTANCE = 9035;
    long ACCEPTED = 9036;
    long NONE = 9037;
    long ANY_CERTIFICATION = 9038;
    long FSC = 9039;
    long SFI = 9040;
    long PEFC = 9041;
    long PAPER_FLOW_SPEC_REQUIRED = 9042;
    long PAPER_FLOW_ITEM_ALREADY_CREATED = 9043;
    long PAPER_PRODUCT_PRICING_REQUIRED_FOR_ALL_PRODUCTS = 9044;
    long PAPER_ORDER_INVALID_SUPPLIER_CHOSEN = 9045;
    long DUPLICATE_SKUS = 9046;
    long OBJECT_STATE_DESC_RFE_SUPPLIER_RFE_SENT = 9050;
    long NO_PERMISSION_TO_ACCEPT_RFE = 9051;
    long TR_TRACKING_TYPE_NAME_ACCEPT_RFE = 9052;
    long TR_TRACKING_TYPE_DESCRIPTION_ACCEPT_RFE = 9053;
    long GET_PRICE = 9054;
    long OF = 9060;
    long MANAGER_SELECTION_REQUIRED = 9065;
    long ORDER_CLASSIFICATION_DESC_DEFAULT = 9066;
    long ORDER_CLASSIFICATION_DESC_PAPER = 9067;
    long ORDER_DISAPPROVE_REJECTION_COMMENTS = 9070;
    long SITE = 9080;
    long PRODUCT = 9081;
    long JOB_REQUEST = 9082;
    long OBJECT_STATE_DESC_PROJECT_STAGING = 9083;
    long SY_STATUS_DESCRIPTION_PM_PROJECT_STAGING = 9084;
    long STAGING = 9085;
    long UNIQUE_SITE_SUFFIX_REQUIRED = 9086;
    long BATCH_SAVE_TASK = 9087;
    long NO_TASKS_FOUND = 9088;
    long TA_TASK_ADD_TIMECARD_TIME_REQUIRED = 9089;
    long SERVICE_PROVIDER = 9095;
    long SITE_ACTIVE = 9096;
    long SITE_INACTIVE = 9097;
    long TA_TASK_TYPE_REVIEW_NEW_PROJECT_NAME = 9098;
    long TA_TASK_TYPE_REVIEW_NEW_PROJECT_DESC = 9099;
    long CO_SERVICE_DESCRIPTION_NGE_CUSTOMIZED_PRODUCT = 9101;
    long DEFAULT_PRODUCT_TEMPLATE = 9102;
    long FTP_FILE_UPLOAD_ERRORS_CANNOT_CONNECT = 9103;
    long FTP_FILE_UPLOAD_ERRORS_WRONG_LOGIN_NAME_AND_PASSWORD = 9104;
    long INVALID_WORKGROUP_RFQ_RECIPIENT = 9105;
    long INTERNAL_RFQ_RECIPIENT_REQUIRED = 9106;
    long MYDESK_FILTER_NEW_JOBS_SUBMITTED_BY_DATE_RECEIVED = 9107;
    long MYDESK_FILTER_NEW_JOBS_SUBMITTED_BY_DATE_DUE = 9108;
    long WORKGROUP_TAB_VERSION_CUSTOM_DATA_CONFIG = 9109;
    long AC_COUNTRY_NAME_CW = 9110;
    long ORDER_TITLE_EXIST = 9111;
    long TRACKING_EVENT_DEFAULT_SEARCH_FILTER = 9112;
    long APPROVER_SELECTION_REQUIRED = 9113;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_REPLACED = 9114;
    long TR_TRACKING_TYPE_CLOSING_CHANGE_ORDER_REPLACED_DESC = 9115;
    long NOTIFICATION_TYPE_CHANGE_ORDER_REPLACED = 9116;

    long OBJECT_STATE_DESC_JOB_REQUEST_DRAFT = 9120;
    long OBJECT_STATE_DESC_JOB_REQUEST_SUBMITTED = 9121;
    long PASSWORD_CASE_ALPHANUMERIC = 9122;

    long UNSPSC_TYPE_82121500 = 9123;

    long WORKGROUP_TAB_SPEC_PRICING_SHEET = 9124;
    long WORKGROUP_TAB_SPEC_PRICING_SHEET_UPLOAD_LOG = 9125;
    long NEW_REPORTS_SETTINGS = 9133;

    long WG_TYPE_TAB_OUTSOURCER= 9150;
    long WG_TYPE_TAB_OUTSOURCING_WITH_RFQ= 9151;
    long WG_TYPE_TAB_BUYER= 9152;
    long WG_TYPE_TAB_SUPPLIER= 9153;
    long WG_TYPE_TAB_CLIENT= 9154;
    long JOB_CREATE_DATE_FILTER = 9155;

    long TR_TRACKING_PROOFHQ_EVENT_CHANGESTATE = 9157;
    long TR_TRACKING_PROOFHQ_EVENT_CHANGESTATE_DESC = 9158;
    long TR_TRACKING_PROOFHQ_EVENT_DECISIONUPDATE = 9159;
    long TR_TRACKING_PROOFHQ_EVENT_DECISIONUPDATE_DESC = 9162;
    long TR_TRACKING_PROOFHQ_EVENT_PROCESSED = 9160;
    long TR_TRACKING_PROOFHQ_EVENT_PROCESSED_DESC = 9163;
    long TR_TRACKING_PROOFHQ_EVENT_NEW_COMMENT = 9164;
    long TR_TRACKING_PROOFHQ_EVENT_NEW_COMMENT_DESC = 9165;
    long TR_TRACKING_PROOFHQ_EVENT_REPLY_COMMENT = 9166;
    long TR_TRACKING_PROOFHQ_EVENT_REPLY_COMMENT_DESC = 9167;

    long TM_INVITATION_TYPE_PROJECT_UPDATE_COLLABORATOR = 9161;
    long AC_TERMS_TYPE_DESCRIPTION_QUOTE = 9168;

    long PR_UOM_YD = 9170;

    long ORDER_COMPLETION_INVALID_CONSTRAINT = 9171;
    long ORDER_ITEM_COMPLETION_INVALID_CONSTRAINT = 9172;

    long RANGE_INVALID_PERCENT_CONSTRAINT = 9175;

    long HOT_PROJECTS_HOT= 9177;
    long HOT_PROJECTS_NOT_HOT= 9178;
    long HOT_PROJECTS_TEAM= 9179;
    long TR_TRACKING_TYPE_RFE_REQUEST_SECOND_LOOK= 9181;
    long TR_TRACKING_TYPE_RFE_REQUEST_SECOND_LOOK_DESC= 9182;
    long TR_TRACKING_TYPE_NAME_AUTOMATION_RULE_APPLIED = 9200;
    long TR_TRACKING_TYPE_DESCRIPTION_AUTOMATION_RULE_APPLIED = 9201;

    long RFE_SUPPLIER_PARTIALLY_ACCEPTED = 9280;
    long NOTIFICATION_TYPE_RFE_PARTIALLY_ACCEPTED = 9281;
    long TR_TRACKING_TYPE_NAME_PARTIALLY_ACCEPT_RFE = 9282;
    long TR_TRACKING_TYPE_DESCRIPTION_PARTIALLY_ACCEPT_RFE = 9283;

    long OBJECT_STATE_DESC_ORDER_FINALIZED = 9290;
    long TR_TRACKING_TYPE_ORDER_MARKED_FINALIZED = 9291;
    long TR_TRACKING_TYPE_ORDER_MARKED_FINALIZED_DESC = 9292;
    long TR_TRACKING_PROPOSAL_EMAILED_TO_CLIENT = 9320;
    long TR_TRACKING_PROPOSAL_EMAILED_TO_CLIENT_DESC = 9321;
    long TR_TRACKING_TYPE_PREMEDIA_INVITED = 9420;
    long TR_TRACKING_TYPE_PREMEDIA_INVITED_DESC = 9421;
    long TR_TRACKING_PREMEDIA_ARTWORK_STATUS_CHANGE = 9433;
    long TR_TRACKING_PREMEDIA_ARTWORK_STATUS_CHANGE_DESC = 9434;

    long PRODUCT_TYPE_DISPOSABLE_STRAWS = 9795;
    long PRODUCT_TYPE_DISPOSABLE_CUTLERY = 9796;
}
// Max ID: 9022
// Count:  4588
