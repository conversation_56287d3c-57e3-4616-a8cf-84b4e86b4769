package com.noosh.app.commons.constant;

public interface ProductionMatrixConstants {
    String P_PREFIX = "P_";
    String PSD_PREFIX = "PS_";
    String PCUSTOM_PREFIX = "PC_";

    String KEY_PROJECT_NAME = P_PREFIX + "name";
    String KEY_PROJECT_CATEGORY = P_PREFIX + "category";
    String KEY_PROJECT_ID = P_PREFIX + "id";
    String KEY_PROJECT_NUMBER = P_PREFIX + "projectNumber";
    String KEY_PROJECT_CLIENT_ACCOUNT = P_PREFIX + "clientAccount";
    String KEY_PROJECT_CREATE_DATE = P_PREFIX + "projectCreateDate";
    String KEY_PROJECT_COMPLETION_DATE = P_PREFIX + "completionDate";
    String KEY_PROJECT_DESCRIPTION = PSD_PREFIX + "description";
    String KEY_PROJECT_COMMENTS = PSD_PREFIX + "comments";
    String KEY_PROJECT_STATE = P_PREFIX + "state";
    String KEY_PROJECT_STATUS = P_PREFIX + "status";
}