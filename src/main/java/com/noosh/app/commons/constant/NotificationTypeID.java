package com.noosh.app.commons.constant;

/**
 * Constant interface: NotificationTypeID
 * Constant source:    DB
 * Constant query:     select NT_NOTIFICATION_TYPE_id, constant_token from NT_NOTIFICATION_TYPE order by 1 asc
 *
 * Copyright 1998-2004 Noosh, Inc.
 *
 * <AUTHOR>
 * @since  Wed Apr 21 10:20:05 PDT 2004
 */
public interface NotificationTypeID
{
    public static final long USER_CREATED = 1000000;
    public static final long USER_ACTIVATED = 1000001;
    public static final long USER_DEACTIVATED = 1000002;
    public static final long RFE_SENT = 1000003;
    public static final long RFE_REJECTED = 1000004;
    public static final long ESTIMATE_RECEIVED = 1000005;
    public static final long ORDER_CREATED = 1000006;
    public static final long ORDER_REJECTED = 1000007;
    public static final long ORDER_RETRACTED = 1000008;
    public static final long ORDER_UPDATED = 1000009;
    public static final long ORDER_ACCEPTED = 1000010;
    public static final long ORDER_CANCELED = 1000011;
    public static final long PROJECT_MEMBER_INVITED = 1000012;
    public static final long PROJECT_MEMBER_REVOKED = 1000013;
    public static final long PROJECT_MEMBER_ROLE_UPDATED = 1000014;
    public static final long RFE_MEMBER_INVITED = 1000015;
    public static final long RFE_MEMBER_REVOKED = 1000016;
    public static final long ESTIMATE_REJECTED = 1000017;
    public static final long CHANGE_ORDER_CREATED = 1000018;
    public static final long CHANGE_ORDER_REJECTED = 1000019;
    public static final long CHANGE_ORDER_RETRACTED = 1000020;
    public static final long CHANGE_ORDER_UPDATED = 1000021;
    public static final long CHANGE_ORDER_ACCEPTED = 1000022;
    public static final long CHANGE_ORDER_CANCELED = 1000023;
    public static final long RFE_DATES_REVISED = 1000024;
    public static final long PROJECT_STATUS_CHANGED = 1000025;
    public static final long RFE_DISMISS_SUPPLIER = 1000026;
    public static final long ORDER_STATUS_CHANGED = 1000027;
    public static final long COMPLETE_SUPPLIER_RATING = 1000028;
    public static final long AUCTION_RFE_SENT = 1000029;
    public static final long HOME_LOGIN_INFO = 1000030;
    public static final long MESSAGE_SENT = 1000031;
    public static final long PROJECT_ANOTHER_MEMBER_INVITED = 1000032;
    public static final long CLOSING_CHANGE_ORDER_CREATED = 1000033;
    public static final long CLOSING_CHANGE_ORDER_UPDATED = 1000034;
    public static final long CLOSING_CHANGE_ORDER_ACCEPTED = 1000035;
    public static final long CLOSING_CHANGE_ORDER_CANCELLED = 1000036;
    public static final long CLOSING_CHANGE_ORDER_REJECTED = 1000037;
    public static final long CLOSING_CHANGE_ORDER_RETRACTED = 1000038;
    public static final long AUCTION_RFE_WINNING_BID = 1000039;
    public static final long RFE_ACCEPTED = 1000040;
    public static final long FILE_VERSION_DELETED = 1000199;
    public static final long FILE_CREATED = 1000200;
    public static final long FILE_EDITED = 1000201;
    public static final long FILE_DELETED = 1000202;
    public static final long FILE_VERSION_CREATED = 1000203;
    public static final long FILE_ACCESS_GRANTED = 1000204;
    public static final long FILE_ACCESS_REVOKED = 1000205;
    public static final long FILE_BATCH_ACTION_FAILED = 1000206;
    public static final long TASK_ASSIGNED = 1000210;
    public static final long TASK_REASSIGNED = 1000211;
    public static final long TASK_DELETED = 1000212;
    public static final long TASK_COMPLETED = 1000213;
    public static final long TASK_UPDATED = 1000214;
    public static final long TASK_READY_TO_START = 1000215;
    public static final long TASK_READY_TO_COMPLETE = 1000216;
    public static final long TASK_RENAMED = 1000217;
    public static final long SHIPMENT_CREATED = 1000218;
    public static final long SHIPMENT_UPDATED = 1000219;
    public static final long SHIPMENT_DELETED = 1000220;
    public static final long TASK_REMINDER = 1000221;
    public static final long PROJECT_TRANSFERED = 1000222;
    public static final long CUSTOM_TRACKING_POSTED = 1000223;
    public static final long RFQ_SENT = 1000224;
    public static final long RFQ_CLOSED = 1000225;
    public static final long RFQ_CANCELLED = 1000226;
    public static final long QUOTE_SENT = 1000227;
    public static final long QUOTE_REJECTED = 1000228;
    public static final long QUOTE_REVISED = 1000229;
    public static final long QUOTE_ACCEPTED = 1000230;
    public static final long QUOTE_RETRACTED = 1000231;
    public static final long ESTIMATE_ACTIVATED = 1000232;
    public static final long INVOICE_SENT = 1000233;
    public static final long INVOICE_REVISED = 1000234;
    public static final long INVOICE_ACCEPTED = 1000235;
    public static final long INVOICE_REJECTED = 1000236;
    public static final long INVOICE_RETRACTED = 1000237;
    public static final long TASK_ASSIGNED_NO_DUE_DATE = 1000238;
    public static final long SYSTEM_ACCOUNT_ACTIVATION = 1000239;
    public static final long SYSTEM_ACCOUNT_ACTIVATION_AGREE_ACCEPTED = 1000240;
    public static final long RFE_RECALLED = 1000241;
    public static final long RFE_CLOSED = 1000242;
    public static final long RFE_REOPENED = 1000243;
    public static final long ESTIMATE_RETRACTED = 1000244;
    public static final long ESTIMATE_INVALIDATED = 1000245;
    public static final long INVOICE_APPROVED = 1000246;
    public static final long SUPPLIER_INVITED = 1000247;
    public static final long SUPPLIER_UNINVITED = 1000248;
    public static final long ORDER_COMPLETED = 1000249;
    public static final long INVENTORY_BELOW_SAFETY  = 1000250;
    public static final long INVENTORY_BELOW_REORDER = 1000251;
    public static final long INVENTORY_ABOVE_MAXIMUM = 1000252;
    public static final long ROUTING_OBJECT_ROUTED  = 1000253;
    public static final long ROUTING_OBJECT_APPROVED = 1000254;
    public static final long ROUTING_OBJECT_DISAPPROVED = 1000255;
    public static final long ROUTING_OBJECT_COMPLETE = 1000256;
    public static final long ROUTING_OBJECT_TIMEOUT = 1000257;
    public static final long ROUTING_OBJECT_CANCELLED = 1000258;
    public static final long ROUTING_OBJECT_UPDATED = 1000259;
    public static final long PASSWORD_RESET = 1000260;

    public static final long JOB_REQUEST_UPDATED = 1000261;
    public static final long JOB_REQUEST_CREATED = 1000262;
    public static final long JOB_REQUEST_DELETED = 1000263;
    public static final long JOB_REQUEST_ACCEPTED = 1000264;
    public static final long JOB_REQUEST_REJECTED = 1000265;
    public static final long JOB_REQUEST_PRICING_UPDATED = 1000266;
    public static final long JOB_REQUEST_IN_PRICING = 1000267;
    public static final long QUOTE_CLOSED = 1000270;

    public static final long PSF_PERSON_CREATED = 1000271;
    public static final long PSF_PASSWORD_RESET = 1000272;
    public static final long RFQ_DECLINED = 1000273;

    public static final long CUSTOM_NOTIFICATION_GENERATED = 1000274;

    //NGE Emails moved to NLNotificationTypeID and NLNotificationTemplateID
}
// Max ID: 1000270
// Count:  100
