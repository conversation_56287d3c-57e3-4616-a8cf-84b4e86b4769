package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.BaseVO;
import com.noosh.app.commons.vo.breakout.BreakoutTypeVO;
import com.noosh.app.commons.vo.breakout.BreakoutVO;
import com.noosh.app.commons.vo.externalItem.ExternalItemVO;
import com.noosh.app.commons.vo.paper.PaperDetailVO;
import com.noosh.app.commons.vo.pricing.PricingVO;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import com.noosh.app.commons.vo.shipment.ShipmentVO;
import com.noosh.app.commons.vo.spec.SpecVO;
import com.noosh.app.commons.vo.uofm.UofmVO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 11/30/17
 */
public class OrderItemVO extends BaseVO {

    private static final long serialVersionUID = 6367533723446165975L;

    private LocalDateTime completionDate;
    private Short includeAUP;
    private boolean isSpecChanged;

    private BigDecimal additionalPrice;
    private Long additionalPriceCurrencyId;
    private BigDecimal unitPrice;
    private Long unitPriceCurrencyId;
    private BigDecimal value;
    private Long valueCurrencyId;
    private BigDecimal quantity;
    private String valueCurrency;

    private String comments;
    private BigDecimal tax;
    private Long taxCurrencyId;
    private BigDecimal taxPercent;
    private BigDecimal shipping;
    private Long shippingCurrencyId;
    private BigDecimal subTotal;
    private Long subTotalCurrencyId;
    private String taxLabelString;
    private Long jobId;
    private Long specNodeId;
    private Long estimateItemPriceId;
    private String editShipmentButton;

    //dual currency
    private BigDecimal exAdditionalPrice;
    private Long exAdditionalPriceCurrencyId;
    private BigDecimal exUnitPrice;
    private Long exUnitPriceCurrencyId;
    private BigDecimal exValue;
    private Long exValueCurrencyId;
    private BigDecimal exTax;
    private Long exTaxCurrencyId;
    private BigDecimal exShipping;
    private Long exShippingCurrencyId;
    private BigDecimal exDiscountOrSurcharge;
    private Long exDiscountOrSurchargeCurrencyId;
    private BigDecimal exSubTotal;
    private Long exSubTotalCurrencyId;

    private String reason;
    private String reasonStrId;
    private String reasonStr;
    private String reasonOther;
    private Double allowedUndersQty;
    private Double allowedOversQty;
    private BigDecimal discountOrSurcharge;
    private Long discountOrSurchargeCurrencyId;

    private ShipmentVO shipment;
    private UofmVO uofm;
    private UofmVO additionalUofm;
    private SpecVO spec;
    private List<BreakoutVO> breakouts;
    private List<BreakoutTypeVO> breakoutTypes;
    private Integer paperDetailSize;
    private List<PropertyAttributeVO> specPaperFields;
    private List<PaperDetailVO> paperDetails;
    private Map<String, Object> logisticsDetails;
    private List<Long> requestIds;

    private List<ExternalItemVO> externalItems;
    private List<PropertyAttributeVO> paperSelections;
    private Long breakoutRatesStrId;
    private Long breakoutUnitsStrId;
    private boolean isTimeMaterials;
    private Long itemIndex;
    private Boolean isOverridingBreakouts;
    private Boolean totalFromBreakouts;
    private Boolean allowBreakouts;
    private Long breakoutTypeId;
    private List<UofmVO> uofms;
    private List<SpecVO> specs;
    private Long estItemOptionIndex;
    private String estItemOptionValue;
    private Boolean isFromRatecard;
    private PricingVO pricingVO;
    //VAT
    private String vatCode;
    private BigDecimal vatRate;

    @Schema(description = "PropertyId to get user fields")
    private Long customPropertyId;
    private Map customAttributes = new HashMap();

    public List<ExternalItemVO> getExternalItems() {
        return externalItems;
    }

    public void setExternalItems(List<ExternalItemVO> externalItems) {
        this.externalItems = externalItems;
    }

    public List<PropertyAttributeVO> getPaperSelections() {
        return paperSelections;
    }

    public void setPaperSelections(List<PropertyAttributeVO> paperSelections) {
        this.paperSelections = paperSelections;
    }

    public String getReasonStrId() {
        return reasonStrId;
    }

    public void setReasonStrId(String reasonStrId) {
        this.reasonStrId = reasonStrId;
    }

    public String getReasonStr() {
        return reasonStr;
    }

    public void setReasonStr(String reasonStr) {
        this.reasonStr = reasonStr;
    }

    public BigDecimal getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(BigDecimal discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return this.exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return this.exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return this.discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public Double getAllowedUndersQty() {
        return allowedUndersQty;
    }

    public void setAllowedUndersQty(Double allowedUndersQty) {
        this.allowedUndersQty = allowedUndersQty;
    }

    public Double getAllowedOversQty() {
        return allowedOversQty;
    }

    public void setAllowedOversQty(Double allowedOversQty) {
        this.allowedOversQty = allowedOversQty;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReasonOther() {
        return reasonOther;
    }

    public void setReasonOther(String reasonOther) {
        this.reasonOther = reasonOther;
    }

    public Long getEstimateItemPriceId() {
        return estimateItemPriceId;
    }

    public void setEstimateItemPriceId(Long estimateItemPriceId) {
        this.estimateItemPriceId = estimateItemPriceId;
    }

    public String getEditShipmentButton() {
        return editShipmentButton;
    }

    public void setEditShipmentButton(String editShipmentButton) {
        this.editShipmentButton = editShipmentButton;
    }

    public String getTaxLabelString() {
        return taxLabelString;
    }

    public void setTaxLabelString(String taxLabelString) {
        this.taxLabelString = taxLabelString;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getSpecNodeId() {
        return this.specNodeId;
    }

    public void setSpecNodeId(Long specNodeId) {
        this.specNodeId = specNodeId;
    }

    public String getValueCurrency() {
        return valueCurrency;
    }

    public void setValueCurrency(String valueCurrency) {
        this.valueCurrency = valueCurrency;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public SpecVO getSpec() {
        return spec;
    }

    public void setSpec(SpecVO spec) {
        this.spec = spec;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Short getIncludeAUP() {
        return includeAUP;
    }

    public void setIncludeAUP(Short includeAUP) {
        this.includeAUP = includeAUP;
    }

    public boolean getIsSpecChanged() {
        return isSpecChanged;
    }

    public void setIsSpecChanged(boolean specChanged) {
        isSpecChanged = specChanged;
    }

    public BigDecimal getAdditionalPrice() {
        return additionalPrice;
    }

    public void setAdditionalPrice(BigDecimal additionalPrice) {
        this.additionalPrice = additionalPrice;
    }

    public UofmVO getAdditionalUofm() {
        return this.additionalUofm;
    }

    public void setAdditionalUofm(UofmVO additionalUofm) {
        this.additionalUofm = additionalUofm;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getTaxPercent() {
        return this.taxPercent;
    }

    public void setTaxPercent(BigDecimal taxPercent) {
        this.taxPercent = taxPercent;
    }

    public String getVatCode() {
        return this.vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public BigDecimal getVatRate() {
        return this.vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public BigDecimal getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal) {
        this.subTotal = subTotal;
    }

    public ShipmentVO getShipment() {
        return shipment;
    }

    public void setShipment(ShipmentVO shipment) {
        this.shipment = shipment;
    }

    public UofmVO getUofm() {
        return uofm;
    }

    public void setUofm(UofmVO uofm) {
        this.uofm = uofm;
    }

    public List<BreakoutVO> getBreakouts() {
        return breakouts;
    }

    public void setBreakouts(List<BreakoutVO> breakouts) {
        this.breakouts = breakouts;
    }

    public List<PaperDetailVO> getPaperDetails() {
        return paperDetails;
    }

    public void setPaperDetails(List<PaperDetailVO> paperDetails) {
        this.paperDetails = paperDetails;
    }

    public Map<String, Object> getLogisticsDetails() {
        return logisticsDetails;
    }

    public void setLogisticsDetails(Map<String, Object> logisticsDetails) {
        this.logisticsDetails = logisticsDetails;
    }

    public Long getBreakoutRatesStrId() {
        return breakoutRatesStrId;
    }

    public void setBreakoutRatesStrId(Long breakoutRatesStrId) {
        this.breakoutRatesStrId = breakoutRatesStrId;
    }

    public Long getBreakoutUnitsStrId() {
        return breakoutUnitsStrId;
    }

    public void setBreakoutUnitsStrId(Long breakoutUnitsStrId) {
        this.breakoutUnitsStrId = breakoutUnitsStrId;
    }

    public boolean isTimeMaterials() {
        return isTimeMaterials;
    }

    public void setTimeMaterials(boolean timeMaterials) {
        isTimeMaterials = timeMaterials;
    }

    public Long getCustomPropertyId() {
        return this.customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Map getCustomAttributes() {
        return this.customAttributes;
    }

    public void setCustomAttributes(Map customAttributes) {
        this.customAttributes = customAttributes;
    }

    public Long getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Long itemIndex) {
        this.itemIndex = itemIndex;
    }

    public Boolean getIsOverridingBreakouts() {
        return this.isOverridingBreakouts;
    }

    public void setIsOverridingBreakouts(Boolean overridingBreakouts) {
        this.isOverridingBreakouts = overridingBreakouts;
    }

    public Boolean getTotalFromBreakouts() {
        return this.totalFromBreakouts;
    }

    public void setTotalFromBreakouts(Boolean totalFromBreakouts) {
        this.totalFromBreakouts = totalFromBreakouts;
    }

    public Boolean getAllowBreakouts() {
        return this.allowBreakouts;
    }

    public void setAllowBreakouts(Boolean allowBreakouts) {
        this.allowBreakouts = allowBreakouts;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public List<BreakoutTypeVO> getBreakoutTypes() {
        return breakoutTypes;
    }

    public void setBreakoutTypes(List<BreakoutTypeVO> breakoutTypes) {
        this.breakoutTypes = breakoutTypes;
    }

    public List<UofmVO> getUofms() {
        return this.uofms;
    }

    public void setUofms(List<UofmVO> uofms) {
        this.uofms = uofms;
    }

    public List<SpecVO> getSpecs() {
        return this.specs;
    }

    public void setSpecs(List<SpecVO> specs) {
        this.specs = specs;
    }

    public Long getEstItemOptionIndex() {
        return this.estItemOptionIndex;
    }

    public void setEstItemOptionIndex(Long estItemOptionIndex) {
        this.estItemOptionIndex = estItemOptionIndex;
    }

    public String getEstItemOptionValue() {
        return estItemOptionValue;
    }

    public void setEstItemOptionValue(String estItemOptionValue) {
        this.estItemOptionValue = estItemOptionValue;
    }

    public Boolean getIsFromRatecard() {
        return this.isFromRatecard;
    }

    public void setIsFromRatecard(Boolean fromRatecard) {
        this.isFromRatecard = fromRatecard;
    }

    public PricingVO getPricingVO() {
        return this.pricingVO;
    }

    public void setPricingVO(PricingVO pricingVO) {
        this.pricingVO = pricingVO;
    }

    public Long getAdditionalPriceCurrencyId() {
        return this.additionalPriceCurrencyId;
    }

    public void setAdditionalPriceCurrencyId(Long additionalPriceCurrencyId) {
        this.additionalPriceCurrencyId = additionalPriceCurrencyId;
    }

    public Long getUnitPriceCurrencyId() {
        return this.unitPriceCurrencyId;
    }

    public void setUnitPriceCurrencyId(Long unitPriceCurrencyId) {
        this.unitPriceCurrencyId = unitPriceCurrencyId;
    }

    public Long getValueCurrencyId() {
        return this.valueCurrencyId;
    }

    public void setValueCurrencyId(Long valueCurrencyId) {
        this.valueCurrencyId = valueCurrencyId;
    }

    public Long getTaxCurrencyId() {
        return this.taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public Long getShippingCurrencyId() {
        return this.shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public Long getSubTotalCurrencyId() {
        return this.subTotalCurrencyId;
    }

    public void setSubTotalCurrencyId(Long subTotalCurrencyId) {
        this.subTotalCurrencyId = subTotalCurrencyId;
    }

    public BigDecimal getExAdditionalPrice() {
        return this.exAdditionalPrice;
    }

    public void setExAdditionalPrice(BigDecimal exAdditionalPrice) {
        this.exAdditionalPrice = exAdditionalPrice;
    }

    public Long getExAdditionalPriceCurrencyId() {
        return this.exAdditionalPriceCurrencyId;
    }

    public void setExAdditionalPriceCurrencyId(Long exAdditionalPriceCurrencyId) {
        this.exAdditionalPriceCurrencyId = exAdditionalPriceCurrencyId;
    }

    public BigDecimal getExUnitPrice() {
        return this.exUnitPrice;
    }

    public void setExUnitPrice(BigDecimal exUnitPrice) {
        this.exUnitPrice = exUnitPrice;
    }

    public Long getExUnitPriceCurrencyId() {
        return this.exUnitPriceCurrencyId;
    }

    public void setExUnitPriceCurrencyId(Long exUnitPriceCurrencyId) {
        this.exUnitPriceCurrencyId = exUnitPriceCurrencyId;
    }

    public BigDecimal getExValue() {
        return this.exValue;
    }

    public void setExValue(BigDecimal exValue) {
        this.exValue = exValue;
    }

    public Long getExValueCurrencyId() {
        return this.exValueCurrencyId;
    }

    public void setExValueCurrencyId(Long exValueCurrencyId) {
        this.exValueCurrencyId = exValueCurrencyId;
    }

    public BigDecimal getExTax() {
        return this.exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return this.exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public BigDecimal getExShipping() {
        return this.exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return this.exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExSubTotal() {
        return this.exSubTotal;
    }

    public void setExSubTotal(BigDecimal exSubTotal) {
        this.exSubTotal = exSubTotal;
    }

    public Long getExSubTotalCurrencyId() {
        return this.exSubTotalCurrencyId;
    }

    public void setExSubTotalCurrencyId(Long exSubTotalCurrencyId) {
        this.exSubTotalCurrencyId = exSubTotalCurrencyId;
    }

    public Integer getPaperDetailSize() {
        return paperDetailSize;
    }

    public void setPaperDetailSize(Integer paperDetailSize) {
        this.paperDetailSize = paperDetailSize;
    }

    public List<PropertyAttributeVO> getSpecPaperFields() {
        return specPaperFields;
    }

    public void setSpecPaperFields(List<PropertyAttributeVO> specPaperFields) {
        this.specPaperFields = specPaperFields;
    }

    public List<Long> getRequestIds() {
        return requestIds;
    }

    public void setRequestIds(List<Long> requestIds) {
        this.requestIds = requestIds;
    }
}
