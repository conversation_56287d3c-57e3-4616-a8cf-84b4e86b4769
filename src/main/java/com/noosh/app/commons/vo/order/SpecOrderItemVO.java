package com.noosh.app.commons.vo.order;

import java.time.LocalDateTime;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 11/15/20
 */
public class SpecOrderItemVO implements Serializable {

    private static final long serialVersionUID = -1263826937419527872L;

    private Long specId;

    private LocalDateTime lastOrderDate;

    private BigDecimal lastOrderQty;

    private BigDecimal lastUnitPrice;

    private Long lastOrderCurrencyId;

    private String lastOrderName;

    private String lastOrderExternalLink;

    public Long getLastOrderCurrencyId() {
        return lastOrderCurrencyId;
    }

    public void setLastOrderCurrencyId(Long lastOrderCurrencyId) {
        this.lastOrderCurrencyId = lastOrderCurrencyId;
    }

    public String getLastOrderName() {
        return lastOrderName;
    }

    public void setLastOrderName(String lastOrderName) {
        this.lastOrderName = lastOrderName;
    }

    public String getLastOrderExternalLink() {
        return lastOrderExternalLink;
    }

    public void setLastOrderExternalLink(String lastOrderExternalLink) {
        this.lastOrderExternalLink = lastOrderExternalLink;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public LocalDateTime getLastOrderDate() {
        return lastOrderDate;
    }

    public void setLastOrderDate(LocalDateTime lastOrderDate) {
        this.lastOrderDate = lastOrderDate;
    }

    public BigDecimal getLastOrderQty() {
        return lastOrderQty;
    }

    public void setLastOrderQty(BigDecimal lastOrderQty) {
        this.lastOrderQty = lastOrderQty;
    }

    public BigDecimal getLastUnitPrice() {
        return lastUnitPrice;
    }

    public void setLastUnitPrice(BigDecimal lastUnitPrice) {
        this.lastUnitPrice = lastUnitPrice;
    }
}
