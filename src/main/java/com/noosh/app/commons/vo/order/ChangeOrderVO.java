package com.noosh.app.commons.vo.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;


/**
 * @auther mario
 * @date 2/25/2020
 */
@Schema(description = "Change order")
public class ChangeOrderVO {
    @Schema(description = "Change order id")
    private Long orderId;
    @Schema(description = "Change order reference")
    private String reference;
    @Schema(description = "Change order name")
    private String orderName;
    @Schema(description = "Change order type; Change, Closing Change")
    private String orderType;
    @Schema(description = "Change order type for mobile")
    private String orderTypeForMobile;
    @Schema(description = "Change order state id")
    private Long stateId;
    @Schema(description = "Change order state; Pending Submission=2000030, Accepted=2000078, Rejected = 2000080, Draft = 2000079, Pending Supplier Acceptance = 2000081, " +
            "Pending Buyer Acceptance=2000082, Cancelled=2000083, Retracted = 2000084, Pending Outsourcer Acceptance=2000085, Pending Client Acceptance=2000086, " +
            "Shipped=2500043, Delivered=2500044, Closed=2500045, Partially shipped=2500064, Accepted (not yet shipped)=2500065, Order Completed=2500074, Replaced=2500213")
    private String state;
    private String stateStrId;
    @Schema(description = "Change order completion date")
    private LocalDateTime completionDate;
    @Schema(description = "Change order Last mod date")
    private LocalDateTime lastChangeDate;
    @Schema(description = "Change order item total price")
    private Double changePrice;
    private Long changePriceCurrencyId;
    private Double discountOrSurchargeTotal;
    private Long discountOrSurchargeTotalCurrencyId;

    //dual currency
    @Schema(description = "Change order ex item total price")
    private Double exChangePrice;
    private Long exChangePriceCurrencyId;
    private Double exDiscountOrSurchargeTotal;
    private Long exDiscountOrSurchargeTotalCurrencyId;

    private boolean isNotGrey;
    @Schema(description = "Change order link, redirect to Enterprise")
    private String changeOrderExternalLink;

    private Long customPropertyId;
    private List<PropertyAttributeVO> customFields;

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public List<PropertyAttributeVO> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<PropertyAttributeVO> customFields) {
        this.customFields = customFields;
    }

    public String getStateStrId() {
        return stateStrId;
    }

    public void setStateStrId(String stateStrId) {
        this.stateStrId = stateStrId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getOrderTypeForMobile() {
        return orderTypeForMobile;
    }

    public void setOrderTypeForMobile(String orderTypeForMobile) {
        this.orderTypeForMobile = orderTypeForMobile;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getLastChangeDate() {
        return lastChangeDate;
    }

    public void setLastChangeDate(LocalDateTime lastChangeDate) {
        this.lastChangeDate = lastChangeDate;
    }

    public Double getChangePrice() {
        return changePrice;
    }

    public void setChangePrice(Double changePrice) {
        this.changePrice = changePrice;
    }

    public Double getExChangePrice() {
        return this.exChangePrice;
    }

    public void setExChangePrice(Double exChangePrice) {
        this.exChangePrice = exChangePrice;
    }

    public Double getDiscountOrSurchargeTotal() {
        return discountOrSurchargeTotal;
    }

    public void setDiscountOrSurchargeTotal(Double discountOrSurchargeTotal) {
        this.discountOrSurchargeTotal = discountOrSurchargeTotal;
    }

    public Double getExDiscountOrSurchargeTotal() {
        return this.exDiscountOrSurchargeTotal;
    }

    public void setExDiscountOrSurchargeTotal(Double exDiscountOrSurchargeTotal) {
        this.exDiscountOrSurchargeTotal = exDiscountOrSurchargeTotal;
    }

    public Long getChangePriceCurrencyId() {
        return this.changePriceCurrencyId;
    }

    public void setChangePriceCurrencyId(Long changePriceCurrencyId) {
        this.changePriceCurrencyId = changePriceCurrencyId;
    }

    public Long getDiscountOrSurchargeTotalCurrencyId() {
        return this.discountOrSurchargeTotalCurrencyId;
    }

    public void setDiscountOrSurchargeTotalCurrencyId(Long discountOrSurchargeTotalCurrencyId) {
        this.discountOrSurchargeTotalCurrencyId = discountOrSurchargeTotalCurrencyId;
    }

    public Long getExChangePriceCurrencyId() {
        return this.exChangePriceCurrencyId;
    }

    public void setExChangePriceCurrencyId(Long exChangePriceCurrencyId) {
        this.exChangePriceCurrencyId = exChangePriceCurrencyId;
    }

    public Long getExDiscountOrSurchargeTotalCurrencyId() {
        return this.exDiscountOrSurchargeTotalCurrencyId;
    }

    public void setExDiscountOrSurchargeTotalCurrencyId(Long exDiscountOrSurchargeTotalCurrencyId) {
        this.exDiscountOrSurchargeTotalCurrencyId = exDiscountOrSurchargeTotalCurrencyId;
    }

    public boolean getIsNotGrey() {
        return isNotGrey;
    }

    public void setIsNotGrey(boolean isNotGrey) {
        this.isNotGrey = isNotGrey;
    }

    public String getChangeOrderExternalLink() {
        return changeOrderExternalLink;
    }

    public void setChangeOrderExternalLink(String changeOrderExternalLink) {
        this.changeOrderExternalLink = changeOrderExternalLink;
    }
}
