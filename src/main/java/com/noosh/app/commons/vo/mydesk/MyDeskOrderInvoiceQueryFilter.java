package com.noosh.app.commons.vo.mydesk;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * User: leilaz
 * Date: 4/7/24
 */
public class MyDeskOrderInvoiceQueryFilter {
    public final static String REACT_MY_DESK_DESKOID_ORDER_INVOICE_FILTER_PREF_PREFIX = "REACT_MY_DESK_DESKOID_ORDER_INVOICE_FILTER_";

    public final static String CONTROLNAME_ORDER_INVOICE_DATE_RANGE_OPTION = "dateRange";

    public final static String CONTROLNAME_ORDER_INVOICE_OPTION = "invoiceType";

    public final static long THIS_MONTH = 30L;

    public final static long BUY_INVOICE = 1L;

    @Schema(description = "Date Range Selected From Dropdown", example = "0")
    private Long dateRange = null;

    @Schema(description = "Invoice Type Selected From Dropdown", example = "0")
    private Long invoiceType = null;

    public Long getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Long invoiceType) {
        this.invoiceType = invoiceType;
    }

    public Long getDateRange() {
        return dateRange;
    }

    public void setDateRange(Long dateRange) {
        this.dateRange = dateRange;
    }
}
