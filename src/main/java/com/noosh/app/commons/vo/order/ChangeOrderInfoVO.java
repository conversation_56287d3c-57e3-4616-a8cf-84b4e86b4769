package com.noosh.app.commons.vo.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 4/14/20
 */
public class ChangeOrderInfoVO implements Serializable {
    private static final long serialVersionUID = -4625593505813227850L;

    private ChangeOrderDetailVO orderGeneral;

    private TermsVO buyerTerms;

    private TermsVO supplierTerms;

    private ChangeOrderButtonVO buttons;

    public ChangeOrderDetailVO getOrderGeneral() {
        return orderGeneral;
    }

    public void setOrderGeneral(ChangeOrderDetailVO orderGeneral) {
        this.orderGeneral = orderGeneral;
    }

    public TermsVO getBuyerTerms() {
        return buyerTerms;
    }

    public void setBuyerTerms(TermsVO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public TermsVO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(TermsVO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public ChangeOrderButtonVO getButtons() {
        return buttons;
    }

    public void setButtons(ChangeOrderButtonVO buttons) {
        this.buttons = buttons;
    }
}
