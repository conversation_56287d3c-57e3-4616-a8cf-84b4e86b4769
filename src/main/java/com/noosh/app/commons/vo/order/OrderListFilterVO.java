package com.noosh.app.commons.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @auther mario
 * @date 3/4/2020
 */
@Schema(description = "Order list filter")
public class OrderListFilterVO {
    @Schema(description = "Order list Cog menu")
    private String cogMenu;
    @Schema(description = "Order list page size")
    private int pageSize;
    @Schema(description = "Order type; buy, sell")
    private String orderType;
    @Schema(description = "Project name")
    private String projectName;
    @Schema(description = "Project link, redirect to Enterprise")
    private String projectExternalLink;
    @Schema(description = "Is broker outsourcer project")
    private Boolean isBrokerOutsourcerProject;
    @Schema(description = "Is supplier project")
    private Boolean isSupplierProject;
    @Schema(description = "Is supplier workgroup")
    private Boolean isSupplierWorkgroup;
    @Schema(description = "Margin Summary link, redirect to Enterprise")
    private String markupSummaryExternalLink;
    @Schema(description = "Detailed Margin Analysis link, redirect to Enterprise")
    private String markupAnalysisExternalLink;

    public String getCogMenu() {
        return cogMenu;
    }

    public void setCogMenu(String cogMenu) {
        this.cogMenu = cogMenu;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectExternalLink() {
        return projectExternalLink;
    }

    public void setProjectExternalLink(String projectExternalLink) {
        this.projectExternalLink = projectExternalLink;
    }

    public Boolean getIsBrokerOutsourcerProject() {
        return isBrokerOutsourcerProject;
    }

    public void setIsBrokerOutsourcerProject(Boolean brokerOutsourcerProject) {
        isBrokerOutsourcerProject = brokerOutsourcerProject;
    }

    public Boolean getIsSupplierProject() {
        return isSupplierProject;
    }

    public void setIsSupplierProject(Boolean supplierProject) {
        isSupplierProject = supplierProject;
    }

    public Boolean getIsSupplierWorkgroup() {
        return isSupplierWorkgroup;
    }

    public void setIsSupplierWorkgroup(Boolean supplierWorkgroup) {
        isSupplierWorkgroup = supplierWorkgroup;
    }

    public String getMarkupSummaryExternalLink() {
        return markupSummaryExternalLink;
    }

    public void setMarkupSummaryExternalLink(String markupSummaryExternalLink) {
        this.markupSummaryExternalLink = markupSummaryExternalLink;
    }

    public String getMarkupAnalysisExternalLink() {
        return markupAnalysisExternalLink;
    }

    public void setMarkupAnalysisExternalLink(String markupAnalysisExternalLink) {
        this.markupAnalysisExternalLink = markupAnalysisExternalLink;
    }
}
