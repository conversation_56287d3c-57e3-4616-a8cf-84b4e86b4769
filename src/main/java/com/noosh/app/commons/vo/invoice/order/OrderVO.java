package com.noosh.app.commons.vo.invoice.order;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 1/10/2022
 */
public class OrderVO {

    private boolean canCreateInvoice;
    private Long orderId;
    private String reference;
    private String title;
    private String buyerWorkgroup;
    private Long statusStrId;
    private LocalDateTime acceptedDate;
    private BigDecimal orderAmount;
    private BigDecimal exOrderAmount;
    private Long exCurrencyId;

    public boolean isCanCreateInvoice() {
        return canCreateInvoice;
    }

    public void setCanCreateInvoice(boolean canCreateInvoice) {
        this.canCreateInvoice = canCreateInvoice;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getBuyerWorkgroup() {
        return buyerWorkgroup;
    }

    public void setBuyerWorkgroup(String buyerWorkgroup) {
        this.buyerWorkgroup = buyerWorkgroup;
    }

    public Long getStatusStrId() {
        return statusStrId;
    }

    public void setStatusStrId(Long statusStrId) {
        this.statusStrId = statusStrId;
    }

    public LocalDateTime getAcceptedDate() {
        return acceptedDate;
    }

    public void setAcceptedDate(LocalDateTime acceptedDate) {
        this.acceptedDate = acceptedDate;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getExOrderAmount() {
        return exOrderAmount;
    }

    public void setExOrderAmount(BigDecimal exOrderAmount) {
        this.exOrderAmount = exOrderAmount;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }
}
