package com.noosh.app.commons.vo.collaboration;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/19/19
 */
public class SyContainableVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long syContainableId;

    private Long parentSyContainableId;

    private Long parentObjectId;

    private Long parentObjectClassId;

    private Long objectId;

    private Long objectClassId;

    private String parentObjectAttr;

    private Short isPublic;

    private Long itemOcObjectStateId;

    private String xParentTitle;

    private Short isLinking;

    public Long getSyContainableId() {
        return syContainableId;
    }

    public void setSyContainableId(Long syContainableId) {
        this.syContainableId = syContainableId;
    }

    public Long getParentSyContainableId() {
        return parentSyContainableId;
    }

    public void setParentSyContainableId(Long parentSyContainableId) {
        this.parentSyContainableId = parentSyContainableId;
    }

    public Long getParentObjectId() {
        return parentObjectId;
    }

    public void setParentObjectId(Long parentObjectId) {
        this.parentObjectId = parentObjectId;
    }

    public Long getParentObjectClassId() {
        return parentObjectClassId;
    }

    public void setParentObjectClassId(Long parentObjectClassId) {
        this.parentObjectClassId = parentObjectClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public String getParentObjectAttr() {
        return parentObjectAttr;
    }

    public void setParentObjectAttr(String parentObjectAttr) {
        this.parentObjectAttr = parentObjectAttr;
    }

    public Short getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Short aPublic) {
        isPublic = aPublic;
    }

    public Long getItemOcObjectStateId() {
        return itemOcObjectStateId;
    }

    public void setItemOcObjectStateId(Long itemOcObjectStateId) {
        this.itemOcObjectStateId = itemOcObjectStateId;
    }

    public String getxParentTitle() {
        return xParentTitle;
    }

    public void setxParentTitle(String xParentTitle) {
        this.xParentTitle = xParentTitle;
    }

    public Short getIsLinking() {
        return isLinking;
    }

    public void setIsLinking(Short linking) {
        isLinking = linking;
    }

}
