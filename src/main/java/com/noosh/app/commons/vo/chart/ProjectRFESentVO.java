package com.noosh.app.commons.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * User: leilaz
 * Date: 2/22/22
 */
public class ProjectRFESentVO {
    @Schema(description = "Project Name")
    private String projectName;
    @Schema(description = "Project Create Date")
    private LocalDateTime projectCreateDate;
    @Schema(description = "RFE Sent Date")
    private LocalDateTime rfeSentDate;
    @Schema(description = "RFE Name")
    private String rfeName;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public LocalDateTime getProjectCreateDate() {
        return projectCreateDate;
    }

    public void setProjectCreateDate(LocalDateTime projectCreateDate) {
        this.projectCreateDate = projectCreateDate;
    }

    public LocalDateTime getRfeSentDate() {
        return rfeSentDate;
    }

    public void setRfeSentDate(LocalDateTime rfeSentDate) {
        this.rfeSentDate = rfeSentDate;
    }

    public String getRfeName() {
        return rfeName;
    }

    public void setRfeName(String rfeName) {
        this.rfeName = rfeName;
    }
}