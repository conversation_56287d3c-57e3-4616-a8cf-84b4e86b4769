package com.noosh.app.commons.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 8/12/2021
 */
public class InvoiceForecastChartItemVO {

    @Schema(description = "Type")
    private String type;
    @Schema(description = "value")
    private Double value;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }
}
