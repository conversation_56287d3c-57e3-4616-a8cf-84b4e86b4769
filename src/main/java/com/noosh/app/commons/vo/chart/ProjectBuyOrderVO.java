package com.noosh.app.commons.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * User: leilaz
 * Date: 2/22/22
 */
public class ProjectBuyOrderVO {
    @Schema(description = "Project Name")
    private String projectName;
    @Schema(description = "Project Create Date")
    private LocalDateTime projectCreateDate;
    @Schema(description = "Order Create Date")
    private LocalDateTime orderCreateDate;
    @Schema(description = "Order Closed Date")
    private LocalDateTime orderClosedDate;
    @Schema(description = "Order Accept Date")
    private LocalDateTime orderAcceptDate;
    @Schema(description = "Order Name")
    private String orderName;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public LocalDateTime getProjectCreateDate() {
        return projectCreateDate;
    }

    public void setProjectCreateDate(LocalDateTime projectCreateDate) {
        this.projectCreateDate = projectCreateDate;
    }

    public LocalDateTime getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(LocalDateTime orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public LocalDateTime getOrderAcceptDate() {
        return orderAcceptDate;
    }

    public void setOrderAcceptDate(LocalDateTime orderAcceptDate) {
        this.orderAcceptDate = orderAcceptDate;
    }

    public LocalDateTime getOrderClosedDate() {
        return orderClosedDate;
    }

    public void setOrderClosedDate(LocalDateTime orderClosedDate) {
        this.orderClosedDate = orderClosedDate;
    }
}