package com.noosh.app.commons.vo.mydesk;

import com.noosh.app.commons.vo.invoice.InvoicePendingMyDeskVO;
import com.noosh.app.commons.vo.order.OrderPendingMyDeskVO;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 4/9/24
 */
public class MyDeskPendingVO implements Serializable {
    private static final long serialVersionUID = -489378106876209952L;

    private boolean canViewOrders;

    private boolean canViewInvoices;

    private List<OrderPendingMyDeskVO> orders;

    private List<OrderPendingMyDeskVO> changeOrders;

    private List<InvoicePendingMyDeskVO> invoices;

    public boolean getCanViewOrders() {
        return canViewOrders;
    }

    public void setCanViewOrders(boolean canViewOrders) {
        this.canViewOrders = canViewOrders;
    }

    public boolean getCanViewInvoices() {
        return canViewInvoices;
    }

    public void setCanViewInvoices(boolean canViewInvoices) {
        this.canViewInvoices = canViewInvoices;
    }

    public List<OrderPendingMyDeskVO> getOrders() {
        return orders;
    }

    public void setOrders(List<OrderPendingMyDeskVO> orders) {
        this.orders = orders;
    }

    public List<OrderPendingMyDeskVO> getChangeOrders() {
        return changeOrders;
    }

    public void setChangeOrders(List<OrderPendingMyDeskVO> changeOrders) {
        this.changeOrders = changeOrders;
    }

    public List<InvoicePendingMyDeskVO> getInvoices() {
        return invoices;
    }

    public void setInvoices(List<InvoicePendingMyDeskVO> invoices) {
        this.invoices = invoices;
    }
}
