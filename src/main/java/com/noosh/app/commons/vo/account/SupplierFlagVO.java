package com.noosh.app.commons.vo.account;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: yangx
 * @Date: 8/11/2021
 */
public class SupplierFlagVO {

    private Boolean isDiversitySupplier = false;
    private List<String> diversityDescriptionStrIdList = new ArrayList<>();
    private Boolean isSecuritySupplier = false;
    private List<String> securityDescriptionStrIdList = new ArrayList<>();
    private Boolean isSustainableSupplier = false;
    private List<String> sustainableDescriptionStrIdList = new ArrayList<>();
    private Boolean isQualitySupplier = false;
    private List<String> qualitySupplierDescriptionStrIdList = new ArrayList<>();
    private Boolean isHHCertifySupplier = false;
    private List<String> hhCertifyDescriptionStrIdList = new ArrayList<>();

    public Boolean getDiversitySupplier() {
        return isDiversitySupplier;
    }

    public void setDiversitySupplier(Boolean diversitySupplier) {
        isDiversitySupplier = diversitySupplier;
    }

    public List<String> getDiversityDescriptionStrIdList() {
        return diversityDescriptionStrIdList;
    }

    public void setDiversityDescriptionStrIdList(List<String> diversityDescriptionStrIdList) {
        this.diversityDescriptionStrIdList = diversityDescriptionStrIdList;
    }

    public Boolean getSecuritySupplier() {
        return isSecuritySupplier;
    }

    public void setSecuritySupplier(Boolean securitySupplier) {
        isSecuritySupplier = securitySupplier;
    }

    public List<String> getSecurityDescriptionStrIdList() {
        return securityDescriptionStrIdList;
    }

    public void setSecurityDescriptionStrIdList(List<String> securityDescriptionStrIdList) {
        this.securityDescriptionStrIdList = securityDescriptionStrIdList;
    }

    public Boolean getSustainableSupplier() {
        return isSustainableSupplier;
    }

    public void setSustainableSupplier(Boolean sustainableSupplier) {
        isSustainableSupplier = sustainableSupplier;
    }

    public List<String> getSustainableDescriptionStrIdList() {
        return sustainableDescriptionStrIdList;
    }

    public void setSustainableDescriptionStrIdList(List<String> sustainableDescriptionStrIdList) {
        this.sustainableDescriptionStrIdList = sustainableDescriptionStrIdList;
    }

    public Boolean getQualitySupplier() {
        return isQualitySupplier;
    }

    public void setQualitySupplier(Boolean qualitySupplier) {
        isQualitySupplier = qualitySupplier;
    }

    public List<String> getQualitySupplierDescriptionStrIdList() {
        return qualitySupplierDescriptionStrIdList;
    }

    public void setQualitySupplierDescriptionStrIdList(List<String> qualitySupplierDescriptionStrIdList) {
        this.qualitySupplierDescriptionStrIdList = qualitySupplierDescriptionStrIdList;
    }

    public Boolean getHHCertifySupplier() {
        return isHHCertifySupplier;
    }

    public void setHHCertifySupplier(Boolean isHHCertifySupplier) {
        this.isHHCertifySupplier = isHHCertifySupplier;
    }

    public List<String> getHhCertifyDescriptionStrIdList() {
        return hhCertifyDescriptionStrIdList;
    }

    public void setHhCertifyDescriptionStrIdList(List<String> hhCertifyDescriptionStrIdList) {
        this.hhCertifyDescriptionStrIdList = hhCertifyDescriptionStrIdList;
    }
}
