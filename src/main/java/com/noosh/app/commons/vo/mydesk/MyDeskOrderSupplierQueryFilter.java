package com.noosh.app.commons.vo.mydesk;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * User: leilaz
 * Date: 4/1/24
 */
public class MyDeskOrderSupplierQueryFilter {
    public final static String REACT_MY_DESK_DESKOID_ORDER_SUPPLIER_FILTER_PREF_PREFIX = "REACT_MY_DESK_DESKOID_ORDER_SUPPLIER_FILTER_";

    public final static String CONTROLNAME_ORDER_SUPPLIER_DATE_TYPE_OPTION = "dateType";
    public final static String CONTROLNAME_ORDER_SUPPLIER_DATE_RANGE_OPTION = "dateRange";

    public final static long THIS_MONTH = 30L;

    public final static String ACCEPTED_DATE = "0";

    @Schema(description = "Date Type Selected From Dropdown", example = "0")
    private String dateType = null;

    @Schema(description = "Date Range Selected From Dropdown", example = "30")
    private Long dateRange = null;

    public String getDateType() {
        return dateType;
    }

    public void setDateType(String dateType) {
        this.dateType = dateType;
    }

    public Long getDateRange() {
        return dateRange;
    }

    public void setDateRange(Long dateRange) {
        this.dateRange = dateRange;
    }
}
