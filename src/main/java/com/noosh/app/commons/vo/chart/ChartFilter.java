package com.noosh.app.commons.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * User: leilaz
 * Date: 2/16/22
 */
public class ChartFilter {
    public final static String PROJECT_EFFICIENT_FILTER_PREF_PREFIX = "REACT_PROJECT_EFFICIENT_FILTER_";
    public final static String ORDER_EFFICIENT_FILTER_PREF_PREFIX = "REACT_ORDER_EFFICIENT_FILTER_";
    public final static String CONTROL_NAME_CONTACTS_SHOW_OPTION = "showOption";

    @Schema(description = "Show Option 30 Last 30 days 180 Last 6 months 365 Last 12 months", example = "30")
    private String showOption = "30";

    public String getShowOption() {
        return showOption;
    }

    public void setShowOption(String showOption) {
        this.showOption = showOption;
    }
}