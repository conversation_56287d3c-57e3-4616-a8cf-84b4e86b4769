package com.noosh.app.commons.vo;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public class ErrorVO {
    private String msg;
    private Map<String, String> msgParams;

    public ErrorVO() {
    }

    public ErrorVO(String msg) {
        this.msg = msg;
    }

    public Map<String, String> getMsgParams() {
        return msgParams;
    }

    public void setMsgParams(Map<String, String> msgParams) {
        this.msgParams = msgParams;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
