package com.noosh.app.commons.vo.rating;

import com.noosh.app.commons.vo.BaseVO;

/**
 * User: leilaz
 * Date: 1/20/19
 */
public class RatingQuestionVO extends BaseVO {
    private Long questionNaireId;

    private Long sectionId;

    private String text;

    private Long weight;

    private Long ordinal;

    public Long getQuestionNaireId() {
        return questionNaireId;
    }

    public void setQuestionNaireId(Long questionNaireId) {
        this.questionNaireId = questionNaireId;
    }

    public Long getSectionId() {
        return sectionId;
    }

    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Long getWeight() {
        return weight;
    }

    public void setWeight(Long weight) {
        this.weight = weight;
    }

    public Long getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Long ordinal) {
        this.ordinal = ordinal;
    }
}
