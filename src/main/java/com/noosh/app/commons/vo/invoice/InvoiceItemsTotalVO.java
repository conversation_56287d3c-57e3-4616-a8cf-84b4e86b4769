package com.noosh.app.commons.vo.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @auther mario
 * @date 7/22/2020
 */
@Schema(description = "Invoice item total")
public class InvoiceItemsTotalVO {
    @Schema(description = "Invoice item sub total")
    private InvoiceItemPriceVO subTotal;
    @Schema(description = "Invoice item total tax")
    private InvoiceItemPriceVO tax;
    @Schema(description = "Invoice item total shipping")
    private InvoiceItemPriceVO shipping;
    @Schema(description = "Invoice item total discount or surcharge")
    private InvoiceItemPriceVO discountOrSurcharge;
    @Schema(description = "Invoice item grand total")
    private InvoiceItemPriceVO grandTotal;

    public InvoiceItemPriceVO getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(InvoiceItemPriceVO subTotal) {
        this.subTotal = subTotal;
    }

    public InvoiceItemPriceVO getTax() {
        return tax;
    }

    public void setTax(InvoiceItemPriceVO tax) {
        this.tax = tax;
    }

    public InvoiceItemPriceVO getShipping() {
        return shipping;
    }

    public void setShipping(InvoiceItemPriceVO shipping) {
        this.shipping = shipping;
    }

    public InvoiceItemPriceVO getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(InvoiceItemPriceVO discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public InvoiceItemPriceVO getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(InvoiceItemPriceVO grandTotal) {
        this.grandTotal = grandTotal;
    }
}
