package com.noosh.app.commons.vo.invoice;

import com.noosh.app.commons.vo.address.AddressVO;
import com.noosh.app.commons.vo.component.DropdownVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 1/16/2022
 */
public class InvoiceInitInfoVO {

    private String projectNumber;
    private String orderReference;
    private String orderTitle;
    private Long buyWorkgroupId;
    private Long paymentMethodStrId;
    private String paymentReferenceNO;
    private List<AddressVO> invoiceToList;
    private AddressVO preparedBy;
    private Boolean isNonBillable;
    private Long nonBillableReasonId;
    private Boolean isItemizedTaxAndShipping;
    private List<DropdownVO> reasonOptions;
    private String invoiceNumber;
    private String referenceNumber;
    private LocalDateTime invoiceDate;
    private LocalDateTime invoiceDueDate;
    private String comments;
    private Long statusId;
    private String invoiceStatusStrId;
    private String taxLabelString;
    private List<InvoiceItemVO> invoiceItems;
    private InvoiceItemsTotalVO invoiceItemsTotal;
    private Map<String, Boolean> buttons;
    private Boolean isFinalInvoice;
    private Boolean shipmentRecordRequired;
    private Boolean noValidationAgainstOrderTotal;
    private Long customPropertyId;
    //dual currency
    private boolean isDualCurrency;
    private Long exCurrencyId;
    private BigDecimal rate;

    private Boolean isSell;

    private Map customAttributes = new HashMap();

    public String getProjectNumber() {
        return projectNumber;
    }

    public void setProjectNumber(String projectNumber) {
        this.projectNumber = projectNumber;
    }

    public String getOrderReference() {
        return orderReference;
    }

    public void setOrderReference(String orderReference) {
        this.orderReference = orderReference;
    }

    public String getOrderTitle() {
        return orderTitle;
    }

    public void setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle;
    }

    public Long getBuyWorkgroupId() {
        return buyWorkgroupId;
    }

    public void setBuyWorkgroupId(Long buyWorkgroupId) {
        this.buyWorkgroupId = buyWorkgroupId;
    }

    public Long getPaymentMethodStrId() {
        return paymentMethodStrId;
    }

    public void setPaymentMethodStrId(Long paymentMethodStrId) {
        this.paymentMethodStrId = paymentMethodStrId;
    }

    public String getPaymentReferenceNO() {
        return paymentReferenceNO;
    }

    public void setPaymentReferenceNO(String paymentReferenceNO) {
        this.paymentReferenceNO = paymentReferenceNO;
    }

    public List<AddressVO> getInvoiceToList() {
        return invoiceToList;
    }

    public void setInvoiceToList(List<AddressVO> invoiceToList) {
        this.invoiceToList = invoiceToList;
    }

    public AddressVO getPreparedBy() {
        return preparedBy;
    }

    public void setPreparedBy(AddressVO preparedBy) {
        this.preparedBy = preparedBy;
    }

    public Boolean getIsNonBillable() {
        return isNonBillable;
    }

    public void setIsNonBillable(Boolean nonBillable) {
        isNonBillable = nonBillable;
    }

    public Long getNonBillableReasonId() {
        return nonBillableReasonId;
    }

    public void setNonBillableReasonId(Long nonBillableReasonId) {
        this.nonBillableReasonId = nonBillableReasonId;
    }

    public Boolean getIsItemizedTaxAndShipping() {
        return isItemizedTaxAndShipping;
    }

    public void setIsItemizedTaxAndShipping(Boolean itemizedTaxAndShipping) {
        isItemizedTaxAndShipping = itemizedTaxAndShipping;
    }

    public List<DropdownVO> getReasonOptions() {
        return reasonOptions;
    }

    public void setReasonOptions(List<DropdownVO> reasonOptions) {
        this.reasonOptions = reasonOptions;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public LocalDateTime getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(LocalDateTime invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public LocalDateTime getInvoiceDueDate() {
        return invoiceDueDate;
    }

    public void setInvoiceDueDate(LocalDateTime invoiceDueDate) {
        this.invoiceDueDate = invoiceDueDate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getStatusId() {
        return statusId;
    }

    public void setStatusId(Long statusId) {
        this.statusId = statusId;
    }

    public String getInvoiceStatusStrId() {
        return invoiceStatusStrId;
    }

    public void setInvoiceStatusStrId(String invoiceStatusStrId) {
        this.invoiceStatusStrId = invoiceStatusStrId;
    }

    public String getTaxLabelString() {
        return taxLabelString;
    }

    public void setTaxLabelString(String taxLabelString) {
        this.taxLabelString = taxLabelString;
    }

    public List<InvoiceItemVO> getInvoiceItems() {
        return invoiceItems;
    }

    public void setInvoiceItems(List<InvoiceItemVO> invoiceItems) {
        this.invoiceItems = invoiceItems;
    }

    public InvoiceItemsTotalVO getInvoiceItemsTotal() {
        return invoiceItemsTotal;
    }

    public void setInvoiceItemsTotal(InvoiceItemsTotalVO invoiceItemsTotal) {
        this.invoiceItemsTotal = invoiceItemsTotal;
    }

    public Map<String, Boolean> getButtons() {
        return buttons;
    }

    public void setButtons(Map<String, Boolean> buttons) {
        this.buttons = buttons;
    }

    public Boolean getIsFinalInvoice() {
        return isFinalInvoice;
    }

    public void setIsFinalInvoice(Boolean finalInvoice) {
        isFinalInvoice = finalInvoice;
    }

    public Boolean getShipmentRecordRequired() {
        return shipmentRecordRequired;
    }

    public void setShipmentRecordRequired(Boolean shipmentRecordRequired) {
        this.shipmentRecordRequired = shipmentRecordRequired;
    }

    public Boolean getNoValidationAgainstOrderTotal() {
        return noValidationAgainstOrderTotal;
    }

    public void setNoValidationAgainstOrderTotal(Boolean noValidationAgainstOrderTotal) {
        this.noValidationAgainstOrderTotal = noValidationAgainstOrderTotal;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public boolean getIsDualCurrency() {
        return isDualCurrency;
    }

    public void setIsDualCurrency(boolean dualCurrency) {
        isDualCurrency = dualCurrency;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Boolean getIsSell() {
        return isSell;
    }

    public void setIsSell(Boolean sell) {
        isSell = sell;
    }

    public Map getCustomAttributes() {
        return customAttributes;
    }

    public void setCustomAttributes(Map customAttributes) {
        this.customAttributes = customAttributes;
    }
}
