package com.noosh.app.commons.vo;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public enum ResponseCode {

    SUCCESS(1, "success"),
    ERROR(2, "error");


    private int code;
    private String msg;

    ResponseCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
