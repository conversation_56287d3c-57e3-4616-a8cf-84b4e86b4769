package com.noosh.app.commons.vo.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;

/**
 * @auther mario
 * @date 7/22/2020
 */
@Schema(description = "Invoice item price")
public class InvoiceItemPriceVO {
    @Schema(description = "Aggr Order Total")
    private BigDecimal order;
    private Long orderCurrencyId;
    private Long previousInvoicedCurrencyId;
    @Schema(description = "Previously Invoiced")
    private BigDecimal previousInvoiced;
    private Long thisInvoiceCurrencyId;
    @Schema(description = "This Invoice")
    private BigDecimal thisInvoice;
    @Schema(description = "Total Invoiced")
    private BigDecimal totalInvoiced;
    private Long totalInvoicedCurrencyId;

    private BigDecimal exOrder;
    private Long exOrderCurrencyId;
    private BigDecimal exPreviousInvoiced;
    private Long exPreviousInvoicedCurrencyId;
    private BigDecimal exThisInvoice;
    private Long exThisInvoiceCurrencyId;
    private BigDecimal exTotalInvoiced;
    private Long exTotalInvoicedCurrencyId;

    public BigDecimal getOrder() {
        return order;
    }

    public void setOrder(BigDecimal order) {
        this.order = order;
    }

    public BigDecimal getPreviousInvoiced() {
        return previousInvoiced;
    }

    public void setPreviousInvoiced(BigDecimal previousInvoiced) {
        this.previousInvoiced = previousInvoiced;
    }

    public BigDecimal getThisInvoice() {
        return thisInvoice;
    }

    public void setThisInvoice(BigDecimal thisInvoice) {
        this.thisInvoice = thisInvoice;
    }

    public BigDecimal getTotalInvoiced() {
        return totalInvoiced;
    }

    public void setTotalInvoiced(BigDecimal totalInvoiced) {
        this.totalInvoiced = totalInvoiced;
    }

    public Long getOrderCurrencyId() {
        return orderCurrencyId;
    }

    public void setOrderCurrencyId(Long orderCurrencyId) {
        this.orderCurrencyId = orderCurrencyId;
    }

    public Long getPreviousInvoicedCurrencyId() {
        return previousInvoicedCurrencyId;
    }

    public void setPreviousInvoicedCurrencyId(Long previousInvoicedCurrencyId) {
        this.previousInvoicedCurrencyId = previousInvoicedCurrencyId;
    }

    public Long getThisInvoiceCurrencyId() {
        return thisInvoiceCurrencyId;
    }

    public void setThisInvoiceCurrencyId(Long thisInvoiceCurrencyId) {
        this.thisInvoiceCurrencyId = thisInvoiceCurrencyId;
    }

    public Long getTotalInvoicedCurrencyId() {
        return totalInvoicedCurrencyId;
    }

    public void setTotalInvoicedCurrencyId(Long totalInvoicedCurrencyId) {
        this.totalInvoicedCurrencyId = totalInvoicedCurrencyId;
    }

    public BigDecimal getExOrder() {
        return exOrder;
    }

    public void setExOrder(BigDecimal exOrder) {
        this.exOrder = exOrder;
    }

    public Long getExOrderCurrencyId() {
        return exOrderCurrencyId;
    }

    public void setExOrderCurrencyId(Long exOrderCurrencyId) {
        this.exOrderCurrencyId = exOrderCurrencyId;
    }

    public BigDecimal getExPreviousInvoiced() {
        return exPreviousInvoiced;
    }

    public void setExPreviousInvoiced(BigDecimal exPreviousInvoiced) {
        this.exPreviousInvoiced = exPreviousInvoiced;
    }

    public Long getExPreviousInvoicedCurrencyId() {
        return exPreviousInvoicedCurrencyId;
    }

    public void setExPreviousInvoicedCurrencyId(Long exPreviousInvoicedCurrencyId) {
        this.exPreviousInvoicedCurrencyId = exPreviousInvoicedCurrencyId;
    }

    public BigDecimal getExThisInvoice() {
        return exThisInvoice;
    }

    public void setExThisInvoice(BigDecimal exThisInvoice) {
        this.exThisInvoice = exThisInvoice;
    }

    public Long getExThisInvoiceCurrencyId() {
        return exThisInvoiceCurrencyId;
    }

    public void setExThisInvoiceCurrencyId(Long exThisInvoiceCurrencyId) {
        this.exThisInvoiceCurrencyId = exThisInvoiceCurrencyId;
    }

    public BigDecimal getExTotalInvoiced() {
        return exTotalInvoiced;
    }

    public void setExTotalInvoiced(BigDecimal exTotalInvoiced) {
        this.exTotalInvoiced = exTotalInvoiced;
    }

    public Long getExTotalInvoicedCurrencyId() {
        return exTotalInvoicedCurrencyId;
    }

    public void setExTotalInvoicedCurrencyId(Long exTotalInvoicedCurrencyId) {
        this.exTotalInvoicedCurrencyId = exTotalInvoicedCurrencyId;
    }
}
