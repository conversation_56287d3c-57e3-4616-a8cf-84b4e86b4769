package com.noosh.app.commons.vo.order;

import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 1/1/24
 */
public class AggregateOrderAmountVO {
    private BigDecimal subTotal;
    private BigDecimal grandTotal;
    private BigDecimal shipping;
    private BigDecimal tax;
    //dual currency
    private BigDecimal exSubTotal;
    private BigDecimal exGrandTotal;
    private BigDecimal exShipping;
    private BigDecimal exTax;
    private Long exCurrencyId;
    private Boolean isDualCurrency;

    public BigDecimal getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal) {
        this.subTotal = subTotal;
    }

    public BigDecimal getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(BigDecimal grandTotal) {
        this.grandTotal = grandTotal;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getExSubTotal() {
        return exSubTotal;
    }

    public void setExSubTotal(BigDecimal exSubTotal) {
        this.exSubTotal = exSubTotal;
    }

    public BigDecimal getExGrandTotal() {
        return exGrandTotal;
    }

    public void setExGrandTotal(BigDecimal exGrandTotal) {
        this.exGrandTotal = exGrandTotal;
    }

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public Boolean getIsDualCurrency() {
        return isDualCurrency;
    }

    public void setIsDualCurrency(Boolean dualCurrency) {
        isDualCurrency = dualCurrency;
    }
}
