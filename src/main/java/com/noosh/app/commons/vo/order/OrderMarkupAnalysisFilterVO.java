package com.noosh.app.commons.vo.order;

/**
 * User: leilaz
 * Date: 5/18/20
 */
public class OrderMarkupAnalysisFilterVO {
    private OrderMarkupAnalysisFilter filter;

    private String actionSellChangeOrdersLink;
    private String actionBuyChangeOrdersLink;
    private String sellOrderListLink;
    private String buyOrderListLink;
    private boolean buyItemizedTaxAndShipping;
    private boolean sellItemizedTaxAndShipping;

    public boolean getBuyItemizedTaxAndShipping() {
        return buyItemizedTaxAndShipping;
    }

    public void setBuyItemizedTaxAndShipping(boolean buyItemizedTaxAndShipping) {
        this.buyItemizedTaxAndShipping = buyItemizedTaxAndShipping;
    }

    public boolean getSellItemizedTaxAndShipping() {
        return sellItemizedTaxAndShipping;
    }

    public void setSellItemizedTaxAndShipping(boolean sellItemizedTaxAndShipping) {
        this.sellItemizedTaxAndShipping = sellItemizedTaxAndShipping;
    }

    public String getSellOrderListLink() {
        return sellOrderListLink;
    }

    public void setSellOrderListLink(String sellOrderListLink) {
        this.sellOrderListLink = sellOrderListLink;
    }

    public String getBuyOrderListLink() {
        return buyOrderListLink;
    }

    public void setBuyOrderListLink(String buyOrderListLink) {
        this.buyOrderListLink = buyOrderListLink;
    }

    public OrderMarkupAnalysisFilter getFilter() {
        return filter;
    }

    public void setFilter(OrderMarkupAnalysisFilter filter) {
        this.filter = filter;
    }

    public String getActionSellChangeOrdersLink() {
        return actionSellChangeOrdersLink;
    }

    public void setActionSellChangeOrdersLink(String actionSellChangeOrdersLink) {
        this.actionSellChangeOrdersLink = actionSellChangeOrdersLink;
    }

    public String getActionBuyChangeOrdersLink() {
        return actionBuyChangeOrdersLink;
    }

    public void setActionBuyChangeOrdersLink(String actionBuyChangeOrdersLink) {
        this.actionBuyChangeOrdersLink = actionBuyChangeOrdersLink;
    }
}
