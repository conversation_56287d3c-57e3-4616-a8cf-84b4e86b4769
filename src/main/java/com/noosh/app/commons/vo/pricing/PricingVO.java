package com.noosh.app.commons.vo.pricing;

import java.util.ArrayList;
import java.util.List;

public class PricingVO {
    private Long contractId;
    private Double value;
    private List<SubTotalVO> subtotals;

    public Long getContractId() {
        return this.contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Double getValue() {
        return this.value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public List<SubTotalVO> getSubtotals() {
        return this.subtotals;
    }

    public void setSubtotals(List<SubTotalVO> subtotals) {
        this.subtotals = subtotals;
    }

    public void composeSubTotals(String subTotals) {
        List<SubTotalVO> subTotalVOS = new ArrayList<>();
        if (subTotals != null && subTotals.length() > 0) {
            String[] subtotals = subTotals.split("__");
            int subtotalCount = subtotals != null ? subtotals.length : 0;
            for (int sInd = 0; sInd < subtotalCount; sInd++) {
                String[] pair = subtotals[sInd].split(":");
                if (pair != null && pair.length == 2) {
                    SubTotalVO subTotalVO = new SubTotalVO();
                    subTotalVO.setName(pair[0]);
                    subTotalVO.setValue(Double.valueOf(pair[1]));
                    subTotalVOS.add(subTotalVO);
                }
            }
        }
        this.setSubtotals(subTotalVOS);
    }
}
