package com.noosh.app.commons.vo.spec;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.vo.BaseVO;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 12/1/17
 */
public class SpecVO extends BaseVO {
    private static final long serialVersionUID = -5237360893949951663L;
    private String fullName;
    private String creator;

    private String specLink;

    @JsonIgnore
    private Long propertyId;

    private String copySpecExternalLink;
    private String editSpecExternalLink;
    private Long spSpecTypeId;
    private SpecTypeVO specType;
    private BigDecimal quantity1;
    private Long nodeId;
    private Long specReferenceId;

    private Map<String, Object> smartFormValues;

    private List<PropertyAttributeVO> customFields;

    public List<PropertyAttributeVO> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<PropertyAttributeVO> customFields) {
        this.customFields = customFields;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public SpecTypeVO getSpecType() {
        return specType;
    }

    public void setSpecType(SpecTypeVO specType) {
        this.specType = specType;
    }

    public String getSpecLink() {
        return specLink;
    }

    public void setSpecLink(String specLink) {
        this.specLink = specLink;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public Long getSpSpecTypeId() {
        return spSpecTypeId;
    }

    public void setSpSpecTypeId(Long spSpecTypeId) {
        this.spSpecTypeId = spSpecTypeId;
    }

    public String getCopySpecExternalLink() {
        return this.copySpecExternalLink;
    }

    public void setCopySpecExternalLink(String copySpecExternalLink) {
        this.copySpecExternalLink = copySpecExternalLink;
    }

    public String getEditSpecExternalLink() {
        return this.editSpecExternalLink;
    }

    public void setEditSpecExternalLink(String editSpecExternalLink) {
        this.editSpecExternalLink = editSpecExternalLink;
    }

    public BigDecimal getQuantity1() {
        return this.quantity1;
    }

    public void setQuantity1(BigDecimal quantity1) {
        this.quantity1 = quantity1;
    }

    public Long getNodeId() {
        return this.nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public Long getSpecReferenceId() {
        return this.specReferenceId;
    }

    public void setSpecReferenceId(Long specReferenceId) {
        this.specReferenceId = specReferenceId;
    }

    public Map<String, Object> getSmartFormValues() {
        return smartFormValues;
    }

    public void setSmartFormValues(Map<String, Object> smartFormValues) {
        this.smartFormValues = smartFormValues;
    }
}
