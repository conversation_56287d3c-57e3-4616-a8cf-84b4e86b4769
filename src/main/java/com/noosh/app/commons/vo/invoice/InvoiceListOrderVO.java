package com.noosh.app.commons.vo.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public class InvoiceListOrderVO {

    @Schema(description = "order id")
    private Long id;
    @Schema(description = "order name")
    private String orderName;
    @Schema(description = "view Order External Url")
    private String viewOrderExternalUrl;
    @Schema(description = "status")
    private String status;
    @Schema(description = "status str id")
    private String statusStrId;
    @Schema(description = "accept Date")
    private LocalDateTime acceptDate;

    @Schema(description = "is Buy Order")
    private Boolean isBuyOrder;
    @Schema(description = "supplierWorkgroupName, show if (project.isOutsourcer == true && order.isBuyOrder == true)  else (project.isBuyer == true)")
    private String supplierWorkgroupName;
    @Schema(description = "buyerWorkgroupName, show if (project.isOutsourcer == true && order.isBuyOrder == false)  else (project.isBuyer == false)")
    private String buyerWorkgroupName;

    @Schema(description = "order Amount")
    private BigDecimal orderAmount;
    private Long orderAmountCurrencyId;
    @Schema(description = "invoice Amount")
    private BigDecimal invoiceAmount;
    private Long invoiceAmountCurrencyId;
    @Schema(description = "remaining Balance")
    private BigDecimal remainingBalance;
    private Long remainingBalanceCurrencyId;

    @Schema(description = "whether is dual currency")
    private Boolean isDualCurrency;
    @Schema(description = "ex order Amount")
    private BigDecimal exOrderAmount;
    private Long exOrderAmountCurrencyId;
    @Schema(description = "ex invoice Amount")
    private BigDecimal exInvoiceAmount;
    private Long exInvoiceAmountCurrencyId;
    @Schema(description = "ex remaining Balance")
    private BigDecimal exRemainingBalance;
    private Long exRemainingBalanceCurrencyId;

    @Schema(description = "is Invoice Adjustment Order")
    private Boolean isInvoiceAdjustmentOrder;

    @Schema(description = "is Aggregate Order")
    private Boolean isAggregateOrder;

    @Schema(description = "invoice list")
    private List<InvoiceListInvoiceVO> invoices;

    public Boolean getIsAggregateOrder() {
        return isAggregateOrder;
    }

    public void setIsAggregateOrder(Boolean aggregateOrder) {
        isAggregateOrder = aggregateOrder;
    }

    public String getStatusStrId() {
        return statusStrId;
    }

    public void setStatusStrId(String statusStrId) {
        this.statusStrId = statusStrId;
    }

    public Boolean getIsBuyOrder() {
        return isBuyOrder;
    }

    public void setIsBuyOrder(Boolean isBuyOrder) {
        this.isBuyOrder = isBuyOrder;
    }

    public String getViewOrderExternalUrl() {
        return viewOrderExternalUrl;
    }

    public void setViewOrderExternalUrl(String viewOrderExternalUrl) {
        this.viewOrderExternalUrl = viewOrderExternalUrl;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getAcceptDate() {
        return acceptDate;
    }

    public void setAcceptDate(LocalDateTime acceptDate) {
        this.acceptDate = acceptDate;
    }

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public String getBuyerWorkgroupName() {
        return buyerWorkgroupName;
    }

    public void setBuyerWorkgroupName(String buyerWorkgroupName) {
        this.buyerWorkgroupName = buyerWorkgroupName;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getRemainingBalance() {
        return remainingBalance;
    }

    public void setRemainingBalance(BigDecimal remainingBalance) {
        this.remainingBalance = remainingBalance;
    }

    public Long getOrderAmountCurrencyId() {
        return orderAmountCurrencyId;
    }

    public void setOrderAmountCurrencyId(Long orderAmountCurrencyId) {
        this.orderAmountCurrencyId = orderAmountCurrencyId;
    }

    public Long getInvoiceAmountCurrencyId() {
        return invoiceAmountCurrencyId;
    }

    public void setInvoiceAmountCurrencyId(Long invoiceAmountCurrencyId) {
        this.invoiceAmountCurrencyId = invoiceAmountCurrencyId;
    }

    public Long getRemainingBalanceCurrencyId() {
        return remainingBalanceCurrencyId;
    }

    public void setRemainingBalanceCurrencyId(Long remainingBalanceCurrencyId) {
        this.remainingBalanceCurrencyId = remainingBalanceCurrencyId;
    }

    public Boolean getIsDualCurrency() {
        return this.isDualCurrency;
    }

    public void setIsDualCurrency(Boolean dualCurrency) {
        this.isDualCurrency = dualCurrency;
    }

    public BigDecimal getExOrderAmount() {
        return exOrderAmount;
    }

    public void setExOrderAmount(BigDecimal exOrderAmount) {
        this.exOrderAmount = exOrderAmount;
    }

    public Long getExOrderAmountCurrencyId() {
        return exOrderAmountCurrencyId;
    }

    public void setExOrderAmountCurrencyId(Long exOrderAmountCurrencyId) {
        this.exOrderAmountCurrencyId = exOrderAmountCurrencyId;
    }

    public BigDecimal getExInvoiceAmount() {
        return exInvoiceAmount;
    }

    public void setExInvoiceAmount(BigDecimal exInvoiceAmount) {
        this.exInvoiceAmount = exInvoiceAmount;
    }

    public Long getExInvoiceAmountCurrencyId() {
        return exInvoiceAmountCurrencyId;
    }

    public void setExInvoiceAmountCurrencyId(Long exInvoiceAmountCurrencyId) {
        this.exInvoiceAmountCurrencyId = exInvoiceAmountCurrencyId;
    }

    public BigDecimal getExRemainingBalance() {
        return exRemainingBalance;
    }

    public void setExRemainingBalance(BigDecimal exRemainingBalance) {
        this.exRemainingBalance = exRemainingBalance;
    }

    public Long getExRemainingBalanceCurrencyId() {
        return exRemainingBalanceCurrencyId;
    }

    public void setExRemainingBalanceCurrencyId(Long exRemainingBalanceCurrencyId) {
        this.exRemainingBalanceCurrencyId = exRemainingBalanceCurrencyId;
    }

    public Boolean getIsInvoiceAdjustmentOrder() {
        return isInvoiceAdjustmentOrder;
    }

    public void setIsInvoiceAdjustmentOrder(Boolean invoiceAdjustmentOrder) {
        isInvoiceAdjustmentOrder = invoiceAdjustmentOrder;
    }

    public List<InvoiceListInvoiceVO> getInvoices() {
        return invoices;
    }

    public void setInvoices(List<InvoiceListInvoiceVO> invoices) {
        this.invoices = invoices;
    }
}
