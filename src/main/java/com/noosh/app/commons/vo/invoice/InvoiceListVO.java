package com.noosh.app.commons.vo.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public class InvoiceListVO {

    @Schema(description = "project Name")
    private String projectName;
    @Schema(description = "project External Link")
    private String projectExternalLink;
    @Schema(description = "create Invoice External Link")
    private String createInvoiceExternalLink;
    @Schema(description = "tab Id, 0 buy 1 sell 2 all")
    private Integer tabId;
    @Schema(description = "show Tabs, true - show buy/sell/all tabs, false - show nothing")
    private Boolean showTabs; 

    @Schema(description = "is Outsourcer")
    private Boolean isOutsourcer;
    @Schema(description = "is Include Buyer Side")
    private Boolean isIncludeBuyerSide;
    @Schema(description = "is Include Supplier Side")
    private Boolean isIncludeSupplierSide;
    @Schema(description = "is Buyer")
    private Boolean isBuyer;

    @Schema(description = "order list")
    private List<InvoiceListOrderVO> orders;

    public String getCreateInvoiceExternalLink() {
        return createInvoiceExternalLink;
    }

    public void setCreateInvoiceExternalLink(String createInvoiceExternalLink) {
        this.createInvoiceExternalLink = createInvoiceExternalLink;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectExternalLink() {
        return projectExternalLink;
    }

    public void setProjectExternalLink(String projectExternalLink) {
        this.projectExternalLink = projectExternalLink;
    }

    public Integer getTabId() {
        return tabId;
    }

    public void setTabId(Integer tabId) {
        this.tabId = tabId;
    }

    public Boolean getShowTabs() {
        return showTabs;
    }

    public void setShowTabs(Boolean showTabs) {
        this.showTabs = showTabs;
    }

    public Boolean getIsOutsourcer() {
        return isOutsourcer;
    }

    public void setIsOutsourcer(Boolean outsourcer) {
        isOutsourcer = outsourcer;
    }

    public Boolean getIsIncludeBuyerSide() {
        return isIncludeBuyerSide;
    }

    public void setIsIncludeBuyerSide(Boolean includeBuyerSide) {
        isIncludeBuyerSide = includeBuyerSide;
    }

    public Boolean getIsIncludeSupplierSide() {
        return isIncludeSupplierSide;
    }

    public void setIsIncludeSupplierSide(Boolean includeSupplierSide) {
        isIncludeSupplierSide = includeSupplierSide;
    }

    public Boolean getIsBuyer() {
        return isBuyer;
    }

    public void setIsBuyer(Boolean buyer) {
        isBuyer = buyer;
    }

    public List<InvoiceListOrderVO> getOrders() {
        return orders;
    }

    public void setOrders(List<InvoiceListOrderVO> orders) {
        this.orders = orders;
    }
}
