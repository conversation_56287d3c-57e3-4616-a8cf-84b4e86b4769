package com.noosh.app.commons.vo.userfield;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Author: <PERSON>henyu Hu
 * @Date: 9/9/2022
 */
public class UserFieldVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -6698825330413658691L;

    private Long id;
    private Long fieldClassId;
    private Long fieldControlId;
    private Long ordinalNumber;
    private String label;
    private Long propertyParamId;
    private String propertyParamName;

    private String fieldValues;
    private String attributes;

    private Boolean isRequired;
    private Boolean isInvisibleToSupplier;
    private Boolean includeInTotal;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFieldClassId() {
        return fieldClassId;
    }

    public void setFieldClassId(Long fieldClassId) {
        this.fieldClassId = fieldClassId;
    }

    public Long getFieldControlId() {
        return fieldControlId;
    }

    public void setFieldControlId(Long fieldControlId) {
        this.fieldControlId = fieldControlId;
    }

    public Long getOrdinalNumber() {
        return ordinalNumber;
    }

    public void setOrdinalNumber(Long ordinalNumber) {
        this.ordinalNumber = ordinalNumber;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Long getPropertyParamId() {
        return propertyParamId;
    }

    public void setPropertyParamId(Long propertyParamId) {
        this.propertyParamId = propertyParamId;
    }

    public String getPropertyParamName() {
        return propertyParamName;
    }

    public void setPropertyParamName(String propertyParamName) {
        this.propertyParamName = propertyParamName;
    }

    public String getFieldValues() {
        return fieldValues;
    }

    public void setFieldValues(String fieldValues) {
        this.fieldValues = fieldValues;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public Boolean getRequired() {
        return isRequired;
    }

    public void setRequired(Boolean required) {
        isRequired = required;
    }

    public Boolean getInvisibleToSupplier() {
        return isInvisibleToSupplier;
    }

    public void setInvisibleToSupplier(Boolean invisibleToSupplier) {
        isInvisibleToSupplier = invisibleToSupplier;
    }

    public Boolean getIncludeInTotal() {
        return includeInTotal;
    }

    public void setIncludeInTotal(Boolean includeInTotal) {
        this.includeInTotal = includeInTotal;
    }
}
