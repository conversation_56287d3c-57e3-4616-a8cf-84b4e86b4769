package com.noosh.app.commons.vo.property;

import java.time.LocalDateTime;

import java.math.BigDecimal;

/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 7/7/2019
 */
public class PropertyAttributeVO {

    private Long prPropertyAttributeId;
    private BigDecimal numberValue;
    private LocalDateTime dateValue;
    private String stringValue;
    private String paramName;
    private Long paramId;
    private Object value;
    private Long prDataTypeId;
    private Long prPropertyId;

    public PropertyAttributeVO() {
    }

    public PropertyAttributeVO(Long prPropertyAttributeId, Object value, String paramName) {
        this.prPropertyAttributeId = prPropertyAttributeId;
        this.paramName = paramName;
        this.value = value;
    }

    public PropertyAttributeVO(Long prPropertyAttributeId, Object value, String paramName, Long paramId) {
        this.prPropertyAttributeId = prPropertyAttributeId;
        this.paramName = paramName;
        this.value = value;
        this.paramId = paramId;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public Long getParamId() {
        return paramId;
    }

    public void setParamId(Long paramId) {
        this.paramId = paramId;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public Long getPrPropertyAttributeId() {
        return prPropertyAttributeId;
    }

    public void setPrPropertyAttributeId(Long prPropertyAttributeId) {
        this.prPropertyAttributeId = prPropertyAttributeId;
    }

    public BigDecimal getNumberValue() {
        return numberValue;
    }

    public void setNumberValue(BigDecimal numberValue) {
        this.numberValue = numberValue;
    }

    public LocalDateTime getDateValue() {
        return dateValue;
    }

    public void setDateValue(LocalDateTime dateValue) {
        this.dateValue = dateValue;
    }

    public String getStringValue() {
        return stringValue;
    }

    public void setStringValue(String stringValue) {
        this.stringValue = stringValue;
    }

    public Long getPrDataTypeId() {
        return prDataTypeId;
    }

    public void setPrDataTypeId(Long prDataTypeId) {
        this.prDataTypeId = prDataTypeId;
    }

    public Long getPrPropertyId() {
        return prPropertyId;
    }

    public void setPrPropertyId(Long prPropertyId) {
        this.prPropertyId = prPropertyId;
    }
}
