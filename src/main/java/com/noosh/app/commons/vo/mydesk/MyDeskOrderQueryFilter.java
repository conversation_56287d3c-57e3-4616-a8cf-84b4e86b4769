package com.noosh.app.commons.vo.mydesk;

import io.swagger.v3.oas.annotations.media.Schema;

public class MyDeskOrderQueryFilter {

    @Schema(description = "Supplier Widget Date Type Selected From Dropdown", example = "0")
    private String supplierDateType = null;

    @Schema(description = "Supplier Widget Date Range Selected From Dropdown", example = "30")
    private Long supplierDateRange = null;

    @Schema(description = "Pending Widget Date Range Selected From Dropdown", example = "30")
    private Long pendingDateRange = null;

    @Schema(description = "Invoice Widget Date Range Selected From Dropdown", example = "0")
    private Long invoiceDateRange = null;

    @Schema(description = "Invoice Type Selected From Dropdown", example = "0")
    private Long invoiceType = null;

    public Long getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Long invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getSupplierDateType() {
        return supplierDateType;
    }

    public void setSupplierDateType(String supplierDateType) {
        this.supplierDateType = supplierDateType;
    }

    public Long getSupplierDateRange() {
        return supplierDateRange;
    }

    public void setSupplierDateRange(Long supplierDateRange) {
        this.supplierDateRange = supplierDateRange;
    }

    public Long getPendingDateRange() {
        return pendingDateRange;
    }

    public void setPendingDateRange(Long pendingDateRange) {
        this.pendingDateRange = pendingDateRange;
    }

    public Long getInvoiceDateRange() {
        return invoiceDateRange;
    }

    public void setInvoiceDateRange(Long invoiceDateRange) {
        this.invoiceDateRange = invoiceDateRange;
    }
}
