package com.noosh.app.commons.vo.component;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * User: <PERSON>
 * Date: 7/23/2018
 */
public class DropdownVO<K> implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "the key of dropdownlist")
    private K id;
    @Schema(description = "the label of dropdownlist")
    private String label;
    private String labelStr;
    private String labelStrId;

    public DropdownVO() {

    }

    public DropdownVO(K id, String label) {
        this.id = id;
        this.label = label;
    }

    public K getId() {
        return id;
    }

    public void setId(K id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getLabelStr() {
        return labelStr;
    }

    public void setLabelStr(String labelStr) {
        this.labelStr = labelStr;
    }

    public String getLabelStrId() {
        return labelStrId;
    }

    public void setLabelStrId(String labelStrId) {
        this.labelStrId = labelStrId;
    }
}
