package com.noosh.app.commons.vo.externalItem;

import com.noosh.app.commons.vo.property.PropertyAttributeVO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * User: leilaz
 * Date: 3/31/22
 */
public class ExternalItemVO implements Serializable {
    private static final long serialVersionUID = 3136588566701776801L;

    private String sku;

    private String description;

    private String qtyUom;

    private BigDecimal minUomQty;

    private Long qtyIncrement;

    private BigDecimal myQty;

    private boolean isManuallyCreated;

    //existing for order detail
    private BigDecimal unitPriceFor;

    private String unitPriceUOMFor;

    private BigDecimal priceFor;

    // New fields
    private Long id;
    private Long objectId;
    private Long objectClassId;
    private Long specId;
    private Long itemPropertyId;
    private Long itemIndex;
    private Long qtyUomFactor;
    private String priceUom;
    private Long priceUomFactor;
    private BigDecimal unitPrice;
    private Long unitPriceCurrencyId;
    private String uomFactors;
    private Long forQty1;
    private BigDecimal qty1;
    private BigDecimal price1;
    private Long price1CurrencyId;
    private BigDecimal unitPrice1;
    private Long unitPrice1CurrencyId;
    private String up1Uom;
    private Float paperWeight1;
    private BigDecimal actualQty1;
    private BigDecimal pricePerTon;
    private Long pricePerTonCurrencyId;
    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getQtyUom() {
        return qtyUom;
    }

    public void setQtyUom(String qtyUom) {
        this.qtyUom = qtyUom;
    }

    public BigDecimal getMinUomQty() {
        return minUomQty;
    }

    public void setMinUomQty(BigDecimal minUomQty) {
        this.minUomQty = minUomQty;
    }

    public Long getQtyIncrement() {
        return qtyIncrement;
    }

    public void setQtyIncrement(Long qtyIncrement) {
        this.qtyIncrement = qtyIncrement;
    }

    public BigDecimal getMyQty() {
        return myQty;
    }

    public void setMyQty(BigDecimal myQty) {
        this.myQty = myQty;
    }

    public boolean getIsManuallyCreated() {
        return isManuallyCreated;
    }

    public void setIsManuallyCreated(boolean manuallyCreated) {
        isManuallyCreated = manuallyCreated;
    }

    public BigDecimal getUnitPriceFor() {
        return unitPriceFor;
    }

    public void setUnitPriceFor(BigDecimal unitPriceFor) {
        this.unitPriceFor = unitPriceFor;
    }

    public String getUnitPriceUOMFor() {
        return unitPriceUOMFor;
    }

    public void setUnitPriceUOMFor(String unitPriceUOMFor) {
        this.unitPriceUOMFor = unitPriceUOMFor;
    }

    public BigDecimal getPriceFor() {
        return priceFor;
    }

    public void setPriceFor(BigDecimal priceFor) {
        this.priceFor = priceFor;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getItemPropertyId() {
        return itemPropertyId;
    }

    public void setItemPropertyId(Long itemPropertyId) {
        this.itemPropertyId = itemPropertyId;
    }

    public Long getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Long itemIndex) {
        this.itemIndex = itemIndex;
    }

    public Long getQtyUomFactor() {
        return qtyUomFactor;
    }

    public void setQtyUomFactor(Long qtyUomFactor) {
        this.qtyUomFactor = qtyUomFactor;
    }

    public String getPriceUom() {
        return priceUom;
    }

    public void setPriceUom(String priceUom) {
        this.priceUom = priceUom;
    }

    public Long getPriceUomFactor() {
        return priceUomFactor;
    }

    public void setPriceUomFactor(Long priceUomFactor) {
        this.priceUomFactor = priceUomFactor;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Long getUnitPriceCurrencyId() {
        return unitPriceCurrencyId;
    }

    public void setUnitPriceCurrencyId(Long unitPriceCurrencyId) {
        this.unitPriceCurrencyId = unitPriceCurrencyId;
    }

    public String getUomFactors() {
        return uomFactors;
    }

    public void setUomFactors(String uomFactors) {
        this.uomFactors = uomFactors;
    }

    public Long getForQty1() {
        return forQty1;
    }

    public void setForQty1(Long forQty1) {
        this.forQty1 = forQty1;
    }

    public BigDecimal getQty1() {
        return qty1;
    }

    public void setQty1(BigDecimal qty1) {
        this.qty1 = qty1;
    }

    public BigDecimal getPrice1() {
        return price1;
    }

    public void setPrice1(BigDecimal price1) {
        this.price1 = price1;
    }

    public Long getPrice1CurrencyId() {
        return price1CurrencyId;
    }

    public void setPrice1CurrencyId(Long price1CurrencyId) {
        this.price1CurrencyId = price1CurrencyId;
    }

    public BigDecimal getUnitPrice1() {
        return unitPrice1;
    }

    public void setUnitPrice1(BigDecimal unitPrice1) {
        this.unitPrice1 = unitPrice1;
    }

    public Long getUnitPrice1CurrencyId() {
        return unitPrice1CurrencyId;
    }

    public void setUnitPrice1CurrencyId(Long unitPrice1CurrencyId) {
        this.unitPrice1CurrencyId = unitPrice1CurrencyId;
    }

    public String getUp1Uom() {
        return up1Uom;
    }

    public void setUp1Uom(String up1Uom) {
        this.up1Uom = up1Uom;
    }

    public Float getPaperWeight1() {
        return paperWeight1;
    }

    public void setPaperWeight1(Float paperWeight1) {
        this.paperWeight1 = paperWeight1;
    }

    public BigDecimal getActualQty1() {
        return actualQty1;
    }

    public void setActualQty1(BigDecimal actualQty1) {
        this.actualQty1 = actualQty1;
    }

    public BigDecimal getPricePerTon() {
        return pricePerTon;
    }

    public void setPricePerTon(BigDecimal pricePerTon) {
        this.pricePerTon = pricePerTon;
    }

    public Long getPricePerTonCurrencyId() {
        return pricePerTonCurrencyId;
    }

    public void setPricePerTonCurrencyId(Long pricePerTonCurrencyId) {
        this.pricePerTonCurrencyId = pricePerTonCurrencyId;
    }
}
