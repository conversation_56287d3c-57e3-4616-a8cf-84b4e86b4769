package com.noosh.app.commons.vo.proposal;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 11/22/2021
 */
public class ProposalTemplateDetailVO {

    private String templateName;
    private String logoName;
    private String detailsLogoExternalLink;
    private Long logoId;
    private Long logoPosition;
    private Long brandingColorsType;
    private String customizedColors;
    private String companyName;
    private Long companyAddressId;
    private String line1;
    private String line2;
    private String line3;
    private String city;
    private String state;
    private String postal;
    private Long countryId;
    private Long countryStrId;
    private String companyPhone;
    private String companyFax;
    private String companyUrl;
    private Boolean includePageNumber;
    private Boolean isLandscape;
    private Boolean includeCoverPage;
    private Boolean includeCoverLetter;
    private String introText;
    private String conclusionText;
    private String closingText;
    private Boolean includeTermsAndConditions;
    private Boolean includeProposalNote;
    private String proposalNote;
    private Boolean includeSignaturePage;
    private Boolean useSpecSummary;
    private Boolean isSpecSummaryCompact;
    private Boolean useSpecSummaryBorder;
    private int specSummaryColumns;
    private String quantityLayout;
    private Boolean includePriceBreakouts;
    private Boolean isActive;
    private Boolean isDefault;
    private Boolean isMarkupVisible;
    private Boolean isSeparateSpecPricing;
    private Boolean sumUpAllQuotedQuantity1;
    private Boolean enableSpecSummaryReg;

    public Boolean getEnableSpecSummaryReg() {
        return enableSpecSummaryReg;
    }

    public void setEnableSpecSummaryReg(Boolean enableSpecSummaryReg) {
        this.enableSpecSummaryReg = enableSpecSummaryReg;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getLogoName() {
        return logoName;
    }

    public void setLogoName(String logoName) {
        this.logoName = logoName;
    }

    public String getDetailsLogoExternalLink() {
        return detailsLogoExternalLink;
    }

    public void setDetailsLogoExternalLink(String detailsLogoExternalLink) {
        this.detailsLogoExternalLink = detailsLogoExternalLink;
    }

    public Long getLogoId() {
        return logoId;
    }

    public void setLogoId(Long logoId) {
        this.logoId = logoId;
    }

    public Long getLogoPosition() {
        return logoPosition;
    }

    public void setLogoPosition(Long logoPosition) {
        this.logoPosition = logoPosition;
    }

    public Long getBrandingColorsType() {
        return brandingColorsType;
    }

    public void setBrandingColorsType(Long brandingColorsType) {
        this.brandingColorsType = brandingColorsType;
    }

    public String getCustomizedColors() {
        return customizedColors;
    }

    public void setCustomizedColors(String customizedColors) {
        this.customizedColors = customizedColors;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getCompanyAddressId() {
        return companyAddressId;
    }

    public void setCompanyAddressId(Long companyAddressId) {
        this.companyAddressId = companyAddressId;
    }

    public String getLine1() {
        return line1;
    }

    public void setLine1(String line1) {
        this.line1 = line1;
    }

    public String getLine2() {
        return line2;
    }

    public void setLine2(String line2) {
        this.line2 = line2;
    }

    public String getLine3() {
        return line3;
    }

    public void setLine3(String line3) {
        this.line3 = line3;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPostal() {
        return postal;
    }

    public void setPostal(String postal) {
        this.postal = postal;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getCountryStrId() {
        return countryStrId;
    }

    public void setCountryStrId(Long countryStrId) {
        this.countryStrId = countryStrId;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public String getCompanyFax() {
        return companyFax;
    }

    public void setCompanyFax(String companyFax) {
        this.companyFax = companyFax;
    }

    public String getCompanyUrl() {
        return companyUrl;
    }

    public void setCompanyUrl(String companyUrl) {
        this.companyUrl = companyUrl;
    }

    public Boolean getIncludePageNumber() {
        return includePageNumber;
    }

    public void setIncludePageNumber(Boolean includePageNumber) {
        this.includePageNumber = includePageNumber;
    }

    public Boolean getIsLandscape() {
        return isLandscape;
    }

    public void setIsLandscape(Boolean landscape) {
        isLandscape = landscape;
    }

    public Boolean getIncludeCoverPage() {
        return includeCoverPage;
    }

    public void setIncludeCoverPage(Boolean includeCoverPage) {
        this.includeCoverPage = includeCoverPage;
    }

    public Boolean getIncludeCoverLetter() {
        return includeCoverLetter;
    }

    public void setIncludeCoverLetter(Boolean includeCoverLetter) {
        this.includeCoverLetter = includeCoverLetter;
    }

    public String getIntroText() {
        return introText;
    }

    public void setIntroText(String introText) {
        this.introText = introText;
    }

    public String getConclusionText() {
        return conclusionText;
    }

    public void setConclusionText(String conclusionText) {
        this.conclusionText = conclusionText;
    }

    public String getClosingText() {
        return closingText;
    }

    public void setClosingText(String closingText) {
        this.closingText = closingText;
    }

    public Boolean getIncludeTermsAndConditions() {
        return includeTermsAndConditions;
    }

    public void setIncludeTermsAndConditions(Boolean includeTermsAndConditions) {
        this.includeTermsAndConditions = includeTermsAndConditions;
    }

    public Boolean getIncludeProposalNote() {
        return includeProposalNote;
    }

    public void setIncludeProposalNote(Boolean includeProposalNote) {
        this.includeProposalNote = includeProposalNote;
    }

    public String getProposalNote() {
        return proposalNote;
    }

    public void setProposalNote(String proposalNote) {
        this.proposalNote = proposalNote;
    }

    public Boolean getIncludeSignaturePage() {
        return includeSignaturePage;
    }

    public void setIncludeSignaturePage(Boolean includeSignaturePage) {
        this.includeSignaturePage = includeSignaturePage;
    }

    public Boolean getUseSpecSummary() {
        return useSpecSummary;
    }

    public void setUseSpecSummary(Boolean useSpecSummary) {
        this.useSpecSummary = useSpecSummary;
    }

    public Boolean getIsSpecSummaryCompact() {
        return isSpecSummaryCompact;
    }

    public void setIsSpecSummaryCompact(Boolean specSummaryCompact) {
        isSpecSummaryCompact = specSummaryCompact;
    }

    public Boolean getUseSpecSummaryBorder() {
        return useSpecSummaryBorder;
    }

    public void setUseSpecSummaryBorder(Boolean useSpecSummaryBorder) {
        this.useSpecSummaryBorder = useSpecSummaryBorder;
    }

    public int getSpecSummaryColumns() {
        return specSummaryColumns;
    }

    public void setSpecSummaryColumns(int specSummaryColumns) {
        this.specSummaryColumns = specSummaryColumns;
    }

    public String getQuantityLayout() {
        return quantityLayout;
    }

    public void setQuantityLayout(String quantityLayout) {
        this.quantityLayout = quantityLayout;
    }

    public Boolean getIncludePriceBreakouts() {
        return includePriceBreakouts;
    }

    public void setIncludePriceBreakouts(Boolean includePriceBreakouts) {
        this.includePriceBreakouts = includePriceBreakouts;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean active) {
        isActive = active;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public Boolean getIsMarkupVisible() {
        return isMarkupVisible;
    }

    public void setIsMarkupVisible(Boolean markupVisible) {
        isMarkupVisible = markupVisible;
    }

    public Boolean getIsSeparateSpecPricing() {
        return isSeparateSpecPricing;
    }

    public void setIsSeparateSpecPricing(Boolean separateSpecPricing) {
        isSeparateSpecPricing = separateSpecPricing;
    }

    public Boolean getSumUpAllQuotedQuantity1() {
        return sumUpAllQuotedQuantity1;
    }

    public void setSumUpAllQuotedQuantity1(Boolean sumUpAllQuotedQuantity1) {
        this.sumUpAllQuotedQuantity1 = sumUpAllQuotedQuantity1;
    }
}
