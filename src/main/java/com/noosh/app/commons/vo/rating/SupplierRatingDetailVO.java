package com.noosh.app.commons.vo.rating;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * User: leilaz
 * Date: 7/5/19
 */
public class SupplierRatingDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long orderId;

    private String orderName;

    private String projectName;

    private String projectLink;

    private LocalDateTime orderCompletionDate;

    private String supplierName;

    private String supplierWorkgroup;

    private Long supplierWorkgroupId;

    private Long buyerWorkgroupId;

    private double grandTotal;

    private List<SupplierRatingVO> rateByOrder;

    private List<RateByOrderItemVO> rateByOrderItem;

    private String orderExternalLink;

    private boolean canCreateOrEdit;

    public boolean isCanCreateOrEdit() {
        return canCreateOrEdit;
    }

    public void setCanCreateOrEdit(boolean canCreateOrEdit) {
        this.canCreateOrEdit = canCreateOrEdit;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public String getOrderExternalLink() {
        return orderExternalLink;
    }

    public void setOrderExternalLink(String orderExternalLink) {
        this.orderExternalLink = orderExternalLink;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectLink() {
        return projectLink;
    }

    public void setProjectLink(String projectLink) {
        this.projectLink = projectLink;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public LocalDateTime getOrderCompletionDate() {
        return orderCompletionDate;
    }

    public void setOrderCompletionDate(LocalDateTime orderCompletionDate) {
        this.orderCompletionDate = orderCompletionDate;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierWorkgroup() {
        return supplierWorkgroup;
    }

    public void setSupplierWorkgroup(String supplierWorkgroup) {
        this.supplierWorkgroup = supplierWorkgroup;
    }

    public double getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(double grandTotal) {
        this.grandTotal = grandTotal;
    }

    public List<SupplierRatingVO> getRateByOrder() {
        return rateByOrder;
    }

    public void setRateByOrder(List<SupplierRatingVO> rateByOrder) {
        this.rateByOrder = rateByOrder;
    }

    public List<RateByOrderItemVO> getRateByOrderItem() {
        return rateByOrderItem;
    }

    public void setRateByOrderItem(List<RateByOrderItemVO> rateByOrderItem) {
        this.rateByOrderItem = rateByOrderItem;
    }
}
