package com.noosh.app.commons.vo.spec;

import java.io.Serializable;
import java.util.List;

/**
 * User: <PERSON>
 * Date: 5/7/2018
 */
public class SpecTypeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private String specTypeName;

    private String specTypeNameStrId;

    private String icon;

    private boolean isDefined;

    private String labelStr;

    private String templateBaseName;

    private boolean isNlp;

    private List<SpecTemplateVO> templates;

    private short isTimeMaterials;

    public boolean getIsNlp() {
        return isNlp;
    }

    public void setIsNlp(boolean nlp) {
        isNlp = nlp;
    }

    public String getLabelStr() {
        return labelStr;
    }

    public void setLabelStr(String labelStr) {
        this.labelStr = labelStr;
    }

    public String getSpecTypeNameStrId() {
        return specTypeNameStrId;
    }

    public void setSpecTypeNameStrId(String specTypeNameStrId) {
        this.specTypeNameStrId = specTypeNameStrId;
    }

    public String getTemplateBaseName() {
        return templateBaseName;
    }

    public void setTemplateBaseName(String templateBaseName) {
        this.templateBaseName = templateBaseName;
    }

    public List<SpecTemplateVO> getTemplates() {
        return templates;
    }

    public void setTemplates(List<SpecTemplateVO> templates) {
        this.templates = templates;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSpecTypeName() {
        return specTypeName;
    }

    public void setSpecTypeName(String specTypeName) {
        this.specTypeName = specTypeName;
    }

    public boolean getIsDefined() {
        return isDefined;
    }

    public void setIsDefined(boolean defined) {
        isDefined = defined;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public short getIsTimeMaterials() {
        return isTimeMaterials;
    }

    public void setIsTimeMaterials(short isTimeMaterials) {
        this.isTimeMaterials = isTimeMaterials;
    }
}