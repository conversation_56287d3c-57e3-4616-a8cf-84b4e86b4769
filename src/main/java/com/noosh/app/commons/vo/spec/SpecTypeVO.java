package com.noosh.app.commons.vo.spec;

/**
 * <AUTHOR>
 * @date 11/29/2021
 */
public class SpecTypeVO {

    private Long specTypeId;
    private String specTypeName;
    private Long specTypeNameStrId;
    private String desciption;
    private String templateName;

    public Long getSpecTypeId() {
        return specTypeId;
    }

    public void setSpecTypeId(Long specTypeId) {
        this.specTypeId = specTypeId;
    }

    public String getSpecTypeName() {
        return specTypeName;
    }

    public void setSpecTypeName(String specTypeName) {
        this.specTypeName = specTypeName;
    }

    public Long getSpecTypeNameStrId() {
        return specTypeNameStrId;
    }

    public void setSpecTypeNameStrId(Long specTypeNameStrId) {
        this.specTypeNameStrId = specTypeNameStrId;
    }

    public String getDesciption() {
        return desciption;
    }

    public void setDesciption(String desciption) {
        this.desciption = desciption;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
}
