package com.noosh.app.commons.vo.costcenter;

import com.noosh.app.commons.dto.costcenter.OrderCostCenterDTO;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 1/11/21
 */
public class AggregatedOrderCostCenterVO implements Serializable {

    private static final long serialVersionUID = 2074740760976718381L;
    private double aggregatedAmount;

    private List<CostCenterAllocationVO> costCenters;

    public List<CostCenterAllocationVO> getCostCenters() {
        return costCenters;
    }

    public void setCostCenters(List<CostCenterAllocationVO> costCenters) {
        this.costCenters = costCenters;
    }

    public double getAggregatedAmount() {
        return aggregatedAmount;
    }

    public void setAggregatedAmount(double aggregatedAmount) {
        this.aggregatedAmount = aggregatedAmount;
    }
}
