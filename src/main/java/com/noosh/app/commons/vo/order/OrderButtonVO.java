package com.noosh.app.commons.vo.order;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 * User: leilaz
 * Date: 3/29/20
 */
public class OrderButtonVO implements Serializable {
    private static final long serialVersionUID = 1034351101484230616L;

    private String editDraftButton;
    private String deleteDraftButton;
    private String editButton;
    private String acceptButton;
    private String routeForApprovalButton;
    private String routeForManagerApprovalButton;
    private String submitButton;
    private String rejectButton;
    private String reorderButton;
    private String cancelButton;
    private String retractButton;
    private String createChangeOrderButton;
    private String dismissButton;
    private String updateButton;
    private String editSupplierRefButton;
    private String editInfoButton;
    private String costCenterAllocationButton;
    private String editShipmentButton;
    private String supplierRatingButton;
    private String sourcingStrategiesButton;
    private String completeButton;
    private String routingApproveButton;
    private String routingDisapproveButton;
	private String V1000IReportLink;
    private String printVendorReportButton;

    public String getV1000IReportLink() {
        return V1000IReportLink;
    }

    public void setV1000IReportLink(String v1000IReportLink) {
        V1000IReportLink = v1000IReportLink;
    }

    public String getEditDraftButton() {
        return editDraftButton;
    }

    public void setEditDraftButton(String editDraftButton) {
        this.editDraftButton = editDraftButton;
    }

    public String getDeleteDraftButton() {
        return deleteDraftButton;
    }

    public void setDeleteDraftButton(String deleteDraftButton) {
        this.deleteDraftButton = deleteDraftButton;
    }

    public String getEditButton() {
        return editButton;
    }

    public void setEditButton(String editButton) {
        this.editButton = editButton;
    }

    public String getAcceptButton() {
        return acceptButton;
    }

    public void setAcceptButton(String acceptButton) {
        this.acceptButton = acceptButton;
    }

    public String getRouteForApprovalButton() {
        return routeForApprovalButton;
    }

    public void setRouteForApprovalButton(String routeForApprovalButton) {
        this.routeForApprovalButton = routeForApprovalButton;
    }

    public String getRouteForManagerApprovalButton() {
        return routeForManagerApprovalButton;
    }

    public void setRouteForManagerApprovalButton(String routeForManagerApprovalButton) {
        this.routeForManagerApprovalButton = routeForManagerApprovalButton;
    }

    public String getSubmitButton() {
        return submitButton;
    }

    public void setSubmitButton(String submitButton) {
        this.submitButton = submitButton;
    }

    public String getRejectButton() {
        return rejectButton;
    }

    public void setRejectButton(String rejectButton) {
        this.rejectButton = rejectButton;
    }

    public String getReorderButton() {
        return reorderButton;
    }

    public void setReorderButton(String reorderButton) {
        this.reorderButton = reorderButton;
    }

    public String getCancelButton() {
        return cancelButton;
    }

    public void setCancelButton(String cancelButton) {
        this.cancelButton = cancelButton;
    }

    public String getRetractButton() {
        return retractButton;
    }

    public void setRetractButton(String retractButton) {
        this.retractButton = retractButton;
    }

    public String getCreateChangeOrderButton() {
        return createChangeOrderButton;
    }

    public void setCreateChangeOrderButton(String createChangeOrderButton) {
        this.createChangeOrderButton = createChangeOrderButton;
    }

    public String getDismissButton() {
        return dismissButton;
    }

    public void setDismissButton(String dismissButton) {
        this.dismissButton = dismissButton;
    }

    public String getUpdateButton() {
        return updateButton;
    }

    public void setUpdateButton(String updateButton) {
        this.updateButton = updateButton;
    }

    public String getEditSupplierRefButton() {
        return editSupplierRefButton;
    }

    public void setEditSupplierRefButton(String editSupplierRefButton) {
        this.editSupplierRefButton = editSupplierRefButton;
    }

    public String getEditInfoButton() {
        return editInfoButton;
    }

    public void setEditInfoButton(String editInfoButton) {
        this.editInfoButton = editInfoButton;
    }

    public String getCostCenterAllocationButton() {
        return costCenterAllocationButton;
    }

    public void setCostCenterAllocationButton(String costCenterAllocationButton) {
        this.costCenterAllocationButton = costCenterAllocationButton;
    }

    public String getEditShipmentButton() {
        return editShipmentButton;
    }

    public void setEditShipmentButton(String editShipmentButton) {
        this.editShipmentButton = editShipmentButton;
    }

    public String getSupplierRatingButton() {
        return supplierRatingButton;
    }

    public void setSupplierRatingButton(String supplierRatingButton) {
        this.supplierRatingButton = supplierRatingButton;
    }

    public String getSourcingStrategiesButton() {
        return sourcingStrategiesButton;
    }

    public void setSourcingStrategiesButton(String sourcingStrategiesButton) {
        this.sourcingStrategiesButton = sourcingStrategiesButton;
    }

    public String getCompleteButton() {
        return completeButton;
    }

    public void setCompleteButton(String completeButton) {
        this.completeButton = completeButton;
    }

    public String getRoutingApproveButton() {
        return routingApproveButton;
    }

    public void setRoutingApproveButton(String routingApproveButton) {
        this.routingApproveButton = routingApproveButton;
    }

    public String getRoutingDisapproveButton() {
        return routingDisapproveButton;
    }

    public void setRoutingDisapproveButton(String routingDisapproveButton) {
        this.routingDisapproveButton = routingDisapproveButton;
    }

    public String getPrintVendorReportButton() {
        return printVendorReportButton;
    }

    public void setPrintVendorReportButton(String printVendorReportButton) {
        this.printVendorReportButton = printVendorReportButton;
    }
}
