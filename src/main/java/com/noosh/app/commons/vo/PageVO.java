package com.noosh.app.commons.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public class PageVO {
    @Schema(description = "Current Page Index")
    private int num;
    @Schema(description = "Page Size")
    private int size;
    @Schema(description = "Total Size of result items")
    private long total;
    @Schema(description = "Sort by Field, It supports multiple fields separated by comma.")
    private String sort;
    @Schema(description = "Sort by order, the order could be ACS or DESC")
    private String order;

    public PageVO(int num, int size) {
        this.num = num;
        this.size = size;
    }

    public PageVO(int num, int size, String sort, String order) {
        this.num = num;
        this.size = size;
        this.sort = sort;
        this.order = order;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    /**
     * get order by, default order by desc
     *
     * @return orderBy
     */
    @JsonIgnore
    public String getOrderBy() {
        String orderBy = null;
        if (getSort() != null) {
            String order = getOrder() == null ? "DESC NULLS LAST" : getOrder() + " NULLS LAST";
            if (getSort().contains(",")) {
                orderBy = getSort().replaceAll(",",  " " + order + ",") + " " + order;

            } else {
                orderBy = getSort() + " " + order;
            }

        }

        return orderBy;
    }
}
