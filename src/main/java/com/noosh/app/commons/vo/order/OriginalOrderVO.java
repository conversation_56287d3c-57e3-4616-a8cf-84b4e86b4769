package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.account.SupplierFlagVO;
import com.noosh.app.commons.vo.rating.SupplierScoreVO;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;


/**
 * @auther mario
 * @date 2/25/2020
 */
@Schema(description = "Original order")
public class OriginalOrderVO {
    @Schema(description = "Order id")
    private Long orderId;
    @Schema(description = "Order reference")
    private String reference;
    @Schema(description = "Order title/reference")
    private String orderName;
    @Schema(description = "Order type; Original, Order")
    private String orderType;
    @Schema(description = "Is Invoice Adjustment Order")
    private Boolean isInvoiceAdjustmentOrder;
    @Schema(description = "Order state id")
    private Long stateId;
    @Schema(description = "Order state; Pending Submission=2000030, Accepted=2000078, Rejected = 2000080, Draft = 2000079, Pending Supplier Acceptance = 2000081, " +
            "Pending Buyer Acceptance=2000082, Cancelled=2000083, Retracted = 2000084, Pending Outsourcer Acceptance=2000085, Pending Client Acceptance=2000086, " +
            "Shipped=2500043, Delivered=2500044, Closed=2500045, Partially shipped=2500064, Accepted (not yet shipped)=2500065, Order Completed=2500074, Replaced=2500213")
    private String state;
    private String stateStrId;
    private String paymentReference;
    @Schema(description = "Order completion date")
    private LocalDateTime completionDate;
    @Schema(description = "Order Last mod date")
    private LocalDateTime lastChangeDate;
    @Schema(description = "Order supplier workgroup id")
    private Long supplierWorkgroupId;
    @Schema(description = "Order supplier workgroup name")
    private String supplierWorkgroupName;
    private SupplierFlagVO supplierFlag;
    private SupplierScoreVO supplierScore;
    @Schema(description = "Order buyer workgroup id")
    private Long buyerWorkgroupId;
    @Schema(description = "Order buyer workgroup name")
    private String buyerWorkgroupName;
    @Schema(description = "Order item total amout")

    private Double amount;
    private Long amountCurrencyId;
    private Double grandTotalValue;
    private Long grandTotalValueCurrencyId;
    private Double totalSaving;
    private boolean isCompleted;
    private Double discountOrSurchargeTotal;
    private Long discountOrSurchargeTotalCurrencyId;

    //dual currency
    private Long exCurrencyId;
    private Double exAmount;
    private Long exAmountCurrencyId;
    private Double exGrandTotalValue;
    private Long exGrandTotalValueCurrencyId;
    private Double exDiscountOrSurchargeTotal;
    private Long exDiscountOrSurchargeTotalCurrencyId;
    private Double exTotalSaving;

    @Schema(description = "Project link, redirect to Enterprise")
    private String printExternalLink;
    @Schema(description = "Supplier Rating link, redirect to Enterprise")
    private String SupplierRatingExternalLink;
    @Schema(description = "Original order link, redirect to Enterprise")
    private String originalOrderExternalLink;
    @Schema(description = "Order with changes link, redirect to Enterprise")
    private String orderWithChangesExternalLink;
    private String sourcingStrategyExternalLink;
    private List<PropertyAttributeVO> customFields;
    @Schema(description = "Change orders list")
    private List<ChangeOrderVO> changeOrders;

    public String getSourcingStrategyExternalLink() {
        return sourcingStrategyExternalLink;
    }

    public void setSourcingStrategyExternalLink(String sourcingStrategyExternalLink) {
        this.sourcingStrategyExternalLink = sourcingStrategyExternalLink;
    }

    public boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(boolean completed) {
        isCompleted = completed;
    }

    public Double getTotalSaving() {
        return totalSaving;
    }

    public void setTotalSaving(Double totalSaving) {
        this.totalSaving = totalSaving;
    }

    public List<PropertyAttributeVO> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<PropertyAttributeVO> customFields) {
        this.customFields = customFields;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public String getStateStrId() {
        return stateStrId;
    }

    public void setStateStrId(String stateStrId) {
        this.stateStrId = stateStrId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Boolean getIsInvoiceAdjustmentOrder() {
        return isInvoiceAdjustmentOrder;
    }

    public void setIsInvoiceAdjustmentOrder(Boolean invoiceAdjustmentOrder) {
        isInvoiceAdjustmentOrder = invoiceAdjustmentOrder;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getLastChangeDate() {
        return lastChangeDate;
    }

    public void setLastChangeDate(LocalDateTime lastChangeDate) {
        this.lastChangeDate = lastChangeDate;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public String getBuyerWorkgroupName() {
        return buyerWorkgroupName;
    }

    public void setBuyerWorkgroupName(String buyerWorkgroupName) {
        this.buyerWorkgroupName = buyerWorkgroupName;
    }

    public Long getExCurrencyId() {
        return this.exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getExAmount() {
        return this.exAmount;
    }

    public void setExAmount(Double exAmount) {
        this.exAmount = exAmount;
    }

    public Double getGrandTotalValue() {
        return grandTotalValue;
    }

    public void setGrandTotalValue(Double grandTotalValue) {
        this.grandTotalValue = grandTotalValue;
    }

    public Double getExGrandTotalValue() {
        return this.exGrandTotalValue;
    }

    public void setExGrandTotalValue(Double exGrandTotalValue) {
        this.exGrandTotalValue = exGrandTotalValue;
    }

    public Double getDiscountOrSurchargeTotal() {
        return discountOrSurchargeTotal;
    }

    public void setDiscountOrSurchargeTotal(Double discountOrSurchargeTotal) {
        this.discountOrSurchargeTotal = discountOrSurchargeTotal;
    }

    public Double getExDiscountOrSurchargeTotal() {
        return this.exDiscountOrSurchargeTotal;
    }

    public void setExDiscountOrSurchargeTotal(Double exDiscountOrSurchargeTotal) {
        this.exDiscountOrSurchargeTotal = exDiscountOrSurchargeTotal;
    }

    public Long getAmountCurrencyId() {
        return this.amountCurrencyId;
    }

    public void setAmountCurrencyId(Long amountCurrencyId) {
        this.amountCurrencyId = amountCurrencyId;
    }

    public Long getExAmountCurrencyId() {
        return this.exAmountCurrencyId;
    }

    public void setExAmountCurrencyId(Long exAmountCurrencyId) {
        this.exAmountCurrencyId = exAmountCurrencyId;
    }

    public Long getGrandTotalValueCurrencyId() {
        return this.grandTotalValueCurrencyId;
    }

    public void setGrandTotalValueCurrencyId(Long grandTotalValueCurrencyId) {
        this.grandTotalValueCurrencyId = grandTotalValueCurrencyId;
    }

    public Long getExGrandTotalValueCurrencyId() {
        return this.exGrandTotalValueCurrencyId;
    }

    public void setExGrandTotalValueCurrencyId(Long exGrandTotalValueCurrencyId) {
        this.exGrandTotalValueCurrencyId = exGrandTotalValueCurrencyId;
    }

    public Long getDiscountOrSurchargeTotalCurrencyId() {
        return this.discountOrSurchargeTotalCurrencyId;
    }

    public void setDiscountOrSurchargeTotalCurrencyId(Long discountOrSurchargeTotalCurrencyId) {
        this.discountOrSurchargeTotalCurrencyId = discountOrSurchargeTotalCurrencyId;
    }

    public Long getExDiscountOrSurchargeTotalCurrencyId() {
        return this.exDiscountOrSurchargeTotalCurrencyId;
    }

    public void setExDiscountOrSurchargeTotalCurrencyId(Long exDiscountOrSurchargeTotalCurrencyId) {
        this.exDiscountOrSurchargeTotalCurrencyId = exDiscountOrSurchargeTotalCurrencyId;
    }

    public String getPrintExternalLink() {
        return printExternalLink;
    }

    public void setPrintExternalLink(String printExternalLink) {
        this.printExternalLink = printExternalLink;
    }

    public String getSupplierRatingExternalLink() {
        return SupplierRatingExternalLink;
    }

    public void setSupplierRatingExternalLink(String supplierRatingExternalLink) {
        SupplierRatingExternalLink = supplierRatingExternalLink;
    }

    public String getOriginalOrderExternalLink() {
        return originalOrderExternalLink;
    }

    public void setOriginalOrderExternalLink(String originalOrderExternalLink) {
        this.originalOrderExternalLink = originalOrderExternalLink;
    }

    public String getOrderWithChangesExternalLink() {
        return orderWithChangesExternalLink;
    }

    public void setOrderWithChangesExternalLink(String orderWithChangesExternalLink) {
        this.orderWithChangesExternalLink = orderWithChangesExternalLink;
    }

    public List<ChangeOrderVO> getChangeOrders() {
        return changeOrders;
    }

    public void setChangeOrders(List<ChangeOrderVO> changeOrders) {
        this.changeOrders = changeOrders;
    }

    public SupplierFlagVO getSupplierFlag() {
        return supplierFlag;
    }

    public void setSupplierFlag(SupplierFlagVO supplierFlag) {
        this.supplierFlag = supplierFlag;
    }

    public SupplierScoreVO getSupplierScore() {
        return supplierScore;
    }

    public void setSupplierScore(SupplierScoreVO supplierScore) {
        this.supplierScore = supplierScore;
    }
}
