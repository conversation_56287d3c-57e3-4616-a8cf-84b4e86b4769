package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.invoice.InvoiceVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * User: leilaz
 * Date: 4/14/20
 */
public class ChangeOrderDetailVO extends OrderGeneralInfoVO {
    private static final long serialVersionUID = -1896030706986091685L;

    private String parentOrderReference;

    private boolean requiredApprovalInvoice;

    private boolean autoAcceptFinalInvoice;

    private boolean invoiceSentOnClosedOrder;

    private boolean isDisableCarryOverChangeOrder;

    private boolean isDisableAcceptAndCarryOver;

    private InvoiceVO finalInvoiceTemplate;

    private List<OrderItemVO> parentOrderItems;

    private BigDecimal invoiceTotalAmount;

    public boolean getIsDisableCarryOverChangeOrder() {
        return isDisableCarryOverChangeOrder;
    }

    public void setIsDisableCarryOverChangeOrder(boolean disableCarryOverChangeOrder) {
        isDisableCarryOverChangeOrder = disableCarryOverChangeOrder;
    }

    public boolean getIsDisableAcceptAndCarryOver() {
        return isDisableAcceptAndCarryOver;
    }

    public void setIsDisableAcceptAndCarryOver(boolean disableAcceptAndCarryOver) {
        isDisableAcceptAndCarryOver = disableAcceptAndCarryOver;
    }

    public boolean isRequiredApprovalInvoice() {
        return requiredApprovalInvoice;
    }

    public void setRequiredApprovalInvoice(boolean requiredApprovalInvoice) {
        this.requiredApprovalInvoice = requiredApprovalInvoice;
    }

    public boolean isAutoAcceptFinalInvoice() {
        return autoAcceptFinalInvoice;
    }

    public void setAutoAcceptFinalInvoice(boolean autoAcceptFinalInvoice) {
        this.autoAcceptFinalInvoice = autoAcceptFinalInvoice;
    }

    public boolean isInvoiceSentOnClosedOrder() {
        return invoiceSentOnClosedOrder;
    }

    public void setInvoiceSentOnClosedOrder(boolean invoiceSentOnClosedOrder) {
        this.invoiceSentOnClosedOrder = invoiceSentOnClosedOrder;
    }

    public InvoiceVO getFinalInvoiceTemplate() {
        return finalInvoiceTemplate;
    }

    public void setFinalInvoiceTemplate(InvoiceVO finalInvoiceTemplate) {
        this.finalInvoiceTemplate = finalInvoiceTemplate;
    }

    public String getParentOrderReference() {
        return parentOrderReference;
    }

    public void setParentOrderReference(String parentOrderReference) {
        this.parentOrderReference = parentOrderReference;
    }

    public List<OrderItemVO> getParentOrderItems() {
        return parentOrderItems;
    }

    public void setParentOrderItems(List<OrderItemVO> parentOrderItems) {
        this.parentOrderItems = parentOrderItems;
    }

    public BigDecimal getInvoiceTotalAmount() {
        return invoiceTotalAmount;
    }

    public void setInvoiceTotalAmount(BigDecimal invoiceTotalAmount) {
        this.invoiceTotalAmount = invoiceTotalAmount;
    }
}
