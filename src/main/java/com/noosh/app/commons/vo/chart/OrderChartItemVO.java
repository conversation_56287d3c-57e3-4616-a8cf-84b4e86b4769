package com.noosh.app.commons.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * User: leilaz
 */
public class OrderChartItemVO {
    @Schema(description = "Average Type")
    private String avgType;
    @Schema(description = "Order Create Days")
    private Double orderCreateDays;
    @Schema(description = "Order Accept Days")
    private Double orderAcceptDays;
    @Schema(description = "Order Close Days")
    private Double orderCloseDays;

    public String getAvgType() {
        return avgType;
    }

    public void setAvgType(String avgType) {
        this.avgType = avgType;
    }

    public Double getOrderCreateDays() {
        return orderCreateDays;
    }

    public void setOrderCreateDays(Double orderCreateDays) {
        this.orderCreateDays = orderCreateDays;
    }

    public Double getOrderCloseDays() {
        return orderCloseDays;
    }

    public void setOrderCloseDays(Double orderCloseDays) {
        this.orderCloseDays = orderCloseDays;
    }

    public Double getOrderAcceptDays() {
        return orderAcceptDays;
    }

    public void setOrderAcceptDays(Double orderAcceptDays) {
        this.orderAcceptDays = orderAcceptDays;
    }
}
