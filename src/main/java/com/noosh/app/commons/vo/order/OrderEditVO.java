package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.address.AddressVO;

import java.io.Serializable;
import java.util.List;

public class OrderEditVO implements Serializable {

    private static final long serialVersionUID = -4426557072044022435L;

    private OrderGeneralInfoVO orderGeneral;

    private TermsVO buyerTerms;

    private TermsVO supplierTerms;

    private Boolean canSaveAndAccept;
    private Boolean canSaveDraft;
    private Boolean canSaveOrder;
    private Boolean canAddItem;
    private Boolean bypassReAcceptanceEnabled;
    private Boolean canByPassReAcceptance;

    //dismiss control
    private Boolean canCloseRfe;
    private Boolean canDismissSupplier;
    private Boolean isCloseRfe;
    private Boolean isDismissUnselectedSupplier;
    private Boolean dismissPrefValue;
    private List<AddressVO> ownerWorkgroupMainAddress;

    public TermsVO getBuyerTerms() {
        return buyerTerms;
    }

    public void setBuyerTerms(TermsVO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public TermsVO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(TermsVO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public OrderGeneralInfoVO getOrderGeneral() {
        return orderGeneral;
    }

    public void setOrderGeneral(OrderGeneralInfoVO orderGeneral) {
        this.orderGeneral = orderGeneral;
    }

    public Boolean getCanSaveAndAccept() {
        return this.canSaveAndAccept;
    }

    public void setCanSaveAndAccept(Boolean canSaveAndAccept) {
        this.canSaveAndAccept = canSaveAndAccept;
    }

    public Boolean getCanSaveDraft() {
        return this.canSaveDraft;
    }

    public void setCanSaveDraft(Boolean canSaveDraft) {
        this.canSaveDraft = canSaveDraft;
    }

    public Boolean getCanSaveOrder() {
        return this.canSaveOrder;
    }

    public void setCanSaveOrder(Boolean canSaveOrder) {
        this.canSaveOrder = canSaveOrder;
    }

    public Boolean getCanCloseRfe() {
        return this.canCloseRfe;
    }

    public void setCanCloseRfe(Boolean canCloseRfe) {
        this.canCloseRfe = canCloseRfe;
    }

    public Boolean getCanDismissSupplier() {
        return this.canDismissSupplier;
    }

    public void setCanDismissSupplier(Boolean canDismissSupplier) {
        this.canDismissSupplier = canDismissSupplier;
    }

    public Boolean getIsCloseRfe() {
        return this.isCloseRfe;
    }

    public void setIsCloseRfe(Boolean closeRfe) {
        this.isCloseRfe = closeRfe;
    }

    public Boolean getIsDismissUnselectedSupplier() {
        return this.isDismissUnselectedSupplier;
    }

    public void setIsDismissUnselectedSupplier(Boolean dismissUnselectedSupplier) {
        this.isDismissUnselectedSupplier = dismissUnselectedSupplier;
    }

    public Boolean getDismissPrefValue() {
        return this.dismissPrefValue;
    }

    public void setDismissPrefValue(Boolean dismissPrefValue) {
        this.dismissPrefValue = dismissPrefValue;
    }

    public Boolean getCanAddItem() {
        return this.canAddItem;
    }

    public void setCanAddItem(Boolean canAddItem) {
        this.canAddItem = canAddItem;
    }

    public Boolean getBypassReAcceptanceEnabled() {
        return bypassReAcceptanceEnabled;
    }

    public void setBypassReAcceptanceEnabled(Boolean bypassReAcceptanceEnabled) {
        this.bypassReAcceptanceEnabled = bypassReAcceptanceEnabled;
    }

    public Boolean getCanByPassReAcceptance() {
        return canByPassReAcceptance;
    }

    public void setCanByPassReAcceptance(Boolean canByPassReAcceptance) {
        this.canByPassReAcceptance = canByPassReAcceptance;
    }

    public List<AddressVO> getOwnerWorkgroupMainAddress() {
        return ownerWorkgroupMainAddress;
    }

    public void setOwnerWorkgroupMainAddress(List<AddressVO> ownerWorkgroupMainAddress) {
        this.ownerWorkgroupMainAddress = ownerWorkgroupMainAddress;
    }
}
