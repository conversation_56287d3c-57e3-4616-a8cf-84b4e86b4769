package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.account.AccountUserVO;

import java.util.List;

public class OrderConfirmVO {
    private Boolean requiresApproval;
    private Boolean requiresManagerApproval;
    private Boolean isAllAtOnce;
    private Boolean autoTimeOut;
    private List<AccountUserVO> approvers;
    private List<AccountUserVO> managerApprovers;
    private TermsVO buyerTerms;
    private TermsVO supplierTerms;

    public Boolean getRequiresApproval() {
        return this.requiresApproval;
    }

    public void setRequiresApproval(Boolean requiresApproval) {
        this.requiresApproval = requiresApproval;
    }

    public Boolean getRequiresManagerApproval() {
        return this.requiresManagerApproval;
    }

    public void setRequiresManagerApproval(Boolean requiresManagerApproval) {
        this.requiresManagerApproval = requiresManagerApproval;
    }

    public Boolean getIsAllAtOnce() {
        return this.isAllAtOnce;
    }

    public void setIsAllAtOnce(Boolean allAtOnce) {
        this.isAllAtOnce = allAtOnce;
    }

    public Boolean getAutoTimeOut() {
        return this.autoTimeOut;
    }

    public void setAutoTimeOut(Boolean autoTimeOut) {
        this.autoTimeOut = autoTimeOut;
    }

    public List<AccountUserVO> getApprovers() {
        return this.approvers;
    }

    public void setApprovers(List<AccountUserVO> approvers) {
        this.approvers = approvers;
    }

    public List<AccountUserVO> getManagerApprovers() {
        return this.managerApprovers;
    }

    public void setManagerApprovers(List<AccountUserVO> managerApprovers) {
        this.managerApprovers = managerApprovers;
    }

    public TermsVO getBuyerTerms() {
        return buyerTerms;
    }

    public void setBuyerTerms(TermsVO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public TermsVO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(TermsVO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

}
