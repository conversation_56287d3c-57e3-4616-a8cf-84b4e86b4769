package com.noosh.app.commons.vo.order;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 4/7/24
 */
public class OrderInvoiceMyDeskListVO implements Serializable {
    private static final long serialVersionUID = 8230323476097682366L;

    private List<OrderInvoiceMyDeskVO> invoicedOrders;

    private List<OrderInvoiceMyDeskVO> notInvoicedOrders;

    private List<OrderInvoiceMyDeskVO> notFullyInvoicedOrders;

    public List<OrderInvoiceMyDeskVO> getNotFullyInvoicedOrders() {
        return notFullyInvoicedOrders;
    }

    public void setNotFullyInvoicedOrders(List<OrderInvoiceMyDeskVO> notFullyInvoicedOrders) {
        this.notFullyInvoicedOrders = notFullyInvoicedOrders;
    }

    public List<OrderInvoiceMyDeskVO> getInvoicedOrders() {
        return invoicedOrders;
    }

    public void setInvoicedOrders(List<OrderInvoiceMyDeskVO> invoicedOrders) {
        this.invoicedOrders = invoicedOrders;
    }

    public List<OrderInvoiceMyDeskVO> getNotInvoicedOrders() {
        return notInvoicedOrders;
    }

    public void setNotInvoicedOrders(List<OrderInvoiceMyDeskVO> notInvoicedOrders) {
        this.notInvoicedOrders = notInvoicedOrders;
    }
}
