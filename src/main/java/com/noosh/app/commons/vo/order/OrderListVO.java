package com.noosh.app.commons.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @auther mario
 * @date 2/25/2020
 */
@Schema(description = "Order list")
public class OrderListVO {
    @Schema(description = "Order type; buy, sell")
    private String orderType;
    @Schema(description = "Is supplier project")
    private Boolean isSupplierProject;
    @Schema(description = "Is broker outsourcer project")
    private Boolean isBrokerOutsourcerProject;
    @Schema(description = "Is buyer project")
    private Boolean isBuyerProject;
    @Schema(description = "Is client project")
    private Boolean isClientProject;
    @Schema(description = "whether to display dual currency")
    private Boolean isDualCurrency;
    @Schema(description = "for client/supplier only, whether to hide base currency, only to display transactional currency")
    private Boolean hideBaseCurrency;
    @Schema(description = "Original orders list")
    private List<OriginalOrderVO> originalOrders;
    @Schema(description = "Project total accepted")
    private Double projectTotalAwarded;
    @Schema(description = "Project total accepted/pending")
    private Double projectTotalAwardedAndPending;
    @Schema(description = "Creat quick order link, redirect to Enterprise")
    private String createQuickOrderExternalLink;

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Boolean getIsBuyerProject() {
        return this.isBuyerProject;
    }

    public void setIsBuyerProject(Boolean buyerProject) {
        this.isBuyerProject = buyerProject;
    }

    public Boolean getIsSupplierProject() {
        return isSupplierProject;
    }

    public void setIsSupplierProject(Boolean supplierProject) {
        isSupplierProject = supplierProject;
    }

    public Boolean getIsBrokerOutsourcerProject() {
        return isBrokerOutsourcerProject;
    }

    public void setIsBrokerOutsourcerProject(Boolean brokerOutsourcerProject) {
        isBrokerOutsourcerProject = brokerOutsourcerProject;
    }

    public Boolean getIsClientProject() {
        return this.isClientProject;
    }

    public void setIsClientProject(Boolean clientProject) {
        this.isClientProject = clientProject;
    }

    public Boolean getIsDualCurrency() {
        return this.isDualCurrency;
    }

    public void setIsDualCurrency(Boolean dualCurrency) {
        this.isDualCurrency = dualCurrency;
    }

    public Boolean getHideBaseCurrency() {
        return this.hideBaseCurrency;
    }

    public void setHideBaseCurrency(Boolean hideBaseCurrency) {
        this.hideBaseCurrency = hideBaseCurrency;
    }

    public List<OriginalOrderVO> getOriginalOrders() {
        return originalOrders;
    }

    public void setOriginalOrders(List<OriginalOrderVO> originalOrders) {
        this.originalOrders = originalOrders;
    }

    public Double getProjectTotalAwarded() {
        return projectTotalAwarded;
    }

    public void setProjectTotalAwarded(Double projectTotalAwarded) {
        this.projectTotalAwarded = projectTotalAwarded;
    }

    public Double getProjectTotalAwardedAndPending() {
        return projectTotalAwardedAndPending;
    }

    public void setProjectTotalAwardedAndPending(Double projectTotalAwardedAndPending) {
        this.projectTotalAwardedAndPending = projectTotalAwardedAndPending;
    }

    public String getCreateQuickOrderExternalLink() {
        return createQuickOrderExternalLink;
    }

    public void setCreateQuickOrderExternalLink(String createQuickOrderExternalLink) {
        this.createQuickOrderExternalLink = createQuickOrderExternalLink;
    }
}
