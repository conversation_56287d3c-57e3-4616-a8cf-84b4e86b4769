package com.noosh.app.commons.vo.costcenter;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 9/14/20
 */
public class OrderCostCenterVO implements Serializable {
    private static final long serialVersionUID = -8942073225029722919L;

    private Long itemId;

    private String itemName;

    private double itemValue;

    private double quantity;

    private String itemExternalLink;

    private List<CostCenterAllocationVO> costCenters;

    private OrderCostCenterVO orderItemTax;
    private OrderCostCenterVO orderItemShipping;

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    public OrderCostCenterVO getOrderItemTax() {
        return orderItemTax;
    }

    public void setOrderItemTax(OrderCostCenterVO orderItemTax) {
        this.orderItemTax = orderItemTax;
    }

    public OrderCostCenterVO getOrderItemShipping() {
        return orderItemShipping;
    }

    public void setOrderItemShipping(OrderCostCenterVO orderItemShipping) {
        this.orderItemShipping = orderItemShipping;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getItemExternalLink() {
        return itemExternalLink;
    }

    public void setItemExternalLink(String itemExternalLink) {
        this.itemExternalLink = itemExternalLink;
    }

    public double getItemValue() {
        return itemValue;
    }

    public void setItemValue(double itemValue) {
        this.itemValue = itemValue;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public List<CostCenterAllocationVO> getCostCenters() {
        return costCenters;
    }

    public void setCostCenters(List<CostCenterAllocationVO> costCenters) {
        this.costCenters = costCenters;
    }
}
