package com.noosh.app.commons.vo.proposal;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 11/1/2021
 */
public class ProposalTemplateVO {

    private Long templateId;
    private String name;
    private LocalDateTime createdDate;
    private String createdFullName;
    private LocalDateTime lastUpdatedDate;
    private String updatedFullName;
    private String detailsTemplateExternalLink;
    private String editTemplateExternalLink;
    private String deleteTemplateExternalLink;
    private Boolean isDefault;

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedFullName() {
        return createdFullName;
    }

    public void setCreatedFullName(String createdFullName) {
        this.createdFullName = createdFullName;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getUpdatedFullName() {
        return updatedFullName;
    }

    public void setUpdatedFullName(String updatedFullName) {
        this.updatedFullName = updatedFullName;
    }

    public String getDetailsTemplateExternalLink() {
        return detailsTemplateExternalLink;
    }

    public void setDetailsTemplateExternalLink(String detailsTemplateExternalLink) {
        this.detailsTemplateExternalLink = detailsTemplateExternalLink;
    }

    public String getEditTemplateExternalLink() {
        return editTemplateExternalLink;
    }

    public void setEditTemplateExternalLink(String editTemplateExternalLink) {
        this.editTemplateExternalLink = editTemplateExternalLink;
    }

    public String getDeleteTemplateExternalLink() {
        return deleteTemplateExternalLink;
    }

    public void setDeleteTemplateExternalLink(String deleteTemplateExternalLink) {
        this.deleteTemplateExternalLink = deleteTemplateExternalLink;
    }
}
