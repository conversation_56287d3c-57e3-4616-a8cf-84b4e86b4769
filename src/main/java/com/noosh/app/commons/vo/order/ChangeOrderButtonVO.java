package com.noosh.app.commons.vo.order;

/**
 * User: leilaz
 * Date: 4/14/20
 */
public class ChangeOrderButtonVO extends OrderButtonVO {
    private static final long serialVersionUID = 6128777478621670002L;

    private String carryOverChangeOrderButton;

    private String acceptAndCarryOverButton;

    private String preAcceptButton;

    public String getCarryOverChangeOrderButton() {
        return carryOverChangeOrderButton;
    }

    public void setCarryOverChangeOrderButton(String carryOverChangeOrderButton) {
        this.carryOverChangeOrderButton = carryOverChangeOrderButton;
    }

    public String getAcceptAndCarryOverButton() {
        return acceptAndCarryOverButton;
    }

    public void setAcceptAndCarryOverButton(String acceptAndCarryOverButton) {
        this.acceptAndCarryOverButton = acceptAndCarryOverButton;
    }

    public String getPreAcceptButton() {
        return preAcceptButton;
    }

    public void setPreAcceptButton(String preAcceptButton) {
        this.preAcceptButton = preAcceptButton;
    }
}
