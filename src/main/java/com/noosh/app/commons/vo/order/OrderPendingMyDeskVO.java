package com.noosh.app.commons.vo.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 4/9/24
 */
public class OrderPendingMyDeskVO implements Serializable {
    private static final long serialVersionUID = 2249461800969153210L;

    private Long orderId;

    private String orderName;

    private String orderExternalUrl;

    private String projectName;

    private Long projectId;

    private String projectExternalUrl;

    private boolean isUserBuyer;

    private boolean isUserSupplier;

    private boolean isChangeOrder;

    private boolean isAggregated;

    private String orderStatusStrId;

    public String getOrderStatusStrId() {
        return orderStatusStrId;
    }

    public void setOrderStatusStrId(String orderStatusStrId) {
        this.orderStatusStrId = orderStatusStrId;
    }

    public boolean getIsAggregated() {
        return isAggregated;
    }

    public void setIsAggregated(boolean aggregated) {
        isAggregated = aggregated;
    }

    public boolean getIsChangeOrder() {
        return isChangeOrder;
    }

    public void setIsChangeOrder(boolean changeOrder) {
        isChangeOrder = changeOrder;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public boolean getIsUserSupplier() {
        return isUserSupplier;
    }

    public void setIsUserSupplier(boolean userSupplier) {
        isUserSupplier = userSupplier;
    }

    public boolean getIsUserBuyer() {
        return isUserBuyer;
    }

    public void setIsUserBuyer(boolean userBuyer) {
        isUserBuyer = userBuyer;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getOrderExternalUrl() {
        return orderExternalUrl;
    }

    public void setOrderExternalUrl(String orderExternalUrl) {
        this.orderExternalUrl = orderExternalUrl;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectExternalUrl() {
        return projectExternalUrl;
    }

    public void setProjectExternalUrl(String projectExternalUrl) {
        this.projectExternalUrl = projectExternalUrl;
    }
}
