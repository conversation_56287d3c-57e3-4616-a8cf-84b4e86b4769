package com.noosh.app.commons.vo.invoice;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.constant.ObjectStateID;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public class InvoiceListInvoiceVO {

    @Schema(description = "invoice id")
    private Long id;
    @Schema(description = "invoice Number")
    private String invoiceNumber;
    @Schema(description = "view Invoice External Link")
    private String viewInvoiceExternalLink;
    @Schema(description = "is Final, Final Invoice should be indicated. ie. 5020378 (Final)")
    private Boolean isFinal;
    @Schema(description = "status")
    private String status;
    @Schema(description = "status str id")
    private String statusStrId;
    @Schema(description = "status name str id")
    private String statusNameStrId;
    @Schema(description = "due Date")
    private LocalDateTime dueDate;
    @Schema(description = "accepted Date")
    private LocalDateTime acceptedDate;
    @Schema(description = "isAccepted, if false, display it in grey.")
    private Boolean isAccepted = false;
    @Schema(description = "is Non Billable")
    private Boolean isNonBillable;
    @Schema(description = "is pending")
    private Boolean isPending;
    @Schema(description = "is approved")
    private Boolean isApproved;
    @Schema(description = "invoice Amount")
    private BigDecimal invoiceAmount;
    @Schema(description = "invoice Amount currency ID")
    private BigDecimal invoiceAmountCurrencyId;
    @Schema(description = "ex invoice Amount")
    private BigDecimal exInvoiceAmount;
    @Schema(description = "ex invoice Amount currency ID")
    private Long exInvoiceAmountCurrencyId;
    @Schema(description = "print External Link")
    private String printExternalLink;
    @Schema(description = "copy External Link")
    private String copyExternalLink;
    @Schema(description = "edit External Link")
    private String editExternalLink;
    @Schema(description = "delete External Link")
    private String deleteExternalLink;
    @Schema(description = "state Id")
    private Long stateId;

    public String getStatusStrId() {
        return statusStrId;
    }

    public void setStatusStrId(String statusStrId) {
        this.statusStrId = statusStrId;
    }

    public Boolean getIsPending() {
        return isPending;
    }

    public void setIsPending(Boolean pending) {
        isPending = pending;
    }

    public Boolean getIsApproved() {
        return isApproved;
    }

    public void setIsApproved(Boolean approved) {
        isApproved = approved;
    }

    public String getStatusNameStrId() {
        return statusNameStrId;
    }

    public void setStatusNameStrId(String statusNameStrId) {
        this.statusNameStrId = statusNameStrId;
    }

    public Boolean getIsAccepted() {
        return isAccepted;
    }

    public void setIsAccepted(Boolean isAccepted) {
        this.isAccepted = isAccepted;
    }

    public Boolean getIsFinal() {
        return isFinal;
    }

    public void setIsFinal(Boolean aFinal) {
        isFinal = aFinal;
    }

    public String getViewInvoiceExternalLink() {
        return viewInvoiceExternalLink;
    }

    public void setViewInvoiceExternalLink(String viewInvoiceExternalLink) {
        this.viewInvoiceExternalLink = viewInvoiceExternalLink;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getAcceptedDate() {
        return acceptedDate;
    }

    public void setAcceptedDate(LocalDateTime acceptedDate) {
        this.acceptedDate = acceptedDate;
    }

    public Boolean getNonBillable() {
        return isNonBillable;
    }

    public void setNonBillable(Boolean nonBillable) {
        isNonBillable = nonBillable;
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getInvoiceAmountCurrencyId() {
        return invoiceAmountCurrencyId;
    }

    public void setInvoiceAmountCurrencyId(BigDecimal invoiceAmountCurrencyId) {
        this.invoiceAmountCurrencyId = invoiceAmountCurrencyId;
    }

    public BigDecimal getExInvoiceAmount() {
        return exInvoiceAmount;
    }

    public void setExInvoiceAmount(BigDecimal exInvoiceAmount) {
        this.exInvoiceAmount = exInvoiceAmount;
    }

    public Long getExInvoiceAmountCurrencyId() {
        return exInvoiceAmountCurrencyId;
    }

    public void setExInvoiceAmountCurrencyId(Long exInvoiceAmountCurrencyId) {
        this.exInvoiceAmountCurrencyId = exInvoiceAmountCurrencyId;
    }

    public String getPrintExternalLink() {
        return printExternalLink;
    }

    public void setPrintExternalLink(String printExternalLink) {
        this.printExternalLink = printExternalLink;
    }

    public String getCopyExternalLink() {
        return copyExternalLink;
    }

    public void setCopyExternalLink(String copyExternalLink) {
        this.copyExternalLink = copyExternalLink;
    }

    public String getEditExternalLink() {
        return editExternalLink;
    }

    public void setEditExternalLink(String editExternalLink) {
        this.editExternalLink = editExternalLink;
    }

    public String getDeleteExternalLink() {
        return deleteExternalLink;
    }

    public void setDeleteExternalLink(String deleteExternalLink) {
        this.deleteExternalLink = deleteExternalLink;
    }

    @JsonIgnore
    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    @JsonIgnore
    public boolean isAccepted() {
        return (this.getStateId() == ObjectStateID.INVOICE_ACCEPTED);
    }
}
