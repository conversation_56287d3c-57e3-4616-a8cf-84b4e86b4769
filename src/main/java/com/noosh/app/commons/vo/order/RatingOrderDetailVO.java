package com.noosh.app.commons.vo.order;

import java.time.LocalDateTime;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/6/19
 */
public class RatingOrderDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long orderId;

    private String orderName;

    private LocalDateTime orderCompletionDate;

    private String supplierName;

    private String supplierWorkgroup;

    private Long supplierWorkgroupId;

    private Long buyerWorkgroupId;

    private double grandTotal;

    private boolean isChangeOrder;

    public boolean getIsChangeOrder() {
        return isChangeOrder;
    }

    public void setIsChangeOrder(boolean changeOrder) {
        isChangeOrder = changeOrder;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public LocalDateTime getOrderCompletionDate() {
        return orderCompletionDate;
    }

    public void setOrderCompletionDate(LocalDateTime orderCompletionDate) {
        this.orderCompletionDate = orderCompletionDate;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getSupplierWorkgroup() {
        return supplierWorkgroup;
    }

    public void setSupplierWorkgroup(String supplierWorkgroup) {
        this.supplierWorkgroup = supplierWorkgroup;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public double getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(double grandTotal) {
        this.grandTotal = grandTotal;
    }
}
