package com.noosh.app.commons.vo.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @auther mario
 * @date 1/14/2020
 */
@Schema(description = "invoice")
public class InvoiceDeskoidInvoiceVO {
    @Schema(description = "invoice Id")
    private Long invoiceId;
    @Schema(description = "status")
    private String status;
    private Long statusId;
    @Schema(description = "is Final")
    private Boolean isFinal;
    @Schema(description = "value")
    private BigDecimal value;
    @Schema(description = "value currency Id")
    private BigDecimal valueCurrencyId;
    @Schema(description = "ex value")
    private BigDecimal exValue;
    @Schema(description = "ex value currency Id")
    private Long exValueCurrencyId;
    @Schema(description = "is Grey Value")
    private Boolean isGreyValue;
    @Schema(description = "due Date")
    private LocalDateTime dueDate;
    @Schema(description = "last Updated Date")
    private LocalDateTime lastUpdatedDate;
    @Schema(description = "invoice External Link")
    private String invoiceExternalLink;
    private Boolean isAccepted = false;
    private Boolean isDualCurrency = false;

    public Boolean getIsAccepted() {
        return isAccepted;
    }

    public void setIsAccepted(Boolean accepted) {
        isAccepted = accepted;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsFinal() {
        return isFinal;
    }

    public void setIsFinal(Boolean aFinal) {
        isFinal = aFinal;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public BigDecimal getExValue() {
        return exValue;
    }

    public void setExValue(BigDecimal exValue) {
        this.exValue = exValue;
    }

    public BigDecimal getValueCurrencyId() {
        return valueCurrencyId;
    }

    public void setValueCurrencyId(BigDecimal valueCurrencyId) {
        this.valueCurrencyId = valueCurrencyId;
    }

    public Long getExValueCurrencyId() {
        return exValueCurrencyId;
    }

    public void setExValueCurrencyId(Long exValueCurrencyId) {
        this.exValueCurrencyId = exValueCurrencyId;
    }

    public Boolean getIsGreyValue() {
        return isGreyValue;
    }

    public void setIsGreyValue(Boolean greyValue) {
        isGreyValue = greyValue;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getInvoiceExternalLink() {
        return invoiceExternalLink;
    }

    public void setInvoiceExternalLink(String invoiceExternalLink) {
        this.invoiceExternalLink = invoiceExternalLink;
    }

    public Long getStatusId() {
        return statusId;
    }

    public void setStatusId(Long statusId) {
        this.statusId = statusId;
    }

    public Boolean getIsDualCurrency() {
        return isDualCurrency;
    }

    public void setIsDualCurrency(Boolean dualCurrency) {
        isDualCurrency = dualCurrency;
    }


}
