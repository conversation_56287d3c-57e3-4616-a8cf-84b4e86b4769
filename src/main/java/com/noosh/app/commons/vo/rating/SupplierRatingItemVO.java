package com.noosh.app.commons.vo.rating;

import com.noosh.app.commons.vo.BaseVO;

/**
 * User: leilaz
 * Date: 1/20/19
 */
public class SupplierRatingItemVO extends BaseVO {
    private String comments;
    private Long grade;
    private Long sectionId;
    private Long questionId;
    private String text;
    private Long weight;
    private Long ordinal;
    private Long point;
    private Long gradeWeight;

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public Long getGradeWeight() {
        return gradeWeight;
    }

    public void setGradeWeight(Long gradeWeight) {
        this.gradeWeight = gradeWeight;
    }

    public Long getPoint() {
        return point;
    }

    public void setPoint(Long point) {
        this.point = point;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getGrade() {
        return grade;
    }

    public void setGrade(Long grade) {
        this.grade = grade;
    }

    public Long getSectionId() {
        return sectionId;
    }

    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Long getWeight() {
        return weight;
    }

    public void setWeight(Long weight) {
        this.weight = weight;
    }

    public Long getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Long ordinal) {
        this.ordinal = ordinal;
    }
}
