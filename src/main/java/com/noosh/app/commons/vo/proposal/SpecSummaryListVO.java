package com.noosh.app.commons.vo.proposal;

import java.util.List;

/**
 * <AUTHOR>
 * @date 11/21/2021
 */
public class SpecSummaryListVO {

    private String createLogoExternalLink;
    private Boolean enableSpecSummaryReg;
    private List<SpecSummaryVO> specSummaryList;

    public Boolean getEnableSpecSummaryReg() {
        return enableSpecSummaryReg;
    }

    public void setEnableSpecSummaryReg(Boolean enableSpecSummaryReg) {
        this.enableSpecSummaryReg = enableSpecSummaryReg;
    }

    public String getCreateLogoExternalLink() {
        return createLogoExternalLink;
    }

    public void setCreateLogoExternalLink(String createLogoExternalLink) {
        this.createLogoExternalLink = createLogoExternalLink;
    }

    public List<SpecSummaryVO> getSpecSummaryList() {
        return specSummaryList;
    }

    public void setSpecSummaryList(List<SpecSummaryVO> specSummaryList) {
        this.specSummaryList = specSummaryList;
    }
}
