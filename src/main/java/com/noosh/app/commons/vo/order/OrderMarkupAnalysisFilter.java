package com.noosh.app.commons.vo.order;

/**
 * User: leilaz
 * Date: 5/18/20
 */
public class OrderMarkupAnalysisFilter {
    // form names
    public final static String CONTROLNAME_INCLUDING_ARCHIVED_JOBS = "includingArchivedJobs";
    public final static String CONTROLNAME_INCLUDING_PENDING = "includePending";
    public final static String CONTROLNAME_DEFAULT_TO_QUOTED_COST = "defaultToQuotedCost";

    public final static String ORDER_MARKUP_ANALYSIS_FILTER_PREF_PREFIX = "REACT_ORDER_MARKUP_ANALYSIS_FILTER_";

    private Integer includePending = 0;
    private Integer includeArchived = 0;
    private Integer defaultToQuotedCost = 0;

    public Integer getIncludePending() {
        return includePending;
    }

    public void setIncludePending(Integer includePending) {
        this.includePending = includePending;
    }

    public Integer getIncludeArchived() {
        return includeArchived;
    }

    public void setIncludeArchived(Integer includeArchived) {
        this.includeArchived = includeArchived;
    }

    public Integer getDefaultToQuotedCost() {
        return defaultToQuotedCost;
    }

    public void setDefaultToQuotedCost(Integer defaultToQuotedCost) {
        this.defaultToQuotedCost = defaultToQuotedCost;
    }
}
