package com.noosh.app.commons.vo.costcenter;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 10/8/20
 */
public class AggregatedOrderCostCenterDetailVO implements Serializable {

    private static final long serialVersionUID = 2450768473376272223L;
    private String editCostCenterExternalLink;

    private AggregatedOrderCostCenterVO itemsTotal;
    private AggregatedOrderCostCenterVO tax;
    private AggregatedOrderCostCenterVO shipping;

    public String getEditCostCenterExternalLink() {
        return editCostCenterExternalLink;
    }

    public void setEditCostCenterExternalLink(String editCostCenterExternalLink) {
        this.editCostCenterExternalLink = editCostCenterExternalLink;
    }

    public AggregatedOrderCostCenterVO getItemsTotal() {
        return itemsTotal;
    }

    public void setItemsTotal(AggregatedOrderCostCenterVO itemsTotal) {
        this.itemsTotal = itemsTotal;
    }

    public AggregatedOrderCostCenterVO getTax() {
        return tax;
    }

    public void setTax(AggregatedOrderCostCenterVO tax) {
        this.tax = tax;
    }

    public AggregatedOrderCostCenterVO getShipping() {
        return shipping;
    }

    public void setShipping(AggregatedOrderCostCenterVO shipping) {
        this.shipping = shipping;
    }
}
