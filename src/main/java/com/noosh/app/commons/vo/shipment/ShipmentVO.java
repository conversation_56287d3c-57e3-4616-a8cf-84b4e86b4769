package com.noosh.app.commons.vo.shipment;

import com.noosh.app.commons.vo.BaseVO;

import java.util.List;

/**
 * User: leilaz
 * Date: 12/1/17
 */
public class ShipmentVO extends BaseVO {
    private static final long serialVersionUID = 9059099686600333279L;
    private Long totalReqQty;
    private Double shipmentDeliveredQty;
    private Double shipmentRecievedQty;

    private List<ShRequestVO> requests;

    public Long getTotalReqQty() {
        return totalReqQty;
    }

    public void setTotalReqQty(Long totalReqQty) {
        this.totalReqQty = totalReqQty;
    }

    public Double getShipmentDeliveredQty() {
        return shipmentDeliveredQty;
    }

    public void setShipmentDeliveredQty(Double shipmentDeliveredQty) {
        this.shipmentDeliveredQty = shipmentDeliveredQty;
    }

    public Double getShipmentRecievedQty() {
        return shipmentRecievedQty;
    }

    public void setShipmentRecievedQty(Double shipmentRecievedQty) {
        this.shipmentRecievedQty = shipmentRecievedQty;
    }

    public List<ShRequestVO> getRequests() {
        return requests;
    }

    public void setRequests(List<ShRequestVO> requests) {
        this.requests = requests;
    }
}
