package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * User: leilaz
 * Date: 11/27/17
 */
@Schema(description = "Terms & Conditions descirption")
public class TermsVO extends BaseVO {
    private static final long serialVersionUID = -2075559727020108233L;
    @Schema(description = "Terms & Conditions text")
    private String text;
    @Schema(description = "Owner workgroup name Terms & Conditions")
    private String ownerWorkgroupName;
    @Schema(description = "Terms type description")
    private String termTypeDescription;
    private String termTypeDescriptionStrId;
    private Boolean isDisplay;

    public String getOwnerWorkgroupName() {
        return ownerWorkgroupName;
    }

    public void setOwnerWorkgroupName(String ownerWorkgroupName) {
        this.ownerWorkgroupName = ownerWorkgroupName;
    }

    public String getTermTypeDescription() {
        return termTypeDescription;
    }

    public void setTermTypeDescription(String termTypeDescription) {
        this.termTypeDescription = termTypeDescription;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTermTypeDescriptionStrId() {
        return termTypeDescriptionStrId;
    }

    public void setTermTypeDescriptionStrId(String termTypeDescriptionStrId) {
        this.termTypeDescriptionStrId = termTypeDescriptionStrId;
    }

    public Boolean getIsDisplay() {
        return isDisplay;
    }

    public void setIsDisplay(Boolean display) {
        isDisplay = display;
    }
}
