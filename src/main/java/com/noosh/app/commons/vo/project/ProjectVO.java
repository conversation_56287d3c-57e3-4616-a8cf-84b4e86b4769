package com.noosh.app.commons.vo.project;

import com.noosh.app.commons.vo.BaseVO;

/**
 * User: leilaz
 * Date: 9/26/17
 */
public class ProjectVO extends BaseVO {
    private boolean isClientProject;
    private boolean isBuyerProject;
    private boolean isSupplierProject;
    private String clientWorkgroup;
    private Long clientWorkgroupId;
    private boolean isClientNotOnNoosh;

    public boolean getIsClientNotOnNoosh() {
        return isClientNotOnNoosh;
    }

    public void setIsClientNotOnNoosh(boolean clientNotOnNoosh) {
        isClientNotOnNoosh = clientNotOnNoosh;
    }

    public String getClientWorkgroup() {
        return clientWorkgroup;
    }

    public void setClientWorkgroup(String clientWorkgroup) {
        this.clientWorkgroup = clientWorkgroup;
    }

    public boolean getIsBuyerProject() {
        return isBuyerProject;
    }

    public void setIsBuyerProject(boolean buyerProject) {
        isBuyerProject = buyerProject;
    }

    public boolean getIsSupplierProject() {
        return this.isSupplierProject;
    }

    public void setIsSupplierProject(boolean supplierProject) {
        this.isSupplierProject = supplierProject;
    }

    public boolean getIsClientProject() {
        return isClientProject;
    }

    public void setIsClientProject(boolean clientProject) {
        isClientProject = clientProject;
    }

    public Long getClientWorkgroupId() {
        return this.clientWorkgroupId;
    }

    public void setClientWorkgroupId(Long clientWorkgroupId) {
        this.clientWorkgroupId = clientWorkgroupId;
    }
}
