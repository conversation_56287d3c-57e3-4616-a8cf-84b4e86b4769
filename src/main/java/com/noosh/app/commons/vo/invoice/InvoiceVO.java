package com.noosh.app.commons.vo.invoice;

import com.noosh.app.commons.vo.BaseVO;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * User: leilaz
 * Date: 4/14/20
 */
public class InvoiceVO extends BaseVO {

    private static final long serialVersionUID = -4181939060192510936L;

    private String ownerReference;

    private String reference;

    private LocalDateTime dueDate;

    private String comments;

    private Boolean isNonBillable;

    private String nonBillableReason;

    private String nonBillableReasonStrId;

    private Boolean sendOnClosedOrder;

    private List<PropertyAttributeVO> customAttr;

    public List<PropertyAttributeVO> getCustomAttr() {
        return customAttr;
    }

    public void setCustomAttr(List<PropertyAttributeVO> customAttr) {
        this.customAttr = customAttr;
    }

    public String getNonBillableReasonStrId() {
        return nonBillableReasonStrId;
    }

    public void setNonBillableReasonStrId(String nonBillableReasonStrId) {
        this.nonBillableReasonStrId = nonBillableReasonStrId;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Boolean getNonBillable() {
        return isNonBillable;
    }

    public void setNonBillable(Boolean nonBillable) {
        isNonBillable = nonBillable;
    }

    public String getNonBillableReason() {
        return nonBillableReason;
    }

    public void setNonBillableReason(String nonBillableReason) {
        this.nonBillableReason = nonBillableReason;
    }

    public Boolean getSendOnClosedOrder() {
        return sendOnClosedOrder;
    }

    public void setSendOnClosedOrder(Boolean sendOnClosedOrder) {
        this.sendOnClosedOrder = sendOnClosedOrder;
    }
}
