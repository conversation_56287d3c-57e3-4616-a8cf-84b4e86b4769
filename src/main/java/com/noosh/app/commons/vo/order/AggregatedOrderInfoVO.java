package com.noosh.app.commons.vo.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 4/9/20
 */
public class AggregatedOrderInfoVO implements Serializable {

    private static final long serialVersionUID = -1231243232059663177L;

    private AggregatedOrderDetailVO orderGeneral;

    private TermsVO buyerTerms;

    private TermsVO supplierTerms;

    private AggregatedOrderButtonVO buttons;

    public AggregatedOrderDetailVO getOrderGeneral() {
        return orderGeneral;
    }

    public void setOrderGeneral(AggregatedOrderDetailVO orderGeneral) {
        this.orderGeneral = orderGeneral;
    }

    public TermsVO getBuyerTerms() {
        return buyerTerms;
    }

    public void setBuyerTerms(TermsVO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public TermsVO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(TermsVO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public AggregatedOrderButtonVO getButtons() {
        return buttons;
    }

    public void setButtons(AggregatedOrderButtonVO buttons) {
        this.buttons = buttons;
    }
}
