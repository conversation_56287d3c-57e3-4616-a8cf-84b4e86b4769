package com.noosh.app.commons.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 8/12/2021
 */
public class ProjectChartItemVO {

    @Schema(description = "Average Type")
    private String avgType;
    @Schema(description = "days")
    private Double days;

    public String getAvgType() {
        return avgType;
    }

    public void setAvgType(String avgType) {
        this.avgType = avgType;
    }

    public Double getDays() {
        return days;
    }

    public void setDays(Double days) {
        this.days = days;
    }
}