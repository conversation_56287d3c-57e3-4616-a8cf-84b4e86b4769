package com.noosh.app.commons.vo;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public class ServerResponse<T> {

    @Schema(description = "The code is used to indicate if the api is successed or failed. " +
            "If api is success, it returns 1, other it returns 2 for error, we may have 3 for other invalid messages",
            example = "1")
    private int code;
    @Schema(description = "The description for code. This is basically used as short error message for Front-end to show error message for a specific ERROR. " +
            "Ex: INVALID_PAGE_SIZE",
            example = "success")
    private String msg;

    @Schema(description = "Data")
    private T data;

    @Schema(description = "Pagination")
    private PageVO page;

    public ServerResponse() {
        super();
    }

    public ServerResponse(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ServerResponse(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public ServerResponse(int code, String msg, T data, PageVO page) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.page = page;
    }

    public static <T> ServerResponse<T> success(){
        return new ServerResponse<T>(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMsg());
    }

    public static <T> ServerResponse<T> success(T data){
        return new ServerResponse<T>(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMsg(), data);
    }

    public static <T> ServerResponse<T> success(T data, PageVO page){
        return new ServerResponse<T>(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMsg(), data, page);
    }

    @Deprecated // use throw new IllegalArgumentException instead of this, will be removed
    public static ServerResponse<String> error(String errMsg){
        return new ServerResponse<>(ResponseCode.ERROR.getCode(), errMsg);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public PageVO getPage() {
        return page;
    }

    public void setPage(PageVO page) {
        this.page = page;
    }
}
