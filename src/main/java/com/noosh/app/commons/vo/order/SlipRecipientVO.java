package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.BaseVO;

/**
 * User: leilaz
 * Date: 12/24/17
 */
public class SlipRecipientVO extends BaseVO {
    private static final long serialVersionUID = 2832274180723074142L;
    private boolean isFontBold;
    private String status;
    private String statusStrId;
    private String orderType;

    public String getStatusStrId() {
        return statusStrId;
    }

    public void setStatusStrId(String statusStrId) {
        this.statusStrId = statusStrId;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public boolean getIsFontBold() {
        return isFontBold;
    }

    public void setIsFontBold(boolean fontBold) {
        isFontBold = fontBold;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
