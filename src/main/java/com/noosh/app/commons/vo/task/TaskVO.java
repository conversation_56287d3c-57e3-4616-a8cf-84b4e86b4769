package com.noosh.app.commons.vo.task;

import com.noosh.app.commons.vo.BaseVO;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * User: leilaz
 * Date: 6/20/17
 */
public class TaskVO extends BaseVO {
    private Long taskTypeId;
    private LocalDateTime receivedDate;
    private LocalDateTime dueDate;
    private LocalDateTime startDate;
    private LocalDateTime actualStartDate;
    private LocalDateTime actualEndDate;
    private Long statusId;
    private Long assigneeUserId;
    private String status;
    private Long workgroupCustomStatusId;
    private String workgroupCustomStatus;
    private Long percentComplete;
    private String description;
    private String comments;
    private String assignee;
    private String priority;
    private boolean isSystemTask;
    private String notificationType;
    private String externalLink;

    public LocalDateTime getActualStartDate() {
        return actualStartDate;
    }

    public void setActualStartDate(LocalDateTime actualStartDate) {
        this.actualStartDate = actualStartDate;
    }

    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public void setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
    }

    public Long getPercentComplete() {
        return percentComplete;
    }

    public void setPercentComplete(Long percentComplete) {
        this.percentComplete = percentComplete;
    }

    public String getWorkgroupCustomStatus() {
        return workgroupCustomStatus;
    }

    public void setWorkgroupCustomStatus(String workgroupCustomStatus) {
        this.workgroupCustomStatus = workgroupCustomStatus;
    }

    public Long getWorkgroupCustomStatusId() {
        return workgroupCustomStatusId;
    }

    public void setWorkgroupCustomStatusId(Long workgroupCustomStatusId) {
        this.workgroupCustomStatusId = workgroupCustomStatusId;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getAssigneeUserId() {
        return assigneeUserId;
    }

    public void setAssigneeUserId(Long assigneeUserId) {
        this.assigneeUserId = assigneeUserId;
    }

    public Long getTaskTypeId() {
        return taskTypeId;
    }

    public void setTaskTypeId(Long taskTypeId) {
        this.taskTypeId = taskTypeId;
    }

    public String getExternalLink() {
        return externalLink;
    }

    public void setExternalLink(String externalLink) {
        this.externalLink = externalLink;
    }

    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    public boolean getIsSystemTask() {
        return isSystemTask;
    }

    public void setIsSystemTask(boolean systemTask) {
        isSystemTask = systemTask;
    }

    public Long getStatusId() {
        return statusId;
    }

    public void setStatusId(Long statusId) {
        this.statusId = statusId;
    }

    public LocalDateTime getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDateTime receivedDate) {
        this.receivedDate = receivedDate;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public static class TimeCardVO implements Serializable {
        private static final long serialVersionUID = 4749727214012468153L;
        private String hours;
        private String recordDate;
        private Long ownerUserId;
        private Long ownerPersonId;
        private String ownerName;

        public TimeCardVO(String hours, String recordDate, Long ownerUserId, Long ownerPersonId, String ownerName) {
            this.hours = hours;
            this.recordDate = recordDate;
            this.ownerUserId = ownerUserId;
            this.ownerPersonId = ownerPersonId;
            this.ownerName = ownerName;
        }

        public TimeCardVO(String hours, String date) {
            this.hours = hours;
            this.recordDate = date;
        }

        public String getHours() {
            return hours;
        }

        public void setHours(String hours) {
            this.hours = hours;
        }

        public String getRecordDate() {
            return recordDate;
        }

        public void setRecordDate(String recordDate) {
            this.recordDate = recordDate;
        }

        public Long getOwnerUserId() {
            return ownerUserId;
        }

        public void setOwnerUserId(Long ownerUserId) {
            this.ownerUserId = ownerUserId;
        }

        public Long getOwnerPersonId() {
            return ownerPersonId;
        }

        public void setOwnerPersonId(Long ownerPersonId) {
            this.ownerPersonId = ownerPersonId;
        }

        public String getOwnerName() {
            return ownerName;
        }

        public void setOwnerName(String ownerName) {
            this.ownerName = ownerName;
        }
    }

    public TimeCardVO generateTimeCard(String hours, String date) {
        TimeCardVO timeCard = new TimeCardVO(hours, date);
        return timeCard;
    }

    public TimeCardVO generateTimeCard(String hours, String date, Long ownerUserId, Long ownerPersonId, String ownerName) {
        TimeCardVO timeCard = new TimeCardVO(hours, date, ownerUserId, ownerPersonId, ownerName);
        return timeCard;
    }
}
