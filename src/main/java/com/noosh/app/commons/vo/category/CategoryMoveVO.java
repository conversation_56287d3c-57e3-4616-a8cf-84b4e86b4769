package com.noosh.app.commons.vo.category;

/**
 * <AUTHOR>
 * @date 11/4/2021
 */
public class CategoryMoveVO {

    private Long categoryId;
    private boolean isMoveDown;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public boolean getIsMoveDown() {
        return isMoveDown;
    }

    public void setIsMoveDown(boolean moveDown) {
        isMoveDown = moveDown;
    }
}
