package com.noosh.app.commons.vo.proposal;

/**
 * <AUTHOR>
 * @date 11/21/2021
 */
public class SpecSummaryVO {

    private Long specSummaryId;
    private String name;
    private String iconHref;
    private String specTypeStr;
    private String specTypeStrId;
    private String specType;
    private boolean isDefault;
    private String editSpecSummaryExternalLink;
    private String deleteSpecSummaryExternalLink;

    public String getSpecTypeStr() {
        return specTypeStr;
    }

    public void setSpecTypeStr(String specTypeStr) {
        this.specTypeStr = specTypeStr;
    }

    public String getSpecTypeStrId() {
        return specTypeStrId;
    }

    public void setSpecTypeStrId(String specTypeStrId) {
        this.specTypeStrId = specTypeStrId;
    }

    public Long getSpecSummaryId() {
        return specSummaryId;
    }

    public void setSpecSummaryId(Long specSummaryId) {
        this.specSummaryId = specSummaryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIconHref() {
        return iconHref;
    }

    public void setIconHref(String iconHref) {
        this.iconHref = iconHref;
    }

    public String getSpecType() {
        return specType;
    }

    public void setSpecType(String specType) {
        this.specType = specType;
    }

    public boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }

    public String getEditSpecSummaryExternalLink() {
        return editSpecSummaryExternalLink;
    }

    public void setEditSpecSummaryExternalLink(String editSpecSummaryExternalLink) {
        this.editSpecSummaryExternalLink = editSpecSummaryExternalLink;
    }

    public String getDeleteSpecSummaryExternalLink() {
        return deleteSpecSummaryExternalLink;
    }

    public void setDeleteSpecSummaryExternalLink(String deleteSpecSummaryExternalLink) {
        this.deleteSpecSummaryExternalLink = deleteSpecSummaryExternalLink;
    }
}
