package com.noosh.app.commons.vo.invoice;

import com.noosh.app.commons.vo.account.SupplierFlagVO;
import com.noosh.app.commons.vo.rating.SupplierScoreVO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

/**
 * @auther mario
 * @date 1/15/2020
 */
@Schema(description = "order")
public class InvoiceDeskoidOrderVO {
    @Schema(description = "order Id")
    private Long orderId;
    @Schema(description = "order Name")
    private String orderName;
    @Schema(description = "order Type")
    private String orderType;
    @Schema(description = "status")
    private String status;
    private Long statusId;
    @Schema(description = "client")
    private String client;
    @Schema(description = "supplier")
    private String supplier;
    @Schema(description = "supplierFlag")
    private SupplierFlagVO supplierFlag;
    @Schema(description = "supplierScore")
    private SupplierScoreVO supplierScore;
    @Schema(description = "value")
    private Double value;
    @Schema(description = "value currency Id")
    private Long valueCurrencyId;
    @Schema(description = "ex value")
    private Double exValue;
    @Schema(description = "ex value currency Id")
    private Long exValueCurrencyId;
    @Schema(description = "total Invoiced Amount")
    private BigDecimal totalInvoicedAmount;
    @Schema(description = "total Invoiced Amount Currency Id")
    private Long totalInvoicedAmountCurrencyId;
    @Schema(description = "ex total Invoiced Amount")
    private BigDecimal exTotalInvoicedAmount;
    @Schema(description = "ex total Invoiced Amount Currency Id")
    private Long exTotalInvoicedAmountCurrencyId;
    @Schema(description = "order External Link")
    private String orderExternalLink;
    @Schema(description = "invoice list")
    private List<InvoiceDeskoidInvoiceVO> invoices;
    private Boolean isDualCurrency = false;

    public SupplierScoreVO getSupplierScore() {
        return supplierScore;
    }

    public void setSupplierScore(SupplierScoreVO supplierScore) {
        this.supplierScore = supplierScore;
    }

    public SupplierFlagVO getSupplierFlag() {
        return supplierFlag;
    }

    public void setSupplierFlag(SupplierFlagVO supplierFlag) {
        this.supplierFlag = supplierFlag;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public BigDecimal getTotalInvoicedAmount() {
        return totalInvoicedAmount;
    }

    public void setTotalInvoicedAmount(BigDecimal totalInvoicedAmount) {
        this.totalInvoicedAmount = totalInvoicedAmount;
    }

    public Double getExValue() {
        return exValue;
    }

    public void setExValue(Double exValue) {
        this.exValue = exValue;
    }

    public BigDecimal getExTotalInvoicedAmount() {
        return exTotalInvoicedAmount;
    }

    public void setExTotalInvoicedAmount(BigDecimal exTotalInvoicedAmount) {
        this.exTotalInvoicedAmount = exTotalInvoicedAmount;
    }

    public Long getValueCurrencyId() {
        return valueCurrencyId;
    }

    public void setValueCurrencyId(Long valueCurrencyId) {
        this.valueCurrencyId = valueCurrencyId;
    }

    public Long getExValueCurrencyId() {
        return exValueCurrencyId;
    }

    public void setExValueCurrencyId(Long exValueCurrencyId) {
        this.exValueCurrencyId = exValueCurrencyId;
    }

    public Long getTotalInvoicedAmountCurrencyId() {
        return totalInvoicedAmountCurrencyId;
    }

    public void setTotalInvoicedAmountCurrencyId(Long totalInvoicedAmountCurrencyId) {
        this.totalInvoicedAmountCurrencyId = totalInvoicedAmountCurrencyId;
    }

    public Long getExTotalInvoicedAmountCurrencyId() {
        return exTotalInvoicedAmountCurrencyId;
    }

    public void setExTotalInvoicedAmountCurrencyId(Long exTotalInvoicedAmountCurrencyId) {
        this.exTotalInvoicedAmountCurrencyId = exTotalInvoicedAmountCurrencyId;
    }

    public String getOrderExternalLink() {
        return orderExternalLink;
    }

    public void setOrderExternalLink(String orderExternalLink) {
        this.orderExternalLink = orderExternalLink;
    }

    public List<InvoiceDeskoidInvoiceVO> getInvoices() {
        return invoices;
    }

    public void setInvoices(List<InvoiceDeskoidInvoiceVO> invoices) {
        this.invoices = invoices;
    }

    public Long getStatusId() {
        return statusId;
    }

    public void setStatusId(Long statusStrId) {
        this.statusId = statusStrId;
    }

    public Boolean getIsDualCurrency() {
        return isDualCurrency;
    }

    public void setIsDualCurrency(Boolean dualCurrency) {
        isDualCurrency = dualCurrency;
    }

}
