package com.noosh.app.commons.vo.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 3/29/20
 */
public class OrderInfoVO implements Serializable {
    private static final long serialVersionUID = -2063996404591233801L;

    private OrderGeneralInfoVO orderGeneral;

    private TermsVO buyerTerms;

    private TermsVO supplierTerms;

    private OrderButtonVO buttons;

    public TermsVO getBuyerTerms() {
        return buyerTerms;
    }

    public void setBuyerTerms(TermsVO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public TermsVO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(TermsVO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public OrderGeneralInfoVO getOrderGeneral() {
        return orderGeneral;
    }

    public void setOrderGeneral(OrderGeneralInfoVO orderGeneral) {
        this.orderGeneral = orderGeneral;
    }

    public OrderButtonVO getButtons() {
        return buttons;
    }

    public void setButtons(OrderButtonVO buttons) {
        this.buttons = buttons;
    }
}
