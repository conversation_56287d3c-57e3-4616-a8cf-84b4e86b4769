package com.noosh.app.commons.vo.proposal;

import com.noosh.app.commons.vo.component.DropdownVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 12/10/2021
 */
public class ProposalTemplateOptionVO {

    private String companyName;
    private String line1;
    private String line2;
    private String line3;
    private String city;
    private String state;
    private String postalCode;
    private Long countryStrId;
    private Long countryId;
    private List<DropdownVO<Long>> logos;
    private List<DropdownVO<Long>> countrys;
    private List<DropdownVO<String>> layouts;
    private Boolean specSummaryRegEnabled;

    public Boolean getSpecSummaryRegEnabled() {
        return specSummaryRegEnabled;
    }

    public void setSpecSummaryRegEnabled(Boolean specSummaryRegEnabled) {
        this.specSummaryRegEnabled = specSummaryRegEnabled;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getLine1() {
        return line1;
    }

    public void setLine1(String line1) {
        this.line1 = line1;
    }

    public String getLine2() {
        return line2;
    }

    public void setLine2(String line2) {
        this.line2 = line2;
    }

    public String getLine3() {
        return line3;
    }

    public void setLine3(String line3) {
        this.line3 = line3;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public Long getCountryStrId() {
        return countryStrId;
    }

    public void setCountryStrId(Long countryStrId) {
        this.countryStrId = countryStrId;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public List<DropdownVO<Long>> getLogos() {
        return logos;
    }

    public void setLogos(List<DropdownVO<Long>> logos) {
        this.logos = logos;
    }

    public List<DropdownVO<Long>> getCountrys() {
        return countrys;
    }

    public void setCountrys(List<DropdownVO<Long>> countrys) {
        this.countrys = countrys;
    }

    public List<DropdownVO<String>> getLayouts() {
        return layouts;
    }

    public void setLayouts(List<DropdownVO<String>> layouts) {
        this.layouts = layouts;
    }
}
