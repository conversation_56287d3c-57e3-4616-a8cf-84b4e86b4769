package com.noosh.app.commons.vo.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 3/27/24
 */
public class OrderSupplierMyDeskVO implements Serializable {
    private static final long serialVersionUID = -8660598596767878063L;

    private Long orderId;

    private String orderName;

    private String orderExternalUrl;

    private String projectName;

    private String projectExternalUrl;

    private boolean isChangeOrder;

    private boolean isUserBuyer;

    private boolean isUserSupplier;

    private double amount;

    private Long projectId;

    private Long orderStateId;

    private boolean isAggregated;

    public boolean getIsAggregated() {
        return isAggregated;
    }

    public void setIsAggregated(boolean aggregated) {
        isAggregated = aggregated;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getOrderStateId() {
        return orderStateId;
    }

    public void setOrderStateId(Long orderStateId) {
        this.orderStateId = orderStateId;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public boolean getIsUserSupplier() {
        return isUserSupplier;
    }

    public void setIsUserSupplier(boolean userSupplier) {
        isUserSupplier = userSupplier;
    }

    public boolean getIsUserBuyer() {
        return isUserBuyer;
    }

    public void setIsUserBuyer(boolean userBuyer) {
        isUserBuyer = userBuyer;
    }

    public boolean getIsChangeOrder() {
        return isChangeOrder;
    }

    public void setIsChangeOrder(boolean changeOrder) {
        isChangeOrder = changeOrder;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public String getOrderExternalUrl() {
        return orderExternalUrl;
    }

    public void setOrderExternalUrl(String orderExternalUrl) {
        this.orderExternalUrl = orderExternalUrl;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectExternalUrl() {
        return projectExternalUrl;
    }

    public void setProjectExternalUrl(String projectExternalUrl) {
        this.projectExternalUrl = projectExternalUrl;
    }
}
