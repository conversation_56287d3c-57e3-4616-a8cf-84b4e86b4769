package com.noosh.app.commons.vo.workgroup;

import com.noosh.app.commons.vo.BaseVO;
import com.noosh.app.commons.vo.account.SupplierFlagVO;
import com.noosh.app.commons.vo.rating.SupplierScoreVO;

/**
 * User: leilaz
 * Date: 9/26/17
 */
public class WorkgroupVO extends BaseVO {
    private String city;

    private String postalCode;

    private String state;

    private String line1;

    private String line2;

    private String line3;

    private String country;

    private String countryStrId;

    private String logo;

    private String email;

    private Long decimalPlaces;

    private SupplierFlagVO supplierFlag;

    private SupplierScoreVO supplierScore;

    public String getCountryStrId() {
        return countryStrId;
    }

    public void setCountryStrId(String countryStrId) {
        this.countryStrId = countryStrId;
    }

    public Long getDecimalPlaces() {
        return decimalPlaces;
    }

    public void setDecimalPlaces(Long decimalPlaces) {
        this.decimalPlaces = decimalPlaces;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getLine1() {
        return line1;
    }

    public void setLine1(String line1) {
        this.line1 = line1;
    }

    public String getLine2() {
        return line2;
    }

    public void setLine2(String line2) {
        this.line2 = line2;
    }

    public String getLine3() {
        return line3;
    }

    public void setLine3(String line3) {
        this.line3 = line3;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public SupplierFlagVO getSupplierFlag() {
        return supplierFlag;
    }

    public void setSupplierFlag(SupplierFlagVO supplierFlag) {
        this.supplierFlag = supplierFlag;
    }

    public SupplierScoreVO getSupplierScore() {
        return supplierScore;
    }

    public void setSupplierScore(SupplierScoreVO supplierScore) {
        this.supplierScore = supplierScore;
    }
}
