package com.noosh.app.commons.vo.uofm;

import com.noosh.app.commons.vo.BaseVO;

/**
 * User: leilaz
 * Date: 12/5/17
 */
public class UofmVO extends BaseVO {
    private boolean isConvertible;
    private String pluralDescription;
    private Long conversionFactor;
    private String description;
    private String pluralDescStrId;
    private String descriptionStrId;

    public String getPluralDescStrId() {
        return pluralDescStrId;
    }

    public void setPluralDescStrId(Long pluralDescStrId) {
        this.pluralDescStrId = pluralDescStrId != null ? String.valueOf(pluralDescStrId) : null;
    }

    public String getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId != null ? String.valueOf(descriptionStrId) : null;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getConversionFactor() {
        return conversionFactor;
    }

    public void setConversionFactor(Long conversionFactor) {
        this.conversionFactor = conversionFactor;
    }

    public boolean getIsConvertible() {
        return isConvertible;
    }

    public void setIsConvertible(boolean convertible) {
        isConvertible = convertible;
    }

    public String getPluralDescription() {
        return pluralDescription;
    }

    public void setPluralDescription(String pluralDescription) {
        this.pluralDescription = pluralDescription;
    }
}
