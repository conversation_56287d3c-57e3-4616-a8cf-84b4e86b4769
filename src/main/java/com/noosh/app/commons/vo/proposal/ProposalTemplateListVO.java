package com.noosh.app.commons.vo.proposal;

import java.util.List;

/**
 * <AUTHOR>
 * @date 10/31/2021
 */
public class ProposalTemplateListVO {

    private String createLogoExternalLink;
    private String backExternalLink;
    private List<ProposalTemplateVO> templates;

    public String getCreateLogoExternalLink() {
        return createLogoExternalLink;
    }

    public void setCreateLogoExternalLink(String createLogoExternalLink) {
        this.createLogoExternalLink = createLogoExternalLink;
    }

    public String getBackExternalLink() {
        return backExternalLink;
    }

    public void setBackExternalLink(String backExternalLink) {
        this.backExternalLink = backExternalLink;
    }

    public List<ProposalTemplateVO> getTemplates() {
        return templates;
    }

    public void setTemplates(List<ProposalTemplateVO> templates) {
        this.templates = templates;
    }
}
