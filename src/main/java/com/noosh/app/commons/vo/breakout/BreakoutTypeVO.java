package com.noosh.app.commons.vo.breakout;

import com.noosh.app.commons.vo.BaseVO;

import java.util.ArrayList;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/5/17
 */
public class BreakoutTypeVO extends BaseVO {
    private static final long serialVersionUID = 57773270317794903L;
    private boolean isQuantity;
    private boolean isIncluded;
    private String pricePer;
    private Integer level;
    private String code;
    private Boolean isRequired;
    private List<BreakoutTypeVO> descendents;

    public String getPricePer() {
        return pricePer;
    }

    public void setPricePer(String pricePer) {
        this.pricePer = pricePer;
    }

    public boolean getIsQuantity() {
        return isQuantity;
    }

    public void setIsQuantity(boolean quantity) {
        isQuantity = quantity;
    }

    public boolean getIsIncluded() {
        return isIncluded;
    }

    public void setIsIncluded(boolean included) {
        isIncluded = included;
    }

    public Integer getLevel() {
        return this.level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<BreakoutTypeVO> getDescendents() {
        return this.descendents;
    }

    public void setDescendents(List<BreakoutTypeVO> descendents) {
        this.descendents = descendents;
    }

    public void addToDescendents(BreakoutTypeVO breakoutTypeVO){
        if (this.descendents == null)
        {
            this.descendents = new ArrayList<>();
        }
        this.descendents.add(breakoutTypeVO);
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getIsRequired() {
        return this.isRequired;
    }

    public void setIsRequired(Boolean required) {
        this.isRequired = required;
    }
}
