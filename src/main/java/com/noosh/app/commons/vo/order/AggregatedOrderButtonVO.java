package com.noosh.app.commons.vo.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 4/9/20
 */
public class AggregatedOrderButtonVO extends OrderButtonVO {

    private static final long serialVersionUID = 7327686475318172842L;

    private String createExternalOrderButton;

    private String carryOverChangeOrderButton;

    private String acceptAndCarryOverButton;

    public String getCreateExternalOrderButton() {
        return createExternalOrderButton;
    }

    public void setCreateExternalOrderButton(String createExternalOrderButton) {
        this.createExternalOrderButton = createExternalOrderButton;
    }

    public String getCarryOverChangeOrderButton() {
        return carryOverChangeOrderButton;
    }

    public void setCarryOverChangeOrderButton(String carryOverChangeOrderButton) {
        this.carryOverChangeOrderButton = carryOverChangeOrderButton;
    }

    public String getAcceptAndCarryOverButton() {
        return acceptAndCarryOverButton;
    }

    public void setAcceptAndCarryOverButton(String acceptAndCarryOverButton) {
        this.acceptAndCarryOverButton = acceptAndCarryOverButton;
    }
}
