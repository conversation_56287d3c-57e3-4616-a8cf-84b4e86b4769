package com.noosh.app.commons.vo.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @auther mario
 * @date 1/14/2020
 */
@Schema(description = "Invoice")
public class InvoiceDeskoidVO {
    @Schema(description = "can View Invoice")
    private Boolean canViewInvoice;
    @Schema(description = "order list")
    private List<InvoiceDeskoidOrderVO> invoiceOrders;
    @Schema(description = "create Invoice External Link")
    private String createInvoiceExternalLink;
    private Long total = 0L;

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public String getCreateInvoiceExternalLink() {
        return createInvoiceExternalLink;
    }

    public void setCreateInvoiceExternalLink(String createInvoiceExternalLink) {
        this.createInvoiceExternalLink = createInvoiceExternalLink;
    }

    public Boolean getCanViewInvoice() {
        return canViewInvoice;
    }

    public void setCanViewInvoice(Boolean canViewInvoice) {
        this.canViewInvoice = canViewInvoice;
    }

    public List<InvoiceDeskoidOrderVO> getInvoiceOrders() {
        return invoiceOrders;
    }

    public void setInvoiceOrders(List<InvoiceDeskoidOrderVO> invoiceOrders) {
        this.invoiceOrders = invoiceOrders;
    }
}
