package com.noosh.app.commons.vo.costcenter;

import com.noosh.app.commons.vo.property.PropertyAttributeVO;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 9/14/20
 */
public class CostCenterAllocationVO implements Serializable {
    private static final long serialVersionUID = -5816261047967523618L;

    private Long costCenterAllocationId;

    private String costCenterName;

    private String costCenterDesc;

    private Double percent;

    private Double amount;

    private Double invoiceAmount;

    private Long costCenterId;

    private List<PropertyAttributeVO> customFields;

    public Double getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(Double invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public String getCostCenterDesc() {
        return costCenterDesc;
    }

    public void setCostCenterDesc(String costCenterDesc) {
        this.costCenterDesc = costCenterDesc;
    }

    public Long getCostCenterAllocationId() {
        return costCenterAllocationId;
    }

    public void setCostCenterAllocationId(Long costCenterAllocationId) {
        this.costCenterAllocationId = costCenterAllocationId;
    }

    public String getCostCenterName() {
        return costCenterName;
    }

    public void setCostCenterName(String costCenterName) {
        this.costCenterName = costCenterName;
    }

    public Double getPercent() {
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public List<PropertyAttributeVO> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<PropertyAttributeVO> customFields) {
        this.customFields = customFields;
    }
}
