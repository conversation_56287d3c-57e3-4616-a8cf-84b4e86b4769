package com.noosh.app.commons.vo.custom;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 6/15/22
 */
public class CustomFieldVO implements Serializable {
    private static final long serialVersionUID = -3808932323090456289L;

    private Long id;

    private String label;

    private String paramName;

    private Boolean isRequired;

    private String[] fieldValues;

    private String size;

    private String maxLength;

    private String rows;

    private String cols;

    private Long customFieldControlId;

    public String getRows() {
        return rows;
    }

    public void setRows(String rows) {
        this.rows = rows;
    }

    public String getCols() {
        return cols;
    }

    public void setCols(String cols) {
        this.cols = cols;
    }

    public Long getCustomFieldControlId() {
        return customFieldControlId;
    }

    public void setCustomFieldControlId(Long customFieldControlId) {
        this.customFieldControlId = customFieldControlId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean required) {
        isRequired = required;
    }

    public String[] getFieldValues() {
        return fieldValues;
    }

    public void setFieldValues(String[] fieldValues) {
        this.fieldValues = fieldValues;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(String maxLength) {
        this.maxLength = maxLength;
    }
}
