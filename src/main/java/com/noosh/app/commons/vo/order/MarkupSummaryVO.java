package com.noosh.app.commons.vo.order;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 5/11/20
 */
public class MarkupSummaryVO implements Serializable {

    private static final long serialVersionUID = 1152645491614547801L;

    private Double accepted;

    private Double pending;

    private Double total;

    private Double acceptedPercent;

    private Double pendingPercent;

    private Double totalPercent;

    public Double getAccepted() {
        return accepted;
    }

    public void setAccepted(Double accepted) {
        this.accepted = accepted;
    }

    public Double getPending() {
        return pending;
    }

    public void setPending(Double pending) {
        this.pending = pending;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public Double getAcceptedPercent() {
        return acceptedPercent;
    }

    public void setAcceptedPercent(Double acceptedPercent) {
        this.acceptedPercent = acceptedPercent;
    }

    public Double getPendingPercent() {
        return pendingPercent;
    }

    public void setPendingPercent(Double pendingPercent) {
        this.pendingPercent = pendingPercent;
    }

    public Double getTotalPercent() {
        return totalPercent;
    }

    public void setTotalPercent(Double totalPercent) {
        this.totalPercent = totalPercent;
    }
}
