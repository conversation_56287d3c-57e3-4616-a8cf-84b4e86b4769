package com.noosh.app.commons.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * User: leilaz
 * Date: 2/22/22
 */
public class ProjectInvoiceVO {
    @Schema(description = "Project Name")
    private String projectName;
    @Schema(description = "Project Create Date")
    private LocalDateTime projectCreateDate;
    @Schema(description = "Invoice Create Date")
    private LocalDateTime invoiceCreateDate;
    @Schema(description = "Invoice Accept Date")
    private LocalDateTime invoiceAcceptDate;
    @Schema(description = "Invoice Ref")
    private String invoiceRef;

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public LocalDateTime getProjectCreateDate() {
        return projectCreateDate;
    }

    public void setProjectCreateDate(LocalDateTime projectCreateDate) {
        this.projectCreateDate = projectCreateDate;
    }

    public String getInvoiceRef() {
        return invoiceRef;
    }

    public void setInvoiceRef(String invoiceRef) {
        this.invoiceRef = invoiceRef;
    }

    public LocalDateTime getInvoiceCreateDate() {
        return invoiceCreateDate;
    }

    public void setInvoiceCreateDate(LocalDateTime invoiceCreateDate) {
        this.invoiceCreateDate = invoiceCreateDate;
    }

    public LocalDateTime getInvoiceAcceptDate() {
        return invoiceAcceptDate;
    }

    public void setInvoiceAcceptDate(LocalDateTime invoiceAcceptDate) {
        this.invoiceAcceptDate = invoiceAcceptDate;
    }
}