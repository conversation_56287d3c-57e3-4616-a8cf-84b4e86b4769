package com.noosh.app.commons.vo.proposal;

/**
 * <AUTHOR>
 * @date 11/25/2021
 */
public class ProposalTemplateBodyVO {

    private Long templateId;
    private String templateName;
    private boolean isActive;
    private boolean isDefault;
    private Long logoId;
    // logo position : 1-Top Right, 2-Top Middle, 3-Top Left
    private Long logoPosition;
    // 1-Default, 2-Customized, 3-Workgroup Color
    private Long brandingColorsType;
    private String customizedColors;
    private String companyName;
    private Long companyAddressId;
    private String line1;
    private String line2;
    private String line3;
    private String city;
    private String state;
    private String postal;
    private Long countryId;
    private String companyPhone;
    private String companyFax;
    private String companyUrl;
    private boolean includePageNumber;
    private boolean isLandscape;
    private boolean includeCoverPage;
    private boolean includeCoverLetter;
    private String introText;
    private String conclusionText;
    private String closingText;
    private boolean includeTermsAndConditions;
    private boolean includeProposalNote;
    private String proposalNote;
    private boolean includeSignaturePage;
    private boolean useSpecSummary;
    private boolean isSpecSummaryCompact;
    private boolean useSpecSummaryBorder;
    private int specSummaryColumns;
    private String quantityLayout;
    private boolean includePriceBreakouts;
    private boolean isMarkupVisible;
    private boolean isSeparateSpecPricing;
    private Boolean sumUpAllQuotedQuantity1;

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(boolean active) {
        isActive = active;
    }

    public boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public Long getLogoId() {
        return logoId;
    }

    public void setLogoId(Long logoId) {
        this.logoId = logoId;
    }

    public Long getLogoPosition() {
        return logoPosition;
    }

    public void setLogoPosition(Long logoPosition) {
        this.logoPosition = logoPosition;
    }

    public Long getBrandingColorsType() {
        return brandingColorsType;
    }

    public void setBrandingColorsType(Long brandingColorsType) {
        this.brandingColorsType = brandingColorsType;
    }

    public String getCustomizedColors() {
        return customizedColors;
    }

    public void setCustomizedColors(String customizedColors) {
        this.customizedColors = customizedColors;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getCompanyAddressId() {
        return companyAddressId;
    }

    public void setCompanyAddressId(Long companyAddressId) {
        this.companyAddressId = companyAddressId;
    }

    public String getLine1() {
        return line1;
    }

    public void setLine1(String line1) {
        this.line1 = line1;
    }

    public String getLine2() {
        return line2;
    }

    public void setLine2(String line2) {
        this.line2 = line2;
    }

    public String getLine3() {
        return line3;
    }

    public void setLine3(String line3) {
        this.line3 = line3;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPostal() {
        return postal;
    }

    public void setPostal(String postal) {
        this.postal = postal;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public String getCompanyFax() {
        return companyFax;
    }

    public void setCompanyFax(String companyFax) {
        this.companyFax = companyFax;
    }

    public String getCompanyUrl() {
        return companyUrl;
    }

    public void setCompanyUrl(String companyUrl) {
        this.companyUrl = companyUrl;
    }

    public boolean isIncludePageNumber() {
        return includePageNumber;
    }

    public void setIncludePageNumber(boolean includePageNumber) {
        this.includePageNumber = includePageNumber;
    }

    public boolean getIsLandscape() {
        return isLandscape;
    }

    public void setIsLandscape(boolean landscape) {
        isLandscape = landscape;
    }

    public boolean isIncludeCoverPage() {
        return includeCoverPage;
    }

    public void setIncludeCoverPage(boolean includeCoverPage) {
        this.includeCoverPage = includeCoverPage;
    }

    public boolean isIncludeCoverLetter() {
        return includeCoverLetter;
    }

    public void setIncludeCoverLetter(boolean includeCoverLetter) {
        this.includeCoverLetter = includeCoverLetter;
    }

    public String getIntroText() {
        return introText;
    }

    public void setIntroText(String introText) {
        this.introText = introText;
    }

    public String getConclusionText() {
        return conclusionText;
    }

    public void setConclusionText(String conclusionText) {
        this.conclusionText = conclusionText;
    }

    public String getClosingText() {
        return closingText;
    }

    public void setClosingText(String closingText) {
        this.closingText = closingText;
    }

    public boolean isIncludeTermsAndConditions() {
        return includeTermsAndConditions;
    }

    public void setIncludeTermsAndConditions(boolean includeTermsAndConditions) {
        this.includeTermsAndConditions = includeTermsAndConditions;
    }

    public boolean isIncludeProposalNote() {
        return includeProposalNote;
    }

    public void setIncludeProposalNote(boolean includeProposalNote) {
        this.includeProposalNote = includeProposalNote;
    }

    public String getProposalNote() {
        return proposalNote;
    }

    public void setProposalNote(String proposalNote) {
        this.proposalNote = proposalNote;
    }

    public boolean isIncludeSignaturePage() {
        return includeSignaturePage;
    }

    public void setIncludeSignaturePage(boolean includeSignaturePage) {
        this.includeSignaturePage = includeSignaturePage;
    }

    public boolean isUseSpecSummary() {
        return useSpecSummary;
    }

    public void setUseSpecSummary(boolean useSpecSummary) {
        this.useSpecSummary = useSpecSummary;
    }

    public boolean getIsSpecSummaryCompact() {
        return isSpecSummaryCompact;
    }

    public void setIsSpecSummaryCompact(boolean specSummaryCompact) {
        isSpecSummaryCompact = specSummaryCompact;
    }

    public boolean isUseSpecSummaryBorder() {
        return useSpecSummaryBorder;
    }

    public void setUseSpecSummaryBorder(boolean useSpecSummaryBorder) {
        this.useSpecSummaryBorder = useSpecSummaryBorder;
    }

    public int getSpecSummaryColumns() {
        return specSummaryColumns;
    }

    public void setSpecSummaryColumns(int specSummaryColumns) {
        this.specSummaryColumns = specSummaryColumns;
    }

    public String getQuantityLayout() {
        return quantityLayout;
    }

    public void setQuantityLayout(String quantityLayout) {
        this.quantityLayout = quantityLayout;
    }

    public boolean isIncludePriceBreakouts() {
        return includePriceBreakouts;
    }

    public void setIncludePriceBreakouts(boolean includePriceBreakouts) {
        this.includePriceBreakouts = includePriceBreakouts;
    }

    public boolean getIsMarkupVisible() {
        return isMarkupVisible;
    }

    public void setIsMarkupVisible(boolean markupVisible) {
        isMarkupVisible = markupVisible;
    }

    public boolean getIsSeparateSpecPricing() {
        return isSeparateSpecPricing;
    }

    public void setIsSeparateSpecPricing(boolean separateSpecPricing) {
        isSeparateSpecPricing = separateSpecPricing;
    }

    public Boolean isSumUpAllQuotedQuantity1() {
        return sumUpAllQuotedQuantity1;
    }

    public void setSumUpAllQuotedQuantity1(Boolean sumUpAllQuotedQuantity1) {
        this.sumUpAllQuotedQuantity1 = sumUpAllQuotedQuantity1;
    }
}
