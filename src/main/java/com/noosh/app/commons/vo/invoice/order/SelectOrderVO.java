package com.noosh.app.commons.vo.invoice.order;

import java.util.List;

/**
 * <AUTHOR>
 * @date 1/10/2022
 */
public class SelectOrderVO {

    private boolean canCreateInvoice;
    private List<OrderVO> orders;

    public boolean isCanCreateInvoice() {
        return canCreateInvoice;
    }

    public void setCanCreateInvoice(boolean canCreateInvoice) {
        this.canCreateInvoice = canCreateInvoice;
    }

    public List<OrderVO> getOrders() {
        return orders;
    }

    public void setOrders(List<OrderVO> orders) {
        this.orders = orders;
    }
}
