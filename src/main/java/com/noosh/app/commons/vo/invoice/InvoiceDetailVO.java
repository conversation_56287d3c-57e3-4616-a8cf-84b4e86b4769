package com.noosh.app.commons.vo.invoice;

import com.noosh.app.commons.vo.order.TermsVO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther mario
 * @date 7/22/2020
 */
@Schema(description = "Invoice detail")
public class InvoiceDetailVO {
    @Schema(description = "Project name")
    private String projectName;
    @Schema(description = "Project link, redirect to Enterprise")
    private String projectExternalUrl;
    @Schema(description = "Is final")
    private Boolean isFinal;
    @Schema(description = "Is non-billable")
    private Boolean isNonBillable;
    @Schema(description = "Is accepted")
    private Boolean isAccepted;
    @Schema(description = "Show close order negotiation")
    private Boolean showCloseOrderNegotiation;
    @Schema(description = "Show cost alloction")
    private Boolean showCostAllocation;
    @Schema(description = "Invoice info")
    private InvoiceInfoVO info;
    @Schema(description = "Tax laber string")
    private String taxLabelString;
    @Schema(description = "Invoice items list")
    private List<InvoiceItemVO> items;
    @Schema(description = "Invoice items total")
    private InvoiceItemsTotalVO itemsTotal;
    @Schema(description = "Supplier terms")
    private TermsVO supplierTerms;
    @Schema(description = "Replace error message")
    private String replaceErrorMessage;
    private String replaceErrorMessageStrId;
    @Schema(description = "PropertyId to get user fields")
    private Long customPropertyId;
    //dual currency
    @Schema(description = "Is dual currency flag")
    private Boolean isDualCurrency;
    @Schema(description = "Dual currency rate")
    private BigDecimal rate;
    @Schema(description = "Dual currency transactional currency id")
    private Long exCurrencyId;
    @Schema(description = "Is sell invoice flag")
    private Boolean isSell;

    private Map customAttributes = new HashMap();
    @Schema(description = "Buttons")
    private Map<String, String> buttons;

    public String getReplaceErrorMessageStrId() {
        return replaceErrorMessageStrId;
    }

    public void setReplaceErrorMessageStrId(String replaceErrorMessageStrId) {
        this.replaceErrorMessageStrId = replaceErrorMessageStrId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectExternalUrl() {
        return projectExternalUrl;
    }

    public void setProjectExternalUrl(String projectExternalUrl) {
        this.projectExternalUrl = projectExternalUrl;
    }

    public Boolean getIsFinal() {
        return isFinal;
    }

    public void setIsFinal(Boolean aFinal) {
        isFinal = aFinal;
    }

    public Boolean getIsNonBillable() {
        return isNonBillable;
    }

    public void setIsNonBillable(Boolean nonBillable) {
        isNonBillable = nonBillable;
    }

    public Boolean getIsAccepted() {
        return isAccepted;
    }

    public void setIsAccepted(Boolean accepted) {
        isAccepted = accepted;
    }

    public Boolean getShowCloseOrderNegotiation() {
        return showCloseOrderNegotiation;
    }

    public void setShowCloseOrderNegotiation(Boolean showCloseOrderNegotiation) {
        this.showCloseOrderNegotiation = showCloseOrderNegotiation;
    }

    public Boolean getShowCostAllocation() {
        return showCostAllocation;
    }

    public void setShowCostAllocation(Boolean showCostAllocation) {
        this.showCostAllocation = showCostAllocation;
    }

    public InvoiceInfoVO getInfo() {
        return info;
    }

    public void setInfo(InvoiceInfoVO info) {
        this.info = info;
    }

    public String getTaxLabelString() {
        return taxLabelString;
    }

    public void setTaxLabelString(String taxLabelString) {
        this.taxLabelString = taxLabelString;
    }

    public List<InvoiceItemVO> getItems() {
        return items;
    }

    public void setItems(List<InvoiceItemVO> items) {
        this.items = items;
    }

    public InvoiceItemsTotalVO getItemsTotal() {
        return itemsTotal;
    }

    public void setItemsTotal(InvoiceItemsTotalVO itemsTotal) {
        this.itemsTotal = itemsTotal;
    }

    public String getReplaceErrorMessage() {
        return replaceErrorMessage;
    }

    public void setReplaceErrorMessage(String replaceErrorMessage) {
        this.replaceErrorMessage = replaceErrorMessage;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Map getCustomAttributes() {
        return customAttributes;
    }

    public void setCustomAttributes(Map customAttributes) {
        this.customAttributes = customAttributes;
    }

    public Map<String, String> getButtons() {
        return buttons;
    }

    public void setButtons(Map<String, String> buttons) {
        this.buttons = buttons;
    }

    public TermsVO getSupplierTerms() {
        return supplierTerms;
    }

    public void setSupplierTerms(TermsVO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public Boolean getIsDualCurrency() {
        return isDualCurrency;
    }

    public void setIsDualCurrency(Boolean dualCurrency) {
        isDualCurrency = dualCurrency;
    }

    public Boolean getIsSell() {
        return isSell;
    }

    public void setIsSell(Boolean sell) {
        isSell = sell;
    }
}
