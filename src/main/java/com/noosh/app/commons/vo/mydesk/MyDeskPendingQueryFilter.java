package com.noosh.app.commons.vo.mydesk;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * User: leilaz
 * Date: 4/10/24
 */
public class MyDeskPendingQueryFilter {
    public final static String REACT_MY_DESK_DESKOID_PENDING_FILTER_PREF_PREFIX = "REACT_MY_DESK_DESKOID_PENDING_FILTER_";

    public final static String CONTROLNAME_PENDING_DATE_RANGE_OPTION = "dateRange";

    public final static long SEVEN_DAYS = 7L;

    @Schema(description = "Date Range Selected From Dropdown", example = "30")
    private Long dateRange = null;

    public Long getDateRange() {
        return dateRange;
    }

    public void setDateRange(Long dateRange) {
        this.dateRange = dateRange;
    }
}
