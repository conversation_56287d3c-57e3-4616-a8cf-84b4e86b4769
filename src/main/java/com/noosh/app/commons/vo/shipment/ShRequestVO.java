package com.noosh.app.commons.vo.shipment;

import com.noosh.app.commons.vo.address.AddressVO;
import org.apache.commons.lang.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

public class ShRequestVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String phone;
    private String company;
    private LocalDateTime deliveryDate;
    private Long quantity;
    private String instruction;
    private String email;
    private String recipientName;
    private String firstName;
    private String lastName;
    private String middleName;
    private String description;
    private Long ordinalNumber;
    private Boolean useSpecPackaging;
    private String workgroupName;

    private String requestTypeStr;
    private Long requestTypeStrId;
    private String shipMethodStr;
    private Long shipMethodStrId;
    private String carrierStr;
    private Long carrierStrId;

    private AddressVO address;
    private Map<String, Object> customAttributes = new HashMap<>();

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public LocalDateTime getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(LocalDateTime deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public String getInstruction() {
        return instruction;
    }

    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRecipientName() {
        StringBuilder name = new StringBuilder();
        if (StringUtils.isNotEmpty(firstName)) {
            name.append(firstName).append(" ");
        }
        if (StringUtils.isNotEmpty(middleName)) {
            name.append(middleName).append(" ");
        }
        if (StringUtils.isNotEmpty(lastName)) {
            name.append(lastName).append(" ");
        }
        return name.toString().trim();
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getOrdinalNumber() {
        return ordinalNumber;
    }

    public void setOrdinalNumber(Long ordinalNumber) {
        this.ordinalNumber = ordinalNumber;
    }

    public Boolean getUseSpecPackaging() {
        return useSpecPackaging;
    }

    public void setUseSpecPackaging(Boolean useSpecPackaging) {
        this.useSpecPackaging = useSpecPackaging;
    }

    public String getWorkgroupName() {
        return workgroupName;
    }

    public void setWorkgroupName(String workgroupName) {
        this.workgroupName = workgroupName;
    }

    public String getRequestTypeStr() {
        return requestTypeStr;
    }

    public void setRequestTypeStr(String requestTypeStr) {
        this.requestTypeStr = requestTypeStr;
    }

    public Long getRequestTypeStrId() {
        return requestTypeStrId;
    }

    public void setRequestTypeStrId(Long requestTypeStrId) {
        this.requestTypeStrId = requestTypeStrId;
    }

    public String getShipMethodStr() {
        return shipMethodStr;
    }

    public void setShipMethodStr(String shipMethodStr) {
        this.shipMethodStr = shipMethodStr;
    }

    public Long getShipMethodStrId() {
        return shipMethodStrId;
    }

    public void setShipMethodStrId(Long shipMethodStrId) {
        this.shipMethodStrId = shipMethodStrId;
    }

    public String getCarrierStr() {
        return carrierStr;
    }

    public void setCarrierStr(String carrierStr) {
        this.carrierStr = carrierStr;
    }

    public Long getCarrierStrId() {
        return carrierStrId;
    }

    public void setCarrierStrId(Long carrierStrId) {
        this.carrierStrId = carrierStrId;
    }

    public AddressVO getAddress() {
        return address;
    }

    public void setAddress(AddressVO address) {
        this.address = address;
    }

    public Map<String, Object> getCustomAttributes() {
        return customAttributes;
    }

    public void setCustomAttributes(Map<String, Object> customAttributes) {
        this.customAttributes = customAttributes;
    }
}