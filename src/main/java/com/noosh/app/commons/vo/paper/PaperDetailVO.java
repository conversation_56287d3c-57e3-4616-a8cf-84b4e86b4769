package com.noosh.app.commons.vo.paper;

import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 3/21/2021
 */
public class PaperDetailVO {

    private String paperName;
    private String usedAs;
    private String grade;
    private String brand;
    private String paperType;
    private String finish;
    private String weight;
    private String paperRequired;
    private BigDecimal sheetHeight;
    private BigDecimal sheetWidth;
    private String sheetUnit;
    private BigDecimal numOfSheets;
    private BigDecimal rollWidth;
    private BigDecimal rollWidthN;
    private BigDecimal rollWidthD;
    private String rollWidthUnit;
    private BigDecimal rollDiameter;
    private String rollDiameterUnit;
    private BigDecimal rollCoreDiameter;
    private String rollCoreDiameterUnit;
    private BigDecimal totalWeight;
    private String totalWeightUnit;
    private String paperGrade;
    private BigDecimal recycledContent;
    private String paperComments;
    private String sfiCertified;
    private String fscCertified;
    private BigDecimal content;

    // Environmental Impact
    private String woodUnit;
    private BigDecimal woodValue;
    private List<PaperEnvImpactEquivalentVO> woodEquivalentImpact;
    private String energyUnit;
    private BigDecimal energyValue;
    private List<PaperEnvImpactEquivalentVO> energyEquivalentImpact;
    private String waterUnit;
    private BigDecimal waterValue;
    private List<PaperEnvImpactEquivalentVO> waterEquivalentImpact;
    private String greenHouseUnit;
    private BigDecimal greenHouseValue;
    private List<PaperEnvImpactEquivalentVO> greenHouseEquivalentImpact;
    private Integer paperGradeId;

    public String getPaperName() {
        return paperName;
    }

    public void setPaperName(String paperName) {
        this.paperName = paperName;
    }

    public String getUsedAs() {
        return usedAs;
    }

    public void setUsedAs(String usedAs) {
        this.usedAs = usedAs;
    }

    public String getGrade() {
        return grade;
    }

    public void setGrade(String grade) {
        this.grade = grade;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getPaperType() {
        return paperType;
    }

    public void setPaperType(String paperType) {
        this.paperType = paperType;
    }

    public String getFinish() {
        return finish;
    }

    public void setFinish(String finish) {
        this.finish = finish;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getPaperRequired() {
        return paperRequired;
    }

    public void setPaperRequired(String paperRequired) {
        this.paperRequired = paperRequired;
    }

    public BigDecimal getSheetHeight() {
        return sheetHeight;
    }

    public void setSheetHeight(BigDecimal sheetHeight) {
        this.sheetHeight = sheetHeight;
    }

    public BigDecimal getSheetWidth() {
        return sheetWidth;
    }

    public void setSheetWidth(BigDecimal sheetWidth) {
        this.sheetWidth = sheetWidth;
    }

    public String getSheetUnit() {
        return sheetUnit;
    }

    public void setSheetUnit(String sheetUnit) {
        this.sheetUnit = sheetUnit;
    }

    public BigDecimal getNumOfSheets() {
        return numOfSheets;
    }

    public void setNumOfSheets(BigDecimal numOfSheets) {
        this.numOfSheets = numOfSheets;
    }

    public BigDecimal getRollWidth() {
        return rollWidth;
    }

    public void setRollWidth(BigDecimal rollWidth) {
        this.rollWidth = rollWidth;
    }

    public BigDecimal getRollWidthN() {
        return rollWidthN;
    }

    public void setRollWidthN(BigDecimal rollWidthN) {
        this.rollWidthN = rollWidthN;
    }

    public BigDecimal getRollWidthD() {
        return rollWidthD;
    }

    public void setRollWidthD(BigDecimal rollWidthD) {
        this.rollWidthD = rollWidthD;
    }

    public String getRollWidthUnit() {
        return rollWidthUnit;
    }

    public void setRollWidthUnit(String rollWidthUnit) {
        this.rollWidthUnit = rollWidthUnit;
    }

    public BigDecimal getRollDiameter() {
        return rollDiameter;
    }

    public void setRollDiameter(BigDecimal rollDiameter) {
        this.rollDiameter = rollDiameter;
    }

    public String getRollDiameterUnit() {
        return rollDiameterUnit;
    }

    public void setRollDiameterUnit(String rollDiameterUnit) {
        this.rollDiameterUnit = rollDiameterUnit;
    }

    public BigDecimal getRollCoreDiameter() {
        return rollCoreDiameter;
    }

    public void setRollCoreDiameter(BigDecimal rollCoreDiameter) {
        this.rollCoreDiameter = rollCoreDiameter;
    }

    public String getRollCoreDiameterUnit() {
        return rollCoreDiameterUnit;
    }

    public void setRollCoreDiameterUnit(String rollCoreDiameterUnit) {
        this.rollCoreDiameterUnit = rollCoreDiameterUnit;
    }

    public BigDecimal getTotalWeight() {
        return totalWeight;
    }

    public void setTotalWeight(BigDecimal totalWeight) {
        this.totalWeight = totalWeight;
    }

    public String getTotalWeightUnit() {
        return totalWeightUnit;
    }

    public void setTotalWeightUnit(String totalWeightUnit) {
        this.totalWeightUnit = totalWeightUnit;
    }

    public String getPaperGrade() {
        return paperGrade;
    }

    public void setPaperGrade(String paperGrade) {
        this.paperGrade = paperGrade;
    }

    public BigDecimal getRecycledContent() {
        return recycledContent;
    }

    public void setRecycledContent(BigDecimal recycledContent) {
        this.recycledContent = recycledContent;
    }

    public String getPaperComments() {
        return paperComments;
    }

    public void setPaperComments(String paperComments) {
        this.paperComments = paperComments;
    }

    public String getWoodUnit() {
        return woodUnit;
    }

    public void setWoodUnit(String woodUnit) {
        this.woodUnit = woodUnit;
    }

    public BigDecimal getWoodValue() {
        return woodValue;
    }

    public void setWoodValue(BigDecimal woodValue) {
        this.woodValue = woodValue;
    }

    public String getEnergyUnit() {
        return energyUnit;
    }

    public void setEnergyUnit(String energyUnit) {
        this.energyUnit = energyUnit;
    }

    public BigDecimal getEnergyValue() {
        return energyValue;
    }

    public void setEnergyValue(BigDecimal energyValue) {
        this.energyValue = energyValue;
    }

    public String getWaterUnit() {
        return waterUnit;
    }

    public void setWaterUnit(String waterUnit) {
        this.waterUnit = waterUnit;
    }

    public BigDecimal getWaterValue() {
        return waterValue;
    }

    public void setWaterValue(BigDecimal waterValue) {
        this.waterValue = waterValue;
    }

    public String getGreenHouseUnit() {
        return greenHouseUnit;
    }

    public void setGreenHouseUnit(String greenHouseUnit) {
        this.greenHouseUnit = greenHouseUnit;
    }

    public BigDecimal getGreenHouseValue() {
        return greenHouseValue;
    }

    public void setGreenHouseValue(BigDecimal greenHouseValue) {
        this.greenHouseValue = greenHouseValue;
    }

    public String getSfiCertified() {
        return sfiCertified;
    }

    public void setSfiCertified(String sfiCertified) {
        this.sfiCertified = sfiCertified;
    }

    public String getFscCertified() {
        return fscCertified;
    }

    public void setFscCertified(String fscCertified) {
        this.fscCertified = fscCertified;
    }

    public BigDecimal getContent() {
        return content;
    }

    public void setContent(BigDecimal content) {
        this.content = content;
    }

    public List<PaperEnvImpactEquivalentVO> getWoodEquivalentImpact() {
        return woodEquivalentImpact;
    }

    public void setWoodEquivalentImpact(List<PaperEnvImpactEquivalentVO> woodEquivalentImpact) {
        this.woodEquivalentImpact = woodEquivalentImpact;
    }

    public List<PaperEnvImpactEquivalentVO> getEnergyEquivalentImpact() {
        return energyEquivalentImpact;
    }

    public void setEnergyEquivalentImpact(List<PaperEnvImpactEquivalentVO> energyEquivalentImpact) {
        this.energyEquivalentImpact = energyEquivalentImpact;
    }

    public List<PaperEnvImpactEquivalentVO> getWaterEquivalentImpact() {
        return waterEquivalentImpact;
    }

    public void setWaterEquivalentImpact(List<PaperEnvImpactEquivalentVO> waterEquivalentImpact) {
        this.waterEquivalentImpact = waterEquivalentImpact;
    }

    public List<PaperEnvImpactEquivalentVO> getGreenHouseEquivalentImpact() {
        return greenHouseEquivalentImpact;
    }

    public void setGreenHouseEquivalentImpact(List<PaperEnvImpactEquivalentVO> greenHouseEquivalentImpact) {
        this.greenHouseEquivalentImpact = greenHouseEquivalentImpact;
    }

    public Integer getPaperGradeId() {
        return paperGradeId;
    }

    public void setPaperGradeId(Integer paperGradeId) {
        this.paperGradeId = paperGradeId;
    }
}
