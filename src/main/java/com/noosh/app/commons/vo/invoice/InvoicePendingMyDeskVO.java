package com.noosh.app.commons.vo.invoice;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 4/10/24
 */
public class InvoicePendingMyDeskVO implements Serializable {
    private static final long serialVersionUID = 43900322149470463L;

    private String projectName;

    private Long projectId;

    private Long invoiceId;

    private String projectExternalUrl;

    private String invoiceExternalUrl;

    private boolean isUserBuyer;

    private boolean isUserSupplier;

    public boolean getIsUserBuyer() {
        return isUserBuyer;
    }

    public void setIsUserBuyer(boolean userBuyer) {
        isUserBuyer = userBuyer;
    }

    public boolean getIsUserSupplier() {
        return isUserSupplier;
    }

    public void setIsUserSupplier(boolean userSupplier) {
        isUserSupplier = userSupplier;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getProjectExternalUrl() {
        return projectExternalUrl;
    }

    public void setProjectExternalUrl(String projectExternalUrl) {
        this.projectExternalUrl = projectExternalUrl;
    }

    public String getInvoiceExternalUrl() {
        return invoiceExternalUrl;
    }

    public void setInvoiceExternalUrl(String invoiceExternalUrl) {
        this.invoiceExternalUrl = invoiceExternalUrl;
    }
}
