package com.noosh.app.commons.vo.breakout;

import java.math.BigDecimal;
import java.util.List;

/**
 * @auther mario
 * @date 8/5/2020
 */
public class InvoiceBreakoutVO {

    private Long id;
    private Long breakoutTypeId;
    private String name;
    private Boolean isQuantity;
    private Long nestingLevel;
    private BigDecimal order;
    private Long orderCurrencyId;
    private BigDecimal previousInvoiced;
    private Long previousInvoicedCurrencyId;
    private BigDecimal thisInvoice;
    private Long thisInvoiceCurrencyId;
    private BigDecimal totalInvoiced;
    private Long totalInvoicedCurrencyId;
    private BigDecimal orderUnitsValue;
    private BigDecimal orderRatesValue;
    private Long orderRatesValueCurrencyId;
    private BigDecimal thisInvoiceUnitsValue;
    private BigDecimal thisInvoiceRatesValue;
    private Long thisInvoiceRatesValueCurrencyId;
    private BigDecimal previousInvoiceUnitsValue;
    private BigDecimal previousInvoiceRatesValue;
    private Long previousInvoiceRatesValueCurrencyId;

    private Long orderValue;
    private Long previousInvoicedValue;
    private Long totalInvoicedValue;
    private BigDecimal orderedUnitPrice;
    private Long orderedUnitPriceCurrencyId;

    //dual currency
    private BigDecimal exOrder;
    private Long exOrderCurrencyId;
    private BigDecimal exPreviousInvoiced;
    private Long exPreviousInvoicedCurrencyId;
    private BigDecimal exThisInvoice;
    private Long exThisInvoiceCurrencyId;
    private BigDecimal exTotalInvoiced;
    private Long exTotalInvoicedCurrencyId;
    private BigDecimal exOrderUnitsValue;
    private BigDecimal exOrderRatesValue;
    private Long exOrderRatesValueCurrencyId;
    private BigDecimal exThisInvoiceUnitsValue;
    private BigDecimal exThisInvoiceRatesValue;
    private Long exThisInvoiceRatesValueCurrencyId;
    private BigDecimal exPreviousInvoiceUnitsValue;
    private BigDecimal exPreviousInvoiceRatesValue;
    private Long exPreviousInvoiceRatesValueCurrencyId;
    private BigDecimal exOrderedUnitPrice;
    private Long exOrderedUnitPriceCurrencyId;

    private Long parentTypeId;

    private List<Long> descendentTypeIds;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getIsQuantity() {
        return isQuantity;
    }

    public void setIsQuantity(Boolean quantity) {
        isQuantity = quantity;
    }

    public Long getNestingLevel() {
        return nestingLevel;
    }

    public void setNestingLevel(Long nestingLevel) {
        this.nestingLevel = nestingLevel;
    }

    public BigDecimal getOrder() {
        return order;
    }

    public void setOrder(BigDecimal order) {
        this.order = order;
    }

    public BigDecimal getPreviousInvoiced() {
        return previousInvoiced;
    }

    public void setPreviousInvoiced(BigDecimal previousInvoiced) {
        this.previousInvoiced = previousInvoiced;
    }

    public BigDecimal getThisInvoice() {
        return thisInvoice;
    }

    public void setThisInvoice(BigDecimal thisInvoice) {
        this.thisInvoice = thisInvoice;
    }

    public BigDecimal getTotalInvoiced() {
        return totalInvoiced;
    }

    public void setTotalInvoiced(BigDecimal totalInvoiced) {
        this.totalInvoiced = totalInvoiced;
    }

    public BigDecimal getOrderUnitsValue() {
        return orderUnitsValue;
    }

    public void setOrderUnitsValue(BigDecimal orderUnitsValue) {
        this.orderUnitsValue = orderUnitsValue;
    }

    public BigDecimal getOrderRatesValue() {
        return orderRatesValue;
    }

    public void setOrderRatesValue(BigDecimal orderRatesValue) {
        this.orderRatesValue = orderRatesValue;
    }

    public BigDecimal getThisInvoiceUnitsValue() {
        return thisInvoiceUnitsValue;
    }

    public void setThisInvoiceUnitsValue(BigDecimal thisInvoiceUnitsValue) {
        this.thisInvoiceUnitsValue = thisInvoiceUnitsValue;
    }

    public BigDecimal getThisInvoiceRatesValue() {
        return thisInvoiceRatesValue;
    }

    public void setThisInvoiceRatesValue(BigDecimal thisInvoiceRatesValue) {
        this.thisInvoiceRatesValue = thisInvoiceRatesValue;
    }

    public BigDecimal getPreviousInvoiceUnitsValue() {
        return previousInvoiceUnitsValue;
    }

    public void setPreviousInvoiceUnitsValue(BigDecimal previousInvoiceUnitsValue) {
        this.previousInvoiceUnitsValue = previousInvoiceUnitsValue;
    }

    public BigDecimal getPreviousInvoiceRatesValue() {
        return previousInvoiceRatesValue;
    }

    public void setPreviousInvoiceRatesValue(BigDecimal previousInvoiceRatesValue) {
        this.previousInvoiceRatesValue = previousInvoiceRatesValue;
    }

    public Long getOrderCurrencyId() {
        return orderCurrencyId;
    }

    public void setOrderCurrencyId(Long orderCurrencyId) {
        this.orderCurrencyId = orderCurrencyId;
    }

    public Long getPreviousInvoicedCurrencyId() {
        return previousInvoicedCurrencyId;
    }

    public void setPreviousInvoicedCurrencyId(Long previousInvoicedCurrencyId) {
        this.previousInvoicedCurrencyId = previousInvoicedCurrencyId;
    }

    public Long getThisInvoiceCurrencyId() {
        return thisInvoiceCurrencyId;
    }

    public void setThisInvoiceCurrencyId(Long thisInvoiceCurrencyId) {
        this.thisInvoiceCurrencyId = thisInvoiceCurrencyId;
    }

    public Long getTotalInvoicedCurrencyId() {
        return totalInvoicedCurrencyId;
    }

    public void setTotalInvoicedCurrencyId(Long totalInvoicedCurrencyId) {
        this.totalInvoicedCurrencyId = totalInvoicedCurrencyId;
    }

    public Long getOrderRatesValueCurrencyId() {
        return orderRatesValueCurrencyId;
    }

    public void setOrderRatesValueCurrencyId(Long orderRatesValueCurrencyId) {
        this.orderRatesValueCurrencyId = orderRatesValueCurrencyId;
    }

    public Long getThisInvoiceRatesValueCurrencyId() {
        return thisInvoiceRatesValueCurrencyId;
    }

    public void setThisInvoiceRatesValueCurrencyId(Long thisInvoiceRatesValueCurrencyId) {
        this.thisInvoiceRatesValueCurrencyId = thisInvoiceRatesValueCurrencyId;
    }

    public Long getPreviousInvoiceRatesValueCurrencyId() {
        return previousInvoiceRatesValueCurrencyId;
    }

    public void setPreviousInvoiceRatesValueCurrencyId(Long previousInvoiceRatesValueCurrencyId) {
        this.previousInvoiceRatesValueCurrencyId = previousInvoiceRatesValueCurrencyId;
    }

    public Long getOrderValue() {
        return orderValue;
    }

    public void setOrderValue(Long orderValue) {
        this.orderValue = orderValue;
    }

    public Long getPreviousInvoicedValue() {
        return previousInvoicedValue;
    }

    public void setPreviousInvoicedValue(Long previousInvoicedValue) {
        this.previousInvoicedValue = previousInvoicedValue;
    }

    public Long getTotalInvoicedValue() {
        return totalInvoicedValue;
    }

    public void setTotalInvoicedValue(Long totalInvoicedValue) {
        this.totalInvoicedValue = totalInvoicedValue;
    }

    public BigDecimal getOrderedUnitPrice() {
        return orderedUnitPrice;
    }

    public void setOrderedUnitPrice(BigDecimal orderedUnitPrice) {
        this.orderedUnitPrice = orderedUnitPrice;
    }

    public Long getOrderedUnitPriceCurrencyId() {
        return orderedUnitPriceCurrencyId;
    }

    public void setOrderedUnitPriceCurrencyId(Long orderedUnitPriceCurrencyId) {
        this.orderedUnitPriceCurrencyId = orderedUnitPriceCurrencyId;
    }

    public BigDecimal getExOrder() {
        return exOrder;
    }

    public void setExOrder(BigDecimal exOrder) {
        this.exOrder = exOrder;
    }

    public Long getExOrderCurrencyId() {
        return exOrderCurrencyId;
    }

    public void setExOrderCurrencyId(Long exOrderCurrencyId) {
        this.exOrderCurrencyId = exOrderCurrencyId;
    }

    public BigDecimal getExPreviousInvoiced() {
        return exPreviousInvoiced;
    }

    public void setExPreviousInvoiced(BigDecimal exPreviousInvoiced) {
        this.exPreviousInvoiced = exPreviousInvoiced;
    }

    public Long getExPreviousInvoicedCurrencyId() {
        return exPreviousInvoicedCurrencyId;
    }

    public void setExPreviousInvoicedCurrencyId(Long exPreviousInvoicedCurrencyId) {
        this.exPreviousInvoicedCurrencyId = exPreviousInvoicedCurrencyId;
    }

    public BigDecimal getExThisInvoice() {
        return exThisInvoice;
    }

    public void setExThisInvoice(BigDecimal exThisInvoice) {
        this.exThisInvoice = exThisInvoice;
    }

    public Long getExThisInvoiceCurrencyId() {
        return exThisInvoiceCurrencyId;
    }

    public void setExThisInvoiceCurrencyId(Long exThisInvoiceCurrencyId) {
        this.exThisInvoiceCurrencyId = exThisInvoiceCurrencyId;
    }

    public BigDecimal getExTotalInvoiced() {
        return exTotalInvoiced;
    }

    public void setExTotalInvoiced(BigDecimal exTotalInvoiced) {
        this.exTotalInvoiced = exTotalInvoiced;
    }

    public Long getExTotalInvoicedCurrencyId() {
        return exTotalInvoicedCurrencyId;
    }

    public void setExTotalInvoicedCurrencyId(Long exTotalInvoicedCurrencyId) {
        this.exTotalInvoicedCurrencyId = exTotalInvoicedCurrencyId;
    }

    public BigDecimal getExOrderUnitsValue() {
        return exOrderUnitsValue;
    }

    public void setExOrderUnitsValue(BigDecimal exOrderUnitsValue) {
        this.exOrderUnitsValue = exOrderUnitsValue;
    }

    public BigDecimal getExOrderRatesValue() {
        return exOrderRatesValue;
    }

    public void setExOrderRatesValue(BigDecimal exOrderRatesValue) {
        this.exOrderRatesValue = exOrderRatesValue;
    }

    public Long getExOrderRatesValueCurrencyId() {
        return exOrderRatesValueCurrencyId;
    }

    public void setExOrderRatesValueCurrencyId(Long exOrderRatesValueCurrencyId) {
        this.exOrderRatesValueCurrencyId = exOrderRatesValueCurrencyId;
    }

    public BigDecimal getExThisInvoiceUnitsValue() {
        return exThisInvoiceUnitsValue;
    }

    public void setExThisInvoiceUnitsValue(BigDecimal exThisInvoiceUnitsValue) {
        this.exThisInvoiceUnitsValue = exThisInvoiceUnitsValue;
    }

    public BigDecimal getExThisInvoiceRatesValue() {
        return exThisInvoiceRatesValue;
    }

    public void setExThisInvoiceRatesValue(BigDecimal exThisInvoiceRatesValue) {
        this.exThisInvoiceRatesValue = exThisInvoiceRatesValue;
    }

    public Long getExThisInvoiceRatesValueCurrencyId() {
        return exThisInvoiceRatesValueCurrencyId;
    }

    public void setExThisInvoiceRatesValueCurrencyId(Long exThisInvoiceRatesValueCurrencyId) {
        this.exThisInvoiceRatesValueCurrencyId = exThisInvoiceRatesValueCurrencyId;
    }

    public BigDecimal getExPreviousInvoiceUnitsValue() {
        return exPreviousInvoiceUnitsValue;
    }

    public void setExPreviousInvoiceUnitsValue(BigDecimal exPreviousInvoiceUnitsValue) {
        this.exPreviousInvoiceUnitsValue = exPreviousInvoiceUnitsValue;
    }

    public BigDecimal getExPreviousInvoiceRatesValue() {
        return exPreviousInvoiceRatesValue;
    }

    public void setExPreviousInvoiceRatesValue(BigDecimal exPreviousInvoiceRatesValue) {
        this.exPreviousInvoiceRatesValue = exPreviousInvoiceRatesValue;
    }

    public Long getExPreviousInvoiceRatesValueCurrencyId() {
        return exPreviousInvoiceRatesValueCurrencyId;
    }

    public void setExPreviousInvoiceRatesValueCurrencyId(Long exPreviousInvoiceRatesValueCurrencyId) {
        this.exPreviousInvoiceRatesValueCurrencyId = exPreviousInvoiceRatesValueCurrencyId;
    }

    public BigDecimal getExOrderedUnitPrice() {
        return exOrderedUnitPrice;
    }

    public void setExOrderedUnitPrice(BigDecimal exOrderedUnitPrice) {
        this.exOrderedUnitPrice = exOrderedUnitPrice;
    }

    public Long getExOrderedUnitPriceCurrencyId() {
        return exOrderedUnitPriceCurrencyId;
    }

    public void setExOrderedUnitPriceCurrencyId(Long exOrderedUnitPriceCurrencyId) {
        this.exOrderedUnitPriceCurrencyId = exOrderedUnitPriceCurrencyId;
    }

    public Long getParentTypeId() {
        return parentTypeId;
    }

    public void setParentTypeId(Long parentTypeId) {
        this.parentTypeId = parentTypeId;
    }

    public List<Long> getDescendentTypeIds() {
        return descendentTypeIds;
    }

    public void setDescendentTypeIds(List<Long> descendentTypeIds) {
        this.descendentTypeIds = descendentTypeIds;
    }
}
