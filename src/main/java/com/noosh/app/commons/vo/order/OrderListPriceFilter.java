package com.noosh.app.commons.vo.order;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

public class OrderListPriceFilter implements Serializable {
    @Serial
    private static final long serialVersionUID = 8474463213620498612L;

    private List<Long> priceFilter;

    public List<Long> getPriceFilter() {
        return priceFilter;
    }

    public void setPriceFilter(List<Long> priceFilter) {
        this.priceFilter = priceFilter;
    }
}
