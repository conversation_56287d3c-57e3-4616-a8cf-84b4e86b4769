package com.noosh.app.commons.vo.breakout;

import com.noosh.app.commons.vo.BaseVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/5/17
 */
public class BreakoutVO extends BaseVO {
    private static final long serialVersionUID = 1303984972891038024L;
    private Long value;
    private BigDecimal price;
    private Long priceCurrencyId;
    private BigDecimal preMarkup;
    private Long preMarkupCurrencyId;
    private BigDecimal exPrice;
    private Long exPriceCurrencyId;
    private BigDecimal exPreMarkup;
    private Long exPreMarkupCurrencyId;
    private BreakoutTypeVO breakoutType;
    private BigDecimal units;
    private BigDecimal rates;
    private Long nestingLevel;
    private Boolean hasQuantity;
    private Long objectId;
    private Long objectClassId;
    private Integer level;
    private List<BreakoutVO> descendents;

    public BigDecimal getPreMarkup() {
        return preMarkup;
    }

    public void setPreMarkup(BigDecimal preMarkup) {
        this.preMarkup = preMarkup;
    }

    public BreakoutTypeVO getBreakoutType() {
        return breakoutType;
    }

    public void setBreakoutType(BreakoutTypeVO breakoutType) {
        this.breakoutType = breakoutType;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getPriceCurrencyId() {
        return this.priceCurrencyId;
    }

    public void setPriceCurrencyId(Long priceCurrencyId) {
        this.priceCurrencyId = priceCurrencyId;
    }

    public BigDecimal getExPrice() {
        return this.exPrice;
    }

    public void setExPrice(BigDecimal exPrice) {
        this.exPrice = exPrice;
    }

    public Long getExPriceCurrencyId() {
        return this.exPriceCurrencyId;
    }

    public void setExPriceCurrencyId(Long exPriceCurrencyId) {
        this.exPriceCurrencyId = exPriceCurrencyId;
    }

    public BigDecimal getExPreMarkup() {
        return this.exPreMarkup;
    }

    public void setExPreMarkup(BigDecimal exPreMarkup) {
        this.exPreMarkup = exPreMarkup;
    }

    public Long getExPreMarkupCurrencyId() {
        return this.exPreMarkupCurrencyId;
    }

    public void setExPreMarkupCurrencyId(Long exPreMarkupCurrencyId) {
        this.exPreMarkupCurrencyId = exPreMarkupCurrencyId;
    }

    public BigDecimal getUnits() {
        return units;
    }

    public void setUnits(BigDecimal units) {
        this.units = units;
    }

    public BigDecimal getRates() {
        return rates;
    }

    public void setRates(BigDecimal rates) {
        this.rates = rates;
    }

    public Long getNestingLevel() {
        return nestingLevel;
    }

    public void setNestingLevel(Long nestingLevel) {
        this.nestingLevel = nestingLevel;
    }

    public Boolean getHasQuantity() {
        return hasQuantity;
    }

    public void setHasQuantity(Boolean hasQuantity) {
        this.hasQuantity = hasQuantity;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Integer getLevel() {
        return this.level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<BreakoutVO> getDescendents() {
        return this.descendents;
    }

    public void setDescendents(List<BreakoutVO> descendents) {
        this.descendents = descendents;
    }
}
