package com.noosh.app.commons.vo.rating;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 7/15/19
 */
public class SectionQuestionVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private boolean naAllowed;
    private Long granularity;
    private List<SectionVO> section;

    public boolean getNaAllowed() {
        return naAllowed;
    }

    public void setNaAllowed(boolean naAllowed) {
        this.naAllowed = naAllowed;
    }

    public Long getGranularity() {
        return granularity;
    }

    public void setGranularity(Long granularity) {
        this.granularity = granularity;
    }

    public List<SectionVO> getSection() {
        return section;
    }

    public void setSection(List<SectionVO> section) {
        this.section = section;
    }
}
