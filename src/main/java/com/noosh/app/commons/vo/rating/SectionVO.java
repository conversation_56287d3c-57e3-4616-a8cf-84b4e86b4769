package com.noosh.app.commons.vo.rating;

import com.noosh.app.commons.vo.BaseVO;

import java.util.List;

/**
 * User: leilaz
 * Date: 1/20/19
 */
public class SectionVO extends BaseVO {

    private String title;

    private Long displayOrder;

    private String str;

    private Long strId;

    private List<RatingQuestionVO> questionList;

    public Long getStrId() {
        return strId;
    }

    public void setStrId(Long strId) {
        this.strId = strId;
    }

    public List<RatingQuestionVO> getQuestionList() {
        return questionList;
    }

    public void setQuestionList(List<RatingQuestionVO> questionList) {
        this.questionList = questionList;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Long displayOrder) {
        this.displayOrder = displayOrder;
    }

    public String getStr() {
        return str;
    }

    public void setStr(String str) {
        this.str = str;
    }
}
