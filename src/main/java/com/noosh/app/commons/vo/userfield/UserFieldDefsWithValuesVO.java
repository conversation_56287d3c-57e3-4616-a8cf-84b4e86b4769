package com.noosh.app.commons.vo.userfield;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Author: Zhenyu Hu
 * @Date: 9/9/2022
 */
public class UserFieldDefsWithValuesVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 7789830579749808301L;

    private Long fieldClassId;
    private List<UserFieldVO> defs;
    private Map<Long, Map<String, Object>> values;

    public Long getFieldClassId() {
        return fieldClassId;
    }

    public void setFieldClassId(Long fieldClassId) {
        this.fieldClassId = fieldClassId;
    }

    public List<UserFieldVO> getUserFields() {
        return defs;
    }

    public void setUserFields(List<UserFieldVO> userFields) {
        this.defs = userFields;
    }

    public Map<Long, Map<String, Object>> getValues() {
        return values;
    }

    public void setValues(Map<Long, Map<String, Object>> values) {
        this.values = values;
    }
}
