package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.account.AccountUserVO;
import com.noosh.app.commons.vo.project.ProjectVO;
import com.noosh.app.commons.vo.workgroup.WorkgroupVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 9/26/17
 */
public class OrderDetailVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long orderId;

    private Long orderVersionId;

    private Long orderTypeId;

    private Long rfeId;

    private String title;

    private LocalDateTime completionDate;

    private LocalDateTime creationDate;

    private String reference;

    private boolean isPaperOrder; //todo add collaborateOrders, gangedOrders

    private String supplierReference;

    private boolean isRejected;

    private String comments;

    private String paymentMethod;

    private String paymentReference;

    private String reasonOther;

    private String reason;

    private String budgetTypeField;

    private boolean closeOrderNegotiation;
    private boolean hideOversAndUnders;


    private double oversPercent;

    private double undersPercent;

    private double subTotal;

    private double tax;
    private Long taxCurrencyId;

    private double taxPercent;
    private String taxLabel;

    private double shipping;
    private Long shippingCurrencyId;
    private double miscCost;
    private Long miscCostCurrencyId;
    private double grandTotal;

    private double discountOrSurcharge;
    private Long discountOrSurchargeCurrencyId;
    private double discountOrSurchargeTotal;
    private double orderItemDiscountSurchargeTotal;

    //dual currency
    private boolean isDualCurrency;
    private boolean hideBaseCurrency;
    private BigDecimal rate;
    private Long exCurrencyId;
    private BigDecimal exTax;
    private Long exTaxCurrencyId;
    private BigDecimal exShipping;
    private Long exShippingCurrencyId;
    private BigDecimal exMiscCost;
    private Long exMiscCostCurrencyId;
    private BigDecimal exDiscountOrSurcharge;
    private Long exDiscountOrSurchargeCurrencyId;

    private boolean itemizedTaxAndShippingEnabled;
    private boolean isEnableComplexVAT;
    private List vatsInfo;
    //paper
    private Boolean isPaperFlow;

    private String taxLabelString;

    private String valueCurrency;

    private Long paymentMethodId;

    private Long budgetTypeId;

    private WorkgroupVO buyerWorkgroup;

    private WorkgroupVO supplierWorkgroup;

    private AccountUserVO buyer;

    private AccountUserVO supplier;

    private OrderStateVO orderState;

    private ProjectVO parent;

    private ProjectVO buyerProject;

    private List<OrderItemVO> orderItems;

    private LocalDateTime orderSendToSupplierDate;

    private TermsVO buyerTerms;
    private TermsVO supplierTerms;

    private Map customAttributes = new HashMap();

    //dismiss control
    private Boolean canCloseRfe;
    private Boolean canDismissSupplier;
    private Boolean isCloseRfe;
    private Boolean isDismissUnselectedSupplier;
    private Boolean dismissPrefValue;

    private Boolean isSensitive;
    private Boolean contractPricingEnabled;
    private Boolean isCompletionDateRequired;
    private Boolean enableSupplierAddPaperDetails;
    private Boolean enableLogistics;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderVersionId() {
        return orderVersionId;
    }

    public void setOrderVersionId(Long orderVersionId) {
        this.orderVersionId = orderVersionId;
    }

    public Long getOrderTypeId() {
        return this.orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public Long getRfeId() {
        return this.rfeId;
    }

    public void setRfeId(Long rfeId) {
        this.rfeId = rfeId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public boolean getIsPaperOrder() {
        return isPaperOrder;
    }

    public void setIsPaperOrder(boolean paperOrder) {
        isPaperOrder = paperOrder;
    }

    public String getSupplierReference() {
        return supplierReference;
    }

    public void setSupplierReference(String supplierReference) {
        this.supplierReference = supplierReference;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public String getReasonOther() {
        return reasonOther;
    }

    public void setReasonOther(String reasonOther) {
        this.reasonOther = reasonOther;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getBudgetTypeField() {
        return budgetTypeField;
    }

    public void setBudgetTypeField(String budgetTypeField) {
        this.budgetTypeField = budgetTypeField;
    }

    public boolean getCloseOrderNegotiation() {
        return this.closeOrderNegotiation;
    }

    public void setCloseOrderNegotiation(boolean closeOrderNegotiation) {
        this.closeOrderNegotiation = closeOrderNegotiation;
    }

    public boolean getHideOversAndUnders() {
        return hideOversAndUnders;
    }

    public void setHideOversAndUnders(boolean hideOversAndUnders) {
        this.hideOversAndUnders = hideOversAndUnders;
    }

    public double getOversPercent() {
        return oversPercent;
    }

    public void setOversPercent(double oversPercent) {
        this.oversPercent = oversPercent;
    }

    public double getUndersPercent() {
        return undersPercent;
    }

    public void setUndersPercent(double undersPercent) {
        this.undersPercent = undersPercent;
    }

    public double getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(double subTotal) {
        this.subTotal = subTotal;
    }

    public double getTax() {
        return tax;
    }

    public void setTax(double tax) {
        this.tax = tax;
    }

    public double getTaxPercent() {
        return this.taxPercent;
    }

    public void setTaxPercent(double taxPercent) {
        this.taxPercent = taxPercent;
    }

    public String getTaxLabel() {
        return this.taxLabel;
    }

    public void setTaxLabel(String taxLabel) {
        this.taxLabel = taxLabel;
    }

    public double getShipping() {
        return shipping;
    }

    public void setShipping(double shipping) {
        this.shipping = shipping;
    }

    public double getMiscCost() {
        return this.miscCost;
    }

    public void setMiscCost(double miscCost) {
        this.miscCost = miscCost;
    }

    public double getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(double grandTotal) {
        this.grandTotal = grandTotal;
    }

    public double getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(double discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public double getDiscountOrSurchargeTotal() {
        return discountOrSurchargeTotal;
    }

    public void setDiscountOrSurchargeTotal(double discountOrSurchargeTotal) {
        this.discountOrSurchargeTotal = discountOrSurchargeTotal;
    }

    public double getOrderItemDiscountSurchargeTotal() {
        return orderItemDiscountSurchargeTotal;
    }

    public void setOrderItemDiscountSurchargeTotal(double orderItemDiscountSurchargeTotal) {
        this.orderItemDiscountSurchargeTotal = orderItemDiscountSurchargeTotal;
    }

    public boolean getIsDualCurrency() {
        return isDualCurrency;
    }

    public void setIsDualCurrency(boolean dualCurrency) {
        isDualCurrency = dualCurrency;
    }

    public boolean getHideBaseCurrency() {
        return hideBaseCurrency;
    }

    public void setHideBaseCurrency(boolean hideBaseCurrency) {
        this.hideBaseCurrency = hideBaseCurrency;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public BigDecimal getExMiscCost() {
        return exMiscCost;
    }

    public void setExMiscCost(BigDecimal exMiscCost) {
        this.exMiscCost = exMiscCost;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public Long getMiscCostCurrencyId() {
        return miscCostCurrencyId;
    }

    public void setMiscCostCurrencyId(Long miscCostCurrencyId) {
        this.miscCostCurrencyId = miscCostCurrencyId;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public Long getExTaxCurrencyId() {
        return exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public Long getExShippingCurrencyId() {
        return exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public Long getExMiscCostCurrencyId() {
        return exMiscCostCurrencyId;
    }

    public void setExMiscCostCurrencyId(Long exMiscCostCurrencyId) {
        this.exMiscCostCurrencyId = exMiscCostCurrencyId;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public String getTaxLabelString() {
        return taxLabelString;
    }

    public void setTaxLabelString(String taxLabelString) {
        this.taxLabelString = taxLabelString;
    }

    public String getValueCurrency() {
        return valueCurrency;
    }

    public void setValueCurrency(String valueCurrency) {
        this.valueCurrency = valueCurrency;
    }

    public Long getPaymentMethodId() {
        return paymentMethodId;
    }

    public void setPaymentMethodId(Long paymentMethodId) {
        this.paymentMethodId = paymentMethodId;
    }

    public Long getBudgetTypeId() {
        return budgetTypeId;
    }

    public void setBudgetTypeId(Long budgetTypeId) {
        this.budgetTypeId = budgetTypeId;
    }

    public WorkgroupVO getBuyerWorkgroup() {
        return buyerWorkgroup;
    }

    public void setBuyerWorkgroup(WorkgroupVO buyerWorkgroup) {
        this.buyerWorkgroup = buyerWorkgroup;
    }

    public WorkgroupVO getSupplierWorkgroup() {
        return supplierWorkgroup;
    }

    public void setSupplierWorkgroup(WorkgroupVO supplierWorkgroup) {
        this.supplierWorkgroup = supplierWorkgroup;
    }

    public AccountUserVO getBuyer() {
        return buyer;
    }

    public void setBuyer(AccountUserVO buyer) {
        this.buyer = buyer;
    }

    public AccountUserVO getSupplier() {
        return supplier;
    }

    public void setSupplier(AccountUserVO supplier) {
        this.supplier = supplier;
    }

    public OrderStateVO getOrderState() {
        return orderState;
    }

    public void setOrderState(OrderStateVO orderState) {
        this.orderState = orderState;
    }

    public ProjectVO getParent() {
        return parent;
    }

    public void setParent(ProjectVO parent) {
        this.parent = parent;
    }

    public ProjectVO getBuyerProject() {
        return buyerProject;
    }

    public void setBuyerProject(ProjectVO buyerProject) {
        this.buyerProject = buyerProject;
    }

    public List<OrderItemVO> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<OrderItemVO> orderItems) {
        this.orderItems = orderItems;
    }

    public LocalDateTime getOrderSendToSupplierDate() {
        return orderSendToSupplierDate;
    }

    public void setOrderSendToSupplierDate(LocalDateTime orderSendToSupplierDate) {
        this.orderSendToSupplierDate = orderSendToSupplierDate;
    }

    public boolean getIsRejected() {
        return isRejected;
    }

    public void setIsRejected(boolean rejected) {
        isRejected = rejected;
    }

    public boolean getItemizedTaxAndShippingEnabled() {
        return itemizedTaxAndShippingEnabled;
    }

    public void setItemizedTaxAndShippingEnabled(boolean itemizedTaxAndShippingEnabled) {
        this.itemizedTaxAndShippingEnabled = itemizedTaxAndShippingEnabled;
    }
    public TermsVO getBuyerTerms() {
        return this.buyerTerms;
    }

    public void setBuyerTerms(TermsVO buyerTerms) {
        this.buyerTerms = buyerTerms;
    }

    public TermsVO getSupplierTerms() {
        return this.supplierTerms;
    }

    public void setSupplierTerms(TermsVO supplierTerms) {
        this.supplierTerms = supplierTerms;
    }

    public Map getCustomAttributes() {
        return customAttributes;
    }

    public void setCustomAttributes(Map customAttributes) {
        this.customAttributes = customAttributes;
    }

    public boolean getIsEnableComplexVAT() {
        return this.isEnableComplexVAT;
    }

    public void setIsEnableComplexVAT(boolean enableComplexVAT) {
        this.isEnableComplexVAT = enableComplexVAT;
    }

    public List getVatsInfo() {
        return this.vatsInfo;
    }

    public void setVatsInfo(List vatsInfo) {
        this.vatsInfo = vatsInfo;
    }

    public Boolean getCanCloseRfe() {
        return this.canCloseRfe;
    }

    public void setCanCloseRfe(Boolean canCloseRfe) {
        this.canCloseRfe = canCloseRfe;
    }

    public Boolean getCanDismissSupplier() {
        return this.canDismissSupplier;
    }

    public void setCanDismissSupplier(Boolean canDismissSupplier) {
        this.canDismissSupplier = canDismissSupplier;
    }

    public Boolean getIsCloseRfe() {
        return this.isCloseRfe;
    }

    public void setIsCloseRfe(Boolean closeRfe) {
        this.isCloseRfe = closeRfe;
    }

    public Boolean getIsDismissUnselectedSupplier() {
        return this.isDismissUnselectedSupplier;
    }

    public void setIsDismissUnselectedSupplier(Boolean dismissUnselectedSupplier) {
        this.isDismissUnselectedSupplier = dismissUnselectedSupplier;
    }

    public Boolean getDismissPrefValue() {
        return this.dismissPrefValue;
    }

    public void setDismissPrefValue(Boolean dismissPrefValue) {
        this.dismissPrefValue = dismissPrefValue;
    }

    public Boolean getIsSensitive() {
        return this.isSensitive;
    }

    public void setIsSensitive(Boolean sensitive) {
        this.isSensitive = sensitive;
    }

    public Boolean getContractPricingEnabled() {
        return this.contractPricingEnabled;
    }

    public void setContractPricingEnabled(Boolean contractPricingEnabled) {
        this.contractPricingEnabled = contractPricingEnabled;
    }

    public Boolean getIsCompletionDateRequired() {
        return this.isCompletionDateRequired;
    }

    public void setIsCompletionDateRequired(Boolean completionDateRequired) {
        this.isCompletionDateRequired = completionDateRequired;
    }

    public Boolean getEnableSupplierAddPaperDetails() {
        return enableSupplierAddPaperDetails;
    }

    public void setEnableSupplierAddPaperDetails(Boolean enableSupplierAddPaperDetails) {
        this.enableSupplierAddPaperDetails = enableSupplierAddPaperDetails;
    }

    public Boolean getEnableLogistics() {
        return enableLogistics;
    }

    public void setEnableLogistics(Boolean enableLogistics) {
        this.enableLogistics = enableLogistics;
    }

    public Boolean getIsPaperFlow() {
        return isPaperFlow;
    }

    public void setIsPaperFlow(Boolean paperFlow) {
        isPaperFlow = paperFlow;
    }

}
