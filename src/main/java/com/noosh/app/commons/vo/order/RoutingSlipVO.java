package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.BaseVO;

import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 12/22/17
 */
public class RoutingSlipVO extends BaseVO {
    private static final long serialVersionUID = 6581262942609350124L;
    private String routingStatus;

    private String routingStatusStrId;

    private boolean isShowRoutingSection;

    private Map<String, String> approveMap;

    private List<SlipRecipientVO> recipients;

    public String getRoutingStatusStrId() {
        return routingStatusStrId;
    }

    public void setRoutingStatusStrId(String routingStatusStrId) {
        this.routingStatusStrId = routingStatusStrId;
    }

    public String getRoutingStatus() {
        return routingStatus;
    }

    public void setRoutingStatus(String routingStatus) {
        this.routingStatus = routingStatus;
    }

    public boolean getIsShowRoutingSection() {
        return isShowRoutingSection;
    }

    public void setIsShowRoutingSection(boolean showRoutingSection) {
        isShowRoutingSection = showRoutingSection;
    }

    public Map<String, String> getApproveMap() {
        return approveMap;
    }

    public void setApproveMap(Map<String, String> approveMap) {
        this.approveMap = approveMap;
    }

    public List<SlipRecipientVO> getRecipients() {
        return recipients;
    }

    public void setRecipients(List<SlipRecipientVO> recipients) {
        this.recipients = recipients;
    }
}
