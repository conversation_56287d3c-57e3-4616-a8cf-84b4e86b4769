package com.noosh.app.commons.vo.order;

import java.math.BigDecimal;
import java.util.List;

/**
 * User: <PERSON>
 * Date: 6/21/18
 */
public class AggregatedOrderDetailVO extends OrderGeneralInfoVO {

    private static final long serialVersionUID = -8023849595957690787L;

    private String parentOrderNumber;

    private double parentOversPercent;

    private double parentUndersPercent;

    private BigDecimal miscCost;
    private Long miscCostCurrencyId;

    private String parentOrderReference;

    private boolean enableCOReason;

    private boolean parentItemizedTaxAndShippingEnabled;

    private boolean parentCompletedAcceptedClosingChangeOrder;

    private boolean parentAccepted;

    private BigDecimal parentTax;
    private Long parentTaxCurrencyId;

    private BigDecimal parentShipping;
    private Long parentShippingCurrencyId;

    private BigDecimal orderItemDiscountSurchargeTotal;
    private Long orderItemDiscountSurchargeTotalCurrencyId;

    private BigDecimal discountOrSurchargeTotal;
    private Long discountOrSurchargeTotalCurrencyId;

    private BigDecimal parentOrderItemDiscountSurchargeTotal;
    private Long parentOrderItemDiscountSurchargeTotalCurrencyId;

    private BigDecimal parentDiscountOrSurchargeTotal;
    private Long parentDiscountOrSurchargeTotalCurrencyId;

    private BigDecimal parentDiscountOrSurcharge;
    private Long parentDiscountOrSurchargeCurrencyId;

    private BigDecimal parentSubTotal;
    private Long parentSubTotalCurrencyId;

    private BigDecimal parentGrandTotal;
    private Long parentGrandTotalCurrencyId;

    //dual currency
    private BigDecimal exMiscCost;
    private Long exMiscCostCurrencyId;

    private BigDecimal parentExTax;
    private Long parentExTaxCurrencyId;

    private BigDecimal parentExShipping;
    private Long parentExShippingCurrencyId;

    private BigDecimal exOrderItemDiscountSurchargeTotal;
    private Long exOrderItemDiscountSurchargeTotalCurrencyId;

    private BigDecimal exDiscountOrSurchargeTotal;
    private Long exDiscountOrSurchargeTotalCurrencyId;

    private BigDecimal parentExOrderItemDiscountSurchargeTotal;
    private Long parentExOrderItemDiscountSurchargeTotalCurrencyId;

    private BigDecimal parentExDiscountOrSurchargeTotal;
    private Long parentExDiscountOrSurchargeTotalCurrencyId;

    private BigDecimal parentExDiscountOrSurcharge;
    private Long parentExDiscountOrSurchargeCurrencyId;

    private BigDecimal parentExSubTotal;
    private Long parentExSubTotalCurrencyId;

    private BigDecimal parentExGrandTotal;
    private Long parentExGrandTotalCurrencyId;

    private String parentValueCurrency;

    private List<OrderItemVO> parentOrderItems;

    private boolean requiredApprovalInvoice;

    private boolean autoAcceptFinalInvoice;

    private boolean invoiceSentOnClosedOrder;

    private Boolean hasPendingChangeOrder;

    private BigDecimal pendingChangeGrandTotal;

    private Long pendingChangeGrandTotalCurrencyId;

    private BigDecimal exPendingChangeGrandTotal;

    private Long exPendingChangeGrandTotalCurrencyId;

    public BigDecimal getParentGrandTotal() {
        return parentGrandTotal;
    }

    public void setParentGrandTotal(BigDecimal parentGrandTotal) {
        this.parentGrandTotal = parentGrandTotal;
    }

    public BigDecimal getParentSubTotal() {
        return parentSubTotal;
    }

    public void setParentSubTotal(BigDecimal parentSubTotal) {
        this.parentSubTotal = parentSubTotal;
    }

    public boolean isParentCompletedAcceptedClosingChangeOrder() {
        return parentCompletedAcceptedClosingChangeOrder;
    }

    public void setParentCompletedAcceptedClosingChangeOrder(boolean parentCompletedAcceptedClosingChangeOrder) {
        this.parentCompletedAcceptedClosingChangeOrder = parentCompletedAcceptedClosingChangeOrder;
    }

    public boolean isParentAccepted() {
        return parentAccepted;
    }

    public void setParentAccepted(boolean parentAccepted) {
        this.parentAccepted = parentAccepted;
    }

    public BigDecimal getParentTax() {
        return parentTax;
    }

    public void setParentTax(BigDecimal parentTax) {
        this.parentTax = parentTax;
    }

    public BigDecimal getParentShipping() {
        return parentShipping;
    }

    public void setParentShipping(BigDecimal parentShipping) {
        this.parentShipping = parentShipping;
    }

    public BigDecimal getParentOrderItemDiscountSurchargeTotal() {
        return parentOrderItemDiscountSurchargeTotal;
    }

    public void setParentOrderItemDiscountSurchargeTotal(BigDecimal parentOrderItemDiscountSurchargeTotal) {
        this.parentOrderItemDiscountSurchargeTotal = parentOrderItemDiscountSurchargeTotal;
    }

    public BigDecimal getParentDiscountOrSurchargeTotal() {
        return parentDiscountOrSurchargeTotal;
    }

    public void setParentDiscountOrSurchargeTotal(BigDecimal parentDiscountOrSurchargeTotal) {
        this.parentDiscountOrSurchargeTotal = parentDiscountOrSurchargeTotal;
    }

    public BigDecimal getParentDiscountOrSurcharge() {
        return parentDiscountOrSurcharge;
    }

    public void setParentDiscountOrSurcharge(BigDecimal parentDiscountOrSurcharge) {
        this.parentDiscountOrSurcharge = parentDiscountOrSurcharge;
    }

    public String getParentValueCurrency() {
        return parentValueCurrency;
    }

    public void setParentValueCurrency(String parentValueCurrency) {
        this.parentValueCurrency = parentValueCurrency;
    }

    public Long getMiscCostCurrencyId() {
        return this.miscCostCurrencyId;
    }

    public void setMiscCostCurrencyId(Long miscCostCurrencyId) {
        this.miscCostCurrencyId = miscCostCurrencyId;
    }

    public Long getParentTaxCurrencyId() {
        return this.parentTaxCurrencyId;
    }

    public void setParentTaxCurrencyId(Long parentTaxCurrencyId) {
        this.parentTaxCurrencyId = parentTaxCurrencyId;
    }

    public Long getParentShippingCurrencyId() {
        return this.parentShippingCurrencyId;
    }

    public void setParentShippingCurrencyId(Long parentShippingCurrencyId) {
        this.parentShippingCurrencyId = parentShippingCurrencyId;
    }

    @Override
    public Long getOrderItemDiscountSurchargeTotalCurrencyId() {
        return this.orderItemDiscountSurchargeTotalCurrencyId;
    }

    @Override
    public void setOrderItemDiscountSurchargeTotalCurrencyId(Long orderItemDiscountSurchargeTotalCurrencyId) {
        this.orderItemDiscountSurchargeTotalCurrencyId = orderItemDiscountSurchargeTotalCurrencyId;
    }

    @Override
    public Long getDiscountOrSurchargeTotalCurrencyId() {
        return this.discountOrSurchargeTotalCurrencyId;
    }

    @Override
    public void setDiscountOrSurchargeTotalCurrencyId(Long discountOrSurchargeTotalCurrencyId) {
        this.discountOrSurchargeTotalCurrencyId = discountOrSurchargeTotalCurrencyId;
    }

    public Long getParentOrderItemDiscountSurchargeTotalCurrencyId() {
        return this.parentOrderItemDiscountSurchargeTotalCurrencyId;
    }

    public void setParentOrderItemDiscountSurchargeTotalCurrencyId(Long parentOrderItemDiscountSurchargeTotalCurrencyId) {
        this.parentOrderItemDiscountSurchargeTotalCurrencyId = parentOrderItemDiscountSurchargeTotalCurrencyId;
    }

    public Long getParentDiscountOrSurchargeTotalCurrencyId() {
        return this.parentDiscountOrSurchargeTotalCurrencyId;
    }

    public void setParentDiscountOrSurchargeTotalCurrencyId(Long parentDiscountOrSurchargeTotalCurrencyId) {
        this.parentDiscountOrSurchargeTotalCurrencyId = parentDiscountOrSurchargeTotalCurrencyId;
    }

    public Long getParentDiscountOrSurchargeCurrencyId() {
        return this.parentDiscountOrSurchargeCurrencyId;
    }

    public void setParentDiscountOrSurchargeCurrencyId(Long parentDiscountOrSurchargeCurrencyId) {
        this.parentDiscountOrSurchargeCurrencyId = parentDiscountOrSurchargeCurrencyId;
    }

    public Long getParentSubTotalCurrencyId() {
        return this.parentSubTotalCurrencyId;
    }

    public void setParentSubTotalCurrencyId(Long parentSubTotalCurrencyId) {
        this.parentSubTotalCurrencyId = parentSubTotalCurrencyId;
    }

    public Long getParentGrandTotalCurrencyId() {
        return this.parentGrandTotalCurrencyId;
    }

    public void setParentGrandTotalCurrencyId(Long parentGrandTotalCurrencyId) {
        this.parentGrandTotalCurrencyId = parentGrandTotalCurrencyId;
    }

    public BigDecimal getExMiscCost() {
        return this.exMiscCost;
    }

    public void setExMiscCost(BigDecimal exMiscCost) {
        this.exMiscCost = exMiscCost;
    }

    public Long getExMiscCostCurrencyId() {
        return this.exMiscCostCurrencyId;
    }

    public void setExMiscCostCurrencyId(Long exMiscCostCurrencyId) {
        this.exMiscCostCurrencyId = exMiscCostCurrencyId;
    }

    public BigDecimal getParentExTax() {
        return this.parentExTax;
    }

    public void setParentExTax(BigDecimal parentExTax) {
        this.parentExTax = parentExTax;
    }

    public Long getParentExTaxCurrencyId() {
        return this.parentExTaxCurrencyId;
    }

    public void setParentExTaxCurrencyId(Long parentExTaxCurrencyId) {
        this.parentExTaxCurrencyId = parentExTaxCurrencyId;
    }

    public BigDecimal getParentExShipping() {
        return this.parentExShipping;
    }

    public void setParentExShipping(BigDecimal parentExShipping) {
        this.parentExShipping = parentExShipping;
    }

    public Long getParentExShippingCurrencyId() {
        return this.parentExShippingCurrencyId;
    }

    public void setParentExShippingCurrencyId(Long parentExShippingCurrencyId) {
        this.parentExShippingCurrencyId = parentExShippingCurrencyId;
    }

    @Override
    public BigDecimal getExOrderItemDiscountSurchargeTotal() {
        return this.exOrderItemDiscountSurchargeTotal;
    }

    @Override
    public void setExOrderItemDiscountSurchargeTotal(BigDecimal exOrderItemDiscountSurchargeTotal) {
        this.exOrderItemDiscountSurchargeTotal = exOrderItemDiscountSurchargeTotal;
    }

    @Override
    public Long getExOrderItemDiscountSurchargeTotalCurrencyId() {
        return this.exOrderItemDiscountSurchargeTotalCurrencyId;
    }

    @Override
    public void setExOrderItemDiscountSurchargeTotalCurrencyId(Long exOrderItemDiscountSurchargeTotalCurrencyId) {
        this.exOrderItemDiscountSurchargeTotalCurrencyId = exOrderItemDiscountSurchargeTotalCurrencyId;
    }

    @Override
    public BigDecimal getExDiscountOrSurchargeTotal() {
        return this.exDiscountOrSurchargeTotal;
    }

    @Override
    public void setExDiscountOrSurchargeTotal(BigDecimal exDiscountOrSurchargeTotal) {
        this.exDiscountOrSurchargeTotal = exDiscountOrSurchargeTotal;
    }

    @Override
    public Long getExDiscountOrSurchargeTotalCurrencyId() {
        return this.exDiscountOrSurchargeTotalCurrencyId;
    }

    @Override
    public void setExDiscountOrSurchargeTotalCurrencyId(Long exDiscountOrSurchargeTotalCurrencyId) {
        this.exDiscountOrSurchargeTotalCurrencyId = exDiscountOrSurchargeTotalCurrencyId;
    }

    public BigDecimal getParentExOrderItemDiscountSurchargeTotal() {
        return this.parentExOrderItemDiscountSurchargeTotal;
    }

    public void setParentExOrderItemDiscountSurchargeTotal(BigDecimal parentExOrderItemDiscountSurchargeTotal) {
        this.parentExOrderItemDiscountSurchargeTotal = parentExOrderItemDiscountSurchargeTotal;
    }

    public Long getParentExOrderItemDiscountSurchargeTotalCurrencyId() {
        return this.parentExOrderItemDiscountSurchargeTotalCurrencyId;
    }

    public void setParentExOrderItemDiscountSurchargeTotalCurrencyId(Long parentExOrderItemDiscountSurchargeTotalCurrencyId) {
        this.parentExOrderItemDiscountSurchargeTotalCurrencyId = parentExOrderItemDiscountSurchargeTotalCurrencyId;
    }

    public BigDecimal getParentExDiscountOrSurchargeTotal() {
        return this.parentExDiscountOrSurchargeTotal;
    }

    public void setParentExDiscountOrSurchargeTotal(BigDecimal parentExDiscountOrSurchargeTotal) {
        this.parentExDiscountOrSurchargeTotal = parentExDiscountOrSurchargeTotal;
    }

    public Long getParentExDiscountOrSurchargeTotalCurrencyId() {
        return this.parentExDiscountOrSurchargeTotalCurrencyId;
    }

    public void setParentExDiscountOrSurchargeTotalCurrencyId(Long parentExDiscountOrSurchargeTotalCurrencyId) {
        this.parentExDiscountOrSurchargeTotalCurrencyId = parentExDiscountOrSurchargeTotalCurrencyId;
    }

    public BigDecimal getParentExDiscountOrSurcharge() {
        return this.parentExDiscountOrSurcharge;
    }

    public void setParentExDiscountOrSurcharge(BigDecimal parentExDiscountOrSurcharge) {
        this.parentExDiscountOrSurcharge = parentExDiscountOrSurcharge;
    }

    public Long getParentExDiscountOrSurchargeCurrencyId() {
        return this.parentExDiscountOrSurchargeCurrencyId;
    }

    public void setParentExDiscountOrSurchargeCurrencyId(Long parentExDiscountOrSurchargeCurrencyId) {
        this.parentExDiscountOrSurchargeCurrencyId = parentExDiscountOrSurchargeCurrencyId;
    }

    public BigDecimal getParentExSubTotal() {
        return this.parentExSubTotal;
    }

    public void setParentExSubTotal(BigDecimal parentExSubTotal) {
        this.parentExSubTotal = parentExSubTotal;
    }

    public Long getParentExSubTotalCurrencyId() {
        return this.parentExSubTotalCurrencyId;
    }

    public void setParentExSubTotalCurrencyId(Long parentExSubTotalCurrencyId) {
        this.parentExSubTotalCurrencyId = parentExSubTotalCurrencyId;
    }

    public BigDecimal getParentExGrandTotal() {
        return this.parentExGrandTotal;
    }

    public void setParentExGrandTotal(BigDecimal parentExGrandTotal) {
        this.parentExGrandTotal = parentExGrandTotal;
    }

    public Long getParentExGrandTotalCurrencyId() {
        return this.parentExGrandTotalCurrencyId;
    }

    public void setParentExGrandTotalCurrencyId(Long parentExGrandTotalCurrencyId) {
        this.parentExGrandTotalCurrencyId = parentExGrandTotalCurrencyId;
    }

    public boolean isInvoiceSentOnClosedOrder() {
        return invoiceSentOnClosedOrder;
    }

    public void setInvoiceSentOnClosedOrder(boolean invoiceSentOnClosedOrder) {
        this.invoiceSentOnClosedOrder = invoiceSentOnClosedOrder;
    }

    public boolean isRequiredApprovalInvoice() {
        return requiredApprovalInvoice;
    }

    public void setRequiredApprovalInvoice(boolean requiredApprovalInvoice) {
        this.requiredApprovalInvoice = requiredApprovalInvoice;
    }

    public boolean isAutoAcceptFinalInvoice() {
        return autoAcceptFinalInvoice;
    }

    public void setAutoAcceptFinalInvoice(boolean autoAcceptFinalInvoice) {
        this.autoAcceptFinalInvoice = autoAcceptFinalInvoice;
    }

    public String getParentOrderNumber() {
        return parentOrderNumber;
    }

    public void setParentOrderNumber(String parentOrderNumber) {
        this.parentOrderNumber = parentOrderNumber;
    }

    public double getParentOversPercent() {
        return parentOversPercent;
    }

    public void setParentOversPercent(double parentOversPercent) {
        this.parentOversPercent = parentOversPercent;
    }

    public double getParentUndersPercent() {
        return parentUndersPercent;
    }

    public void setParentUndersPercent(double parentUndersPercent) {
        this.parentUndersPercent = parentUndersPercent;
    }

    public BigDecimal getMiscCost() {
        return miscCost;
    }

    public void setMiscCost(BigDecimal miscCost) {
        this.miscCost = miscCost;
    }

    public String getParentOrderReference() {
        return parentOrderReference;
    }

    public void setParentOrderReference(String parentOrderReference) {
        this.parentOrderReference = parentOrderReference;
    }

    public boolean isEnableCOReason() {
        return enableCOReason;
    }

    public void setEnableCOReason(boolean enableCOReason) {
        this.enableCOReason = enableCOReason;
    }

    public boolean isParentItemizedTaxAndShippingEnabled() {
        return parentItemizedTaxAndShippingEnabled;
    }

    public void setParentItemizedTaxAndShippingEnabled(boolean parentItemizedTaxAndShippingEnabled) {
        this.parentItemizedTaxAndShippingEnabled = parentItemizedTaxAndShippingEnabled;
    }

    public BigDecimal getOrderItemDiscountSurchargeTotal() {
        return orderItemDiscountSurchargeTotal;
    }

    public void setOrderItemDiscountSurchargeTotal(BigDecimal orderItemDiscountSurchargeTotal) {
        this.orderItemDiscountSurchargeTotal = orderItemDiscountSurchargeTotal;
    }

    public BigDecimal getDiscountOrSurchargeTotal() {
        return discountOrSurchargeTotal;
    }

    public void setDiscountOrSurchargeTotal(BigDecimal discountOrSurchargeTotal) {
        this.discountOrSurchargeTotal = discountOrSurchargeTotal;
    }

    public List<OrderItemVO> getParentOrderItems() {
        return parentOrderItems;
    }

    public void setParentOrderItems(List<OrderItemVO> parentOrderItems) {
        this.parentOrderItems = parentOrderItems;
    }

    public Boolean getHasPendingChangeOrder() {
        return hasPendingChangeOrder;
    }

    public void setHasPendingChangeOrder(Boolean hasPendingChangeOrder) {
        this.hasPendingChangeOrder = hasPendingChangeOrder;
    }

    public BigDecimal getPendingChangeGrandTotal() {
        return pendingChangeGrandTotal;
    }

    public void setPendingChangeGrandTotal(BigDecimal pendingChangeGrandTotal) {
        this.pendingChangeGrandTotal = pendingChangeGrandTotal;
    }

    public Long getPendingChangeGrandTotalCurrencyId() {
        return pendingChangeGrandTotalCurrencyId;
    }

    public void setPendingChangeGrandTotalCurrencyId(Long pendingChangeGrandTotalCurrencyId) {
        this.pendingChangeGrandTotalCurrencyId = pendingChangeGrandTotalCurrencyId;
    }

    public BigDecimal getExPendingChangeGrandTotal() {
        return exPendingChangeGrandTotal;
    }

    public void setExPendingChangeGrandTotal(BigDecimal exPendingChangeGrandTotal) {
        this.exPendingChangeGrandTotal = exPendingChangeGrandTotal;
    }

    public Long getExPendingChangeGrandTotalCurrencyId() {
        return exPendingChangeGrandTotalCurrencyId;
    }

    public void setExPendingChangeGrandTotalCurrencyId(Long exPendingChangeGrandTotalCurrencyId) {
        this.exPendingChangeGrandTotalCurrencyId = exPendingChangeGrandTotalCurrencyId;
    }
}

