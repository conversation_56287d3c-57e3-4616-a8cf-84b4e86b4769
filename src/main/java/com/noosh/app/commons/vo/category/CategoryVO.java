package com.noosh.app.commons.vo.category;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 10/31/2021
 */
public class CategoryVO {

    private Long categoryId;
    private String name;
    private String description;
    private String upCategoryExternalLink;
    private String downCategoryExternalLink;
    private String editCategoryExternalLink;
    private String deleteCategoryExternalLink;

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUpCategoryExternalLink() {
        return upCategoryExternalLink;
    }

    public void setUpCategoryExternalLink(String upCategoryExternalLink) {
        this.upCategoryExternalLink = upCategoryExternalLink;
    }

    public String getDownCategoryExternalLink() {
        return downCategoryExternalLink;
    }

    public void setDownCategoryExternalLink(String downCategoryExternalLink) {
        this.downCategoryExternalLink = downCategoryExternalLink;
    }

    public String getEditCategoryExternalLink() {
        return editCategoryExternalLink;
    }

    public void setEditCategoryExternalLink(String editCategoryExternalLink) {
        this.editCategoryExternalLink = editCategoryExternalLink;
    }

    public String getDeleteCategoryExternalLink() {
        return deleteCategoryExternalLink;
    }

    public void setDeleteCategoryExternalLink(String deleteCategoryExternalLink) {
        this.deleteCategoryExternalLink = deleteCategoryExternalLink;
    }
}
