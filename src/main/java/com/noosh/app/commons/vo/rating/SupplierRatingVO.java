package com.noosh.app.commons.vo.rating;

import com.noosh.app.commons.vo.BaseVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * User: leilaz
 * Date: 1/20/19
 */
public class SupplierRatingVO extends BaseVO {
    private String ratingComment;
    private Long averageGrade;
    private Integer granularity;
    private boolean NaAllowed;
    private LocalDateTime completionDate;
    private List<SupplierRatingItemVO> ratingItem;

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public String getRatingComment() {
        return ratingComment;
    }

    public void setRatingComment(String ratingComment) {
        this.ratingComment = ratingComment;
    }

    public Long getAverageGrade() {
        return averageGrade;
    }

    public void setAverageGrade(Long averageGrade) {
        this.averageGrade = averageGrade;
    }

    public Integer getGranularity() {
        return granularity;
    }

    public void setGranularity(Integer granularity) {
        this.granularity = granularity;
    }

    public boolean isNaAllowed() {
        return NaAllowed;
    }

    public void setNaAllowed(boolean naAllowed) {
        NaAllowed = naAllowed;
    }

    public List<SupplierRatingItemVO> getRatingItem() {
        return ratingItem;
    }

    public void setRatingItem(List<SupplierRatingItemVO> ratingItem) {
        this.ratingItem = ratingItem;
    }
}
