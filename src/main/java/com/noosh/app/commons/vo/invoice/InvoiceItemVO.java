package com.noosh.app.commons.vo.invoice;

import com.noosh.app.commons.dto.invoice.RequestDTO;
import com.noosh.app.commons.vo.breakout.InvoiceBreakoutVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther mario
 * @date 7/22/2020
 */
@Schema(description = "Invoice item")
public class InvoiceItemVO {

    private Long id;
    @Schema(description = "Spec icon")
    private String icon;
    @Schema(description = "Spec title")
    private String title;
    @Schema(description = "Job id")
    private Long jobId;
    @Schema(description = "View spec link")
    private String viewSpecLink;
    @Schema(description = "show shipment records according to invoice item id")
    private String associatedShipmentRecords;
    @Schema(description = "Price breakouts list")
    private List<InvoiceBreakoutVO> breakouts;
    @Schema(description = "Item total quantity")
    private InvoiceItemPriceVO totalQty;
    @Schema(description = "Item cost")
    private InvoiceItemPriceVO itemCost;
    @Schema(description = "Item tax")
    private InvoiceItemPriceVO itemTax;
    @Schema(description = "Item shipping")
    private InvoiceItemPriceVO itemShipping;
    @Schema(description = "Item sub total")
    private InvoiceItemPriceVO itemSubTotal;
    @Schema(description = "Item discount or surcharge")
    private InvoiceItemPriceVO discountOrSurcharge;
    private Long breakoutUnitsStrId;
    private Long breakoutRatesStrId;
    private Boolean isTimeMaterials;
    @Schema(description = "PropertyId to get user fields")
    private Long customPropertyId;
    private Map customAttributes = new HashMap();

    private List<DropdownVO<Long>> shipmentRecords;
    private List<RequestDTO> shipmentRecordsWithQty;
    private List<Long> selectedShipmentRecordIds;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getViewSpecLink() {
        return viewSpecLink;
    }

    public void setViewSpecLink(String viewSpecLink) {
        this.viewSpecLink = viewSpecLink;
    }

    public String getAssociatedShipmentRecords() {
        return associatedShipmentRecords;
    }

    public void setAssociatedShipmentRecords(String associatedShipmentRecords) {
        this.associatedShipmentRecords = associatedShipmentRecords;
    }

    public List<RequestDTO> getShipmentRecordsWithQty() {
        return shipmentRecordsWithQty;
    }

    public void setShipmentRecordsWithQty(List<RequestDTO> shipmentRecordsWithQty) {
        this.shipmentRecordsWithQty = shipmentRecordsWithQty;
    }

    public List<InvoiceBreakoutVO> getBreakouts() {
        return breakouts;
    }

    public void setBreakouts(List<InvoiceBreakoutVO> breakouts) {
        this.breakouts = breakouts;
    }

    public InvoiceItemPriceVO getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(InvoiceItemPriceVO totalQty) {
        this.totalQty = totalQty;
    }

    public InvoiceItemPriceVO getItemCost() {
        return itemCost;
    }

    public void setItemCost(InvoiceItemPriceVO itemCost) {
        this.itemCost = itemCost;
    }

    public InvoiceItemPriceVO getItemTax() {
        return itemTax;
    }

    public void setItemTax(InvoiceItemPriceVO itemTax) {
        this.itemTax = itemTax;
    }

    public InvoiceItemPriceVO getItemShipping() {
        return itemShipping;
    }

    public void setItemShipping(InvoiceItemPriceVO itemShipping) {
        this.itemShipping = itemShipping;
    }

    public InvoiceItemPriceVO getItemSubTotal() {
        return itemSubTotal;
    }

    public void setItemSubTotal(InvoiceItemPriceVO itemSubTotal) {
        this.itemSubTotal = itemSubTotal;
    }

    public InvoiceItemPriceVO getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(InvoiceItemPriceVO discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public Long getBreakoutUnitsStrId() {
        return breakoutUnitsStrId;
    }

    public void setBreakoutUnitsStrId(Long breakoutUnitsStrId) {
        this.breakoutUnitsStrId = breakoutUnitsStrId;
    }

    public Long getBreakoutRatesStrId() {
        return breakoutRatesStrId;
    }

    public void setBreakoutRatesStrId(Long breakoutRatesStrId) {
        this.breakoutRatesStrId = breakoutRatesStrId;
    }

    public Boolean getTimeMaterials() {
        return isTimeMaterials;
    }

    public void setTimeMaterials(Boolean timeMaterials) {
        isTimeMaterials = timeMaterials;
    }

    public List<DropdownVO<Long>> getShipmentRecords() {
        return shipmentRecords;
    }

    public void setShipmentRecords(List<DropdownVO<Long>> shipmentRecords) {
        this.shipmentRecords = shipmentRecords;
    }

    public List<Long> getSelectedShipmentRecordIds() {
        return selectedShipmentRecordIds;
    }

    public void setSelectedShipmentRecordIds(List<Long> selectedShipmentRecordIds) {
        this.selectedShipmentRecordIds = selectedShipmentRecordIds;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Map getCustomAttributes() {
        return customAttributes;
    }

    public void setCustomAttributes(Map customAttributes) {
        this.customAttributes = customAttributes;
    }
}
