package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * User: leilaz
 * Date: 9/26/17
 */
public class OrderStateVO extends BaseVO {
    private String description;

    private String descriptionStrId;

    private String comments;

    @Schema(description = "Status format; status_draft, status_sent, status_rejected, status_normal")
    private String statusFormat;

    public String getStatusFormat() {
        return statusFormat;
    }

    public void setStatusFormat(String statusFormat) {
        this.statusFormat = statusFormat;
    }

    public String getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(String descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
}
