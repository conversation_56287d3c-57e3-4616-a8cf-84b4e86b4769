package com.noosh.app.commons.vo.invoice;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * @auther mario
 * @date 7/22/2020
 */
@Schema(description = "Invoice info")
public class InvoiceInfoVO {
    @Schema(description = "Project number")
    private String projectNo;
    @Schema(description = "Project name")
    private String projectName;
    @Schema(description = "Order reference")
    private String orderRef;
    @Schema(description = "Order title")
    private String orderTitle;
    @Schema(description = "Order Id")
    private Long orderId;
    @Schema(description = "Order type Id")
    private Long orderTypeId;
    @Schema(description = "Buy workgroup id")
    private Long buyWorkgroupId;
    @Schema(description = "Order payment method")
    private String orderPaymentMethod;
    private String orderPaymentMethodStrId;
    @Schema(description = "Order payment reference")
    private String orderPaymentRefNo;
    @Schema(description = "Invoice to; line1, line2, line3, city, state, postal, country")
    private String invoiceTo;
    private String invoiceToWithoutCountry;
    private String invoiceCountryNameStrId;
    @Schema(description = "Invoice prepare by")
    private String invoicePrepareBy;
    private String invoicePrepareByWithoutCountry;
    private String invoicePrepareByNameStrId;
    @Schema(description = "Non-billable Reason")
    private String nonBillableReason;
    private String nonBillableReasonStrId;
    @Schema(description = "Invoice no")
    private String invoiceNo;
    @Schema(description = "Invoice reference")
    private String invoiceRefNo;
    @Schema(description = "Invoice date")
    private LocalDateTime invoiceDate;
    @Schema(description = "Invoice due date")
    private LocalDateTime invoiceDueDate;
    @Schema(description = "Invoice status, Draft=2500088, Pending Buyer Acceptance=2500089, Accepted=2500090, Rejected=2500091, Retracted=2500092")
    private String invoiceStatus;
    private String invoiceStatusStrId;
    @Schema(description = "Invoice formatted status")
    private String invoiceFormattedStatus;
    @Schema(description = "Invoice submit date")
    private LocalDateTime invoiceSubmitDate;
    @Schema(description = "Invoice update date")
    private LocalDateTime invoiceUpdateDate;
    @Schema(description = "Invoice accept date")
    private LocalDateTime invoiceAcceptDate;
    @Schema(description = "Invoice state change comment")
    private String invoiceReasons;
    @Schema(description = "Invoice comments")
    private String invoiceComments;
    @Schema(description = "Creator workgroup name")
    private String creatorWrokgroupName;
    @Schema(description = "Creator company logo")
    private String creatorCompanyLogo;
    private Boolean isPending;
    private Boolean isApproved;

    public Long getBuyWorkgroupId() {
        return buyWorkgroupId;
    }

    public void setBuyWorkgroupId(Long buyWorkgroupId) {
        this.buyWorkgroupId = buyWorkgroupId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Boolean getIsPending() {
        return isPending;
    }

    public void setIsPending(Boolean pending) {
        isPending = pending;
    }

    public Boolean getIsApproved() {
        return isApproved;
    }

    public void setIsApproved(Boolean approved) {
        isApproved = approved;
    }

    public String getInvoicePrepareByNameStrId() {
        return invoicePrepareByNameStrId;
    }

    public void setInvoicePrepareByNameStrId(String invoicePrepareByNameStrId) {
        this.invoicePrepareByNameStrId = invoicePrepareByNameStrId;
    }

    public String getInvoicePrepareByWithoutCountry() {
        return invoicePrepareByWithoutCountry;
    }

    public void setInvoicePrepareByWithoutCountry(String invoicePrepareByWithoutCountry) {
        this.invoicePrepareByWithoutCountry = invoicePrepareByWithoutCountry;
    }

    public String getInvoiceToWithoutCountry() {
        return invoiceToWithoutCountry;
    }

    public void setInvoiceToWithoutCountry(String invoiceToWithoutCountry) {
        this.invoiceToWithoutCountry = invoiceToWithoutCountry;
    }

    public String getNonBillableReasonStrId() {
        return nonBillableReasonStrId;
    }

    public void setNonBillableReasonStrId(String nonBillableReasonStrId) {
        this.nonBillableReasonStrId = nonBillableReasonStrId;
    }

    public String getInvoiceStatusStrId() {
        return invoiceStatusStrId;
    }

    public void setInvoiceStatusStrId(String invoiceStatusStrId) {
        this.invoiceStatusStrId = invoiceStatusStrId;
    }

    public String getInvoiceCountryNameStrId() {
        return invoiceCountryNameStrId;
    }

    public void setInvoiceCountryNameStrId(String invoiceCountryNameStrId) {
        this.invoiceCountryNameStrId = invoiceCountryNameStrId;
    }

    public String getOrderPaymentMethodStrId() {
        return orderPaymentMethodStrId;
    }

    public void setOrderPaymentMethodStrId(String orderPaymentMethodStrId) {
        this.orderPaymentMethodStrId = orderPaymentMethodStrId;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getOrderRef() {
        return orderRef;
    }

    public void setOrderRef(String orderRef) {
        this.orderRef = orderRef;
    }

    public String getOrderTitle() {
        return orderTitle;
    }

    public void setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle;
    }

    public String getOrderPaymentMethod() {
        return orderPaymentMethod;
    }

    public void setOrderPaymentMethod(String orderPaymentMethod) {
        this.orderPaymentMethod = orderPaymentMethod;
    }

    public String getOrderPaymentRefNo() {
        return orderPaymentRefNo;
    }

    public void setOrderPaymentRefNo(String orderPaymentRefNo) {
        this.orderPaymentRefNo = orderPaymentRefNo;
    }

    public String getInvoiceTo() {
        return invoiceTo;
    }

    public void setInvoiceTo(String invoiceTo) {
        this.invoiceTo = invoiceTo;
    }

    public String getInvoicePrepareBy() {
        return invoicePrepareBy;
    }

    public void setInvoicePrepareBy(String invoicePrepareBy) {
        this.invoicePrepareBy = invoicePrepareBy;
    }

    public String getNonBillableReason() {
        return nonBillableReason;
    }

    public void setNonBillableReason(String nonBillableReason) {
        this.nonBillableReason = nonBillableReason;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceRefNo() {
        return invoiceRefNo;
    }

    public void setInvoiceRefNo(String invoiceRefNo) {
        this.invoiceRefNo = invoiceRefNo;
    }

    public LocalDateTime getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(LocalDateTime invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public LocalDateTime getInvoiceDueDate() {
        return invoiceDueDate;
    }

    public void setInvoiceDueDate(LocalDateTime invoiceDueDate) {
        this.invoiceDueDate = invoiceDueDate;
    }

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public String getInvoiceFormattedStatus() {
        return invoiceFormattedStatus;
    }

    public void setInvoiceFormattedStatus(String invoiceFormattedStatus) {
        this.invoiceFormattedStatus = invoiceFormattedStatus;
    }

    public LocalDateTime getInvoiceSubmitDate() {
        return invoiceSubmitDate;
    }

    public void setInvoiceSubmitDate(LocalDateTime invoiceSubmitDate) {
        this.invoiceSubmitDate = invoiceSubmitDate;
    }

    public LocalDateTime getInvoiceUpdateDate() {
        return invoiceUpdateDate;
    }

    public void setInvoiceUpdateDate(LocalDateTime invoiceUpdateDate) {
        this.invoiceUpdateDate = invoiceUpdateDate;
    }

    public LocalDateTime getInvoiceAcceptDate() {
        return invoiceAcceptDate;
    }

    public void setInvoiceAcceptDate(LocalDateTime invoiceAcceptDate) {
        this.invoiceAcceptDate = invoiceAcceptDate;
    }

    public String getInvoiceReasons() {
        return invoiceReasons;
    }

    public void setInvoiceReasons(String invoiceReasons) {
        this.invoiceReasons = invoiceReasons;
    }

    public String getInvoiceComments() {
        return invoiceComments;
    }

    public void setInvoiceComments(String invoiceComments) {
        this.invoiceComments = invoiceComments;
    }

    public String getCreatorWrokgroupName() {
        return creatorWrokgroupName;
    }

    public void setCreatorWrokgroupName(String creatorWrokgroupName) {
        this.creatorWrokgroupName = creatorWrokgroupName;
    }

    public String getCreatorCompanyLogo() {
        return creatorCompanyLogo;
    }

    public void setCreatorCompanyLogo(String creatorCompanyLogo) {
        this.creatorCompanyLogo = creatorCompanyLogo;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }
}
