package com.noosh.app.commons.entity.rating;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 1/20/19
 */
@Entity
@Table(name = "SR_SECTION")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SrSection extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SrSection_id_generated")
    @SequenceGenerator(name = "SrSection_id_generated", sequenceName = "SR_SECTION_SEQ", allocationSize = 1)
    @Column(name = "SR_SECTION_ID")
    private Long id;

    @Column(name = "TITLE")
    private String title;

    @Column(name = "DISPLAY_ORDER")
    private Long displayOrder;

    @Column(name = "DESCRIPTION_STR_ID")
    private Long strId;

    @Column(name = "DESCRIPTION_STR")
    private String str;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Long displayOrder) {
        this.displayOrder = displayOrder;
    }

    public Long getStrId() {
        return strId;
    }

    public void setStrId(Long strId) {
        this.strId = strId;
    }

    public String getStr() {
        return str;
    }

    public void setStr(String str) {
        this.str = str;
    }
}
