package com.noosh.app.commons.entity.invoice;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: lukez
 * @Date: 12/28/2015
 */
@Entity
@Table(name = "PC_INVOICE_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class InvoiceItem extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "PC_INVOICE_ITEM_ID")
    private Long id;

    @Column(name = "PC_INVOICE_ID")
    private Long invoiceId;

    @Column(name = "SP_SPEC_ID")
    private Long specId;

    @Column(name = "PC_JOB_ID")
    private Long jobId;

    @Column(name = "QUANTITY")
    private BigDecimal quantity;

    @Column(name = "AMOUNT")
    private BigDecimal amount;

    @Column(name = "AMOUNT_AC_CURRENCY_ID")
    private Long amountCurrencyId;

    @Column(name = "INCLUDE_BREAKOUTS")
    private Boolean includeBreakouts;

    @Column(name = "PC_BREAKOUT_TYPE_ID")
    private Long pcBreakoutTypeId;

    @Column(name = "ITEM_INDEX")
    private Long itemIndex;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "D_OR_S")
    private BigDecimal discountOrSurcharge;

    @Column(name = "D_OR_S_AC_CURRENCY_ID")
    private Long discountOrSurchargeCurrencyId;

    @Column(name = "TAX")
    private BigDecimal tax;

    @Column(name = "TAX_AC_CURRENCY_ID")
    private Long taxCurrencyId;

    @Column(name = "SHIPPING")
    private BigDecimal shipping;

    @Column(name = "SHIPPING_AC_CURRENCY_ID")
    private Long shippingCurrencyId;

    //Dual currency related fields
    @Column(name = "EXAMOUNT")
    private BigDecimal exAmount;

    @Column(name = "EXAMOUNT_AC_CURRENCY_ID")
    private Long exAmountCurrencyId;

    @Column(name = "EXTAX")
    private BigDecimal exTax;

    @Column(name = "EXTAX_AC_CURRENCY_ID")
    private Long exTaxCurrencyId;

    @Column(name = "EXSHIPPING")
    private BigDecimal exShipping;

    @Column(name = "EXSHIPPING_AC_CURRENCY_ID")
    private Long exShippingCurrencyId;

    @Column(name = "EX_D_OR_S")
    private BigDecimal exDiscountOrSurcharge;

    @Column(name = "EX_D_OR_S_AC_CURRENCY_ID")
    private Long exDiscountOrSurchargeCurrencyId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getAmountCurrencyId() {
        return amountCurrencyId;
    }

    public void setAmountCurrencyId(Long amountCurrencyId) {
        this.amountCurrencyId = amountCurrencyId;
    }

    public Boolean getIncludeBreakouts() {
        return includeBreakouts;
    }

    public void setIncludeBreakouts(Boolean includeBreakouts) {
        this.includeBreakouts = includeBreakouts;
    }

    public Long getPcBreakoutTypeId() {
        return pcBreakoutTypeId;
    }

    public void setPcBreakoutTypeId(Long pcBreakoutTypeId) {
        this.pcBreakoutTypeId = pcBreakoutTypeId;
    }

    public Long getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Long itemIndex) {
        this.itemIndex = itemIndex;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public BigDecimal getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(BigDecimal discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public BigDecimal getExAmount() {
        return exAmount;
    }

    public void setExAmount(BigDecimal exAmount) {
        this.exAmount = exAmount;
    }

    public Long getExAmountCurrencyId() {
        return exAmountCurrencyId;
    }

    public void setExAmountCurrencyId(Long exAmountCurrencyId) {
        this.exAmountCurrencyId = exAmountCurrencyId;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }
}
