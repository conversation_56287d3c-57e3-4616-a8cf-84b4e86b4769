package com.noosh.app.commons.entity.estimate;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.breakout.Breakout;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * User: leilaz
 * Date: 5/8/17
 */
@Entity
@Table(name="EM_ITEM_OPTION")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONE)
public class ItemOption extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = -8227545249938480693L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EM_ITEM_OPTION_id_generated")
    @SequenceGenerator(name = "EM_ITEM_OPTION_id_generated", sequenceName = "EM_ITEM_OPTION_SEQ", allocationSize = 1)
    @Column(name = "EM_ITEM_OPTION_ID")
    private Long id;

    @Column(name = "ITEM_OBJECT_ID")
    private Long itemObjectId;

    @Column(name = "OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "EM_OPTION_TYPE_ID")
    private Long optionTypeId;

    @Column(name = "OPTION_INDEX")
    private Long optionIndex;

    @Column(name = "VALUE")
    private String value;

    @Column(name = "BU_UOFM_ID")
    private Long buUOFMId;

    @Column(name = "INIT_PRICE")
    private BigDecimal initPrice;

    @Column(name = "DECREMENT_PRICE_PERCENT")
    private BigDecimal decrementPricePercent;

    @Column(name = "DEC_PRICE")
    private BigDecimal decPrice;

    @Column(name = "INIT_PRICE_AC_CURRENCY_ID")
    private Long initPriceCurrencyId;

    @Column(name = "DEC_PRICE_AC_CURRENCY_ID")
    private Long decPriceCurrencyId;

    @Column(name = "ADD_BU_UOFM_ID")
    private Long addBuUOFMId;

    @Column(name = "PRD_PRICE")
    private BigDecimal prdPrice;

    @Column(name = "PRD_PRICE_AC_CURRENCY_ID")
    private Long prdPriceCurrencyId;

    @Column(name = "CONFIDENCE_LEVEL")
    private BigDecimal confidenceLevel;

    @OneToMany
    @JoinColumns({@JoinColumn(name = "OBJECT_ID", referencedColumnName = "EM_ITEM_OPTION_ID", insertable = false, updatable = false),
            @JoinColumn(name = "OC_OBJECT_CLASS_ID", referencedColumnName = "OC_OBJECT_CLASS_ID", insertable = false, updatable = false)})
    private List<Breakout> breakouts;

    public List<Breakout> getBreakouts() {
        return breakouts;
    }

    public void setBreakouts(List<Breakout> breakouts) {
        this.breakouts = breakouts;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getItemObjectId() {
        return itemObjectId;
    }

    public void setItemObjectId(Long itemObjectId) {
        this.itemObjectId = itemObjectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getOptionTypeId() {
        return optionTypeId;
    }

    public void setOptionTypeId(Long optionTypeId) {
        this.optionTypeId = optionTypeId;
    }

    public Long getOptionIndex() {
        return optionIndex;
    }

    public void setOptionIndex(Long optionIndex) {
        this.optionIndex = optionIndex;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Long getBuUOFMId() {
        return buUOFMId;
    }

    public void setBuUOFMId(Long buUOFMId) {
        this.buUOFMId = buUOFMId;
    }

    public BigDecimal getInitPrice() {
        return initPrice;
    }

    public void setInitPrice(BigDecimal initPrice) {
        this.initPrice = initPrice;
    }

    public BigDecimal getDecrementPricePercent() {
        return decrementPricePercent;
    }

    public void setDecrementPricePercent(BigDecimal decrementPricePercent) {
        this.decrementPricePercent = decrementPricePercent;
    }

    public BigDecimal getDecPrice() {
        return decPrice;
    }

    public void setDecPrice(BigDecimal decPrice) {
        this.decPrice = decPrice;
    }

    public Long getInitPriceCurrencyId() {
        return initPriceCurrencyId;
    }

    public void setInitPriceCurrencyId(Long initPriceCurrencyId) {
        this.initPriceCurrencyId = initPriceCurrencyId;
    }

    public Long getDecPriceCurrencyId() {
        return decPriceCurrencyId;
    }

    public void setDecPriceCurrencyId(Long decPriceCurrencyId) {
        this.decPriceCurrencyId = decPriceCurrencyId;
    }

    public Long getAddBuUOFMId() {
        return addBuUOFMId;
    }

    public void setAddBuUOFMId(Long addBuUOFMId) {
        this.addBuUOFMId = addBuUOFMId;
    }

    public BigDecimal getPrdPrice() {
        return prdPrice;
    }

    public void setPrdPrice(BigDecimal prdPrice) {
        this.prdPrice = prdPrice;
    }

    public Long getPrdPriceCurrencyId() {
        return prdPriceCurrencyId;
    }

    public void setPrdPriceCurrencyId(Long prdPriceCurrencyId) {
        this.prdPriceCurrencyId = prdPriceCurrencyId;
    }

    public BigDecimal getConfidenceLevel() {
        return confidenceLevel;
    }

    public void setConfidenceLevel(BigDecimal confidenceLevel) {
        this.confidenceLevel = confidenceLevel;
    }

    public double getValueInDouble()
    {
        String value = this.getValue();
        double quantity = (value != null && value.length() > 0) ? Double.parseDouble(value) : 0.0;
        return quantity;
    }
}
