package com.noosh.app.commons.entity.routing;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 10/15/17
 */
@Entity
@Table(name = "PS_ROUTING_SLIP")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class RoutingSlip extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PS_ROUTING_SLIP_ID_generated")
    @SequenceGenerator(name = "PS_ROUTING_SLIP_ID_generated", sequenceName = "PS_ROUTING_SLIP_SEQ", allocationSize = 1)
    @Column(name = "PS_ROUTING_SLIP_ID")
    private Long id;

    private Long acWorkgroupId;

    private String slipName;

    private Long objectId;

    private Long objectClassId;

    private Long fromUserId;

    private String subject;

    private String messageText;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime dateSubmitted;

    private Short isAllAtOnce;

    private Long currentRecipientId;

    private Long recipientTimeoutPeriod;

    private Short trackStatus;

    private Short notifyWhenDone;

    private Short firstResponseMode;

    private Long parentObjectId;

    private Long parentObjectClassId;

    private Long ocObjectStateId;

    private Short stopRoutingOnDisapproval;

    private Short isSequentialApprovals;

    private Short isManagerApprovals;

    private Short hasManagerApproval;

    private Short isManagerRoutingSlip;

    private Short isEdit;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "routingSlip")
    private List<RoutingRecipient> routingRecipients;


    public List<RoutingRecipient> getRoutingRecipients() {
        return routingRecipients;
    }

    public void setRoutingRecipients(List<RoutingRecipient> routingRecipients) {
        this.routingRecipients = routingRecipients;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAcWorkgroupId() {
        return acWorkgroupId;
    }

    public void setAcWorkgroupId(Long acWorkgroupId) {
        this.acWorkgroupId = acWorkgroupId;
    }

    public String getSlipName() {
        return slipName;
    }

    public void setSlipName(String slipName) {
        this.slipName = slipName;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getFromUserId() {
        return fromUserId;
    }

    public void setFromUserId(Long fromUserId) {
        this.fromUserId = fromUserId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMessageText() {
        return messageText;
    }

    public void setMessageText(String messageText) {
        this.messageText = messageText;
    }

    public LocalDateTime getDateSubmitted() {
        return dateSubmitted;
    }

    public void setDateSubmitted(LocalDateTime dateSubmitted) {
        this.dateSubmitted = dateSubmitted;
    }

    public Short getIsAllAtOnce() {
        return isAllAtOnce;
    }

    public void setIsAllAtOnce(Short allAtOnce) {
        isAllAtOnce = allAtOnce;
    }

    public Long getCurrentRecipientId() {
        return currentRecipientId;
    }

    public void setCurrentRecipientId(Long currentRecipientId) {
        this.currentRecipientId = currentRecipientId;
    }

    public Long getRecipientTimeoutPeriod() {
        return recipientTimeoutPeriod;
    }

    public void setRecipientTimeoutPeriod(Long recipientTimeoutPeriod) {
        this.recipientTimeoutPeriod = recipientTimeoutPeriod;
    }

    public Short getTrackStatus() {
        return trackStatus;
    }

    public void setTrackStatus(Short trackStatus) {
        this.trackStatus = trackStatus;
    }

    public Short getNotifyWhenDone() {
        return notifyWhenDone;
    }

    public void setNotifyWhenDone(Short notifyWhenDone) {
        this.notifyWhenDone = notifyWhenDone;
    }

    public Short getFirstResponseMode() {
        return firstResponseMode;
    }

    public void setFirstResponseMode(Short firstResponseMode) {
        this.firstResponseMode = firstResponseMode;
    }

    public Long getParentObjectId() {
        return parentObjectId;
    }

    public void setParentObjectId(Long parentObjectId) {
        this.parentObjectId = parentObjectId;
    }

    public Long getParentObjectClassId() {
        return parentObjectClassId;
    }

    public void setParentObjectClassId(Long parentObjectClassId) {
        this.parentObjectClassId = parentObjectClassId;
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }

    public Short getStopRoutingOnDisapproval() {
        return stopRoutingOnDisapproval;
    }

    public void setStopRoutingOnDisapproval(Short stopRoutingOnDisapproval) {
        this.stopRoutingOnDisapproval = stopRoutingOnDisapproval;
    }

    public Short getIsSequentialApprovals() {
        return isSequentialApprovals;
    }

    public void setIsSequentialApprovals(Short sequentialApprovals) {
        isSequentialApprovals = sequentialApprovals;
    }

    public Short getIsManagerApprovals() {
        return isManagerApprovals;
    }

    public void setIsManagerApprovals(Short managerApprovals) {
        isManagerApprovals = managerApprovals;
    }

    public Short getHasManagerApproval() {
        return hasManagerApproval;
    }

    public void setHasManagerApproval(Short hasManagerApproval) {
        this.hasManagerApproval = hasManagerApproval;
    }

    public Short getIsManagerRoutingSlip() {
        return isManagerRoutingSlip;
    }

    public void setIsManagerRoutingSlip(Short managerRoutingSlip) {
        isManagerRoutingSlip = managerRoutingSlip;
    }

    public Short getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(Short edit) {
        isEdit = edit;
    }



}
