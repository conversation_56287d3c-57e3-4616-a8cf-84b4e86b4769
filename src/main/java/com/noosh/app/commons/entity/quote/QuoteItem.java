package com.noosh.app.commons.entity.quote;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "PC_QUOTE_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class QuoteItem extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @Column(name = "PC_QUOTE_ITEM_ID")
    private Long id;

    @Column(name = "PC_QUOTE_ID")
    private Long quoteId;

    @Column(name = "PC_RFQ_ITEM_ID")
    private Long rfqItemId;

    @Column(name = "SP_SPEC_ID")
    private Long specId;

    @Column(name = "IS_PRE_MARKUP_VISIBLE")
    private Boolean isPreMarkupVisible;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "COMPLETION_DATE")
    private LocalDateTime completionDate;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "BU_UOFM_ID")
    private Long uofmId;

    @Column(name = "ADD_BU_UOFM_ID")
    private Long addUofmId;

    @Column(name = "IS_RFQ_SPEC_CHANGED")
    private Boolean isRfqSpecChanged;

    @Column(name = "PC_JOB_ID")
    private Long jobId;

    @Column(name = "SP_SPEC_NODE_ID")
    private Long specNodeId;

    @Column(name = "PC_BREAKOUT_TYPE_ID")
    private Long breakoutTypeid;

    @Column(name = "TOTAL_FROM_BREAKOUTS")
    private Boolean isTotalFromBreakouts;

    @Column(name = "PC_REASON_ID")
    private Long reasonId;

    @Column(name = "REASON_OTHER")
    private String reasonOther;

    @Column(name = "QUOTED_PC_QUOTE_PRICE_ID")
    private Long quotedQuotePriceId;

    @Column(name = "CHOSEN_PC_QUOTE_PRICE_ID")
    private Long chosenQuotePriceId;


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="QUOTED_PC_QUOTE_PRICE_ID", insertable = false, updatable = false)
    private QuotePrice quotedQuotePrice;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="CHOSEN_PC_QUOTE_PRICE_ID", insertable = false, updatable = false)
    private QuotePrice chosenQuotePrice;

    @OneToMany(mappedBy = "quoteItem", fetch = FetchType.LAZY)
    private List<QuotePrice> quotePrices;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="PC_QUOTE_ID", insertable = false, updatable = false)
    private Quote quote;

    public Quote getQuote() {
        return quote;
    }

    public void setQuote(Quote quote) {
        this.quote = quote;
    }

    public BigDecimal getItemTotal() {
        if (getChosenQuotePrice() != null)
            return getChosenQuotePrice().getPrice();
        if (getQuotedQuotePrice() != null)
            return getQuotedQuotePrice().getPrice();
        return null;
    }

    public Long getItemTotalCurrencyId() {
        if (getChosenQuotePrice() != null)
            return getChosenQuotePrice().getPriceCurrencyId();
        if (getQuotedQuotePrice() != null)
            return getQuotedQuotePrice().getPriceCurrencyId();
        return null;
    }

    public BigDecimal getExItemTotal() {
        if (getChosenQuotePrice() != null)
            return getChosenQuotePrice().getExPrice();
        if (getQuotedQuotePrice() != null)
            return getQuotedQuotePrice().getExPrice();
        return null;
    }

    public Long getExItemTotalCurrencyId() {
        if (getChosenQuotePrice() != null)
            return getChosenQuotePrice().getExPriceCurrencyId();
        if (getQuotedQuotePrice() != null)
            return getQuotedQuotePrice().getExPriceCurrencyId();
        return null;
    }

    public Long getQuotedQuotePriceId() {
        return quotedQuotePriceId;
    }

    public void setQuotedQuotePriceId(Long quotedQuotePriceId) {
        this.quotedQuotePriceId = quotedQuotePriceId;
    }

    public Long getChosenQuotePriceId() {
        return chosenQuotePriceId;
    }

    public void setChosenQuotePriceId(Long chosenQuotePriceId) {
        this.chosenQuotePriceId = chosenQuotePriceId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    public Long getRfqItemId() {
        return rfqItemId;
    }

    public void setRfqItemId(Long rfqItemId) {
        this.rfqItemId = rfqItemId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Boolean isPreMarkupVisible() {
        return isPreMarkupVisible;
    }

    public void setPreMarkupVisible(Boolean preMarkupVisible) {
        isPreMarkupVisible = preMarkupVisible;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getUofmId() {
        return uofmId;
    }

    public void setUofmId(Long uofmId) {
        this.uofmId = uofmId;
    }

    public Long getAddUofmId() {
        return addUofmId;
    }

    public void setAddUofmId(Long addUofmId) {
        this.addUofmId = addUofmId;
    }

    public Boolean isRfqSpecChanged() {
        return isRfqSpecChanged;
    }

    public void setRfqSpecChanged(Boolean rfqSpecChanged) {
        isRfqSpecChanged = rfqSpecChanged;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getSpecNodeId() {
        return specNodeId;
    }

    public void setSpecNodeId(Long specNodeId) {
        this.specNodeId = specNodeId;
    }

    public Long getBreakoutTypeid() {
        return breakoutTypeid;
    }

    public void setBreakoutTypeid(Long breakoutTypeid) {
        this.breakoutTypeid = breakoutTypeid;
    }

    public Boolean isTotalFromBreakouts() {
        return isTotalFromBreakouts;
    }

    public void setTotalFromBreakouts(Boolean totalFromBreakouts) {
        isTotalFromBreakouts = totalFromBreakouts;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public String getReasonOther() {
        return reasonOther;
    }

    public void setReasonOther(String reasonOther) {
        this.reasonOther = reasonOther;
    }

    public QuotePrice getQuotedQuotePrice() {
        return quotedQuotePrice;
    }

    public void setQuotedQuotePrice(QuotePrice quotedQuotePrice) {
        this.quotedQuotePrice = quotedQuotePrice;
    }

    public QuotePrice getChosenQuotePrice() {
        return chosenQuotePrice;
    }

    public void setChosenQuotePrice(QuotePrice chosenQuotePrice) {
        this.chosenQuotePrice = chosenQuotePrice;
    }

    public List<QuotePrice> getQuotePrices() {
        return quotePrices;
    }

    public void setQuotePrices(List<QuotePrice> quotePrices) {
        this.quotePrices = quotePrices;
    }

}
