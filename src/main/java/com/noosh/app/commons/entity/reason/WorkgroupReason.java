package com.noosh.app.commons.entity.reason;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.order.PcCoReason;
import com.noosh.app.commons.entity.order.PcReason;
import org.hibernate.annotations.*;

import jakarta.persistence.*;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/4/20
 */
@Entity
@Table(name = "PC_WORKGROUP_REASON")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONE)
public class WorkgroupReason extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_WORKGROUP_REASON_ID_GENERATED")
    @SequenceGenerator(name = "PC_WORKGROUP_REASON_ID_GENERATED", sequenceName = "PC_WORKGROUP_REASON_SEQ", allocationSize = 1)
    @Column(name = "PC_WORKGROUP_REASON_ID")
    private Long id;

    @Column(name = "PC_REASON_ID")
    private Long reasonId;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "IS_ACTIVE")
    private Boolean isActive;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_REASON_ID", referencedColumnName = "PC_REASON_ID", insertable = false, updatable = false)
    private PcReason pcReason;

    public PcReason getPcReason() {
        return pcReason;
    }

    public void setPcReason(PcReason pcReason) {
        this.pcReason = pcReason;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean active) {
        isActive = active;
    }
}
