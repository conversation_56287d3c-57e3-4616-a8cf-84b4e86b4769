package com.noosh.app.commons.entity.account;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "AC_PERSON")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Person extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ac_person_id")
    private Long id;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "middle_name")
    private String middleName;

    @Column(name = "alias_name")
    private String aliasName;

    @Column(name = "ac_address_id")
    private Long addressId;


    @Column(name = "is_public")
    private Boolean isPublic;

    @Column(name = "invited_by_user_id")
    private Long invitedByUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "activation_date")
    private LocalDateTime activationDate;

    @Column(name = "ne_is_ne_user")
    private Boolean isNEUser;

    @Column(name = "ac_person_profile_img")
    private String profileImg;

    @Column(name = "ne_is_demo_ne_user")
    private Boolean isDemoUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }


    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Long getInvitedByUserId() {
        return invitedByUserId;
    }

    public void setInvitedByUserId(Long invitedByUserId) {
        this.invitedByUserId = invitedByUserId;
    }

    public LocalDateTime getActivationDate() {
        return activationDate;
    }

    public void setActivationDate(LocalDateTime activationDate) {
        this.activationDate = activationDate;
    }

    public Boolean getIsNEUser() {
        return isNEUser;
    }

    public void setIsNEUser(Boolean isNEUser) {
        this.isNEUser = isNEUser;
    }

    public String getProfileImg() {
        return profileImg;
    }

    public void setProfileImg(String profileImg) {
        this.profileImg = profileImg;
    }

    public Boolean getIsDemoUser() {
        return isDemoUser;
    }

    public void setIsDemoUser(Boolean isDemoUser) {
        this.isDemoUser = isDemoUser;
    }


    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Person person = (Person) o;

        if (!Objects.equals(id, person.id)) return false;

        return true;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    public String getFullName() {
        return firstName + " " + (middleName == null ? "" : middleName + " ") + lastName;
    }

}
