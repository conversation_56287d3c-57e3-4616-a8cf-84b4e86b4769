package com.noosh.app.commons.entity.logo;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "NF_RESOURCE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Resource extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "NF_RESOURCE_ID_GENERATED")
    @SequenceGenerator(name = "NF_RESOURCE_ID_GENERATED", sequenceName = "NF_RESOURCE_SEQ", allocationSize = 1)
    @Column(name = "NF_RESOURCE_ID")
    private Long id;

    @Column(name = "PATH")
    private String path;

    @Lob
    @Basic(fetch = FetchType.LAZY)
    @Column(name = "CONTENT", columnDefinition = "BLOB")
    private byte[] content;

    @Column(name = "CONTENT_SIZE")
    private long contentSize;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public long getContentSize() {
        return contentSize;
    }

    public void setContentSize(long contentSize) {
        this.contentSize = contentSize;
    }
}
