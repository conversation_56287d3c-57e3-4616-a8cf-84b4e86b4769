package com.noosh.app.commons.entity.order;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/22/17
 */
@Entity
@Table(name = "BU_PAYMENT_METHOD")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class PaymentMethod extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BU_PAYMENT_METHOD_ID_GENERATED")
    @SequenceGenerator(name = "BU_PAYMENT_METHOD_ID_GENERATED", sequenceName = "BU_PAYMENT_METHOD_SEQ", allocationSize = 1)
    @Column(name = "BU_PAYMENT_METHOD_ID")
    private Long id;

    @Column(name = "CONSTANT_TOKEN")
    private String token;

    @Column(name = "DESCRIPTION_STR_ID")
    private Long descriptionStrId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }
}
