package com.noosh.app.commons.entity.address;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 10/5/15
 */

@Entity
@Table(name = "AC_ADDRESS")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Address extends NooshAuditingEntity implements Serializable {
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "address_id_generated")
    @SequenceGenerator(name = "address_id_generated", sequenceName = "AC_ADDRESS_SEQ", allocationSize = 1)
    @Column(name = "AC_ADDRESS_ID")
    private Long id;

    private String description;

    @Column(name = "LINE_1")
    private String line1;

    @Column(name = "LINE_2")
    private String line2;

    @Column(name = "LINE_3")
    private String line3;

    private String city;

    private String state;

    private String postal;

    @Column(name = "AC_COUNTRY_ID")
    private Long countryId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_COUNTRY_ID", insertable = false, updatable = false)
    private Country country;

    public Country getCountry() {
        return country;
    }

    public void setCountry(Country country) {
        this.country = country;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLine1() {
        return line1;
    }

    public void setLine1(String line1) {
        this.line1 = line1;
    }

    public String getLine2() {
        return line2;
    }

    public void setLine2(String line2) {
        this.line2 = line2;
    }

    public String getLine3() {
        return line3;
    }

    public void setLine3(String line3) {
        this.line3 = line3;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getPostal() {
        return postal;
    }

    public void setPostal(String postal) {
        this.postal = postal;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }
}
