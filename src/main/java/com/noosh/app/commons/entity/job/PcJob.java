package com.noosh.app.commons.entity.job;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.spec.SpecReference;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 5/19/16
 */
@Entity
@Table(name = "PC_JOB")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class PcJob extends NooshAuditingEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pcJob_id_generated")
    @SequenceGenerator(name = "pcJob_id_generated", sequenceName = "PC_JOB_SEQ", allocationSize = 1)
    @Column(name="PC_JOB_ID")
    private Long pcJobId;

    private String jobName;

    private Long ownerWorkgroupId;

    @Column(name="SP_SPEC_REFERENCE_ID")
    private Long spSpecReferenceId;

    private Long currentSpSpecId;

    private Short isTransferrable;

    private Long ocObjectStateId;

    private static final long serialVersionUID = 1L;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "pcJob")
    private List<PcJobStatus> jobStatuses;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SP_SPEC_REFERENCE_ID", insertable = false, updatable = false)
    private SpecReference specReference;

    public SpecReference getSpecReference() {
        return specReference;
    }

    public void setSpecReference(SpecReference specReference) {
        this.specReference = specReference;
    }

    public List<PcJobStatus> getJobStatuses() {
        return jobStatuses;
    }

    public void setJobStatuses(List<PcJobStatus> jobStatuses) {
        this.jobStatuses = jobStatuses;
    }

    public Long getPcJobId() {
        return pcJobId;
    }

    public void setPcJobId(Long pcJobId) {
        this.pcJobId = pcJobId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName == null ? null : jobName.trim();
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getSpSpecReferenceId() {
        return spSpecReferenceId;
    }

    public void setSpSpecReferenceId(Long spSpecReferenceId) {
        this.spSpecReferenceId = spSpecReferenceId;
    }

    public Long getCurrentSpSpecId() {
        return currentSpSpecId;
    }

    public void setCurrentSpSpecId(Long currentSpSpecId) {
        this.currentSpSpecId = currentSpSpecId;
    }

    public Short getIsTransferrable() {
        return isTransferrable;
    }

    public void setIsTransferrable(Short isTransferrable) {
        this.isTransferrable = isTransferrable;
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }
}
