package com.noosh.app.commons.entity.terms;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 1/15/2019
 */
@Entity
@Table(name="AC_TERMS_TYPE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class TermsType extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ac_terms_type_id_generated")
    @SequenceGenerator(name = "ac_terms_type_id_generated", sequenceName = "AC_TERMS_TYPE_SEQ", allocationSize = 1)
    @Column(name="AC_TERMS_TYPE_ID")
    private Long id;

    @Column(name="CONSTANT_TOKEN")
    private String constantToken;

    @Column(name="DESCRIPTION_STR_ID")
    private Long descriptionStrId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }
}
