package com.noosh.app.commons.entity.category;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "CO_CATEGORY_OBJECT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class CategoryObject extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "co_category_object_id_generated")
    @SequenceGenerator(name = "co_category_object_id_generated", sequenceName = "CO_CATEGORY_OBJECT_SEQ", allocationSize = 1)
    @Column(name = "CO_CATEGORY_OBJECT_ID")
    private Long id;

    @Column(name = "CO_CATEGORY_ID")
    private Long categoryId;

    @Column(name = "OBJECT_ID")
    private Long objectId;

    @Column(name = "OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CO_CATEGORY_ID", insertable = false, updatable = false)
    private Category category;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }
}
