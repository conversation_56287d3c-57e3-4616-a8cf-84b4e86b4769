package com.noosh.app.commons.entity.project;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 5/9/16
 */
@Entity
@Table(name = "PM_PROJECT_HOT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProjectHot extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pm_project_hot_id_generated")
    @SequenceGenerator(name = "pm_project_hot_id_generated", sequenceName = "PM_PROJECT_HOT_SEQ", allocationSize = 1)
    @Column(name = "PM_PROJECT_HOT_ID")
    private Long id;

    @Column(name = "PM_PROJECT_ID")
    private Long pmProjectId;

    @Column(name = "AC_ACCOUNT_USER_ID")
    private Long userId;

    @Column(name = "HOT_FOR_TEAM")
    private Short forTeam;

    @Column(name = "HOT_FOR_USER")
    private Short forUser;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPmProjectId() {
        return pmProjectId;
    }

    public void setPmProjectId(Long pmProjectId) {
        this.pmProjectId = pmProjectId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Short getForTeam() {
        return forTeam;
    }

    public void setForTeam(Short forTeam) {
        this.forTeam = forTeam;
    }

    public Short getForUser() {
        return forUser;
    }

    public void setForUser(Short forUser) {
        this.forUser = forUser;
    }
}
