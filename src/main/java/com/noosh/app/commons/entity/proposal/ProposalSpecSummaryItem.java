package com.noosh.app.commons.entity.proposal;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "PC_PROPOSAL_SPEC_SUM_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProposalSpecSummaryItem extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_PROPOSAL_SPEC_SUM_ITEM_ID_GENERATED")
    @SequenceGenerator(name = "PC_PROPOSAL_SPEC_SUM_ITEM_ID_GENERATED", sequenceName = "PC_PROPOSAL_SPEC_SUM_ITEM_SEQ", allocationSize = 1)
    @Column(name = "PC_PROPOSAL_SPEC_SUM_ITEM_ID")
    private Long id;

    @Column(name = "PC_PROPOSAL_SPEC_SUMMARY_ID")
    private Long proposalSpecSummaryId;

    @Column(name = "PR_PROPERTY_TYPE_ID")
    private Long propertyTypeId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProposalSpecSummaryId() {
        return proposalSpecSummaryId;
    }

    public void setProposalSpecSummaryId(Long proposalSpecSummaryId) {
        this.proposalSpecSummaryId = proposalSpecSummaryId;
    }

    public Long getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Long propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }
}
