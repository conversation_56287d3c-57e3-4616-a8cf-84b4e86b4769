package com.noosh.app.commons.entity.order;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.account.Contact;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/25/17
 */
@Entity
@Table(name = "OR_BILLING_RECIPIENT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class OrBillingRecipient extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "OR_BILLING_RECIPIENT_GENERATED")
    @SequenceGenerator(name = "OR_BILLING_RECIPIENT_GENERATED", sequenceName = "OR_BILLING_RECIPIENT_SEQ", allocationSize = 1)
    @Column(name = "OR_BILLING_RECIPIENT_ID")
    private Long id;

    @Column(name = "CM_CONTACT_ID")
    private Long contactId;

    @Column(name = "OR_ORDER_ID")
    private Long orderId;

    @Column(name = "COMMENTS")
    private String comments;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CM_CONTACT_ID", insertable = false, updatable = false)
    private Contact contact;

    public Contact getContact() {
        return contact;
    }

    public void setContact(Contact contact) {
        this.contact = contact;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
}
