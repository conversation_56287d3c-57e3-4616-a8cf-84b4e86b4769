package com.noosh.app.commons.entity.property;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: neals
 * @Date: 05/19/2016
 */
@Entity
@Table(name = "AC_CUSTOM_FIELD_TYPE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class CustomFieldType extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "AC_CUSTOM_FIELD_TYPE_ID")
    private Long id;

    @Column(name = "CONSTANT_TOKEN")
    private String constantToken;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }
}
