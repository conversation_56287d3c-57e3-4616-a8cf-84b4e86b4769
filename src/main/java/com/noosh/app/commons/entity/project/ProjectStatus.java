package com.noosh.app.commons.entity.project;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 10/5/15
 */
@Entity
@Table(name = "PM_PROJECT_STATUS")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProjectStatus extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @Column(name = "pm_project_status_id")
    private Long id;

    @Column(name = "name_str")
    private String name;

    @Column(name = "name_str_id")
    private Long nameId;

    @Column(name = "description")
    private String description;

    @Column(name = "ac_workgroup_id")
    private Long workgroupId;

    @Column(name = "is_default")
    private Boolean isDefault;

    @Column(name = "parent_pm_project_status_id")
    private Long parentStatusId;

    @Column(name = "STATUS_ORDER")
    private Long statusOrder;

    public Long getStatusOrder() {
        return statusOrder;
    }

    public void setStatusOrder(Long statusOrder) {
        this.statusOrder = statusOrder;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getNameId() {
        return nameId;
    }

    public void setNameId(Long nameId) {
        this.nameId = nameId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Long getParentStatusId() {
        return parentStatusId;
    }

    public void setParentStatusId(Long parentStatusId) {
        this.parentStatusId = parentStatusId;
    }
}
