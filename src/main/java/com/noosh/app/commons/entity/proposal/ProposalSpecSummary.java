package com.noosh.app.commons.entity.proposal;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.logo.WorkgroupLogo;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "PC_PROPOSAL_SPEC_SUMMARY")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProposalSpecSummary extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_PROPOSAL_SPEC_SUMMARY_ID_GENERATED")
    @SequenceGenerator(name = "PC_PROPOSAL_SPEC_SUMMARY_ID_GENERATED", sequenceName = "PC_PROPOSAL_SPEC_SUMMARY_SEQ", allocationSize = 1)
    @Column(name = "PC_PROPOSAL_SPEC_SUMMARY_ID")
    private Long id;

    @Column(name = "SP_SPEC_TYPE_ID")
    private Long specTypeId;

    @Column(name = "OWNER_AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "PR_PROPERTY_TYPE_ID")
    private Long propertyTypeId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "IS_DEFAULT")
    private boolean isDefault;

    @Column(name = "IS_DISABLED")
    private Long isDisabled;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSpecTypeId() {
        return specTypeId;
    }

    public void setSpecTypeId(Long specTypeId) {
        this.specTypeId = specTypeId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getPropertyTypeId() {
        return propertyTypeId;
    }

    public void setPropertyTypeId(Long propertyTypeId) {
        this.propertyTypeId = propertyTypeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public Long getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Long isDisabled) {
        this.isDisabled = isDisabled;
    }
}
