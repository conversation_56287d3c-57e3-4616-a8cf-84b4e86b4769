package com.noosh.app.commons.entity.property;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "pr_property_param")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class PropertyParam extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pr_property_param_ID_generated")
    @SequenceGenerator(name = "pr_property_param_ID_generated", sequenceName = "PR_PROPERTY_PARAM_SEQ", allocationSize = 1)
    @Column(name = "PR_PROPERTY_PARAM_ID")
    private Long prPropertyParamId;

    private String paramName;

    private Long prPropertyTypeId;

    private String description;

    private Short isReportField;

    private Long prDataTypeId;

    public Long getPrPropertyParamId() {
        return prPropertyParamId;
    }

    public void setPrPropertyParamId(Long prPropertyParamId) {
        this.prPropertyParamId = prPropertyParamId;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName == null ? null : paramName.trim();
    }

    public Long getPrPropertyTypeId() {
        return prPropertyTypeId;
    }

    public void setPrPropertyTypeId(Long prPropertyTypeId) {
        this.prPropertyTypeId = prPropertyTypeId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Short getIsReportField() {
        return isReportField;
    }

    public void setIsReportField(Short isReportField) {
        this.isReportField = isReportField;
    }

    public Long getPrDataTypeId() {
        return prDataTypeId;
    }

    public void setPrDataTypeId(Long prDataTypeId) {
        this.prDataTypeId = prDataTypeId;
    }
}