package com.noosh.app.commons.entity.shipment;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 4/21/2021
 */
@Entity
@Table(name = "SH_METHOD")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Method extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = -5059821562257287060L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "method_id_generated")
    @SequenceGenerator(name = "method_id_generated", sequenceName = "SH_METHOD_SEQ", allocationSize = 1)
    @Column(name = "SH_METHOD_ID")
    private Long id;

    @Column(name = "NAME_STR_ID")
    private Long nameStrId;

    @Column(name = "IS_ACTIVE")
    private Long isActive;

    @Column(name = "NAME_STR")
    private String nameStr;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public Long getIsActive() {
        return isActive;
    }

    public void setIsActive(Long isActive) {
        this.isActive = isActive;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }
}
