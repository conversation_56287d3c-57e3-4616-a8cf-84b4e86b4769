package com.noosh.app.commons.entity.uofm;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/30/17
 */
@Entity
@Table(name = "BU_UOFM_SPEC_TYPE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class UofmSpecType extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = -7005297568389426910L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BU_UOFM_SPEC_TYPE_ID_GENERATED")
    @SequenceGenerator(name = "BU_UOFM_SPEC_TYPE_ID_GENERATED", sequenceName = "BU_UOFM_SPEC_TYPE_SEQ", allocationSize = 1)
    @Column(name="BU_UOFM_SPEC_TYPE_ID")
    private Long id;

    @Column(name = "BU_UOFM_ID")
    private Long uofmId;

    @Column(name = "SP_SPEC_TYPE_ID")
    private Long specTypeId;

    @Column(name = "IS_DEFAULT")
    private Short isDefault;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUofmId() {
        return uofmId;
    }

    public void setUofmId(Long uofmId) {
        this.uofmId = uofmId;
    }

    public Long getSpecTypeId() {
        return specTypeId;
    }

    public void setSpecTypeId(Long specTypeId) {
        this.specTypeId = specTypeId;
    }

    public Short getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Short aDefault) {
        isDefault = aDefault;
    }
}
