package com.noosh.app.commons.entity.spec;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.property.Property;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 5/19/16
 */
@Entity
@Table(name = "SP_SPEC_REFERENCE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SpecReference extends NooshAuditingEntity implements Serializable {
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "specReference_id_generated")
    @SequenceGenerator(name = "specReference_id_generated", sequenceName = "SP_SPEC_REFERENCE_SEQ", allocationSize = 1)
    @Column(name="SP_SPEC_REFERENCE_ID")
    private Long spSpecReferenceId;

    private String specName;

    private String refNumber;

    private Long ownerWorkgroupId;

    private Long preferredSpSpecId;

    @Column(name="SP_SPEC_TYPE_ID")
    private Long spSpecTypeId;

    private String sku;

    private Short isMaster;

    private String description;

    private Long buClientWorkgroupId;

    private Long buUofmId;

    private String ownerDesc;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPrPropertyId;

    private Short isObsolete;

    private static final long serialVersionUID = 1L;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SP_SPEC_TYPE_ID", insertable = false, updatable = false)
    private SpecType specType;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CUSTOM_PR_PROPERTY_ID", referencedColumnName = "PR_PROPERTY_ID", insertable = false, updatable = false)
    private Property property;

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public SpecType getSpecType() {
        return specType;
    }

    public void setSpecType(SpecType specType) {
        this.specType = specType;
    }

    public Long getSpSpecReferenceId() {
        return spSpecReferenceId;
    }

    public void setSpSpecReferenceId(Long spSpecReferenceId) {
        this.spSpecReferenceId = spSpecReferenceId;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName == null ? null : specName.trim();
    }

    public String getRefNumber() {
        return refNumber;
    }

    public void setRefNumber(String refNumber) {
        this.refNumber = refNumber == null ? null : refNumber.trim();
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getPreferredSpSpecId() {
        return preferredSpSpecId;
    }

    public void setPreferredSpSpecId(Long preferredSpSpecId) {
        this.preferredSpSpecId = preferredSpSpecId;
    }

    public Long getSpSpecTypeId() {
        return spSpecTypeId;
    }

    public void setSpSpecTypeId(Long spSpecTypeId) {
        this.spSpecTypeId = spSpecTypeId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public Short getIsMaster() {
        return isMaster;
    }

    public void setIsMaster(Short isMaster) {
        this.isMaster = isMaster;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }

    public Long getBuUofmId() {
        return buUofmId;
    }

    public void setBuUofmId(Long buUofmId) {
        this.buUofmId = buUofmId;
    }

    public String getOwnerDesc() {
        return ownerDesc;
    }

    public void setOwnerDesc(String ownerDesc) {
        this.ownerDesc = ownerDesc == null ? null : ownerDesc.trim();
    }

    public Long getCustomPrPropertyId() {
        return customPrPropertyId;
    }

    public void setCustomPrPropertyId(Long customPrPropertyId) {
        this.customPrPropertyId = customPrPropertyId;
    }

    public Short getIsObsolete() {
        return isObsolete;
    }

    public void setIsObsolete(Short isObsolete) {
        this.isObsolete = isObsolete;
    }
}
