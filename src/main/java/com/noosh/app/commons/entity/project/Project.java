package com.noosh.app.commons.entity.project;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "PM_PROJECT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Project extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pm_project_id_generated")
    @SequenceGenerator(name = "pm_project_id_generated", sequenceName = "PM_PROJECT_SEQ", allocationSize = 1)
    @Column(name = "pm_project_id")
    private Long id;

    @Column(name = "reference")
    private String reference;

    @Column(name = "project_number")
    private String projectNumber;

    @Column(name = "name")
    private String name;

    @Column(name = "client_account")
    private String clientAccount;

    @Column(name = "oc_object_state_id")
    private Long objectStateId;

    @Column(name = "owner_ac_workgroup_id")
    private Long ownerWorkgroupId;

    @Column(name = "master_pm_project_id")
    private Long masterProjectId;

    @Column(name = "original_pm_project_id")
    private Long originalProjectId;

    @Column(name = "pm_project_status_id")
    private Long projectStatusId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "custom_pr_property_id")
    private Long customPropertyId;

    @Column(name = "is_linked")
    private Boolean isLinked;

    @Column(name = "project_oc_object_class_id")
    private Long objectClassId;

    @Column(name = "client_ac_workgroup_id")
    private Long clientWorkgroupId;

    @Column(name = "portal_ac_workgroup_id")
    private Long portalWorkgroupId;

    @Column(name = "parent_pm_project_id")
    private Long parentProjectId;

    @Column(name = "bu_client_workgroup_id")
    private Long buClientWorkgroupId;

    @Column(name = "is_hot")
    private Boolean isHot;

    @Column(name="PP_PSF_ID")
    private Long psfId;

    @Column(name = "PAPER_FLOW")
    private Boolean isPaperFlow;

    @Column(name = "PAPER_DIRECT")
    private Boolean isPaperDirect;

    @Column(name = "is_noosh_lite_created")
    private Boolean isNGECreated;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "project_activity_date")
    private LocalDateTime activityDate;

    @Column(name = "is_draft")
    private Boolean isDraft;

    @Column(name = "AC_SOURCE_TYPE_ID")
    private Long sourceTypeId;

    @Column(name = "IS_NGE_FILE_UI")
    private Boolean isNGEFileUI;


    public Boolean getIsPaperDirect() {
        return isPaperDirect == null ? false : isPaperDirect;
    }

    public void setIsPaperDirect(Boolean paperDirect) {
        isPaperDirect = paperDirect;
    }

    public Boolean getIsPaperFlow() {
        return isPaperFlow == null ? false : isPaperFlow;
    }

    public void setIsPaperFlow(Boolean paperFlow) {
        isPaperFlow = paperFlow;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getProjectNumber() {
        return projectNumber;
    }

    public void setProjectNumber(String projectNumber) {
        this.projectNumber = projectNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getClientAccount() {
        return clientAccount;
    }

    public void setClientAccount(String clientAccount) {
        this.clientAccount = clientAccount;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getMasterProjectId() {
        return masterProjectId;
    }

    public void setMasterProjectId(Long masterProjectId) {
        this.masterProjectId = masterProjectId;
    }

    public Long getOriginalProjectId() {
        return originalProjectId;
    }

    public void setOriginalProjectId(Long originalProjectId) {
        this.originalProjectId = originalProjectId;
    }

    public Long getProjectStatusId() {
        return projectStatusId;
    }

    public void setProjectStatusId(Long projectStatusId) {
        this.projectStatusId = projectStatusId;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Boolean getIsLinked() {
        return isLinked == null ? false : true;
    }

    public void setIsLinked(Boolean isLinked) {
        this.isLinked = isLinked;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getClientWorkgroupId() {
        return clientWorkgroupId == null ? -1 : clientWorkgroupId;
    }

    public void setClientWorkgroupId(Long clientWorkgroupId) {
        this.clientWorkgroupId = clientWorkgroupId;
    }

    public Long getPortalWorkgroupId() {
        return portalWorkgroupId;
    }

    public void setPortalWorkgroupId(Long portalWorkgroupId) {
        this.portalWorkgroupId = portalWorkgroupId;
    }

    public Long getParentProjectId() {
        return parentProjectId;
    }

    public void setParentProjectId(Long parentProjectId) {
        this.parentProjectId = parentProjectId;
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId == null ? -1 : buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }

    public Boolean getIsHot() {
        return isHot == null ? false : isHot;
    }

    public void setIsHot(Boolean isHot) {
        this.isHot = isHot;
    }

    public Long getPsfId() {
        return psfId == null ? -1 : psfId;
    }

    public void setPsfId(Long psfId) {
        this.psfId = psfId;
    }

    public Boolean getIsNGECreated() {
        return isNGECreated == null ? false : true;
    }

    public void setIsNGECreated(Boolean isNGECreated) {
        this.isNGECreated = isNGECreated;
    }

    public LocalDateTime getActivityDate() {
        return activityDate;
    }

    public void setActivityDate(LocalDateTime activityDate) {
        this.activityDate = activityDate;
    }

    public Boolean getIsDraft() {
        return isDraft == null ? false : true;
    }

    public void setIsDraft(Boolean isDraft) {
        this.isDraft = isDraft;
    }

    public Long getSourceTypeId() {
        return sourceTypeId == null ? -1 : sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public Boolean getIsNGEFileUI() {
        return isNGEFileUI == null ? false : true;
    }

    public void setIsNGEFileUI(Boolean isNGEFileUI) {
        this.isNGEFileUI = isNGEFileUI;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Project project = (Project) o;

        if ( ! Objects.equals(id, project.id)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "Project{" +
                "id=" + id +
                ", reference='" + reference + "'" +
                ", projectNumber='" + projectNumber + "'" +
                ", name='" + name + "'" +
                ", clientAccount='" + clientAccount + "'" +
                ", objectStateId='" + objectStateId + "'" +
                ", ownerWorkgroupId='" + ownerWorkgroupId + "'" +
                ", masterProjectId='" + masterProjectId + "'" +
                ", originalProjectId='" + originalProjectId + "'" +
                ", projectStatusId='" + projectStatusId + "'" +
                ", completionDate='" + completionDate + "'" +
                ", customPropertyId='" + customPropertyId + "'" +
                ", isLinked='" + isLinked + "'" +
                ", objectClassId='" + objectClassId + "'" +
                ", clientWorkgroupId='" + clientWorkgroupId + "'" +
                ", portalWorkgroupId='" + portalWorkgroupId + "'" +
                ", parentProjectId='" + parentProjectId + "'" +
                ", buClientWorkgroupId='" + buClientWorkgroupId + "'" +
                ", isHot='" + isHot + "'" +
                ", isNGECreated='" + isNGECreated + "'" +
                ", activityDate='" + activityDate + "'" +
                ", isDraft='" + isDraft + "'" +
                '}';
    }

    public String getProjectTitle() {
        String prNum = getProjectNumber();
        if (prNum == null || prNum.trim().length() == 0) {
            prNum = getReference();
        }
        return getName() + " (" + prNum + ")";
    }

}
