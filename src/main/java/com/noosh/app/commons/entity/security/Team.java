package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 10/19/15
 */

@Entity
@Table(name = "TM_TEAM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Team extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "tm_team_id")
    private Long id;

    @Column(name = "parent_tm_team_id")
    private Long parentTeamId;

    @Column(name = "ac_workgroup_id")
    private Long workgroupId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentTeamId() {
        return parentTeamId;
    }

    public void setParentTeamId(Long parentTeamId) {
        this.parentTeamId = parentTeamId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }
}
