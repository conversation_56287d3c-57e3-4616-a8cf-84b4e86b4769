package com.noosh.app.commons.entity.uofm;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/29/17
 */
@Entity
@Table(name = "BU_UOFM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Uofm extends NooshAuditingEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BU_UOFM_ID_GENERATED")
    @SequenceGenerator(name = "BU_UOFM_ID_GENERATED", sequenceName = "BU_UOFM_SEQ", allocationSize = 1)
    @Column(name="BU_UOFM_ID")
    private Long id;

    private String constantToken;

    private Long descriptionStrId;

    private Long conversionFactor;

    private Long relativeToBuUofmId;

    private Long pluralDescStrId;

    private String ansiToken;

    private Short isSystem;

    private Long acWorkgroupId;

    private String container;

    private Short isDisabled;

    private String unuomCode;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken == null ? null : constantToken.trim();
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public Long getConversionFactor() {
        return conversionFactor;
    }

    public void setConversionFactor(Long conversionFactor) {
        this.conversionFactor = conversionFactor;
    }

    public Long getRelativeToBuUofmId() {
        return relativeToBuUofmId;
    }

    public void setRelativeToBuUofmId(Long relativeToBuUofmId) {
        this.relativeToBuUofmId = relativeToBuUofmId;
    }

    public Long getPluralDescStrId() {
        return pluralDescStrId;
    }

    public void setPluralDescStrId(Long pluralDescStrId) {
        this.pluralDescStrId = pluralDescStrId;
    }

    public String getAnsiToken() {
        return ansiToken;
    }

    public void setAnsiToken(String ansiToken) {
        this.ansiToken = ansiToken == null ? null : ansiToken.trim();
    }

    public Short getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Short isSystem) {
        this.isSystem = isSystem;
    }

    public Long getAcWorkgroupId() {
        return acWorkgroupId;
    }

    public void setAcWorkgroupId(Long acWorkgroupId) {
        this.acWorkgroupId = acWorkgroupId;
    }

    public String getContainer() {
        return container;
    }

    public void setContainer(String container) {
        this.container = container == null ? null : container.trim();
    }

    public Short getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Short isDisabled) {
        this.isDisabled = isDisabled;
    }

    public String getUnuomCode() {
        return unuomCode;
    }

    public void setUnuomCode(String unuomCode) {
        this.unuomCode = unuomCode == null ? null : unuomCode.trim();
    }
}
