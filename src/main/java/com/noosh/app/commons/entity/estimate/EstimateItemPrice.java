package com.noosh.app.commons.entity.estimate;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 5/7/17
 */
@Entity
@Table(name="EM_ESTIMATE_ITEM_PRICE")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONE)
public class EstimateItemPrice extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 5828832271291807321L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EM_ESTIMATE_ITEM_PRICE_id_generated")
    @SequenceGenerator(name = "EM_ESTIMATE_ITEM_PRICE_id_generated", sequenceName = "EM_ESTIMATE_ITEM_PRICE_SEQ", allocationSize = 1)
    @Column(name = "EM_ESTIMATE_ITEM_PRICE_ID")
    private Long id;

    @Column(name = "EM_ESTIMATE_ITEM_ID")
    private Long estimateItemId;

    @Column(name = "EM_ITEM_OPTION_ID")
    private Long itemOptionId;

    @Column(name = "PRICE")
    private BigDecimal price;

    @Column(name = "PRICE_AC_CURRENCY_ID")
    private Long priceCurrencyId;

    @Column(name = "BU_UOFM_ID")
    private Long buUOFMId;

    @Column(name = "ADD_PRICE")
    private BigDecimal addPrice;

    @Column(name = "ADD_PRICE_AC_CURRENCY_ID")
    private Long addPriceCurrencyId;

    @Column(name = "ADD_BU_UOFM_ID")
    private Long addBuUOFMId;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long propertyId;

    @Column(name = "TAX")
    private BigDecimal tax;

    @Column(name = "TAX_AC_CURRENCY_ID")
    private Long taxCurrencyId;

    @Column(name = "SHIPPING")
    private BigDecimal shipping;

    @Column(name = "SHIPPING_AC_CURRENCY_ID")
    private Long shippingCurrencyId;

    @Column(name = "EXPRICE")
    private BigDecimal exPrice;

    @Column(name = "EXPRICE_AC_CURRENCY_ID")
    private Long exPriceCurrencyId;

    @Column(name = "EXTAX")
    private BigDecimal exTax;

    @Column(name = "EXTAX_AC_CURRENCY_ID")
    private Long exTaxCurrencyId;

    @Column(name = "EXSHIPPING")
    private BigDecimal exShipping;

    @Column(name = "EXSHIPPING_AC_CURRENCY_ID")
    private Long exShippingCurrencyId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEstimateItemId() {
        return estimateItemId;
    }

    public void setEstimateItemId(Long estimateItemId) {
        this.estimateItemId = estimateItemId;
    }

    public Long getItemOptionId() {
        return itemOptionId;
    }

    public void setItemOptionId(Long itemOptionId) {
        this.itemOptionId = itemOptionId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getPriceCurrencyId() {
        return priceCurrencyId;
    }

    public void setPriceCurrencyId(Long priceCurrencyId) {
        this.priceCurrencyId = priceCurrencyId;
    }

    public Long getBuUOFMId() {
        return buUOFMId;
    }

    public void setBuUOFMId(Long buUOFMId) {
        this.buUOFMId = buUOFMId;
    }

    public BigDecimal getAddPrice() {
        return addPrice;
    }

    public void setAddPrice(BigDecimal addPrice) {
        this.addPrice = addPrice;
    }

    public Long getAddPriceCurrencyId() {
        return addPriceCurrencyId;
    }

    public void setAddPriceCurrencyId(Long addPriceCurrencyId) {
        this.addPriceCurrencyId = addPriceCurrencyId;
    }

    public Long getAddBuUOFMId() {
        return addBuUOFMId;
    }

    public void setAddBuUOFMId(Long addBuUOFMId) {
        this.addBuUOFMId = addBuUOFMId;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public BigDecimal getExPrice() {
        return exPrice;
    }

    public void setExPrice(BigDecimal exPrice) {
        this.exPrice = exPrice;
    }

    public Long getExPriceCurrencyId() {
        return exPriceCurrencyId;
    }

    public void setExPriceCurrencyId(Long exPriceCurrencyId) {
        this.exPriceCurrencyId = exPriceCurrencyId;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getTotalPrice() {
        BigDecimal total = getPrice();
        total = (total == null) ? getTax() : total.add(getTax());
        total = (total == null) ? getShipping() : total.add(getShipping());
        return total;
    }

    public BigDecimal getExTotalPrice() {
        BigDecimal total = getExPrice();
        total = (total == null) ? getExTax() : total.add(getExTax());
        total = (total == null) ? getExShipping() : total.add(getExShipping());
        return total;
    }
}
