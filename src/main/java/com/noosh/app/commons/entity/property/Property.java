package com.noosh.app.commons.entity.property;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;

import java.io.Serializable;
import java.util.Set;

@Entity
@Table(name = "PR_PROPERTY")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONE)
public class Property extends NooshAuditingEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PR_PROPERTY_ID_generated")
    @GenericGenerator(
            name = "PR_PROPERTY_ID_generated",
            strategy = "io.hypersistence.utils.hibernate.id.BatchSequenceGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence", value = "PR_PROPERTY_SEQ"),
                    @org.hibernate.annotations.Parameter(name = "fetch_size", value = "5")
    })
    @Column(name = "PR_PROPERTY_ID")
    private Long id;

    private String propertyName;

    private Long prPropertyTypeId;

    private Long parentPropertyId;

    private Long originalPropertyId;

    private Long clonedFromPropertyId;

    private Long objectId;

    private Long objectClassId;

    private Long ownerAcWorkgroupId;

    @JsonIgnore
    @OneToMany(orphanRemoval = true, mappedBy = "property", fetch = FetchType.EAGER)
    private Set<PropertyAttribute> propertyAttributeSet;

    public Set<PropertyAttribute> getPropertyAttributeSet() {
        return propertyAttributeSet;
    }

    public void setPropertyAttributeSet(Set<PropertyAttribute> propertyAttributeSet) {
        this.propertyAttributeSet = propertyAttributeSet;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPropertyName() {
        return propertyName;
    }

    public void setPropertyName(String propertyName) {
        this.propertyName = propertyName == null ? null : propertyName.trim();
    }

    public Long getPrPropertyTypeId() {
        return prPropertyTypeId;
    }

    public void setPrPropertyTypeId(Long prPropertyTypeId) {
        this.prPropertyTypeId = prPropertyTypeId;
    }

    public Long getParentPropertyId() {
        return parentPropertyId;
    }

    public void setParentPropertyId(Long parentPropertyId) {
        this.parentPropertyId = parentPropertyId;
    }

    public Long getOriginalPropertyId() {
        return originalPropertyId;
    }

    public void setOriginalPropertyId(Long originalPropertyId) {
        this.originalPropertyId = originalPropertyId;
    }

    public Long getClonedFromPropertyId() {
        return clonedFromPropertyId;
    }

    public void setClonedFromPropertyId(Long clonedFromPropertyId) {
        this.clonedFromPropertyId = clonedFromPropertyId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getOwnerAcWorkgroupId() {
        return ownerAcWorkgroupId;
    }

    public void setOwnerAcWorkgroupId(Long ownerAcWorkgroupId) {
        this.ownerAcWorkgroupId = ownerAcWorkgroupId;
    }
}