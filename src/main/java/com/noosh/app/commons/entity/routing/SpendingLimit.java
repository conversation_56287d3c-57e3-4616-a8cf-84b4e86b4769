package com.noosh.app.commons.entity.routing;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.account.AccountUser;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 10/13/17
 */
@Entity
@Table(name = "BU_SPENDING_LIMIT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SpendingLimit extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 4888669893094344197L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BU_SPENDING_LIMIT_ID_GENERATED")
    @SequenceGenerator(name = "BU_SPENDING_LIMIT_ID_GENERATED", sequenceName = "BU_SPENDING_LIMIT_SEQ", allocationSize = 1)
    @Column(name="BU_SPENDING_LIMIT_ID")
    private Long id;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "AC_ACCOUNT_USER_ID")
    private Long userId;

    @Column(name = "AMOUNT")
    private BigDecimal amount;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_ACCOUNT_USER_ID", referencedColumnName = "USER_ID", insertable = false, updatable = false)
    private AccountUser accountUser;

    public AccountUser getAccountUser() {
        return accountUser;
    }

    public void setAccountUser(AccountUser accountUser) {
        this.accountUser = accountUser;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
}
