package com.noosh.app.commons.entity.shipment;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 4/21/2021
 */
@Entity
@Table(name = "SH_CARRIER")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Carrier extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = -5059821562257287060L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "carrier_id_generated")
    @SequenceGenerator(name = "carrier_id_generated", sequenceName = "SH_CARRIER_SEQ", allocationSize = 1)
    @Column(name = "SH_CARRIER_ID")
    private Long id;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "NAME_STR_ID")
    private Long nameStrId;

    @Column(name = "NAME_STR")
    private String nameStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }
}
