package com.noosh.app.commons.entity.security;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: neals
 * @Date: 01/11/2016
 */
@Entity
@Table(name = "AC_WORKGROUP_SD")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class WorkgroupSd extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "AC_WORKGROUP_SD_ID")
    private Long id;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "DUNS")
    private String duns;

    @Column(name = "DEFAULT_AC_LOCALE_ID")
    private Long defaultLocaleId;

    @Column(name = "IS_PUBLIC")
    private Boolean isPublic;

    @Column(name = "IS_INTERNAL")
    private Boolean isInternal;

    @Column(name = "FINANCIAL_NUMBER")
    private Long financialNumber;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "ACTIVATION_DATE")
    private LocalDateTime activationDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "CONTRACT_DATE")
    private LocalDateTime contractDate;

    @Column(name = "LOGO_URL")
    private String logoUrl;

    @Column(name = "LOGO_ONE_URL")
    private String logoOneUrl;

    @Column(name = "WEBSITE")
    private String website;

    @Column(name = "STANDARD_HOURS")
    private Double standardHours;

    @Column(name = "STANDARD_CAPACITY")
    private Double standardCapacity;

    @Column(name = "STANDARD_END_OF_DAY")
    private Double standardEndOfDay;

    @Column(name = "IS_CAPABILITY_SEARCHABLE")
    private Boolean isCapabilitySearchable;

    @Column(name = "PARTNER_GROUPING")
    private String partnerGrouping;

    @Column(name = "BILLING_ACCOUNT_LABEL")
    private String billingAccountLabel;

    @Column(name = "BILLING_ACCOUNT_CODE")
    private String billingAccountCode;

    @Column(name = "INCLUDE_IN_BILLING")
    private Boolean includeInBilling;

    @Column(name = "REVENUE")
    private Double revenue;

    @Column(name = "REVENUE_CATEGORY")
    private String revenueCategory;

    @Column(name = "IS_PAPER_SUPPLIER")
    private Boolean isPaperSupplier;

    @Column(name = "UI_SETTINGS")
    private String uiSettings;

    @OneToOne
    @JoinColumn(name = "AC_WORKGROUP_ID", referencedColumnName = "AC_WORKGROUP_ID", insertable = false, updatable = false)
    private Workgroup workgroup;

    public Workgroup getWorkgroup() {
        return workgroup;
    }

    public void setWorkgroup(Workgroup workgroup) {
        this.workgroup = workgroup;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDuns() {
        return duns;
    }

    public void setDuns(String duns) {
        this.duns = duns;
    }

    public Long getDefaultLocaleId() {
        return defaultLocaleId;
    }

    public void setDefaultLocaleId(Long defaultLocaleId) {
        this.defaultLocaleId = defaultLocaleId;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsInternal() {
        return isInternal;
    }

    public void setIsInternal(Boolean isInternal) {
        this.isInternal = isInternal;
    }

    public Long getFinancialNumber() {
        return financialNumber;
    }

    public void setFinancialNumber(Long financialNumber) {
        this.financialNumber = financialNumber;
    }

    public LocalDateTime getActivationDate() {
        return activationDate;
    }

    public void setActivationDate(LocalDateTime activationDate) {
        this.activationDate = activationDate;
    }

    public LocalDateTime getContractDate() {
        return contractDate;
    }

    public void setContractDate(LocalDateTime contractDate) {
        this.contractDate = contractDate;
    }

    public String getLogoUrl() {
        return logoUrl;
    }

    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl;
    }

    public String getLogoOneUrl() {
        return logoOneUrl;
    }

    public void setLogoOneUrl(String logoOneUrl) {
        this.logoOneUrl = logoOneUrl;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public Double getStandardHours() {
        return standardHours;
    }

    public void setStandardHours(Double standardHours) {
        this.standardHours = standardHours;
    }

    public Double getStandardCapacity() {
        return standardCapacity;
    }

    public void setStandardCapacity(Double standardCapacity) {
        this.standardCapacity = standardCapacity;
    }

    public Double getStandardEndOfDay() {
        return standardEndOfDay;
    }

    public void setStandardEndOfDay(Double standardEndOfDay) {
        this.standardEndOfDay = standardEndOfDay;
    }

    public Boolean getIsCapabilitySearchable() {
        return isCapabilitySearchable;
    }

    public void setIsCapabilitySearchable(Boolean isCapabilitySearchable) {
        this.isCapabilitySearchable = isCapabilitySearchable;
    }

    public String getPartnerGrouping() {
        return partnerGrouping;
    }

    public void setPartnerGrouping(String partnerGrouping) {
        this.partnerGrouping = partnerGrouping;
    }

    public String getBillingAccountLabel() {
        return billingAccountLabel;
    }

    public void setBillingAccountLabel(String billingAccountLabel) {
        this.billingAccountLabel = billingAccountLabel;
    }

    public String getBillingAccountCode() {
        return billingAccountCode;
    }

    public void setBillingAccountCode(String billingAccountCode) {
        this.billingAccountCode = billingAccountCode;
    }

    public Boolean getIncludeInBilling() {
        return includeInBilling;
    }

    public void setIncludeInBilling(Boolean includeInBilling) {
        this.includeInBilling = includeInBilling;
    }

    public Double getRevenue() {
        return revenue;
    }

    public void setRevenue(Double revenue) {
        this.revenue = revenue;
    }

    public String getRevenueCategory() {
        return revenueCategory;
    }

    public void setRevenueCategory(String revenueCategory) {
        this.revenueCategory = revenueCategory;
    }

    public Boolean getIsPaperSupplier() {
        return isPaperSupplier;
    }

    public void setIsPaperSupplier(Boolean isPaperSupplier) {
        this.isPaperSupplier = isPaperSupplier;
    }

    public String getUiSettings() {
        return uiSettings;
    }

    public void setUiSettings(String uiSettings) {
        this.uiSettings = uiSettings;
    }
}
