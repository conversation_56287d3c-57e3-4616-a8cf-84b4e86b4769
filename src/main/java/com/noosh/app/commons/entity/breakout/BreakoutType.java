package com.noosh.app.commons.entity.breakout;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 5/9/17
 */
@Entity
@Table(name = "PC_BREAKOUT_TYPE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class BreakoutType extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_BREAKOUT_TYPE_ID_GENERATED")
    @SequenceGenerator(name = "PC_BREAKOUT_TYPE_ID_GENERATED", sequenceName = "PC_BREAKOUT_TYPE_SEQ", allocationSize = 1)
    @Column(name = "PC_BREAKOUT_TYPE_ID")
    private Long id;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "PARENT_PC_BREAKOUT_TYPE_ID")
    private Long parentTypeId;

    @Column(name = "ROOT_PC_BREAKOUT_TYPE_ID")
    private Long rootTypeId;

    @Column(name = "IS_LOCKED")
    private Boolean isLocked;

    @Column(name = "IS_CURRENT")
    private Boolean isCurrent;

    @Column(name = "NAME")
    private String name;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "IS_REQUIRED")
    private Boolean isRequired;

    @Column(name = "IS_QUANTITY")
    private Boolean isQuantity;

    @Column(name = "ALL_SUPPLIERS")
    private Boolean allSuppliers;

    @Column(name = "SORT_ORDER")
    private Long sortOrder;

    @Column(name = "CUSTOM1")
    private String custom1;

    @Column(name = "CLONE_OF")
    private Long cloneOf;

    @Column(name = "IS_INCLUDED")
    private Boolean isIncluded;

    @Column(name = "PRICE_PER")
    private String pricePer;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long propertyId;

    @Column(name = "CODE")
    private String code;

    @Column(name = "ALL_CLIENTS")
    private Boolean allClients;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getParentTypeId() {
        return parentTypeId;
    }

    public void setParentTypeId(Long parentTypeId) {
        this.parentTypeId = parentTypeId;
    }

    public Long getRootTypeId() {
        return rootTypeId;
    }

    public void setRootTypeId(Long rootTypeId) {
        this.rootTypeId = rootTypeId;
    }

    public Boolean getIsLocked() {
        return isLocked == null ? false : isLocked;
    }

    public void setIsLocked(Boolean locked) {
        isLocked = locked;
    }

    public Boolean getIsCurrent() {
        return isCurrent == null ? false : isCurrent;
    }

    public void setIsCurrent(Boolean current) {
        isCurrent = current;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsRequired() {
        return isRequired == null ? false : isRequired;
    }

    public void setIsRequired(Boolean required) {
        isRequired = required;
    }

    public Boolean getIsQuantity() {
        return isQuantity == null ? false : isQuantity;
    }

    public void setIsQuantity(Boolean quantity) {
        isQuantity = quantity;
    }

    public Boolean getAllSuppliers() {
        return allSuppliers == null ? false : allSuppliers;
    }

    public void setAllSuppliers(Boolean allSuppliers) {
        this.allSuppliers = allSuppliers;
    }

    public Long getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Long sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public Long getCloneOf() {
        return cloneOf;
    }

    public void setCloneOf(Long cloneOf) {
        this.cloneOf = cloneOf;
    }

    public Boolean getIsIncluded() {
        return isIncluded == null ? false : isIncluded;
    }

    public void setIsIncluded(Boolean included) {
        isIncluded = included;
    }

    public String getPricePer() {
        return pricePer;
    }

    public void setPricePer(String pricePer) {
        this.pricePer = pricePer;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getAllClients() {
        return allClients == null ? false : allClients;
    }

    public void setAllClients(Boolean allClients) {
        this.allClients = allClients;
    }

    public double getPricePerInDouble() {
        String pricePerStr = getPricePer();
        double pricePer = (pricePerStr != null && pricePerStr.length() > 0) ? Double.parseDouble(pricePerStr) : 0.0;
        return pricePer;
    }
}
