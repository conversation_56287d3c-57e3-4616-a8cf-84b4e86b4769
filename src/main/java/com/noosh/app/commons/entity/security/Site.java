package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 4/6/16
 */
@Entity
@Table(name = "NL_SITE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Site extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "site_id_generated")
    @SequenceGenerator(name = "site_id_generated", sequenceName = "NL_SITE_SEQ", allocationSize = 1)
    @Column(name = "NL_SITE_ID")
    private Long id;

    private String name;

    private Long doDocumentId;

    private String urlSuffix;

    private Long nlSiteStatusId;

    private Short isInviteOnly;

    private Long buClientWorkgroupId;

    private String nlSiteLogoImg;

    private String nlSiteProductImg;

    private String nlSiteBackgroundImg;

    private Long nlServiceProviderId;

    private Long serviceAcWorkgroupId;

    private Long ocObjectStateId;

    private Short isDefaultShareProjects;

    private Long siteOwnerUserId;

    private Long acCustomFormId;

    private Short accountRepresentative;

    private Short isListProductsAsIcon;

    private Short isQuoting;

    private Long nlSiteLoginLayout;

    private String nlSiteLoginTitle;

    private String nlSiteLoginBody;

    private Short isEnableRoleBasedEmails;

    private Long productIconColumnSize;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getDoDocumentId() {
        return doDocumentId;
    }

    public void setDoDocumentId(Long doDocumentId) {
        this.doDocumentId = doDocumentId;
    }

    public String getUrlSuffix() {
        return urlSuffix;
    }

    public void setUrlSuffix(String urlSuffix) {
        this.urlSuffix = urlSuffix;
    }

    public Long getNlSiteStatusId() {
        return nlSiteStatusId;
    }

    public void setNlSiteStatusId(Long nlSiteStatusId) {
        this.nlSiteStatusId = nlSiteStatusId;
    }

    public Short getInviteOnly() {
        return isInviteOnly;
    }

    public void setInviteOnly(Short inviteOnly) {
        isInviteOnly = inviteOnly;
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }

    public String getNlSiteLogoImg() {
        return nlSiteLogoImg;
    }

    public void setNlSiteLogoImg(String nlSiteLogoImg) {
        this.nlSiteLogoImg = nlSiteLogoImg;
    }

    public String getNlSiteProductImg() {
        return nlSiteProductImg;
    }

    public void setNlSiteProductImg(String nlSiteProductImg) {
        this.nlSiteProductImg = nlSiteProductImg;
    }

    public String getNlSiteBackgroundImg() {
        return nlSiteBackgroundImg;
    }

    public void setNlSiteBackgroundImg(String nlSiteBackgroundImg) {
        this.nlSiteBackgroundImg = nlSiteBackgroundImg;
    }

    public Long getNlServiceProviderId() {
        return nlServiceProviderId;
    }

    public void setNlServiceProviderId(Long nlServiceProviderId) {
        this.nlServiceProviderId = nlServiceProviderId;
    }

    public Long getServiceAcWorkgroupId() {
        return serviceAcWorkgroupId;
    }

    public void setServiceAcWorkgroupId(Long serviceAcWorkgroupId) {
        this.serviceAcWorkgroupId = serviceAcWorkgroupId;
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }

    public Short getDefaultShareProjects() {
        return isDefaultShareProjects;
    }

    public void setDefaultShareProjects(Short defaultShareProjects) {
        isDefaultShareProjects = defaultShareProjects;
    }

    public Long getSiteOwnerUserId() {
        return siteOwnerUserId;
    }

    public void setSiteOwnerUserId(Long siteOwnerUserId) {
        this.siteOwnerUserId = siteOwnerUserId;
    }

    public Long getAcCustomFormId() {
        return acCustomFormId;
    }

    public void setAcCustomFormId(Long acCustomFormId) {
        this.acCustomFormId = acCustomFormId;
    }

    public Short getAccountRepresentative() {
        return accountRepresentative;
    }

    public void setAccountRepresentative(Short accountRepresentative) {
        this.accountRepresentative = accountRepresentative;
    }

    public Short getListProductsAsIcon() {
        return isListProductsAsIcon;
    }

    public void setListProductsAsIcon(Short listProductsAsIcon) {
        isListProductsAsIcon = listProductsAsIcon;
    }

    public Short getQuoting() {
        return isQuoting;
    }

    public void setQuoting(Short quoting) {
        isQuoting = quoting;
    }

    public Long getNlSiteLoginLayout() {
        return nlSiteLoginLayout;
    }

    public void setNlSiteLoginLayout(Long nlSiteLoginLayout) {
        this.nlSiteLoginLayout = nlSiteLoginLayout;
    }

    public String getNlSiteLoginTitle() {
        return nlSiteLoginTitle;
    }

    public void setNlSiteLoginTitle(String nlSiteLoginTitle) {
        this.nlSiteLoginTitle = nlSiteLoginTitle;
    }

    public String getNlSiteLoginBody() {
        return nlSiteLoginBody;
    }

    public void setNlSiteLoginBody(String nlSiteLoginBody) {
        this.nlSiteLoginBody = nlSiteLoginBody;
    }

    public Short getEnableRoleBasedEmails() {
        return isEnableRoleBasedEmails;
    }

    public void setEnableRoleBasedEmails(Short enableRoleBasedEmails) {
        isEnableRoleBasedEmails = enableRoleBasedEmails;
    }

    public Long getProductIconColumnSize() {
        return productIconColumnSize;
    }

    public void setProductIconColumnSize(Long productIconColumnSize) {
        this.productIconColumnSize = productIconColumnSize;
    }
}
