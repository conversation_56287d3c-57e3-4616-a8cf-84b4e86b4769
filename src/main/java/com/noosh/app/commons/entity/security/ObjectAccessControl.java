package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 11/9/15
 */
@Entity
@Table(name = "RE_OBJECT_ACCESS_CONTROL")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ObjectAccessControl extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RE_OBJECT_ACCESS_CONTROL_ID_GENERATED")
    @SequenceGenerator(name = "RE_OBJECT_ACCESS_CONTROL_ID_GENERATED", sequenceName = "RE_OBJECT_ACCESS_CONTROL_SEQ", allocationSize = 1)
    @Column(name = "RE_OBJECT_ACCESS_CONTROL_ID")
    private Long id;

    private Long tmTeamId;

    private Long userId;

    private Long acWorkgroupId;

    private Long reRoleId;

    private Long reRoleClassId;

    private Short teamAccess;

    private Short globalAccess;

    private Long reAccessContextId;

    private Long objectId;

    private Short accessIsAllowed;

    private Long authorUserId;

    private Long authorAcWorkgroupId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTmTeamId() {
        return tmTeamId;
    }

    public void setTmTeamId(Long tmTeamId) {
        this.tmTeamId = tmTeamId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getAcWorkgroupId() {
        return acWorkgroupId;
    }

    public void setAcWorkgroupId(Long acWorkgroupId) {
        this.acWorkgroupId = acWorkgroupId;
    }

    public Long getReRoleId() {
        return reRoleId;
    }

    public void setReRoleId(Long reRoleId) {
        this.reRoleId = reRoleId;
    }

    public Long getReRoleClassId() {
        return reRoleClassId;
    }

    public void setReRoleClassId(Long reRoleClassId) {
        this.reRoleClassId = reRoleClassId;
    }

    public Short getTeamAccess() {
        return teamAccess;
    }

    public void setTeamAccess(Short teamAccess) {
        this.teamAccess = teamAccess;
    }

    public Short getGlobalAccess() {
        return globalAccess;
    }

    public void setGlobalAccess(Short globalAccess) {
        this.globalAccess = globalAccess;
    }

    public Long getReAccessContextId() {
        return reAccessContextId;
    }

    public void setReAccessContextId(Long reAccessContextId) {
        this.reAccessContextId = reAccessContextId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Short getAccessIsAllowed() {
        return accessIsAllowed;
    }

    public void setAccessIsAllowed(Short accessIsAllowed) {
        this.accessIsAllowed = accessIsAllowed;
    }

    public Long getAuthorUserId() {
        return authorUserId;
    }

    public void setAuthorUserId(Long authorUserId) {
        this.authorUserId = authorUserId;
    }

    public Long getAuthorAcWorkgroupId() {
        return authorAcWorkgroupId;
    }

    public void setAuthorAcWorkgroupId(Long authorAcWorkgroupId) {
        this.authorAcWorkgroupId = authorAcWorkgroupId;
    }
}
