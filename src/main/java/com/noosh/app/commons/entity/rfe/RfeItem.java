package com.noosh.app.commons.entity.rfe;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 05/30/2016
 */

@Entity
@Table(name="EM_RFE_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class RfeItem extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @Column(name="EM_RFE_ITEM_ID")
    private Long id;

    @Column(name="EM_RFE_ID")
    private Long rfeId;

    @Column(name="SP_SPEC_ID")
    private Long specId;

    @Column(name="REFERENCE")
    private String reference;

    @Column(name="OWNER_REFERENCE")
    private String ownerReference;

    @Column(name="COMMENTS")
    private String comments;

    @Column(name="CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name="PC_BREAKOUT_TYPE_ID")
    private Long breakoutTypeId;

    @Column(name="PC_JOB_ID")
    private Long jobId;

    @Column(name="SP_SPEC_NODE_ID")
    private Long specNodeId;

    @Column(name="IS_OVERRIDING_BREAKOUTS")
    private Boolean isOverridingBreakouts;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRfeId() {
        return rfeId;
    }

    public void setRfeId(Long rfeId) {
        this.rfeId = rfeId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getSpecNodeId() {
        return specNodeId;
    }

    public void setSpecNodeId(Long specNodeId) {
        this.specNodeId = specNodeId;
    }

    public Boolean getIsOverridingBreakouts() {
        return isOverridingBreakouts;
    }

    public void setIsOverridingBreakouts(Boolean isOverridingBreakouts) {
        this.isOverridingBreakouts = isOverridingBreakouts;
    }

}
