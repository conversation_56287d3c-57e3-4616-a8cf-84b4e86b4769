package com.noosh.app.commons.entity.order;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.job.PcJob;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.shipment.Shipment;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.uofm.Uofm;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 10/15/15
 */

@Entity
@Table(name = "OR_ORDER_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class OrderItem extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "OR_ORDER_ITEM_ID_GENERATED")
    @SequenceGenerator(name = "OR_ORDER_ITEM_ID_GENERATED", sequenceName = "OR_ORDER_ITEM_SEQ", allocationSize = 1)
    @Column(name = "OR_ORDER_ITEM_ID")
    private Long id;

    @Column(name = "or_order_version_id")
    private Long orderVersionId;

    @Column(name = "em_estimate_item_price_id")
    private Long estimateItemPriceId;

    private String comments;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "BU_UOFM_ID")
    private Long uofmId;

    private BigDecimal value;

    private BigDecimal quantity;

    @Column(name = "item_index")
    private Integer itemIndex;

    @Column(name = "VALUE_AC_CURRENCY_ID")
    private Long valueCurrencyId;

    @Column(name = "is_spec_changed")
    private Boolean isSpecChanged;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "add_price_ac_currency_id")
    private Long addPriceCurrencyId;

    @Column(name = "add_price")
    private BigDecimal addPrice;

    @Column(name = "add_bu_uofm_id")
    private BigDecimal addUofmId;

    @Column(name = "or_purchase_request_item_id")
    private Long purchaseRequestItemId;

    @Column(name = "pc_quote_item_id")
    private Long quoteItemId;

    @Column(name = "SP_SPEC_ID")
    private Long specId;

    @Column(name = "pc_breakout_type_id")
    private Long breakoutTypeId;

    @Column(name = "total_from_breakouts")
    private Boolean totalFromBreakouts;

    @Column(name = "allow_breakouts")
    private Boolean allowBreakouts;

    @Column(name = "PC_JOB_ID")
    private Long jobId;

    @Column(name = "sp_spec_node_id")
    private Long specNodeId;

    @Column(name = "item_attribute")
    private String itemAttribute;

    @Column(name = "d_or_s")
    private BigDecimal dors;

    @Column(name = "d_or_s_ac_currency_id")
    private Long dorsCurrencyId;

    @Column(name = "is_overriding_breakouts")
    private Boolean isOverridingBreakouts;

    @Column(name = "markup_percent")
    private BigDecimal markupPercent;

    private BigDecimal shipping;

    @Column(name = "shipping_ac_currency_id")
    private Long shippingCurrencyId;

    private BigDecimal tax;

    @Column(name = "tax_ac_currency_id")
    private Long taxCurrencyId;

    @Column(name = "EXVALUE")
    private BigDecimal exValue;

    @Column(name = "EXVALUE_AC_CURRENCY_ID")
    private Long exValueCurrencyId;

    @Column(name = "EXTAX")
    private BigDecimal exTax;

    @Column(name = "EXTAX_AC_CURRENCY_ID")
    private Long exTaxCurrencyId;

    @Column(name = "EXSHIPPING")
    private BigDecimal exShipping;

    @Column(name = "EXSHIPPING_AC_CURRENCY_ID")
    private Long exShippingCurrencyId;

    @Column(name = "EXADD_PRICE")
    private BigDecimal exAddPrice;

    @Column(name = "EXADD_PRICE_AC_CURRENCY_ID")
    private Long exAddPriceCurrencyId;

    @Column(name = "EX_D_OR_S")
    private BigDecimal exDors;

    @Column(name = "EX_D_OR_S_AC_CURRENCY_ID")
    private Long exDorsCurrencyId;

    @Column(name = "VAT_CODE")
    private String vatCode;

    @Column(name = "VAT_RATE")
    private BigDecimal vatRate;

    @Column(name = "budget_ac_custom_field_id")
    private Long budgetCustomFieldId;

    @Column(name = "PC_REASON_ID")
    private Long reasonId;

    @Column(name = "REASON_OTHER")
    private String reasonOther;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_JOB_ID", insertable = false, updatable = false, referencedColumnName = "PC_JOB_ID")
    private Shipment shipment;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BU_UOFM_ID", insertable = false, updatable = false)
    private Uofm uofm;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CUSTOM_PR_PROPERTY_ID", referencedColumnName = "PR_PROPERTY_ID", insertable = false, updatable = false)
    private Property property;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SP_SPEC_ID", referencedColumnName = "SP_SPEC_ID", insertable = false, updatable = false)
    private Spec spec;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VALUE_AC_CURRENCY_ID", referencedColumnName = "AC_CURRENCY_ID", insertable = false, updatable = false)
    private NCurrency defaultCurrency;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_REASON_ID", referencedColumnName = "PC_CO_REASON_ID", insertable = false, updatable = false)
    private PcCoReason reason;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_JOB_ID", insertable = false, updatable = false)
    private PcJob pcJob;

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public Shipment getShipment() {
        return shipment;
    }

    public void setShipment(Shipment shipment) {
        this.shipment = shipment;
    }

    public Uofm getUofm() {
        return uofm;
    }

    public void setUofm(Uofm uofm) {
        this.uofm = uofm;
    }
//
//    public Property getProperty() {
//        return property;
//    }
//
//    public void setProperty(Property property) {
//        this.property = property;
//    }

    public NCurrency getDefaultCurrency() {
        return defaultCurrency;
    }

    public void setDefaultCurrency(NCurrency defaultCurrency) {
        this.defaultCurrency = defaultCurrency;
    }

    public PcCoReason getReason() {
        return reason;
    }

    public void setReason(PcCoReason reason) {
        this.reason = reason;
    }

    public PcJob getPcJob() {
        return pcJob;
    }

    public void setPcJob(PcJob pcJob) {
        this.pcJob = pcJob;
    }

    public Spec getSpec() {
        return spec;
    }

    public void setSpec(Spec spec) {
        this.spec = spec;
    }

    public String getReasonOther() {
        return reasonOther;
    }

    public void setReasonOther(String reasonOther) {
        this.reasonOther = reasonOther;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderVersionId() {
        return orderVersionId;
    }

    public void setOrderVersionId(Long orderVersionId) {
        this.orderVersionId = orderVersionId;
    }

    public Long getEstimateItemPriceId() {
        return estimateItemPriceId;
    }

    public void setEstimateItemPriceId(Long estimateItemPriceId) {
        this.estimateItemPriceId = estimateItemPriceId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getUofmId() {
        return uofmId;
    }

    public void setUofmId(Long uofmId) {
        this.uofmId = uofmId;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Integer getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Integer itemIndex) {
        this.itemIndex = itemIndex;
    }

    public Long getValueCurrencyId() {
        return valueCurrencyId;
    }

    public void setValueCurrencyId(Long valueCurrencyId) {
        this.valueCurrencyId = valueCurrencyId;
    }

    public Boolean getIsSpecChanged() {
        return isSpecChanged;
    }

    public void setIsSpecChanged(Boolean isSpecChanged) {
        this.isSpecChanged = isSpecChanged;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getAddPriceCurrencyId() {
        return addPriceCurrencyId;
    }

    public void setAddPriceCurrencyId(Long addPriceCurrencyId) {
        this.addPriceCurrencyId = addPriceCurrencyId;
    }

    public BigDecimal getAddPrice() {
        return addPrice;
    }

    public void setAddPrice(BigDecimal addPrice) {
        this.addPrice = addPrice;
    }

    public BigDecimal getAddUofmId() {
        return addUofmId;
    }

    public void setAddUofmId(BigDecimal addUofmId) {
        this.addUofmId = addUofmId;
    }

    public Long getPurchaseRequestItemId() {
        return purchaseRequestItemId;
    }

    public void setPurchaseRequestItemId(Long purchaseRequestItemId) {
        this.purchaseRequestItemId = purchaseRequestItemId;
    }

    public Long getQuoteItemId() {
        return quoteItemId;
    }

    public void setQuoteItemId(Long quoteItemId) {
        this.quoteItemId = quoteItemId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public Boolean getTotalFromBreakouts() {
        return totalFromBreakouts;
    }

    public void setTotalFromBreakouts(Boolean totalFromBreakouts) {
        this.totalFromBreakouts = totalFromBreakouts;
    }

    public Boolean getAllowBreakouts() {
        return allowBreakouts;
    }

    public void setAllowBreakouts(Boolean allowBreakouts) {
        this.allowBreakouts = allowBreakouts;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getSpecNodeId() {
        return specNodeId;
    }

    public void setSpecNodeId(Long specNodeId) {
        this.specNodeId = specNodeId;
    }

    public String getItemAttribute() {
        return itemAttribute;
    }

    public void setItemAttribute(String itemAttribute) {
        this.itemAttribute = itemAttribute;
    }

    public BigDecimal getDors() {
        return dors;
    }

    public void setDors(BigDecimal dors) {
        this.dors = dors;
    }

    public Long getDorsCurrencyId() {
        return dorsCurrencyId;
    }

    public void setDorsCurrencyId(Long dorsCurrencyId) {
        this.dorsCurrencyId = dorsCurrencyId;
    }

    public Boolean getIsOverridingBreakouts() {
        return isOverridingBreakouts;
    }

    public void setIsOverridingBreakouts(Boolean isOverridingBreakouts) {
        this.isOverridingBreakouts = isOverridingBreakouts;
    }

    public BigDecimal getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(BigDecimal markupPercent) {
        this.markupPercent = markupPercent;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public String getVatCode() {
        return this.vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public BigDecimal getVatRate() {
        return this.vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public Long getBudgetCustomFieldId() {
        return budgetCustomFieldId;
    }

    public void setBudgetCustomFieldId(Long budgetCustomFieldId) {
        this.budgetCustomFieldId = budgetCustomFieldId;
    }

    public BigDecimal getExValue() {
        return this.exValue;
    }

    public void setExValue(BigDecimal exValue) {
        this.exValue = exValue;
    }

    public Long getExValueCurrencyId() {
        return this.exValueCurrencyId;
    }

    public void setExValueCurrencyId(Long exValueCurrencyId) {
        this.exValueCurrencyId = exValueCurrencyId;
    }

    public Long getExAddPriceCurrencyId() {
        return this.exAddPriceCurrencyId;
    }

    public void setExAddPriceCurrencyId(Long exAddPriceCurrencyId) {
        this.exAddPriceCurrencyId = exAddPriceCurrencyId;
    }

    public BigDecimal getExAddPrice() {
        return this.exAddPrice;
    }

    public void setExAddPrice(BigDecimal exAddPrice) {
        this.exAddPrice = exAddPrice;
    }

    public BigDecimal getExDors() {
        return this.exDors;
    }

    public void setExDors(BigDecimal exDors) {
        this.exDors = exDors;
    }

    public Long getExDorsCurrencyId() {
        return this.exDorsCurrencyId;
    }

    public void setExDorsCurrencyId(Long exDorsCurrencyId) {
        this.exDorsCurrencyId = exDorsCurrencyId;
    }

    public BigDecimal getExShipping() {
        return this.exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return this.exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExTax() {
        return this.exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return this.exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        OrderItem orderItem = (OrderItem) o;

        return id.equals(orderItem.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
