package com.noosh.app.commons.entity.account;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/11/17
 */
@Entity
@Table(name = "CM_CONTACT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Contact extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "contact_id_generated")
    @SequenceGenerator(name = "contact_id_generated", sequenceName = "CM_CONTACT_SEQ", allocationSize = 1)
    @Column(name = "CM_CONTACT_ID")
    private Long id;

    @Column(name = "FIRST_NAME")
    private String firstName;

    @Column(name = "LAST_NAME")
    private String lastName;

    @Column(name = "MIDDLE_NAME")
    private String middleName;

    @Column(name = "TITLE")
    private String title;

    @Column(name = "PHONE_NUMBER_1")
    private String phoneNumber1;

    @Column(name = "PHONE_NUMBER_2")
    private String phoneNumber2;

    @Column(name = "MOBILE_NUMBER")
    private String mobileNumber;

    @Column(name = "PAGER_NUMBER")
    private String pagerNumber;

    @Column(name = "FAX_NUMBER")
    private String faxNumber;

    @Column(name = "COMPANY_NAME")
    private String companyName;

    @Column(name = "AC_ADDRESS_ID")
    private Long addressId;

    @Column(name = "OWNER_OBJECT_ID")
    private Long ownerObjectId;

    @Column(name = "CM_CONTACT_OWNER_TYPE_ID")
    private Long contactOwnerTypeId;

    @Column(name = "IS_LIVE_USER")
    private Boolean isLiveUser;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "WEBSITE")
    private String website;

    @Column(name = "CONTACT_NOTES")
    private String contactNotes;

    @Column(name = "CONTACT_USER_ID")
    private Long contactUserId;

    @Column(name = "IS_SYSTEM_GENERATED")
    private Boolean isSystemGenerated;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPrPropertyId;

    @Column(name = "CM_CONTACT_TYPE_ID")
    private Long contactTypeId;

    @Column(name = "IS_LOCKED")
    private Boolean isLocked;

    @Column(name = "IS_OBSOLETE")
    private Boolean isObsolete;

    @Column(name = "IS_SUPPLIER_INVITE")
    private Boolean isSupplierInvite;

    @Column(name = "CONTACT_IMPORTED_TYPE_ID")
    private Long contactImportedTypeId;

    @Column(name = "CONTACT_IMPROTED_ID")
    private String contactImportedId;

    @Column(name = "CONTACT_USER_TYPE_ID")
    private Long contactUserTypeId;

    @Column(name = "CONTACT_IMPROTED_PROFILE_IMG")
    private String contactImportedProfileImg;

    @Column(name = "AC_SOURCE_TYPE_ID")
    private Long acSourceTypeId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CONTACT_USER_ID", referencedColumnName = "USER_ID", insertable = false, updatable = false)
    private AccountUser accountUser;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_ADDRESS_ID", referencedColumnName = "AC_ADDRESS_ID", insertable = false, updatable = false)
    private Address address;

    public AccountUser getAccountUser() {
        return accountUser;
    }

    public void setAccountUser(AccountUser accountUser) {
        this.accountUser = accountUser;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPhoneNumber1() {
        return phoneNumber1;
    }

    public void setPhoneNumber1(String phoneNumber1) {
        this.phoneNumber1 = phoneNumber1;
    }

    public String getPhoneNumber2() {
        return phoneNumber2;
    }

    public void setPhoneNumber2(String phoneNumber2) {
        this.phoneNumber2 = phoneNumber2;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getPagerNumber() {
        return pagerNumber;
    }

    public void setPagerNumber(String pagerNumber) {
        this.pagerNumber = pagerNumber;
    }

    public String getFaxNumber() {
        return faxNumber;
    }

    public void setFaxNumber(String faxNumber) {
        this.faxNumber = faxNumber;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public Long getOwnerObjectId() {
        return ownerObjectId;
    }

    public void setOwnerObjectId(Long ownerObjectId) {
        this.ownerObjectId = ownerObjectId;
    }

    public Long getContactOwnerTypeId() {
        return contactOwnerTypeId;
    }

    public void setContactOwnerTypeId(Long contactOwnerTypeId) {
        this.contactOwnerTypeId = contactOwnerTypeId;
    }

    public Boolean getIsLiveUser() {
        return isLiveUser;
    }

    public void setIsLiveUser(Short liveUser) {
        isLiveUser = liveUser == null || liveUser.shortValue() == 0 ? false : true;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getContactNotes() {
        return contactNotes;
    }

    public void setContactNotes(String contactNotes) {
        this.contactNotes = contactNotes;
    }

    public Long getContactUserId() {
        return contactUserId;
    }

    public void setContactUserId(Long contactUserId) {
        this.contactUserId = contactUserId;
    }

    public Boolean getIsSystemGenerated() {
        return isSystemGenerated;
    }

    public void setIsSystemGenerated(Short systemGenerated) {
        isSystemGenerated = systemGenerated == null || systemGenerated.shortValue() == 0 ? false : true;
    }

    public Long getCustomPrPropertyId() {
        return customPrPropertyId;
    }

    public void setCustomPrPropertyId(Long customPrPropertyId) {
        this.customPrPropertyId = customPrPropertyId;
    }

    public Long getContactTypeId() {
        return contactTypeId;
    }

    public void setContactTypeId(Long contactTypeId) {
        this.contactTypeId = contactTypeId;
    }

    public Boolean getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(Short locked) {
        isLocked = locked == null || locked.shortValue() == 0 ? false : true;
    }

    public Boolean getIsObsolete() {
        return isObsolete;
    }

    public void setIsObsolete(Short obsolete) {
        isObsolete = obsolete == null || obsolete.shortValue() == 0 ? false : true;
    }

    public Boolean getIsSupplierInvite() {
        return isSupplierInvite;
    }

    public void setIsSupplierInvite(Short supplierInvite) {
        isSupplierInvite = supplierInvite == null || supplierInvite.shortValue() == 0 ? false : true;
    }

    public Long getContactImportedTypeId() {
        return contactImportedTypeId;
    }

    public void setContactImportedTypeId(Long contactImportedTypeId) {
        this.contactImportedTypeId = contactImportedTypeId;
    }

    public String getContactImportedId() {
        return contactImportedId;
    }

    public void setContactImportedId(String contactImportedId) {
        this.contactImportedId = contactImportedId;
    }

    public Long getContactUserTypeId() {
        return contactUserTypeId;
    }

    public void setContactUserTypeId(Long contactUserTypeId) {
        this.contactUserTypeId = contactUserTypeId;
    }

    public String getContactImportedProfileImg() {
        return contactImportedProfileImg;
    }

    public void setContactImportedProfileImg(String contactImportedProfileImg) {
        this.contactImportedProfileImg = contactImportedProfileImg;
    }

    public Long getAcSourceTypeId() {
        return acSourceTypeId;
    }

    public void setAcSourceTypeId(Long acSourceTypeId) {
        this.acSourceTypeId = acSourceTypeId;
    }

    public String getContactFullName() {
        if (firstName == null && middleName == null && lastName == null) {
            return accountUser.getPerson().getFullName();
        }
        return firstName + " " + (middleName == null ? "" : middleName) + " " + lastName;
    }
}
