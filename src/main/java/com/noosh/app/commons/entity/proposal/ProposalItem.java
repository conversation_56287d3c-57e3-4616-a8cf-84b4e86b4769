package com.noosh.app.commons.entity.proposal;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.quote.QuoteItem;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "PC_PROPOSAL_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProposalItem extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_PROPOSAL_ITEM_ID_GENERATED")
    @SequenceGenerator(name = "PC_PROPOSAL_ITEM_ID_GENERATED", sequenceName = "PC_PROPOSAL_ITEM_SEQ", allocationSize = 1)
    @Column(name = "PC_PROPOSAL_ITEM_ID")
    private Long id;

    @Column(name = "PC_PROPOSAL_ID")
    private Long proposalId;

    @Column(name = "PC_QUOTE_ITEM_ID")
    private Long quoteItemId;

    @Column(name = "ITEM_INDEX")
    private Long itemIndex;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "PC_PROPOSAL_SPEC_SUMMARY_ID")
    private Long proposalSpecSummaryId;

    @Column(name = "IS_A_S_INFO")
    private Boolean isShowAllSpecInfo;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="PC_QUOTE_ITEM_ID", insertable = false, updatable = false)
    private QuoteItem quoteItem;

    public QuoteItem getQuoteItem() {
        return quoteItem;
    }

    public void setQuoteItem(QuoteItem quoteItem) {
        this.quoteItem = quoteItem;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProposalId() {
        return proposalId;
    }

    public void setProposalId(Long proposalId) {
        this.proposalId = proposalId;
    }

    public Long getQuoteItemId() {
        return quoteItemId;
    }

    public void setQuoteItemId(Long quoteItemId) {
        this.quoteItemId = quoteItemId;
    }

    public Long getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Long itemIndex) {
        this.itemIndex = itemIndex;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getProposalSpecSummaryId() {
        return proposalSpecSummaryId;
    }

    public void setProposalSpecSummaryId(Long proposalSpecSummaryId) {
        this.proposalSpecSummaryId = proposalSpecSummaryId;
    }

    public Boolean getIsShowAllSpecInfo() {
        return isShowAllSpecInfo;
    }

    public void setIsShowAllSpecInfo(Boolean showAllSpecInfo) {
        isShowAllSpecInfo = showAllSpecInfo;
    }
}
