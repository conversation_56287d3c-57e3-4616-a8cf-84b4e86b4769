package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: neals
 * @Date: 12/29/2015
 */
@Entity
@Table(name = "RE_ROLE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Role extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @Column(name = "RE_ROLE_ID")
    private Long id;

    @Column(name = "PARENT_RE_ROLE_ID")
    private Long parentRoleId;

    @Column(name = "BASE_RE_ROLE_ID")
    private Long baseRoleId;

    @Column(name = "RE_ROLE_CLASS_ID")
    private Long roleClassId;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "CONSTANT_TOKEN")
    private String constantToken;

    @Column(name = "NAME")
    private String name;

    @Column(name = "ROLE_NAME_STR")
    private String roleNameStr;

    @Column(name = "ROLE_NAME_STR_ID")
    private Long roleNameStrId;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "CUSTOMIZABLE")
    private Boolean customizable;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentRoleId() {
        return parentRoleId;
    }

    public void setParentRoleId(Long parentRoleId) {
        this.parentRoleId = parentRoleId;
    }

    public Long getBaseRoleId() {
        return baseRoleId;
    }

    public void setBaseRoleId(Long baseRoleId) {
        this.baseRoleId = baseRoleId;
    }

    public Long getRoleClassId() {
        return roleClassId;
    }

    public void setRoleClassId(Long roleClassId) {
        this.roleClassId = roleClassId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRoleNameStr() {
        return roleNameStr;
    }

    public void setRoleNameStr(String roleNameStr) {
        this.roleNameStr = roleNameStr;
    }

    public Long getRoleNameStrId() {
        return roleNameStrId;
    }

    public void setRoleNameStrId(Long roleNameStrId) {
        this.roleNameStrId = roleNameStrId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getCustomizable() {
        return customizable;
    }

    public void setCustomizable(Boolean customizable) {
        this.customizable = customizable;
    }

    @Override
    public String toString() {
        return "Role{" +
                "id=" + id +
                ", parentRoleId=" + parentRoleId +
                ", baseRoleId=" + baseRoleId +
                ", roleClassId=" + roleClassId +
                ", workgroupId=" + workgroupId +
                ", constantToken=" + constantToken +
                ", name='" + name + '\'' +
                ", roleNameStr='" + roleNameStr + '\'' +
                ", roleNameStrId=" + roleNameStrId +
                ", description='" + description + '\'' +
                ", customizable=" + customizable +
                '}';
    }
}
