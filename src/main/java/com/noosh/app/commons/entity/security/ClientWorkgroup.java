package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 4/6/16
 */
@Entity
@Table(name = "BU_CLIENT_WORKGROUP")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ClientWorkgroup extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "clientWorkgroup_id_generated")
    @SequenceGenerator(name = "clientWorkgroup_id_generated", sequenceName = "BU_CLIENT_WORKGROUP_SEQ", allocationSize = 1)
    @Column(name = "BU_CLIENT_WORKGROUP_ID")
    private Long id;

    @Column(name = "OWNER_AC_WORKGROUP_ID")
    private Long ownerAcWorkgroupId;

    @Column(name = "CLIENT_AC_WORKGROUP_ID")
    private Long clientAcWorkgroupId;

    private BigDecimal markupPercent;

    private BigDecimal markup;

    private Long markupAcCurrencyId;

    private Short isMarkupVisible;

    private String name;

    private Long acAddressId;

    private Short isNoosh;

    private Short isReferenced;

    private Short isInactive;

    private String clientCode;

    private Long customPrPropertyId;

    private Short psfRequiresPricing;

    private Long buPaymentMethodId;

    private Long defaultClientUserId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CLIENT_AC_WORKGROUP_ID", referencedColumnName = "AC_WORKGROUP_ID", insertable = false, updatable = false)
    private Workgroup workgroup;

    public Workgroup getWorkgroup() {
        return workgroup;
    }

    public void setWorkgroup(Workgroup workgroup) {
        this.workgroup = workgroup;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOwnerAcWorkgroupId() {
        return ownerAcWorkgroupId;
    }

    public void setOwnerAcWorkgroupId(Long ownerAcWorkgroupId) {
        this.ownerAcWorkgroupId = ownerAcWorkgroupId;
    }

    public Long getClientAcWorkgroupId() {
        return clientAcWorkgroupId;
    }

    public void setClientAcWorkgroupId(Long clientAcWorkgroupId) {
        this.clientAcWorkgroupId = clientAcWorkgroupId;
    }

    public BigDecimal getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(BigDecimal markupPercent) {
        this.markupPercent = markupPercent;
    }

    public BigDecimal getMarkup() {
        return markup;
    }

    public void setMarkup(BigDecimal markup) {
        this.markup = markup;
    }

    public Long getMarkupAcCurrencyId() {
        return markupAcCurrencyId;
    }

    public void setMarkupAcCurrencyId(Long markupAcCurrencyId) {
        this.markupAcCurrencyId = markupAcCurrencyId;
    }

    public Short getMarkupVisible() {
        return isMarkupVisible;
    }

    public void setMarkupVisible(Short markupVisible) {
        isMarkupVisible = markupVisible;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getAcAddressId() {
        return acAddressId;
    }

    public void setAcAddressId(Long acAddressId) {
        this.acAddressId = acAddressId;
    }

    public Short getNoosh() {
        return isNoosh;
    }

    public void setNoosh(Short noosh) {
        isNoosh = noosh;
    }

    public Short getReferenced() {
        return isReferenced;
    }

    public void setReferenced(Short referenced) {
        isReferenced = referenced;
    }

    public Short getInactive() {
        return isInactive;
    }

    public void setInactive(Short inactive) {
        isInactive = inactive;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public Long getCustomPrPropertyId() {
        return customPrPropertyId;
    }

    public void setCustomPrPropertyId(Long customPrPropertyId) {
        this.customPrPropertyId = customPrPropertyId;
    }

    public Short getPsfRequiresPricing() {
        return psfRequiresPricing;
    }

    public void setPsfRequiresPricing(Short psfRequiresPricing) {
        this.psfRequiresPricing = psfRequiresPricing;
    }

    public Long getBuPaymentMethodId() {
        return buPaymentMethodId;
    }

    public void setBuPaymentMethodId(Long buPaymentMethodId) {
        this.buPaymentMethodId = buPaymentMethodId;
    }

    public Long getDefaultClientUserId() {
        return defaultClientUserId;
    }

    public void setDefaultClientUserId(Long defaultClientUserId) {
        this.defaultClientUserId = defaultClientUserId;
    }
}
