package com.noosh.app.commons.entity.category;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.List;
import java.util.StringTokenizer;

/**
 * @Author: neals
 * @Date: 05/18/2016
 */

@Entity
@Table(name = "CO_CATEGORY")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Category extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "category_id_generated")
    @SequenceGenerator(name = "category_id_generated", sequenceName = "CO_CATEGORY_SEQ", allocationSize = 1)
    @Column(name = "CO_CATEGORY_ID")
    private Long id;

    @Column(name = "NAME")
    private String name;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "USER_ID")
    private Long userId;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "OBJECT_ATTR")
    private String objectAttr;

    @Column(name = "IS_DISABLED")
    private Boolean isDisabled;

    @JsonIgnore
    @OneToMany(mappedBy = "category", fetch = FetchType.LAZY)
    private List<CategoryClass> categoryClasses;

    @JsonIgnore
    @OneToMany(mappedBy = "category", fetch = FetchType.LAZY)
    private List<CategoryObject> categoryObjects;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public String getObjectAttr() {
        return objectAttr;
    }

    public void setObjectAttr(String objectAttr) {
        this.objectAttr = objectAttr;
    }

    public Boolean getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Boolean isDisabled) {
        this.isDisabled = isDisabled;
    }

    public List<CategoryClass> getCategoryClasses() {
        return categoryClasses;
    }

    public void setCategoryClasses(List<CategoryClass> categoryClasses) {
        this.categoryClasses = categoryClasses;
    }

    public List<CategoryObject> getCategoryObjects() {
        return categoryObjects;
    }

    public void setCategoryObjects(List<CategoryObject> categoryObjects) {
        this.categoryObjects = categoryObjects;
    }

}
