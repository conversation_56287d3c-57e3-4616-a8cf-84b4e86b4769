package com.noosh.app.commons.entity.proposal;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.logo.WorkgroupLogo;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "PC_PROPOSAL_TEMPLATE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProposalTemplate extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_PROPOSAL_TEMPLATE_ID_GENERATED")
    @SequenceGenerator(name = "PC_PROPOSAL_TEMPLATE_ID_GENERATED", sequenceName = "PC_PROPOSAL_TEMPLATE_SEQ", allocationSize = 1)
    @Column(name = "PC_PROPOSAL_TEMPLATE_ID")
    private Long id;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "COMPANY_NAME")
    private String companyName;

    @Column(name = "COMPANY_AC_ADDRESS_ID")
    private Long companyAddressId;

    @Column(name = "COMPANY_PHONE")
    private String companyPhone;

    @Column(name = "COMPANY_FAX")
    private String companyFax;

    @Column(name = "COMPANY_URL")
    private String companyUrl;

    @Column(name = "INTRO_TEXT")
    private String introText;

    @Column(name = "CONCLUSION_TEXT")
    private String conclusionText;

    @Column(name = "CLOSING_TEXT")
    private String closingText;

    @Column(name = "IS_MARKUP_VISIBLE")
    private Boolean isMarkupVisible;

    @Column(name = "IS_ACTIVE")
    private Boolean isActive;

    @Column(name = "IS_DEFAULT")
    private Boolean isDefault;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "AC_WG_LOGO_ID")
    private Long logoId;

    @Column(name = "USE_SPEC_SUMMARY")
    private Boolean useSpecSummary;

    @Column(name = "IS_SPEC_SUMMARY_COMPACT")
    private Boolean isSpecSummaryCompact;

    @Column(name = "INCLUDE_COVER_PAGE")
    private Boolean includeCoverPage;

    @Column(name = "INCLUDE_COVER_LETTER")
    private Boolean includeCoverLetter;

    @Column(name = "INCLUDE_TERMS_CONDITIONS")
    private Boolean includeTermsConditions;

    @Column(name = "INCLUDE_SIGNATURE_PAGE")
    private Boolean includeSignaturePage;

    @Column(name = "INCLUDE_PAGE_NUMBER")
    private Boolean includePageNumber;

    @Column(name = "LAYOUT")
    private String layout;

    @Column(name = "INCLUDE_PRICE_BREAKOUTS")
    private Boolean includePriceBreakouts;

    @Column(name = "IS_LANDSCAPE")
    private Boolean isLandscape;

    @Column(name = "USE_SPEC_SUMMARY_BORDER")
    private Boolean useSpecSummaryBorder;

    @Column(name = "SPEC_SUMMARY_NUM_COLUMNS")
    private int specSummaryNumColumns;

    @Column(name = "INCLUDE_PROPOSAL_NOTE")
    private Boolean includeProposalNote;

    @Column(name = "PROPOSAL_NOTE")
    private String proposalNote;

    @Column(name = "IS_SEPARATE_SPEC_PRICING")
    private Boolean isSeparateSpecPricing;

    // 1-Top Right, 2-Top Middle, 3-Top Left
    @Column(name = "LOGO_POSITION")
    private Long logoPosition;

    // 1-Default, 2-Customized, 3-Workgroup Color
    @Column(name = "BRANDING_COLORS_TYPE")
    private Long brandingColorsType;

    @Column(name = "CUSTOMIZED_COLORS")
    private String customizedColors;

    @Column(name = "SUMUP_ALL_QUOTED_QUANTITY1")
    private Boolean sumUpAllQuotedQuantity1;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_WG_LOGO_ID", insertable = false, updatable = false)
    private WorkgroupLogo workgroupLogo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getCompanyAddressId() {
        return companyAddressId;
    }

    public void setCompanyAddressId(Long companyAddressId) {
        this.companyAddressId = companyAddressId;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public String getCompanyFax() {
        return companyFax;
    }

    public void setCompanyFax(String companyFax) {
        this.companyFax = companyFax;
    }

    public String getCompanyUrl() {
        return companyUrl;
    }

    public void setCompanyUrl(String companyUrl) {
        this.companyUrl = companyUrl;
    }

    public String getIntroText() {
        return introText;
    }

    public void setIntroText(String introText) {
        this.introText = introText;
    }

    public String getConclusionText() {
        return conclusionText;
    }

    public void setConclusionText(String conclusionText) {
        this.conclusionText = conclusionText;
    }

    public String getClosingText() {
        return closingText;
    }

    public void setClosingText(String closingText) {
        this.closingText = closingText;
    }

    public Boolean isMarkupVisible() {
        return isMarkupVisible;
    }

    public void setMarkupVisible(Boolean markupVisible) {
        isMarkupVisible = markupVisible;
    }


    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean active) {
        isActive = active;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getLogoId() {
        return logoId;
    }

    public void setLogoId(Long logoId) {
        this.logoId = logoId;
    }

    public Boolean isUseSpecSummary() {
        return useSpecSummary;
    }

    public void setUseSpecSummary(Boolean useSpecSummary) {
        this.useSpecSummary = useSpecSummary;
    }

    public Boolean isSpecSummaryCompact() {
        return isSpecSummaryCompact;
    }

    public void setSpecSummaryCompact(Boolean specSummaryCompact) {
        isSpecSummaryCompact = specSummaryCompact;
    }

    public Boolean isIncludeCoverPage() {
        return includeCoverPage;
    }

    public void setIncludeCoverPage(Boolean includeCoverPage) {
        this.includeCoverPage = includeCoverPage;
    }

    public Boolean isIncludeCoverLetter() {
        return includeCoverLetter;
    }

    public void setIncludeCoverLetter(Boolean includeCoverLetter) {
        this.includeCoverLetter = includeCoverLetter;
    }

    public Boolean isIncludeTermsConditions() {
        return includeTermsConditions;
    }

    public void setIncludeTermsConditions(Boolean includeTermsConditions) {
        this.includeTermsConditions = includeTermsConditions;
    }

    public Boolean isIncludeSignaturePage() {
        return includeSignaturePage;
    }

    public void setIncludeSignaturePage(Boolean includeSignaturePage) {
        this.includeSignaturePage = includeSignaturePage;
    }

    public Boolean isIncludePageNumber() {
        return includePageNumber;
    }

    public void setIncludePageNumber(Boolean includePageNumber) {
        this.includePageNumber = includePageNumber;
    }

    public String getLayout() {
        return layout;
    }

    public void setLayout(String layout) {
        this.layout = layout;
    }

    public Boolean isIncludePriceBreakouts() {
        return includePriceBreakouts;
    }

    public void setIncludePriceBreakouts(Boolean includePriceBreakouts) {
        this.includePriceBreakouts = includePriceBreakouts;
    }

    public Boolean isLandscape() {
        return isLandscape;
    }

    public void setLandscape(Boolean landscape) {
        isLandscape = landscape;
    }

    public Boolean isUseSpecSummaryBorder() {
        return useSpecSummaryBorder;
    }

    public void setUseSpecSummaryBorder(Boolean useSpecSummaryBorder) {
        this.useSpecSummaryBorder = useSpecSummaryBorder;
    }

    public int getSpecSummaryNumColumns() {
        return specSummaryNumColumns;
    }

    public void setSpecSummaryNumColumns(int specSummaryNumColumns) {
        this.specSummaryNumColumns = specSummaryNumColumns;
    }

    public Boolean isIncludeProposalNote() {
        return includeProposalNote;
    }

    public void setIncludeProposalNote(Boolean includeProposalNote) {
        this.includeProposalNote = includeProposalNote;
    }

    public String getProposalNote() {
        return proposalNote;
    }

    public void setProposalNote(String proposalNote) {
        this.proposalNote = proposalNote;
    }

    public Boolean isSeparateSpecPricing() {
        return isSeparateSpecPricing;
    }

    public void setSeparateSpecPricing(Boolean separateSpecPricing) {
        isSeparateSpecPricing = separateSpecPricing;
    }

    public Long getLogoPosition() {
        return logoPosition;
    }

    public void setLogoPosition(Long logoPosition) {
        this.logoPosition = logoPosition;
    }

    public Long getBrandingColorsType() {
        return brandingColorsType;
    }

    public void setBrandingColorsType(Long brandingColorsType) {
        this.brandingColorsType = brandingColorsType;
    }

    public String getCustomizedColors() {
        return customizedColors;
    }

    public void setCustomizedColors(String customizedColors) {
        this.customizedColors = customizedColors;
    }

    public Boolean isSumUpAllQuotedQuantity1() {
        return sumUpAllQuotedQuantity1;
    }

    public void setSumUpAllQuotedQuantity1(Boolean sumUpAllQuotedQuantity1) {
        this.sumUpAllQuotedQuantity1 = sumUpAllQuotedQuantity1;
    }

    public WorkgroupLogo getWorkgroupLogo() {
        return workgroupLogo;
    }

    public void setWorkgroupLogo(WorkgroupLogo workgroupLogo) {
        this.workgroupLogo = workgroupLogo;
    }
}
