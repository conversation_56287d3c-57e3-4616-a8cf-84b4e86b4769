package com.noosh.app.commons.entity.quote;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: neals
 * @Date: 12/09/2015
 */
@Entity
@Table(name = "PC_QUOTE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Quote extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @Column(name = "PC_QUOTE_ID")
    private Long id;

    @Column(name = "PC_RFQ_ID")
    private Long rfqId;

    @Column(name = "PC_RFQ_SUPPLIER_ID")
    private Long rfqSupplierId;

    @Column(name = "reference")
    private String reference;

    @Column(name = "OWNER_REFERENCE")
    private String ownerReference;

    @Column(name = "TITLE")
    private String title;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "COMMENTS")
    private String comments;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "EXPIRATION_DATE")
    private LocalDateTime expirationDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "COMPLETION_DATE")
    private LocalDateTime completionDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "SUBMIT_DATE")
    private LocalDateTime submitDate;

    @Column(name = "BUYER_AC_WORKGROUP_ID")
    private Long buyerWorkgroupId;

    @Column(name = "SUPPLIER_AC_WORKGROUP_ID")
    private Long supplierWorkgroupId;

    @Column(name = "SUPPLIER_USER_ID")
    private Long supplierUserId;

    @Column(name = "SUPPLIER_AC_TERMS_ID")
    private Long supplierTermsId;

    @Column(name = "CO_SERVICE_ID")
    private Long serviceId;

    @Column(name = "OC_OBJECT_STATE_ID")
    private Long stateId;

    @Column(name = "OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "STATE_CHANGE_COMMENT")
    private String stateChangeComment;

    @Column(name = "IS_SUPPLIER_VISIBLE")
    private Boolean isSupplierVisible;

    @Column(name = "TAX")
    private BigDecimal tax;

    @Column(name = "TAX_AC_CURRENCY_ID")
    private Long taxCurrencyId;

    @Column(name = "SHIPPING")
    private BigDecimal shipping;

    @Column(name = "SHIPPING_AC_CURRENCY_ID")
    private Long shippingCurrencyId;

    @Column(name = "BU_CLIENT_WORKGROUP_ID")
    private Long clientWorkgroupId;

    @Column(name = "ITEMIZED_TNS")
    private Boolean isItemizedTns;

    @Column(name = "IS_GENERATED")
    private Boolean isGenerated;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "ACCEPT_DATE")
    private LocalDateTime acceptDate;

    @Column(name = "AC_SOURCE_TYPE_ID")
    private Boolean sourceTypeId;

    @Column(name = "IS_CREATED_MARGIN_ON")
    private Boolean isCreatedAtMarginOn;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "quote")
    private List<QuoteItem> quoteItemList;


    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRfqId() {
        return rfqId;
    }

    public void setRfqId(Long rfqId) {
        this.rfqId = rfqId;
    }

    public Long getRfqSupplierId() {
        return rfqSupplierId;
    }

    public void setRfqSupplierId(Long rfqSupplierId) {
        this.rfqSupplierId = rfqSupplierId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public Long getSupplierUserId() {
        return supplierUserId;
    }

    public void setSupplierUserId(Long supplierUserId) {
        this.supplierUserId = supplierUserId;
    }

    public Long getSupplierTermsId() {
        return supplierTermsId;
    }

    public void setSupplierTermsId(Long supplierTermsId) {
        this.supplierTermsId = supplierTermsId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public String getStateChangeComment() {
        return stateChangeComment;
    }

    public void setStateChangeComment(String stateChangeComment) {
        this.stateChangeComment = stateChangeComment;
    }

    public Boolean isSupplierVisible() {
        return isSupplierVisible != null ? isSupplierVisible : false;
    }

    public void setSupplierVisible(Boolean supplierVisible) {
        isSupplierVisible = supplierVisible;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public Long getClientWorkgroupId() {
        return clientWorkgroupId;
    }

    public void setClientWorkgroupId(Long clientWorkgroupId) {
        this.clientWorkgroupId = clientWorkgroupId;
    }

    public Boolean isItemizedTns() {
        return isItemizedTns;
    }

    public void setItemizedTns(Boolean itemizedTns) {
        isItemizedTns = itemizedTns;
    }

    public Boolean isGenerated() {
        return isGenerated;
    }

    public void setGenerated(Boolean generated) {
        isGenerated = generated;
    }

    public LocalDateTime getAcceptDate() {
        return acceptDate;
    }

    public void setAcceptDate(LocalDateTime acceptDate) {
        this.acceptDate = acceptDate;
    }

    public Boolean isSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Boolean sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public Boolean getIsCreatedAtMarginOn() {
        return isCreatedAtMarginOn;
    }

    public void setIsCreatedAtMarginOn(Boolean createdAtMarginOn) {
        isCreatedAtMarginOn = createdAtMarginOn;
    }

    public List<QuoteItem> getQuoteItemList() {
        return quoteItemList;
    }

    public void setQuoteItemList(List<QuoteItem> quoteItemList) {
        this.quoteItemList = quoteItemList;
    }
}
