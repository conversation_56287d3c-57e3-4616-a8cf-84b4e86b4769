package com.noosh.app.commons.entity.routing;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 10/16/17
 */
@Entity
@Table(name = "PS_ROUTING_RECIPIENT")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONE)
public class RoutingRecipient extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PS_ROUTING_RECIPIENT_ID_generated")
    @SequenceGenerator(name = "PS_ROUTING_RECIPIENT_ID_generated", sequenceName = "PS_ROUTING_RECIPIENT_SEQ", allocationSize = 1)
    @Column(name = "PS_ROUTING_RECIPIENT_ID")
    private Long id;

    @Column(name = "PS_ROUTING_SLIP_ID")
    private Long psRoutingSlipId;

    @Column(name = "TO_USER_ID")
    private Long toUserId;

    private Long routingOrder;

    private Long response;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime dateRouted;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime dateResponded;

    private Long retriesRemaining;

    private String comments;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TO_USER_ID", referencedColumnName = "USER_ID", insertable = false, updatable = false)
    private AccountUser toUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PS_ROUTING_SLIP_ID", referencedColumnName = "PS_ROUTING_SLIP_ID", insertable = false, updatable = false)
    private RoutingSlip routingSlip;

    public RoutingSlip getRoutingSlip() {
        return routingSlip;
    }

    public void setRoutingSlip(RoutingSlip routingSlip) {
        this.routingSlip = routingSlip;
    }

    public AccountUser getToUser() {
        return toUser;
    }

    public void setToUser(AccountUser toUser) {
        this.toUser = toUser;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPsRoutingSlipId() {
        return psRoutingSlipId;
    }

    public void setPsRoutingSlipId(Long psRoutingSlipId) {
        this.psRoutingSlipId = psRoutingSlipId;
    }

    public Long getToUserId() {
        return toUserId;
    }

    public void setToUserId(Long toUserId) {
        this.toUserId = toUserId;
    }

    public Long getRoutingOrder() {
        return routingOrder;
    }

    public void setRoutingOrder(Long routingOrder) {
        this.routingOrder = routingOrder;
    }

    public Long getResponse() {
        return response;
    }

    public void setResponse(Long response) {
        this.response = response;
    }

    public LocalDateTime getDateRouted() {
        return dateRouted;
    }

    public void setDateRouted(LocalDateTime dateRouted) {
        this.dateRouted = dateRouted;
    }

    public LocalDateTime getDateResponded() {
        return dateResponded;
    }

    public void setDateResponded(LocalDateTime dateResponded) {
        this.dateResponded = dateResponded;
    }

    public Long getRetriesRemaining() {
        return retriesRemaining;
    }

    public void setRetriesRemaining(Long retriesRemaining) {
        this.retriesRemaining = retriesRemaining;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
}
