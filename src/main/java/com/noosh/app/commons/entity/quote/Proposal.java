package com.noosh.app.commons.entity.quote;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: neals
 * @Date: 06/15/2016
 */
@Entity
@Table(name="PC_PROPOSAL")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Proposal extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name="PC_PROPOSAL_ID")
    private Long id;

    @Column(name="PC_QUOTE_ID")
    private Long quoteId;

    @Column(name="TITLE")
    private String title;

    @Column(name="REFERENCE")
    private String reference;

    @Column(name="OWNER_REFERENCE")
    private String ownerReference;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="PREPARE_DATE")
    private LocalDateTime prepareDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="RESPOND_BY_DATE")
    private LocalDateTime respondByDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="EXPIRATION_DATE")
    private LocalDateTime expirationDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="COMPLETION_DATE")
    private LocalDateTime completionDate;

    @Column(name="SALUTE_TEXT")
    private String saluteText;

    @Column(name="INTRO_TEXT")
    private String introText;

    @Column(name="CONCLUSION_TEXT")
    private String conclusionText;

    @Column(name="CLOSING_TEXT")
    private String closingText;

    @Column(name="IS_MARKUP_VISIBLE")
    private Boolean isMarkupVisible;

    @Column(name="IS_AGGREGATION_ENABLE")
    private Boolean isAggregationEnable;

    @Column(name="TAX")
    private BigDecimal tax;

    @Column(name="SHIPPING")
    private BigDecimal shipping;

    @Column(name="AC_WG_LOGO_ID")
    private Long logoId;

    @Column(name="OUTSOURCER_NAME")
    private String outsourcerName;

    @Column(name="OUTSOURCER_AC_ADDRESS_ID")
    private Long outsourcerAddressId;

    @Column(name="OUTSOURCER_PHONE")
    private String outsourcerPhone;

    @Column(name="OUTSOURCER_FAX")
    private String outsourcerFax;

    @Column(name="OUTSOURCER_URL")
    private String outsourcerUrl;

    @Column(name="CLIENT_NAME")
    private String clientName;

    @Column(name="CLIENT_AC_ADDRESS_ID")
    private Long clientAddressId;

    @Column(name="CLIENT_PHONE")
    private String clientPhone;

    @Column(name="CLIENT_FAX")
    private String clientFax;

    @Column(name="CLIENT_URL")
    private String clientUrl;

    @Column(name="CO_SERVICE_ID")
    private Long serviceId;

    @Column(name="AC_TERMS_ID")
    private Long termsId;

    @Column(name="OC_OBJECT_STATE_ID")
    private Long objectStateId;

    @Column(name="CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name="STATE_CHANGE_COMMENT")
    private String stateChangeComment;

    @Column(name="PREPARED_USER_ID")
    private Long preparedUserId;

    @Column(name="PREPARED_NAME")
    private String preparedName;

    @Column(name="PREPARED_TITLE")
    private String preparedTitle;

    @Column(name="USE_SPEC_SUMMARY")
    private Boolean useSpecSummary;

    @Column(name="IS_SPEC_SUMMARY_COMPACT")
    private Boolean isSpecSummaryCompact;

    @Column(name="INCLUDE_COVER_PAGE")
    private Boolean includeCoverPage;

    @Column(name="INCLUDE_COVER_LETTER")
    private Boolean includeCoverLetter;

    @Column(name="INCLUDE_TERMS_CONDITIONS")
    private Boolean includeTermsAndConditions;

    @Column(name="INCLUDE_SIGNATURE_PAGE")
    private Boolean includeSignaturePage;

    @Column(name="INCLUDE_PAGE_NUMBER")
    private Boolean includePageNumber;

    @Column(name="IS_LANDSCAPE")
    private Boolean isLandscape;

    @Column(name="INCLUDE_PRICE_BREAKOUTS")
    private Boolean includePriceBreakouts;

    @Column(name="LAYOUT")
    private String layout;

    @Column(name="USE_SPEC_SUMMARY_BORDER")
    private Boolean useSpecSummaryBorder;

    @Column(name="SPEC_SUMMARY_NUM_COLUMNS")
    private Long specSummaryColumns;

    @Column(name="INCLUDE_PROPOSAL_NOTE")
    private Boolean includeProposalNote;

    @Column(name="PROPOSAL_NOTE")
    private String proposalNote;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public LocalDateTime getPrepareDate() {
        return prepareDate;
    }

    public void setPrepareDate(LocalDateTime prepareDate) {
        this.prepareDate = prepareDate;
    }

    public LocalDateTime getRespondByDate() {
        return respondByDate;
    }

    public void setRespondByDate(LocalDateTime respondByDate) {
        this.respondByDate = respondByDate;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public String getSaluteText() {
        return saluteText;
    }

    public void setSaluteText(String saluteText) {
        this.saluteText = saluteText;
    }

    public String getIntroText() {
        return introText;
    }

    public void setIntroText(String introText) {
        this.introText = introText;
    }

    public String getConclusionText() {
        return conclusionText;
    }

    public void setConclusionText(String conclusionText) {
        this.conclusionText = conclusionText;
    }

    public String getClosingText() {
        return closingText;
    }

    public void setClosingText(String closingText) {
        this.closingText = closingText;
    }

    public Boolean getIsMarkupVisible() {
        return isMarkupVisible;
    }

    public void setIsMarkupVisible(Boolean isMarkupVisible) {
        this.isMarkupVisible = isMarkupVisible;
    }

    public Boolean getIsAggregationEnable() {
        return isAggregationEnable;
    }

    public void setIsAggregationEnable(Boolean isAggregationEnable) {
        this.isAggregationEnable = isAggregationEnable;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getLogoId() {
        return logoId;
    }

    public void setLogoId(Long logoId) {
        this.logoId = logoId;
    }

    public String getOutsourcerName() {
        return outsourcerName;
    }

    public void setOutsourcerName(String outsourcerName) {
        this.outsourcerName = outsourcerName;
    }

    public Long getOutsourcerAddressId() {
        return outsourcerAddressId;
    }

    public void setOutsourcerAddressId(Long outsourcerAddressId) {
        this.outsourcerAddressId = outsourcerAddressId;
    }

    public String getOutsourcerPhone() {
        return outsourcerPhone;
    }

    public void setOutsourcerPhone(String outsourcerPhone) {
        this.outsourcerPhone = outsourcerPhone;
    }

    public String getOutsourcerFax() {
        return outsourcerFax;
    }

    public void setOutsourcerFax(String outsourcerFax) {
        this.outsourcerFax = outsourcerFax;
    }

    public String getOutsourcerUrl() {
        return outsourcerUrl;
    }

    public void setOutsourcerUrl(String outsourcerUrl) {
        this.outsourcerUrl = outsourcerUrl;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Long getClientAddressId() {
        return clientAddressId;
    }

    public void setClientAddressId(Long clientAddressId) {
        this.clientAddressId = clientAddressId;
    }

    public String getClientPhone() {
        return clientPhone;
    }

    public void setClientPhone(String clientPhone) {
        this.clientPhone = clientPhone;
    }

    public String getClientFax() {
        return clientFax;
    }

    public void setClientFax(String clientFax) {
        this.clientFax = clientFax;
    }

    public String getClientUrl() {
        return clientUrl;
    }

    public void setClientUrl(String clientUrl) {
        this.clientUrl = clientUrl;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getTermsId() {
        return termsId;
    }

    public void setTermsId(Long termsId) {
        this.termsId = termsId;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public String getStateChangeComment() {
        return stateChangeComment;
    }

    public void setStateChangeComment(String stateChangeComment) {
        this.stateChangeComment = stateChangeComment;
    }

    public Long getPreparedUserId() {
        return preparedUserId;
    }

    public void setPreparedUserId(Long preparedUserId) {
        this.preparedUserId = preparedUserId;
    }

    public String getPreparedName() {
        return preparedName;
    }

    public void setPreparedName(String preparedName) {
        this.preparedName = preparedName;
    }

    public String getPreparedTitle() {
        return preparedTitle;
    }

    public void setPreparedTitle(String preparedTitle) {
        this.preparedTitle = preparedTitle;
    }

    public Boolean getUseSpecSummary() {
        return useSpecSummary;
    }

    public void setUseSpecSummary(Boolean useSpecSummary) {
        this.useSpecSummary = useSpecSummary;
    }

    public Boolean getIsSpecSummaryCompact() {
        return isSpecSummaryCompact;
    }

    public void setIsSpecSummaryCompact(Boolean isSpecSummaryCompact) {
        this.isSpecSummaryCompact = isSpecSummaryCompact;
    }

    public Boolean getIncludeCoverPage() {
        return includeCoverPage;
    }

    public void setIncludeCoverPage(Boolean includeCoverPage) {
        this.includeCoverPage = includeCoverPage;
    }

    public Boolean getIncludeCoverLetter() {
        return includeCoverLetter;
    }

    public void setIncludeCoverLetter(Boolean includeCoverLetter) {
        this.includeCoverLetter = includeCoverLetter;
    }

    public Boolean getIncludeTermsAndConditions() {
        return includeTermsAndConditions;
    }

    public void setIncludeTermsAndConditions(Boolean includeTermsAndConditions) {
        this.includeTermsAndConditions = includeTermsAndConditions;
    }

    public Boolean getIncludeSignaturePage() {
        return includeSignaturePage;
    }

    public void setIncludeSignaturePage(Boolean includeSignaturePage) {
        this.includeSignaturePage = includeSignaturePage;
    }

    public Boolean getIncludePageNumber() {
        return includePageNumber;
    }

    public void setIncludePageNumber(Boolean includePageNumber) {
        this.includePageNumber = includePageNumber;
    }

    public Boolean getIsLandscape() {
        return isLandscape;
    }

    public void setIsLandscape(Boolean isLandscape) {
        this.isLandscape = isLandscape;
    }

    public Boolean getIncludePriceBreakouts() {
        return includePriceBreakouts;
    }

    public void setIncludePriceBreakouts(Boolean includePriceBreakouts) {
        this.includePriceBreakouts = includePriceBreakouts;
    }

    public String getLayout() {
        return layout;
    }

    public void setLayout(String layout) {
        this.layout = layout;
    }

    public Boolean getUseSpecSummaryBorder() {
        return useSpecSummaryBorder;
    }

    public void setUseSpecSummaryBorder(Boolean useSpecSummaryBorder) {
        this.useSpecSummaryBorder = useSpecSummaryBorder;
    }

    public Long getSpecSummaryColumns() {
        return specSummaryColumns;
    }

    public void setSpecSummaryColumns(Long specSummaryColumns) {
        this.specSummaryColumns = specSummaryColumns;
    }

    public Boolean getIncludeProposalNote() {
        return includeProposalNote;
    }

    public void setIncludeProposalNote(Boolean includeProposalNote) {
        this.includeProposalNote = includeProposalNote;
    }

    public String getProposalNote() {
        return proposalNote;
    }

    public void setProposalNote(String proposalNote) {
        this.proposalNote = proposalNote;
    }

}
