package com.noosh.app.commons.entity.spec;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * User: leilaz
 * Date: 5/19/16
 */
@Entity
@Table(name = "SP_SPEC")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Spec extends NooshAuditingEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "spec_id_generated")
    @SequenceGenerator(name = "spec_id_generated", sequenceName = "SP_SPEC_SEQ", allocationSize = 1)
    @Column(name="SP_SPEC_ID")
    private Long id;

    private String specName;

    private Long originalSpecId;

    private Long cloneOf;

    @Column(name = "PR_PROPERTY_ID")
    private Long prPropertyId;

    @Column(name="SP_SPEC_TYPE_ID")
    private Long spSpecTypeId;

    private LocalDateTime contentsModDate;

    private Short isLocked;

    private Short isTemplate;

    private LocalDateTime specCreateDate;

    private Long specCreateUserId;

    private Long templateSpSpecId;

    private Short isActive;

    private Short includeAup;

    @Column(name = "SP_SPEC_REFERENCE_ID")
    private Long spSpecReferenceId;

    private Long acAttrProductTypeId;

    private Long acAttrUnspscTypeId;

    private BigDecimal quantity1;

    private BigDecimal quantity2;

    private BigDecimal quantity3;

    private BigDecimal quantity4;

    private BigDecimal quantity5;

    private Long lockCount;

    private Short isObsolete;

    private BigDecimal specWeight;

    private Short isItemVersion;

    private Short isCurrentVersion;

    private String versionNumber;

    private Long ocObjectStateId;

    private Long buUofmId;

    private String revisionReason;

    private Long specUserStateId;

    private Short isPsfSpecTemplate;

    private String custom1;

    private String tag;

    private Long defaultSpecTypeId;

    private Long acSourceTypeId;

    private static final long serialVersionUID = 1L;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SP_SPEC_REFERENCE_ID", insertable = false, updatable = false)
    private SpecReference specReference;

    @OneToOne(mappedBy = "spec", fetch = FetchType.EAGER)
    private SpecNode specNode;

    public SpecReference getSpecReference() {
        return specReference;
    }

    public void setSpecReference(SpecReference specReference) {
        this.specReference = specReference;
    }

    public SpecNode getSpecNode() {
        return specNode;
    }

    public void setSpecNode(SpecNode specNode) {
        this.specNode = specNode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName == null ? null : specName.trim();
    }

    public Long getOriginalSpecId() {
        return originalSpecId;
    }

    public void setOriginalSpecId(Long originalSpecId) {
        this.originalSpecId = originalSpecId;
    }

    public Long getCloneOf() {
        return cloneOf;
    }

    public void setCloneOf(Long cloneOf) {
        this.cloneOf = cloneOf;
    }

    public Long getPrPropertyId() {
        return prPropertyId;
    }

    public void setPrPropertyId(Long prPropertyId) {
        this.prPropertyId = prPropertyId;
    }

    public Long getSpSpecTypeId() {
        return spSpecTypeId;
    }

    public void setSpSpecTypeId(Long spSpecTypeId) {
        this.spSpecTypeId = spSpecTypeId;
    }

    public LocalDateTime getContentsModDate() {
        return contentsModDate;
    }

    public void setContentsModDate(LocalDateTime contentsModDate) {
        this.contentsModDate = contentsModDate;
    }

    public Short getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(Short isLocked) {
        this.isLocked = isLocked;
    }

    public Short getIsTemplate() {
        return isTemplate;
    }

    public void setIsTemplate(Short isTemplate) {
        this.isTemplate = isTemplate;
    }

    public LocalDateTime getSpecCreateDate() {
        return specCreateDate;
    }

    public void setSpecCreateDate(LocalDateTime specCreateDate) {
        this.specCreateDate = specCreateDate;
    }

    public Long getSpecCreateUserId() {
        return specCreateUserId;
    }

    public void setSpecCreateUserId(Long specCreateUserId) {
        this.specCreateUserId = specCreateUserId;
    }

    public Long getTemplateSpSpecId() {
        return templateSpSpecId;
    }

    public void setTemplateSpSpecId(Long templateSpSpecId) {
        this.templateSpSpecId = templateSpSpecId;
    }

    public Short getIsActive() {
        return isActive;
    }

    public void setIsActive(Short isActive) {
        this.isActive = isActive;
    }

    public Short getIncludeAup() {
        return includeAup;
    }

    public void setIncludeAup(Short includeAup) {
        this.includeAup = includeAup;
    }

    public Long getSpSpecReferenceId() {
        return spSpecReferenceId;
    }

    public void setSpSpecReferenceId(Long spSpecReferenceId) {
        this.spSpecReferenceId = spSpecReferenceId;
    }

    public Long getAcAttrProductTypeId() {
        return acAttrProductTypeId;
    }

    public void setAcAttrProductTypeId(Long acAttrProductTypeId) {
        this.acAttrProductTypeId = acAttrProductTypeId;
    }

    public Long getAcAttrUnspscTypeId() {
        return acAttrUnspscTypeId;
    }

    public void setAcAttrUnspscTypeId(Long acAttrUnspscTypeId) {
        this.acAttrUnspscTypeId = acAttrUnspscTypeId;
    }

    public BigDecimal getQuantity1() {
        return quantity1;
    }

    public void setQuantity1(BigDecimal quantity1) {
        this.quantity1 = quantity1;
    }

    public BigDecimal getQuantity2() {
        return quantity2;
    }

    public void setQuantity2(BigDecimal quantity2) {
        this.quantity2 = quantity2;
    }

    public BigDecimal getQuantity3() {
        return quantity3;
    }

    public void setQuantity3(BigDecimal quantity3) {
        this.quantity3 = quantity3;
    }

    public BigDecimal getQuantity4() {
        return quantity4;
    }

    public void setQuantity4(BigDecimal quantity4) {
        this.quantity4 = quantity4;
    }

    public BigDecimal getQuantity5() {
        return quantity5;
    }

    public void setQuantity5(BigDecimal quantity5) {
        this.quantity5 = quantity5;
    }

    public Long getLockCount() {
        return lockCount;
    }

    public void setLockCount(Long lockCount) {
        this.lockCount = lockCount;
    }

    public Short getIsObsolete() {
        return isObsolete;
    }

    public void setIsObsolete(Short isObsolete) {
        this.isObsolete = isObsolete;
    }

    public BigDecimal getSpecWeight() {
        return specWeight;
    }

    public void setSpecWeight(BigDecimal specWeight) {
        this.specWeight = specWeight;
    }

    public Short getIsItemVersion() {
        return isItemVersion;
    }

    public void setIsItemVersion(Short isItemVersion) {
        this.isItemVersion = isItemVersion;
    }

    public Short getIsCurrentVersion() {
        return isCurrentVersion;
    }

    public void setIsCurrentVersion(Short isCurrentVersion) {
        this.isCurrentVersion = isCurrentVersion;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber == null ? null : versionNumber.trim();
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }

    public Long getBuUofmId() {
        return buUofmId;
    }

    public void setBuUofmId(Long buUofmId) {
        this.buUofmId = buUofmId;
    }

    public String getRevisionReason() {
        return revisionReason;
    }

    public void setRevisionReason(String revisionReason) {
        this.revisionReason = revisionReason == null ? null : revisionReason.trim();
    }

    public Long getSpecUserStateId() {
        return specUserStateId;
    }

    public void setSpecUserStateId(Long specUserStateId) {
        this.specUserStateId = specUserStateId;
    }

    public Short getIsPsfSpecTemplate() {
        return isPsfSpecTemplate;
    }

    public void setIsPsfSpecTemplate(Short isPsfSpecTemplate) {
        this.isPsfSpecTemplate = isPsfSpecTemplate;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1 == null ? null : custom1.trim();
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag == null ? null : tag.trim();
    }

    public Long getDefaultSpecTypeId() {
        return defaultSpecTypeId;
    }

    public void setDefaultSpecTypeId(Long defaultSpecTypeId) {
        this.defaultSpecTypeId = defaultSpecTypeId;
    }

    public Long getAcSourceTypeId() {
        return acSourceTypeId;
    }

    public void setAcSourceTypeId(Long acSourceTypeId) {
        this.acSourceTypeId = acSourceTypeId;
    }

    public boolean getIsImmutable() {
        if ((getIsLocked() != null && getIsLocked().shortValue() == (short) 1) || (getLockCount() != null && getLockCount().longValue() > (long) 0))
            return true;
        return false;
    }
}
