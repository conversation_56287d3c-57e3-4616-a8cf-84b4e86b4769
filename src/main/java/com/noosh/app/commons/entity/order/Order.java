package com.noosh.app.commons.entity.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "OR_ORDER")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Order implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "order_id_generator")
    @SequenceGenerator(name = "order_id_generator", sequenceName = "OR_ORDER_SEQ", allocationSize = 1)
    @Column(name = "or_order_id")
    private Long id;

    @Column(name = "or_order_version_id")
    private Long orderVersionId;

    @Column(name = "parent_or_order_id")
    private Long parentOrderId;

    @Column(name = "aggregated_or_order_id")
    private Long aggregatedOrderId;

    @Column(name = "is_closing")
    private Boolean isClosing;

    @Column(name = "close_rfe_on_submit")
    private Boolean closeRfeOnSubmit;

    @Column(name = "dismiss_on_submit")
    private Boolean dismissOnSubmit;

    @Column(name = "orig_or_order_id")
    private Long origOrderId;

    @Column(name = "ac_source_type_id")
    private Long sourceTypeId;

    @Column(name = "create_user_id", nullable = false)
    private Long createUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "create_date", nullable = false)
    private LocalDateTime createDate = LocalDateTime.now();

    @Column(name = "mod_user_id")
    private Long modUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "mod_date")
    private LocalDateTime modDate = LocalDateTime.now();

    @Column(name = "inv_adj_parent_order_id")
    private Long invoiceAdjustmentParentOrderId;

    @JsonIgnore
    @OneToMany(orphanRemoval = true, fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "order")
    private Set<OrderState> orderStateSet = new HashSet<>();

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public Long getModUserId() {
        return modUserId;
    }

    public void setModUserId(Long modUserId) {
        this.modUserId = modUserId;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public Long getInvoiceAdjustmentParentOrderId() {
        return invoiceAdjustmentParentOrderId;
    }

    public void setInvoiceAdjustmentParentOrderId(Long invoiceAdjustmentParentOrderId) {
        this.invoiceAdjustmentParentOrderId = invoiceAdjustmentParentOrderId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderVersionId() {
        return orderVersionId;
    }

    public void setOrderVersionId(Long orderVersionId) {
        this.orderVersionId = orderVersionId;
    }

    public Long getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(Long parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public Long getAggregatedOrderId() {
        return aggregatedOrderId;
    }

    public void setAggregatedOrderId(Long aggregatedOrderId) {
        this.aggregatedOrderId = aggregatedOrderId;
    }

    public Boolean getIsClosing() {
        return isClosing == null ? Boolean.FALSE : isClosing;
    }

    public void setIsClosing(Boolean isClosing) {
        this.isClosing = isClosing;
    }

    public Boolean getCloseRfeOnSubmit() {
        return closeRfeOnSubmit == null ? Boolean.FALSE : closeRfeOnSubmit;
    }

    public void setCloseRfeOnSubmit(Boolean closeRfeOnSubmit) {
        this.closeRfeOnSubmit = closeRfeOnSubmit;
    }

    public Boolean getDismissOnSubmit() {
        return dismissOnSubmit == null ? Boolean.FALSE : dismissOnSubmit;
    }

    public void setDismissOnSubmit(Boolean dismissOnSubmit) {
        this.dismissOnSubmit = dismissOnSubmit;
    }

    public Long getOrigOrderId() {
        return origOrderId;
    }

    public void setOrigOrderId(Long origOrderId) {
        this.origOrderId = origOrderId;
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public Set<OrderState> getOrderStateSet() {
        return orderStateSet;
    }

    public void setOrderStateSet(Set<OrderState> orderStateSet) {
        this.orderStateSet = orderStateSet;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Order order = (Order) o;

        return id.equals(order.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
