package com.noosh.app.commons.entity.rating;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/19/16
 */
@Entity
@Table(name = "SR_QUESTION")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SrQuestion extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SrQuestion_id_generated")
    @SequenceGenerator(name = "SrQuestion_id_generated", sequenceName = "SR_QUESTION_SEQ", allocationSize = 1)
    @Column(name = "SR_QUESTION_ID")
    private Long id;

    @Column(name = "SR_QUESTIONNAIRE_ID")
    private Long questionNaireId;

    @Column(name = "SR_SECTION_ID")
    private Long sectionId;

    @Column(name = "TEXT")
    private String text;

    @Column(name = "WEIGHT")
    private Long weight;

    @Column(name = "ORDINAL")
    private Long ordinal;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuestionNaireId() {
        return questionNaireId;
    }

    public void setQuestionNaireId(Long questionNaireId) {
        this.questionNaireId = questionNaireId;
    }

    public Long getSectionId() {
        return sectionId;
    }

    public void setSectionId(Long sectionId) {
        this.sectionId = sectionId;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Long getWeight() {
        return weight;
    }

    public void setWeight(Long weight) {
        this.weight = weight;
    }

    public Long getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Long ordinal) {
        this.ordinal = ordinal;
    }
}
