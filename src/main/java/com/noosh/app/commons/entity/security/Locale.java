package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name="AC_LOCALE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Locale extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ac_locale_id_generated")
    @SequenceGenerator(name = "ac_locale_id_generated", sequenceName = "AC_LOCALE_SEQ", allocationSize = 1)
    @Column(name="AC_LOCALE_ID")
    private Long id;

    @Column(name="LOCALE_CODE")
    private String localeCode;

    @Column(name="CONSTANT_TOKEN")
    private String constantToken;

    @Column(name="DESCRIPTION_STR_ID")
    private Long descriptionStrId;

    @Column(name="SORT_LANGUAGE")
    private String sortLanguage;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLocaleCode() {
        return localeCode;
    }

    public void setLocaleCode(String localeCode) {
        this.localeCode = localeCode;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public String getSortLanguage() {
        return sortLanguage;
    }

    public void setSortLanguage(String sortLanguage) {
        this.sortLanguage = sortLanguage;
    }
}