package com.noosh.app.commons.entity.estimate;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.rfe.RfeItem;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 5/4/17
 */
@Entity
@Table(name="EM_ESTIMATE_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class EstimateItem extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 2295983712515048061L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "em_estimate_item_id_generated")
    @SequenceGenerator(name = "em_estimate_item_id_generated", sequenceName = "EM_ESTIMATE_ITEM_SEQ", allocationSize = 1)
    @Column(name = "EM_ESTIMATE_ITEM_ID")
    private Long id;

    @Column(name = "EM_ESTIMATE_ID")
    private Long estimateId;

    @Column(name = "EM_RFE_ITEM_ID")
    private Long rfeItemId;

    @Column(name = "REFERENCE")
    private String reference;

    @Column(name = "OWNER_REFERENCE")
    private String ownerReference;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "COMMENTS")
    private String comments;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "EXPIRATION_DATE")
    private LocalDateTime expirationDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "SUBMIT_DATE")
    private LocalDateTime submitDate;

    @Column(name = "SUBMIT_USER_ID")
    private Long submitUserId;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long propertyId;

    @Column(name = "SP_SPEC_ID")
    private Long specId;

    @Column(name = "PC_BREAKOUT_TYPE_ID")
    private Long breakoutTypeId;

    @Column(name = "TOTAL_FROM_BREAKOUTS")
    private Boolean totalFromBreakouts;

    @Column(name = "IS_ESTIMATED")
    private Boolean isEstimated;

    @Column(name = "PC_JOB_ID")
    private Long pcJobId;

    @Column(name = "SP_SPEC_NODE_ID")
    private Long specNodeId;

    @Column(name = "IS_SUPPLIER_OPTION")
    private Boolean isSupplierOption;

    @Column(name = "VAT_CODE")
    private String vatCode;

    @Column(name = "VAT_RATE")
    private BigDecimal vatRate;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SP_SPEC_ID", insertable = false, updatable = false)
    private Spec spec;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "EM_RFE_ITEM_ID", insertable = false, updatable = false)
    private RfeItem rfeItem;

    public RfeItem getRfeItem() {
        return rfeItem;
    }

    public void setRfeItem(RfeItem rfeItem) {
        this.rfeItem = rfeItem;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEstimateId() {
        return estimateId;
    }

    public void setEstimateId(Long estimateId) {
        this.estimateId = estimateId;
    }

    public Long getRfeItemId() {
        return rfeItemId;
    }

    public void setRfeItemId(Long rfeItemId) {
        this.rfeItemId = rfeItemId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public Long getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(Long submitUserId) {
        this.submitUserId = submitUserId;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public Boolean getTotalFromBreakouts() {
        return totalFromBreakouts;
    }

    public void setTotalFromBreakouts(Boolean totalFromBreakouts) {
        this.totalFromBreakouts = totalFromBreakouts;
    }

    public Boolean getIsEstimated() {
        return isEstimated;
    }

    public void setIsEstimated(Boolean estimated) {
        isEstimated = estimated;
    }

    public Long getPcJobId() {
        return pcJobId;
    }

    public void setPcJobId(Long pcJobId) {
        this.pcJobId = pcJobId;
    }

    public Long getSpecNodeId() {
        return specNodeId;
    }

    public void setSpecNodeId(Long specNodeId) {
        this.specNodeId = specNodeId;
    }

    public Boolean getIsSupplierOption() {
        return isSupplierOption;
    }

    public void setIsSupplierOption(Boolean supplierOption) {
        isSupplierOption = supplierOption;
    }

    public String getVatCode() {
        return vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public BigDecimal getVatRate() {
        return vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public Spec getSpec() {
        return this.spec;
    }

    public void setSpec(Spec spec) {
        this.spec = spec;
    }
}
