package com.noosh.app.commons.entity.workgroup;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 11/11/15
 */

@Entity
@Table(name = "AC_WORKGROUP")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Workgroup extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @Column(name = "AC_WORKGROUP_ID")
    private Long id;

    @Column(name = "NAME")
    private String name;

    @Column(name = "PARENT_AC_WORKGROUP_ID")
    private Long parentWorkgroupId;

    @Column(name = "AC_COMPANY_ID")
    private Long companyId;

    @Column(name = "AC_WORKGROUP_TYPE_ID")
    private Long workGroupTypeId;

    @Column(name = "DEFAULT_AC_CURRENCY_ID")
    private Long defaultCurrencyId;

    @Column(name = "OC_OBJECT_STATE_ID")
    private Long statusId;

    @Column(name = "PORTAL")
    private String portal;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "AC_WORKGROUP_GUID")
    private String gUId;

    @Column(name = "IS_LOCKED")
    private Boolean isLocked;

    @Column(name = "AC_AUTH_METHOD_ID")
    private Long authenticationMethodId;

    @Column(name = "PARTNER_AC_WORKGROUP_ID")
    private Long partnerWgId;

    @Column(name = "IS_TRIAL")
    private Boolean isTrial;

    @Column(name = "AC_SOURCE_TYPE_ID")
    private Long sourceTypeId;

    @Column(name = "SIGN_UP_SUBDOMAIN")
    private String signUpSubdomain;

    @Column(name = "APP_HOME")
    private Long appHome;

    @Column(name = "DECIMAL_PLACES")
    private Long decimalPlaces;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPortal() {
        return portal;
    }

    public void setPortal(String portal) {
        this.portal = portal;
    }

    public Long getWorkGroupTypeId() {
        return workGroupTypeId;
    }

    public void setWorkGroupTypeId(Long workGroupTypeId) {
        this.workGroupTypeId = workGroupTypeId;
    }

    public Long getParentWorkgroupId() {
        return parentWorkgroupId;
    }

    public void setParentWorkgroupId(Long parentWorkgroupId) {
        this.parentWorkgroupId = parentWorkgroupId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getDefaultCurrencyId() {
        return defaultCurrencyId;
    }

    public void setDefaultCurrencyId(Long defaultCurrencyId) {
        this.defaultCurrencyId = defaultCurrencyId;
    }

    public Long getStatusId() {
        return statusId;
    }

    public void setStatusId(Long statusId) {
        this.statusId = statusId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public String getgUId() {
        return gUId;
    }

    public void setgUId(String gUId) {
        this.gUId = gUId;
    }

    public Boolean getIsLocked() {
        return isLocked;
    }

    public void setIsLocked(Boolean isLocked) {
        this.isLocked = isLocked;
    }

    public Long getAuthenticationMethodId() {
        return authenticationMethodId;
    }

    public void setAuthenticationMethodId(Long authenticationMethodId) {
        this.authenticationMethodId = authenticationMethodId;
    }

    public Long getPartnerWgId() {
        return partnerWgId;
    }

    public void setPartnerWgId(Long partnerWgId) {
        this.partnerWgId = partnerWgId;
    }

    public Boolean getIsTrial() {
        return isTrial;
    }

    public void setIsTrial(Boolean isTrial) {
        this.isTrial = isTrial;
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public String getSignUpSubdomain() {
        return signUpSubdomain;
    }

    public void setSignUpSubdomain(String signUpSubdomain) {
        this.signUpSubdomain = signUpSubdomain;
    }

    public Long getAppHome() {
        return appHome;
    }

    public void setAppHome(Long appHome) {
        this.appHome = appHome;
    }

    public Long getDecimalPlaces() {
        return decimalPlaces;
    }

    public void setDecimalPlaces(Long decimalPlaces) {
        this.decimalPlaces = decimalPlaces;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Workgroup wg = (Workgroup) o;

        if (!Objects.equals(id, wg.id)) return false;

        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }
}
