package com.noosh.app.commons.entity.tracking;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 11/8/15
 */
@Entity
@Table(name = "TR_TRACKING")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Tracking extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "tracking_id_generated")
    @SequenceGenerator(name = "tracking_id_generated", sequenceName = "TR_TRACKING_SEQ", allocationSize = 1)
    @Column(name = "TR_TRACKING_ID")
    private Long trTrackingId;

    private Long trTrackingTypeId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime recordedDate;

    @Column(name = "ENACTING_USER_ID")
    private Long enactingUserId;

    private String comments;

    private String i18nData;

    private Long customPrPropertyId;

    private Short isUserPosted;

    private Long acSourceTypeId;

    @Column(name = "TRACKING_OBJECT_ID")
    private Long objectId;

    @Column(name = "TRACKING_OBJECT_CLASS_ID")
    private Long objectClassId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ENACTING_USER_ID", referencedColumnName = "user_id", insertable = false, updatable = false)
    private AccountUser enactingUser;

    public AccountUser getEnactingUser() {
        return enactingUser;
    }

    public void setEnactingUser(AccountUser enactingUser) {
        this.enactingUser = enactingUser;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getTrTrackingId() {
        return trTrackingId;
    }

    public void setTrTrackingId(Long trTrackingId) {
        this.trTrackingId = trTrackingId;
    }

    public Long getTrTrackingTypeId() {
        return trTrackingTypeId;
    }

    public void setTrTrackingTypeId(Long trTrackingTypeId) {
        this.trTrackingTypeId = trTrackingTypeId;
    }

    public LocalDateTime getRecordedDate() {
        return recordedDate;
    }

    public void setRecordedDate(LocalDateTime recordedDate) {
        this.recordedDate = recordedDate;
    }

    public Long getEnactingUserId() {
        return enactingUserId;
    }

    public void setEnactingUserId(Long enactingUserId) {
        this.enactingUserId = enactingUserId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments == null ? null : comments.trim();
    }

    public String getI18nData() {
        return i18nData;
    }

    public void setI18nData(String i18nData) {
        this.i18nData = i18nData == null ? null : i18nData.trim();
    }

    public Long getCustomPrPropertyId() {
        return customPrPropertyId;
    }

    public void setCustomPrPropertyId(Long customPrPropertyId) {
        this.customPrPropertyId = customPrPropertyId;
    }

    public Short getIsUserPosted() {
        return isUserPosted;
    }

    public void setIsUserPosted(Short isUserPosted) {
        this.isUserPosted = isUserPosted;
    }

    public Long getAcSourceTypeId() {
        return acSourceTypeId;
    }

    public void setAcSourceTypeId(Long acSourceTypeId) {
        this.acSourceTypeId = acSourceTypeId;
    }
}
