package com.noosh.app.commons.entity.breakout;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 5/9/17
 */
@Entity
@Table(name = "PC_BREAKOUT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Breakout extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_BREAKOUT_ID_GENERATED")
    @SequenceGenerator(name = "PC_BREAKOUT_ID_GENERATED", sequenceName = "PC_BREAKOUT_SEQ", allocationSize = 1)
    @Column(name = "PC_BREAKOUT_ID")
    private Long id;

    @Column(name = "PC_BREAKOUT_TYPE_ID")
    private Long breakoutTypeId;

    @Column(name = "NESTING_LEVEL")
    private Long nestingLevel;

    @Column(name = "IS_LEAF_NODE")
    private Boolean isLeafNode;

    @Column(name = "HAS_QUANTITY")
    private Boolean hasQuantity;

    @Column(name = "VALUE")
    private Long value;

    @Column(name = "PRICE")
    private BigDecimal price;

    @Column(name = "PRICE_AC_CURRENCY_ID")
    private Long priceCurrencyId;

    @Column(name = "EXPRICE")
    private BigDecimal exPrice;

    @Column(name = "EXPRICE_AC_CURRENCY_ID")
    private Long exPriceCurrencyId;

    @Column(name = "OBJECT_ID")
    private Long objectId;

    @Column(name = "OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "CUSTOM1")
    private String custom1;

    @Column(name = "PRE_MARKUP")
    private BigDecimal preMarkup;

    @Column(name = "PRE_MARKUP_AC_CURRENCY_ID")
    private Long preMarkupCurrencyId;

    @Column(name = "EXPRE_MARKUP")
    private BigDecimal exPreMarkup;

    @Column(name = "EXPRE_MARKUP_AC_CURRENCY_ID")
    private Long exPreMarkupCurrencyId;

    @Column(name = "MARKUP_PERCENT")
    private BigDecimal markupPercent;

    @Column(name = "MARKUP_FIXED")
    private BigDecimal markupFixed;

    @Column(name = "MARKUP_FIXED_AC_CURRENCY_ID")
    private Long markupCurrencyId;

    @Column(name = "EXMARKUP_FIX")
    private BigDecimal exMarkupFixed;

    @Column(name = "EXMARKUP_FIX_AC_CURRENCY_ID")
    private Long exMarkupFixedCurrencyId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_BREAKOUT_TYPE_ID", insertable = false, updatable = false)
    private BreakoutType breakoutType;

    @Column(name = "UNITS")
    private BigDecimal units;

    @Column(name = "RATES")
    private BigDecimal rates;

    @Column(name = "RATES_AC_CURRENCY_ID")
    private Long ratesCurrencyId;

    public BreakoutType getBreakoutType() {
        return breakoutType;
    }

    public void setBreakoutType(BreakoutType breakoutType) {
        this.breakoutType = breakoutType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBreakoutTypeId() {
        return breakoutTypeId;
    }

    public void setBreakoutTypeId(Long breakoutTypeId) {
        this.breakoutTypeId = breakoutTypeId;
    }

    public Long getNestingLevel() {
        return nestingLevel;
    }

    public void setNestingLevel(Long nestingLevel) {
        this.nestingLevel = nestingLevel;
    }

    public Boolean getIsLeafNode() {
        return isLeafNode == null ? false : isLeafNode;
    }

    public void setIsLeafNode(Boolean leafNode) {
        isLeafNode = leafNode;
    }

    public Boolean getHasQuantity() {
        return hasQuantity == null ? false : hasQuantity;
    }

    public void setHasQuantity(Boolean hasQuantity) {
        this.hasQuantity = hasQuantity;
    }

    public Long getValue() {
        return value;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getPriceCurrencyId() {
        return priceCurrencyId;
    }

    public void setPriceCurrencyId(Long priceCurrencyId) {
        this.priceCurrencyId = priceCurrencyId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    public BigDecimal getPreMarkup() {
        return preMarkup;
    }

    public void setPreMarkup(BigDecimal preMarkup) {
        this.preMarkup = preMarkup;
    }

    public Long getPreMarkupCurrencyId() {
        return preMarkupCurrencyId;
    }

    public void setPreMarkupCurrencyId(Long preMarkupCurrencyId) {
        this.preMarkupCurrencyId = preMarkupCurrencyId;
    }

    public BigDecimal getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(BigDecimal markupPercent) {
        this.markupPercent = markupPercent;
    }

    public BigDecimal getMarkupFixed() {
        return markupFixed;
    }

    public void setMarkupFixed(BigDecimal markupFixed) {
        this.markupFixed = markupFixed;
    }

    public Long getMarkupCurrencyId() {
        return markupCurrencyId;
    }

    public void setMarkupCurrencyId(Long markupCurrencyId) {
        this.markupCurrencyId = markupCurrencyId;
    }

    public BigDecimal getExPrice() {
        return this.exPrice;
    }

    public void setExPrice(BigDecimal exPrice) {
        this.exPrice = exPrice;
    }

    public Long getExPriceCurrencyId() {
        return this.exPriceCurrencyId;
    }

    public void setExPriceCurrencyId(Long exPriceCurrencyId) {
        this.exPriceCurrencyId = exPriceCurrencyId;
    }

    public BigDecimal getExPreMarkup() {
        return this.exPreMarkup;
    }

    public void setExPreMarkup(BigDecimal exPreMarkup) {
        this.exPreMarkup = exPreMarkup;
    }

    public Long getExPreMarkupCurrencyId() {
        return this.exPreMarkupCurrencyId;
    }

    public void setExPreMarkupCurrencyId(Long exPreMarkupCurrencyId) {
        this.exPreMarkupCurrencyId = exPreMarkupCurrencyId;
    }

    public BigDecimal getExMarkupFixed() {
        return this.exMarkupFixed;
    }

    public void setExMarkupFixed(BigDecimal exMarkupFixed) {
        this.exMarkupFixed = exMarkupFixed;
    }

    public Long getExMarkupFixedCurrencyId() {
        return this.exMarkupFixedCurrencyId;
    }

    public void setExMarkupFixedCurrencyId(Long exMarkupFixedCurrencyId) {
        this.exMarkupFixedCurrencyId = exMarkupFixedCurrencyId;
    }

    public BigDecimal getUnits() {
        return units;
    }

    public void setUnits(BigDecimal units) {
        this.units = units;
    }

    public BigDecimal getRates() {
        return rates;
    }

    public void setRates(BigDecimal rates) {
        this.rates = rates;
    }

    public Long getRatesCurrencyId() {
        return ratesCurrencyId;
    }

    public void setRatesCurrencyId(Long ratesCurrencyId) {
        this.ratesCurrencyId = ratesCurrencyId;
    }
}
