package com.noosh.app.commons.entity.rfe;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import com.noosh.app.commons.entity.security.Workgroup;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 05/30/2016
 */

@Entity
@Table(name = "EM_RFE_SUPPLIER")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class RfeSupplier extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "EM_RFE_SUPPLIER_ID")
    private Long id;

    @Column(name = "EM_RFE_ID")
    private Long rfeId;

    @Column(name = "USER_ID")
    private Long userId;

    @Column(name = "AC_WORKGROUP_ID")
    private Long groupId;

    @Column(name = "OC_OBJECT_STATE_ID")
    private Long stateId;

    @Column(name = "STATE_CHANGE_COMMENT")
    private String stateChangeComment;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "INVITED_DATE")
    private LocalDateTime invitedDate;

    @Column(name = "IS_SELF_INVITED")
    private Boolean isSelfInvited;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "LAST_VIEW_DATE")
    private LocalDateTime lastViewDate;

    @Column(name = "LAST_VIEW_USER_ID")
    private Long lastViewUserId;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="AC_WORKGROUP_ID", insertable = false, updatable = false)
    private Workgroup workgroup;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="USER_ID", insertable = false, updatable = false)
    private AccountUser accountUser;

    public AccountUser getAccountUser() {
        return accountUser;
    }

    public void setAccountUser(AccountUser accountUser) {
        this.accountUser = accountUser;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRfeId() {
        return rfeId;
    }

    public void setRfeId(Long rfeId) {
        this.rfeId = rfeId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public String getStateChangeComment() {
        return stateChangeComment;
    }

    public void setStateChangeComment(String stateChangeComment) {
        this.stateChangeComment = stateChangeComment;
    }

    public LocalDateTime getInvitedDate() {
        return invitedDate;
    }

    public void setInvitedDate(LocalDateTime invitedDate) {
        this.invitedDate = invitedDate;
    }

    public Boolean getIsSelfInvited() {
        return isSelfInvited;
    }

    public void setIsSelfInvited(Boolean isSelfInvited) {
        this.isSelfInvited = isSelfInvited;
    }

    public LocalDateTime getLastViewDate() {
        return lastViewDate;
    }

    public void setLastViewDate(LocalDateTime lastViewDate) {
        this.lastViewDate = lastViewDate;
    }

    public Long getLastViewUserId() {
        return lastViewUserId;
    }

    public void setLastViewUserId(Long lastViewUserId) {
        this.lastViewUserId = lastViewUserId;
    }

    public Workgroup getWorkgroup() {
        return workgroup;
    }

    public void setWorkgroup(Workgroup workgroup) {
        this.workgroup = workgroup;
    }

    public boolean isActive() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_ESTIMATING) ||
                (this.getStateId() == ObjectStateID.RFE_SUPPLIER_IN_AUCTION) ||
                (this.getStateId() == ObjectStateID.RFE_SUPPLIER_AWARD_AUCTION ) ||
                (this.getStateId() == ObjectStateID.RFE_SUPPLIER_CLOSED ) ||
                (this.getStateId() == ObjectStateID.RFE_SUPPLIER_PARTIALLY_ACCEPTED);
        return bRet;
    }

    public boolean isBidding() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_IN_AUCTION);
        return bRet;
    }

    public boolean isInAwarding() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_AWARD_AUCTION);
        return bRet;
    }

    public boolean isPendingSupplierAcceptance() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_RFE_SENT);
        return bRet;
    }

    public boolean isEstimating() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_ESTIMATING);
        return bRet;
    }

    public boolean isClosed() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_CLOSED);
        return bRet;
    }

    public boolean isDeclined() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_REJECTED);
        return bRet;
    }

    public boolean isPartiallyAccepted() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_PARTIALLY_ACCEPTED);
        return bRet;
    }

    public boolean isDismissed() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_DISMISSED);
        return bRet;
    }

    public boolean isAutoPricing() {
        boolean bRet = (this.getStateId() == ObjectStateID.RFE_SUPPLIER_AUTO_PRICING);
        return bRet;
    }
}