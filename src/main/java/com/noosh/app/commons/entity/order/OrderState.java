package com.noosh.app.commons.entity.order;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.constant.ActionTypeID;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: <PERSON>-<PERSON><PERSON>
 * @Date: 10/14/15
 */
@Entity
@Table(name = "OR_ORDER_STATE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class OrderState implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "order_state_id_generator")
    @SequenceGenerator(name = "order_state_id_generator", sequenceName = "OR_ORDER_STATE_SEQ", allocationSize = 1)
    @Column(name = "or_order_state_id")
    private Long id;

    @Column(name = "or_order_id")
    private Long orderId;

    @Column(name = "oc_object_state_id")
    private Long objectStateId;

    @Column(name = "is_current")
    private Boolean isCurrent;

    @Column(name = "original_or_order_id")
    private Long origOrderId;

    @Column(name = "comments")
    private String comments;

    @Column(name = "STATE_CREATE_USER_ID")
    private Long stateCreateUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "STATE_CREATE_DATE")
    private LocalDateTime stateCreateDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "LAST_CHANGE_DATE")
    private LocalDateTime lastChangeDate;

    @Column(name = "create_user_id", nullable = false)
    private Long createUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "create_date", nullable = false)
    private LocalDateTime createDate = LocalDateTime.now();

    @Column(name = "mod_user_id")
    private Long modUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "mod_date")
    private LocalDateTime modDate = LocalDateTime.now();

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "or_order_id", insertable = false , updatable = false)
    private Order order;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "oc_object_state_id", insertable = false, updatable = false)
    private ObjectState objectState;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "STATE_CREATE_USER_ID", referencedColumnName = "USER_ID", insertable = false, updatable = false)
    private AccountUser creator;

    public AccountUser getCreator() {
        return creator;
    }

    public void setCreator(AccountUser creator) {
        this.creator = creator;
    }

    public ObjectState getObjectState() {
        return objectState;
    }

    public void setObjectState(ObjectState objectState) {
        this.objectState = objectState;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public Long getModUserId() {
        return modUserId;
    }

    public void setModUserId(Long modUserId) {
        this.modUserId = modUserId;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Long getStateCreateUserId() {
        return stateCreateUserId;
    }

    public void setStateCreateUserId(Long stateCreateUserId) {
        this.stateCreateUserId = stateCreateUserId;
    }

    public LocalDateTime getStateCreateDate() {
        return stateCreateDate;
    }

    public void setStateCreateDate(LocalDateTime stateCreateDate) {
        this.stateCreateDate = stateCreateDate;
    }

    public LocalDateTime getLastChangeDate() {
        return lastChangeDate;
    }

    public void setLastChangeDate(LocalDateTime lastChangeDate) {
        this.lastChangeDate = lastChangeDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public Boolean getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Boolean isCurrent) {
        this.isCurrent = isCurrent;
    }

    public Long getOrigOrderId() {
        return origOrderId;
    }

    public void setOrigOrderId(Long origOrderId) {
        this.origOrderId = origOrderId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public static OrderState factory(final long orderId, final boolean isCurrent, final long stateId) {
        OrderState os = new OrderState();
        os.setOrderId(orderId);
        os.setIsCurrent(isCurrent);
        os.setObjectStateId(stateId);
        os.setOrigOrderId(orderId);
        return os;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        OrderState os = (OrderState) o;

        return id.equals(os.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    public static long getNewStateId(long actionId, boolean isUserBuyer, boolean hasOfflineBuyer, boolean isClientProject) {
        // find new stateId, based on the action, etc:

        if (actionId == ActionTypeID.CREATE) {
            return ObjectStateID.ORDER_DRAFT;
        } else if (actionId == ActionTypeID.ROUTE) {
            return ObjectStateID.ORDER_PENDING_SUBMISSION;
        /*
        } else if (actionId==ActionTypeID.APPROVE) {
             return ObjectStateID.APPROVED;

        } else if (actionId==ActionTypeID.DISAPPROVE) {
             return ObjectStateID.DISAPPROVED;
        */
        } else if (actionId == ActionTypeID.SEND || actionId == ActionTypeID.EDIT) {
            if (hasOfflineBuyer ) {
                return ObjectStateID.ORDER_OUTSOURCER_TO_ACCEPT;
            } else if (isUserBuyer) {
                if (isClientProject) {
                    return ObjectStateID.ORDER_OUTSOURCER_TO_ACCEPT;
                } else {
                    return ObjectStateID.ORDER_SUPPLIER_TO_ACCEPT;
                }
            } else {
                if (isClientProject) {
                    return ObjectStateID.ORDER_CLIENT_TO_ACCEPT;
                } else {
                    return ObjectStateID.ORDER_BUYER_TO_ACCEPT;
                }
            }

        } else if (actionId==ActionTypeID.ACCEPT) {
            return ObjectStateID.ORDER_ACCEPTED;

        } else if (actionId==ActionTypeID.REJECT) {
            return ObjectStateID.ORDER_REJECTED;

        } else if (actionId==ActionTypeID.RETRACT) {
            return ObjectStateID.ORDER_RETRACTED;

        } else if (actionId==ActionTypeID.CANCEL) {
            return ObjectStateID.ORDER_CANCELLED;

        } else if (actionId==ActionTypeID.MARK_ACCEPTED) {
            return ObjectStateID.ORDER_ACCEPTED;

        } else if (actionId==ActionTypeID.MARK_ACCEPTED_NOT_YET_SHIPPED) {
            return ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED;

        } else if (actionId==ActionTypeID.MARK_PARTIALLY_SHIPPED) {
            return ObjectStateID.ORDER_PARTIALLY_SHIPPED;

        } else if (actionId==ActionTypeID.MARK_SHIPPED) {
            return ObjectStateID.ORDER_SHIPPED;

        } else if (actionId==ActionTypeID.MARK_DELIVERED) {
            return ObjectStateID.ORDER_DELIVERED;

        } else if (actionId==ActionTypeID.MARK_COMPLETED) {
            return ObjectStateID.ORDER_COMPLETED;

        } else {
            return -1;
        }
    }
}
