package com.noosh.app.commons.entity.security;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: Hung-<PERSON><PERSON>
 * @Date: 10/19/15
 */
@Entity
@Table(name = "TM_TEAM_MEMBER")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class TeamMember extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "tm_team_member_id")
    private Long id;

    @Column(name = "tm_team_id")
    private Long teamId;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "re_role_id")
    private Long roleId;

    @Column(name = "invitor_user_id")
    private Long invitorUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "date_invited")
    private LocalDateTime invitedDate;

    @Column(name = "current_record")
    private Boolean isCurrent;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "user_id", insertable = false, updatable = false)
    private AccountUser accountUser;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "re_role_id", referencedColumnName = "RE_ROLE_ID", insertable = false, updatable = false)
    private Role role;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getInvitorUserId() {
        return invitorUserId;
    }

    public void setInvitorUserId(Long invitorUserId) {
        this.invitorUserId = invitorUserId;
    }

    public LocalDateTime getInvitedDate() {
        return invitedDate;
    }

    public void setInvitedDate(LocalDateTime invitedDate) {
        this.invitedDate = invitedDate;
    }

    public Boolean getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Boolean isCurrent) {
        this.isCurrent = isCurrent;
    }

    public AccountUser getAccountUser() {
        return accountUser;
    }

    public void setAccountUser(AccountUser accountUser) {
        this.accountUser = accountUser;
    }

    public Role getRole() {
        return role;
    }

    public void setRole(Role role) {
        this.role = role;
    }
}
