package com.noosh.app.commons.entity.costcenter;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.property.Property;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;

import java.io.Serializable;

/**
 * User: yangx
 * Date: 6/19/20
 */
@Entity
@Table(name = "AC_COSTCENTER_ALLOCATION")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class CostCenterAllocation extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ac_costcenter_allocation_id_generated")
    @GenericGenerator(
            name = "ac_costcenter_allocation_id_generated",
            strategy = "io.hypersistence.utils.hibernate.id.BatchSequenceGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence", value = "AC_COSTCENTER_ALLOCATION_SEQ"),
                    @org.hibernate.annotations.Parameter(name = "fetch_size", value = "10")
    })
    @Column(name="AC_COSTCENTER_ALLOCATION_ID")
    private Long id;

    @Column(name="AC_COSTCENTER_ALLOC_TYPE_ID")
    private Long typeId;

    @Column(name="AC_COSTCENTER_ID")
    private Long costCenterId;

    @Column(name="PERCENT")
    private Double percent;

    @Column(name="OBJECT_ID")
    private Long objectId;

    @Column(name="OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name="CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name="COMPLEMENT")
    private Double complement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_COSTCENTER_ID", insertable = false, updatable = false)
    private CostCenter costCenter;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_COSTCENTER_ALLOC_TYPE_ID", insertable = false, updatable = false)
    private CostCenterAllocType costCenterAllocType;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CUSTOM_PR_PROPERTY_ID", referencedColumnName = "PR_PROPERTY_ID", insertable = false, updatable = false)
    private Property property;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public Long getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(Long costCenterId) {
        this.costCenterId = costCenterId;
    }

    public Double getPercent() {
        return percent;
    }

    public void setPercent(Double percent) {
        this.percent = percent;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Double getComplement() {
        return complement;
    }

    public void setComplement(Double complement) {
        this.complement = complement;
    }

    public CostCenter getCostCenter() {
        return costCenter;
    }

    public void setCostCenter(CostCenter costCenter) {
        this.costCenter = costCenter;
    }

    public CostCenterAllocType getCostCenterAllocType() {
        return costCenterAllocType;
    }

    public void setCostCenterAllocType(CostCenterAllocType costCenterAllocType) {
        this.costCenterAllocType = costCenterAllocType;
    }

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public CostCenterAllocation() {
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        CostCenterAllocation costCenterAllocation = (CostCenterAllocation) o;

        return id.equals(costCenterAllocation.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
