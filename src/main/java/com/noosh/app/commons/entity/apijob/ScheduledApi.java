package com.noosh.app.commons.entity.apijob;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "NO_SCHEDULED_API")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ScheduledApi {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "noScheduledApiId_generated")
    @SequenceGenerator(name = "noScheduledApiId_generated", sequenceName = "NO_SCHEDULED_API_SEQ", allocationSize = 1)
    @Column(name = "no_scheduled_api_id")
    private Integer noScheduledApiId;

    @Column(name = "protocol")
    private String protocol;

    @Column(name = "host")
    private String host;

    @Column(name = "path")
    private String path;

    @Column(name = "method")
    private String method;

    @Lob
    @Column(name = "headers")
    private String headers;

    @Lob
    @Column(name = "body")
    private String body;

    @Column(name = "callback_url")
    private String callbackUrl;

    @Column(name = "create_user_id")
    private Integer createUserId;

    @Column(name = "create_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    @Column(name = "mod_user_id")
    private Integer modUserId;

    @Column(name = "mod_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date modDate;

    @Column(name = "request_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date requestDate;

    @Column(name = "response_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date responseDate;

    @Column(name = "response_code")
    private Integer responseCode;

    @Lob
    @Column(name = "response_body")
    private String responseBody;

    @Column(name = "callback_code")
    private Integer callbackCode;

    public Integer getNoScheduledApiId() {
        return noScheduledApiId;
    }

    public void setNoScheduledApiId(Integer noScheduledApiId) {
        this.noScheduledApiId = noScheduledApiId;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getHeaders() {
        return headers;
    }

    public void setHeaders(String headers) {
        this.headers = headers;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getModUserId() {
        return modUserId;
    }

    public void setModUserId(Integer modUserId) {
        this.modUserId = modUserId;
    }

    public Date getModDate() {
        return modDate;
    }

    public void setModDate(Date modDate) {
        this.modDate = modDate;
    }

    public Date getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(Date requestDate) {
        this.requestDate = requestDate;
    }

    public Date getResponseDate() {
        return responseDate;
    }

    public void setResponseDate(Date responseDate) {
        this.responseDate = responseDate;
    }

    public Integer getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(Integer responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(String responseBody) {
        this.responseBody = responseBody;
    }

    public Integer getCallbackCode() {
        return callbackCode;
    }

    public void setCallbackCode(Integer callbackCode) {
        this.callbackCode = callbackCode;
    }
}