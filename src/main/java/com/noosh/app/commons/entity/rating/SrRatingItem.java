package com.noosh.app.commons.entity.rating;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/19/16
 */
@Entity
@Table(name = "SR_RATING_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SrRatingItem extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SrRatingItem_id_generated")
    @SequenceGenerator(name = "SrRatingItem_id_generated", sequenceName = "SR_RATING_ITEM_SEQ", allocationSize = 1)
    @Column(name = "SR_RATING_ITEM_ID")
    private Long id;

    @Column(name = "SR_RATING_ID")
    private Long ratingId;

    @Column(name = "SR_QUESTION_ID")
    private Long questionId;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "GRADE")
    private Long grade;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SR_QUESTION_ID", insertable = false, updatable = false)
    private SrQuestion srQuestion;

    @JsonIgnore
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SR_RATING_ID", insertable = false, updatable = false)
    private SrRating srRating;

    public SrRating getSrRating() {
        return srRating;
    }

    public void setSrRating(SrRating srRating) {
        this.srRating = srRating;
    }

    public SrQuestion getSrQuestion() {
        return srQuestion;
    }

    public void setSrQuestion(SrQuestion srQuestion) {
        this.srQuestion = srQuestion;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRatingId() {
        return ratingId;
    }

    public void setRatingId(Long ratingId) {
        this.ratingId = ratingId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getGrade() {
        return grade;
    }

    public void setGrade(Long grade) {
        this.grade = grade;
    }
}
