package com.noosh.app.commons.entity.account;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/29/20
 */
@Entity
@Table(name="BU_SUPPLIER_RELATION")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SupplierRelation extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = -1806551656694975522L;

    @Id
    @Column(name="BU_SUPPLIER_RELATION_ID")
    private Long id;

    @Column(name = "OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "OBJECT_ID")
    private Long objectId;

    @Column(name = "BU_SUP_CLASSIF_ID")
    private Long supplierClassificationId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BU_SUP_CLASSIF_ID", insertable = false, updatable = false)
    private SupplierClassification supplierClassification;

    public SupplierClassification getSupplierClassification() {
        return supplierClassification;
    }

    public void setSupplierClassification(SupplierClassification supplierClassification) {
        this.supplierClassification = supplierClassification;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getSupplierClassificationId() {
        return supplierClassificationId;
    }

    public void setSupplierClassificationId(Long supplierClassificationId) {
        this.supplierClassificationId = supplierClassificationId;
    }
}
