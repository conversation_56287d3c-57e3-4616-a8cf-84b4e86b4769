package com.noosh.app.commons.entity.property;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: neals
 * @Date: 05/18/2016
 */

@Entity
@Table(name = "AC_CUSTOM_FIELD")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class CustomField extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "AC_CUSTOM_FIELD_ID")
    private Long id;

    @Column(name = "AC_CUSTOM_FIELD_CLASS_ID")
    private Long customFieldClassId;

    @Column(name = "AC_CUSTOM_FIELD_CONTROL_ID")
    private Long customFieldControlId;

    @Column(name = "OWNER_AC_WORKGROUP_ID")
    private Long ownerWorkgroupId;

    @Column(name = "ORDINAL_NUMBER")
    private Long ordinalNumber;

    @Column(name = "LABEL")
    private String label;

    @Column(name = "IS_REQUIRED")
    private Boolean isRequired;

    @Column(name = "FIELD_VALUES")
    private String fieldValues;

    @Column(name = "ATTRIBUTES")
    private String attributes;

    @Column(name = "INCLUDE_IN_TOTAL")
    private Boolean includeInTotal;

    @Column(name = "PR_PROPERTY_PARAM_ID")
    private Long propertyParamId;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_CUSTOM_FIELD_CONTROL_ID", insertable = false, updatable = false)
    private CustomFieldControl customFieldControl;

    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PR_PROPERTY_PARAM_ID", insertable = false, updatable = false)
    private PropertyParam propertyParam;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCustomFieldClassId() {
        return customFieldClassId;
    }

    public void setCustomFieldClassId(Long customFieldClassId) {
        this.customFieldClassId = customFieldClassId;
    }

    public Long getCustomFieldControlId() {
        return customFieldControlId;
    }

    public void setCustomFieldControlId(Long customFieldControlId) {
        this.customFieldControlId = customFieldControlId;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getOrdinalNumber() {
        return ordinalNumber;
    }

    public void setOrdinalNumber(Long ordinalNumber) {
        this.ordinalNumber = ordinalNumber;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Boolean getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    public String getFieldValues() {
        return fieldValues;
    }

    public void setFieldValues(String fieldValues) {
        this.fieldValues = fieldValues;
    }

    public String getAttributes() {
        return attributes;
    }

    public void setAttributes(String attributes) {
        this.attributes = attributes;
    }

    public Boolean getIncludeInTotal() {
        return includeInTotal;
    }

    public void setIncludeInTotal(Boolean includeInTotal) {
        this.includeInTotal = includeInTotal;
    }

    public Long getPropertyParamId() {
        return propertyParamId;
    }

    public void setPropertyParamId(Long propertyParamId) {
        this.propertyParamId = propertyParamId;
    }

    public CustomFieldControl getCustomFieldControl() {
        return customFieldControl;
    }

    public void setCustomFieldControl(CustomFieldControl customFieldControl) {
        this.customFieldControl = customFieldControl;
    }

    public PropertyParam getPropertyParam() {
        return propertyParam;
    }

    public void setPropertyParam(PropertyParam propertyParam) {
        this.propertyParam = propertyParam;
    }
}
