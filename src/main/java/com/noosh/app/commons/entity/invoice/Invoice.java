package com.noosh.app.commons.entity.invoice;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: Lukez
 * @Date: 12/28/2015
 */
@Entity
@Table(name = "PC_INVOICE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Invoice extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "PC_INVOICE_ID")
    private Long id;

    @Column(name = "OR_ORDER_ID")
    private Long orderId;

    @Column(name = "REFERENCE")
    private String reference;

    @Column(name = "OWNER_REFERENCE")
    private String ownerReference;

    @Column(name = "BUYER_USER_ID")
    private Long buyerUserId;

    @Column(name = "BUYER_AC_WORKGROUP_ID")
    private Long buyerWorkgroupId;

    @Column(name = "SUPPLIER_USER_ID")
    private Long supplierUserId;

    @Column(name = "SUPPLIER_AC_WORKGROUP_ID")
    private Long supplierWorkgroupId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "INVOICE_DATE")
    private LocalDateTime invoiceDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "DUE_DATE")
    private LocalDateTime dueDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "SUBMIT_DATE")
    private LocalDateTime submitDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "ACCEPTED_DATE")
    private LocalDateTime acceptedDate;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "TAX")
    private BigDecimal tax;

    @Column(name = "TAX_AC_CURRENCY_ID")
    private Long taxCurrencyId;

    @Column(name = "SHIPPING")
    private BigDecimal shipping;

    @Column(name = "SHIPPING_AC_CURRENCY_ID")
    private Long shippingCurrencyId;

    @Column(name = "MISC_COST")
    private BigDecimal miscCost;

    @Column(name = "MISC_COST_AC_CURRENCY_ID")
    private Long miscCostCurrencyId;

    @Column(name = "OC_OBJECT_STATE_ID")
    private Long stateId;

    @Column(name = "STATE_CHANGE_COMMENT")
    private String stateChangeComment;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "PREPARE_USER_ID")
    private Long prepareUserId;

    @Column(name = "INVOICE_CM_CONTACT_ID")
    private Long invoiceCMContactId;

    @Column(name = "IS_NON_BILLABLE")
    private Boolean isNonBillable;

    @Column(name = "NON_BILLABLE_REASON_ID")
    private Long nonBillableReasonId;

    @Column(name = "IS_APPROVED")
    private Boolean isApproved;

    @Column(name = "IS_FINAL")
    private Boolean isFinal;

    @Column(name = "D_OR_S")
    private BigDecimal discountOrSurcharge;

    @Column(name = "D_OR_S_AC_CURRENCY_ID")
    private Long discountOrSurchargeCurrencyId;

    @Column(name = "IS_TEMPLATE")
    private Boolean isTemplate;

    @Column(name = "SEND_ON_CLOSED_ORDER")
    private Boolean sendOnClosedOrder;

    //Dual currency related fields
    @Column(name = "RATE")
    private BigDecimal rate;

    @Column(name = "EX_CURRENCYID")
    private Long exCurrencyId;

    @Column(name = "EXTAX")
    private BigDecimal exTax;

    @Column(name = "EXTAX_AC_CURRENCY_ID")
    private Long exTaxCurrencyId;

    @Column(name = "EXSHIPPING")
    private BigDecimal exShipping;

    @Column(name = "EXSHIPPING_AC_CURRENCY_ID")
    private Long exShippingCurrencyId;

    @Column(name = "EXMISC_COST")
    private BigDecimal exMiscCost;

    @Column(name = "EXMISC_COST_AC_CURRENCY_ID")
    private Long exMiscCostCurrencyId;

    @Column(name = "EX_D_OR_S")
    private BigDecimal exDiscountOrSurcharge;

    @Column(name = "EX_D_OR_S_AC_CURRENCY_ID")
    private Long exDiscountOrSurchargeCurrencyId;

    @Column(name = "AC_TERMS_ID")
    private Long termsId;

    //
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CUSTOM_PR_PROPERTY_ID", referencedColumnName = "PR_PROPERTY_ID", insertable = false, updatable = false)
    private Property property;

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    @OneToMany
    @JoinColumn(name="PC_INVOICE_ID")
    private List<InvoiceItem> invoiceItemList;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public Long getBuyerUserId() {
        return buyerUserId;
    }

    public void setBuyerUserId(Long buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierUserId() {
        return supplierUserId;
    }

    public void setSupplierUserId(Long supplierUserId) {
        this.supplierUserId = supplierUserId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public LocalDateTime getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(LocalDateTime invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public LocalDateTime getAcceptedDate() {
        return acceptedDate;
    }

    public void setAcceptedDate(LocalDateTime acceptedDate) {
        this.acceptedDate = acceptedDate;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public BigDecimal getMiscCost() {
        return miscCost;
    }

    public void setMiscCost(BigDecimal miscCost) {
        this.miscCost = miscCost;
    }

    public Long getMiscCostCurrencyId() {
        return miscCostCurrencyId;
    }

    public void setMiscCostCurrencyId(Long miscCostCurrencyId) {
        this.miscCostCurrencyId = miscCostCurrencyId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public String getStateChangeComment() {
        return stateChangeComment;
    }

    public void setStateChangeComment(String stateChangeComment) {
        this.stateChangeComment = stateChangeComment;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getPrepareUserId() {
        return prepareUserId;
    }

    public void setPrepareUserId(Long prepareUserId) {
        this.prepareUserId = prepareUserId;
    }

    public Long getInvoiceCMContactId() {
        return invoiceCMContactId;
    }

    public void setInvoiceCMContactId(Long invoiceCMContactId) {
        this.invoiceCMContactId = invoiceCMContactId;
    }

    public Boolean getNonBillable() {
        return isNonBillable;
    }

    public void setNonBillable(Boolean nonBillable) {
        isNonBillable = nonBillable;
    }

    public Long getNonBillableReasonId() {
        return nonBillableReasonId;
    }

    public void setNonBillableReasonId(Long nonBillableReasonId) {
        this.nonBillableReasonId = nonBillableReasonId;
    }

    public Boolean getApproved() {
        return isApproved;
    }

    public void setApproved(Boolean approved) {
        isApproved = approved;
    }

    public Boolean getFinal() {
        return isFinal;
    }

    public void setFinal(Boolean aFinal) {
        isFinal = aFinal;
    }

    public BigDecimal getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(BigDecimal discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public Boolean getTemplate() {
        return isTemplate;
    }

    public void setTemplate(Boolean template) {
        isTemplate = template;
    }

    public Boolean getSendOnClosedOrder() {
        return sendOnClosedOrder;
    }

    public void setSendOnClosedOrder(Boolean sendOnClosedOrder) {
        this.sendOnClosedOrder = sendOnClosedOrder;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public Long getExTaxCurrencyId() {
        return exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExShippingCurrencyId() {
        return exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExMiscCost() {
        return exMiscCost;
    }

    public void setExMiscCost(BigDecimal exMiscCost) {
        this.exMiscCost = exMiscCost;
    }

    public Long getExMiscCostCurrencyId() {
        return exMiscCostCurrencyId;
    }

    public void setExMiscCostCurrencyId(Long exMiscCostCurrencyId) {
        this.exMiscCostCurrencyId = exMiscCostCurrencyId;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public Long getTermsId() {
        return termsId;
    }

    public void setTermsId(Long termsId) {
        this.termsId = termsId;
    }

    public List<InvoiceItem> getInvoiceItemList() {
        return invoiceItemList;
    }

    public void setInvoiceItemList(List<InvoiceItem> invoiceItemList) {
        this.invoiceItemList = invoiceItemList;
    }

    public boolean isPending() {
        return (this.getStateId() == ObjectStateID.INVOICE_PENDING_ACCEPTANCE) ? true : false;
    }

    public boolean isDraft() {
        return (this.getStateId() == ObjectStateID.INVOICE_DRAFT) ? true : false;
    }

    public boolean isAccepted() {
        return (this.getStateId() == ObjectStateID.INVOICE_ACCEPTED) ? true : false;
    }

    public boolean isRejected() {
        return (this.getStateId() == ObjectStateID.INVOICE_REJECTED) ? true : false;
    }

    public boolean isRetracted() {
        return (this.getStateId() == ObjectStateID.INVOICE_RETRACTED) ? true : false;
    }
}
