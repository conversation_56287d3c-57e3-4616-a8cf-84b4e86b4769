package com.noosh.app.commons.entity.terms;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/25/17
 */
@Entity
@Table(name = "AC_TERMS")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class AcTerms extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "AC_TERMS_ID_GENERATED")
    @SequenceGenerator(name = "AC_TERMS_ID_GENERATED", sequenceName = "AC_TERMS_SEQ", allocationSize = 1)
    @Column(name = "AC_TERMS_ID")
    private Long id;

    @Column(name = "TEXT")
    private String text;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "AC_TERMS_TYPE_ID")
    private Long termsTypeId;

    @Column(name = "VERSION_NUMBER")
    private Long versionNumber;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getTermsTypeId() {
        return termsTypeId;
    }

    public void setTermsTypeId(Long termsTypeId) {
        this.termsTypeId = termsTypeId;
    }

    public Long getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }
}
