package com.noosh.app.commons.entity.reason;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.order.PcCoReason;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 1/10/18
 */
@Entity
@Table(name = "PC_WORKGROUP_CO_REASON")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class PcWorkgroupCoReason extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_WORKGROUP_CO_REASON_ID_GENERATED")
    @SequenceGenerator(name = "PC_WORKGROUP_CO_REASON_ID_GENERATED", sequenceName = "PC_WORKGROUP_CO_REASON_SEQ", allocationSize = 1)
    @Column(name = "PC_WORKGROUP_CO_REASON_ID")
    private Long id;

    @Column(name = "PC_CO_REASON_ID")
    private Long reasonId;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "IS_ACTIVE")
    private Boolean isActive;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_CO_REASON_ID", referencedColumnName = "PC_CO_REASON_ID", insertable = false, updatable = false)
    private PcCoReason pcCoReason;

    public PcCoReason getPcCoReason() {
        return pcCoReason;
    }

    public void setPcCoReason(PcCoReason pcCoReason) {
        this.pcCoReason = pcCoReason;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean active) {
        isActive = active;
    }
}
