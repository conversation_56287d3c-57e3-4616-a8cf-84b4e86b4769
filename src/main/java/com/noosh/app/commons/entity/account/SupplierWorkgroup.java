package com.noosh.app.commons.entity.account;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: neals
 * @Date: 08/04/2016
 */
@Entity
@Table(name="BU_SUPPLIER_WORKGROUP")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SupplierWorkgroup extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1961799553749536183L;
    @Id
    @Column(name="BU_SUPPLIER_WORKGROUP_ID")
    private Long id;

    @Column(name="OWNER_AC_WORKGROUP_ID")
    private Long ownerWorkgroupId;

    @Column(name="SUPPLIER_AC_WORKGROUP_ID")
    private Long supplierWorkgroupId;

    @Column(name="IS_APPROVED")
    private Boolean isApproved;

    @Column(name="CLIENT_AC_WORKGROUP_ID")
    private Long clientWorkgroupId;

    @Column(name="ALIAS")
    private String alias;

    @Column(name="ACCEPT_QUOTE")
    private Boolean acceptQuote;

    @Column(name="ACCEPT_CHANGE_ORDER")
    private Boolean acceptChangeOrder;

    @Column(name="SUPPLIER_CODE")
    private String supplierCode;

    @Column(name="DEFAULT_SUPPLIER_USER_ID")
    private Long defaultSupplierUserId;

    @Column(name="CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public Boolean getIsApproved() {
        return isApproved;
    }

    public void setIsApproved(Boolean isApproved) {
        this.isApproved = isApproved;
    }

    public Long getClientWorkgroupId() {
        return clientWorkgroupId;
    }

    public void setClientWorkgroupId(Long clientWorkgroupId) {
        this.clientWorkgroupId = clientWorkgroupId;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Boolean getAcceptQuote() {
        return acceptQuote;
    }

    public void setAcceptQuote(Boolean acceptQuote) {
        this.acceptQuote = acceptQuote;
    }

    public Boolean getAcceptChangeOrder() {
        return acceptChangeOrder;
    }

    public void setAcceptChangeOrder(Boolean acceptChangeOrder) {
        this.acceptChangeOrder = acceptChangeOrder;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public Long getDefaultSupplierUserId() {
        return defaultSupplierUserId;
    }

    public void setDefaultSupplierUserId(Long defaultSupplierUserId) {
        this.defaultSupplierUserId = defaultSupplierUserId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }
}
