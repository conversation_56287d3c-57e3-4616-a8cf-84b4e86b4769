package com.noosh.app.commons.entity.order;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 1/10/18
 */
@Entity
@Table(name = "PC_CO_REASON")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class PcCoReason extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_CO_REASON_ID_GENERATED")
    @SequenceGenerator(name = "PC_CO_REASON_ID_GENERATED", sequenceName = "PC_CO_REASON_SEQ", allocationSize = 1)
    @Column(name = "PC_CO_REASON_ID")
    private Long id;

    @Column(name = "NAME_STR_ID")
    private Long nameStrId;

    @Column(name = "CONSTANT_TOKEN")
    private String token;

    @Column(name = "NAME_STR")
    private String nameStr;

    @Column(name = "IS_SYSTEM")
    private Boolean isSystem;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Short system) {
        isSystem = !(system == null || system.shortValue() == 0);
    }

}
