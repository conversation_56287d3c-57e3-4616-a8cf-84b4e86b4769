package com.noosh.app.commons.entity.property;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: neals
 * @Date: 05/19/2016
 */
@Entity
@Table(name = "AC_CUSTOM_FIELD_CONTROL")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class CustomFieldControl extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "AC_CUSTOM_FIELD_CONTROL_ID")
    private Long id;

    @Column(name = "AC_CUSTOM_FIELD_TYPE_ID")
    private Long customFieldTypeId;

    @Column(name = "CONSTANT_TOKEN")
    private String constantToken;

    @Column(name = "DESCRIPTION_STR_ID")
    private Long descriptionStrId;


    @JsonIgnore
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_CUSTOM_FIELD_TYPE_ID", insertable = false, updatable = false)
    private CustomFieldType customFieldType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCustomFieldTypeId() {
        return customFieldTypeId;
    }

    public void setCustomFieldTypeId(Long customFieldTypeId) {
        this.customFieldTypeId = customFieldTypeId;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public CustomFieldType getCustomFieldType() {
        return customFieldType;
    }

    public void setCustomFieldType(CustomFieldType customFieldType) {
        this.customFieldType = customFieldType;
    }
}
