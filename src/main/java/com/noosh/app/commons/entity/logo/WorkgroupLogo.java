package com.noosh.app.commons.entity.logo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@Entity
@Table(name = "AC_WG_LOGO")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class WorkgroupLogo extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "AC_WG_LOGO_ID_GENERATED")
    @SequenceGenerator(name = "AC_WG_LOGO_ID_GENERATED", sequenceName = "AC_WG_LOGO_SEQ", allocationSize = 1)
    @Column(name = "AC_WG_LOGO_ID")
    private Long id;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "USER_FILENAME")
    private String userFileName;

    @Column(name = "RESOURCE_PATH")
    private String resourcePath;

    @Column(name = "WIDTH")
    private long width;

    @Column(name = "HEIGHT")
    private long height;

    @Column(name = "BYTE_COUNT")
    private long byteCount;

    @Column(name = "IS_ACTIVE")
    private boolean isActive;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserFileName() {
        return userFileName;
    }

    public void setUserFileName(String userFileName) {
        this.userFileName = userFileName;
    }

    public String getResourcePath() {
        return resourcePath;
    }

    public void setResourcePath(String resourcePath) {
        this.resourcePath = resourcePath;
    }

    public long getWidth() {
        return width;
    }

    public void setWidth(long width) {
        this.width = width;
    }

    public long getHeight() {
        return height;
    }

    public void setHeight(long height) {
        this.height = height;
    }

    public long getByteCount() {
        return byteCount;
    }

    public void setByteCount(long byteCount) {
        this.byteCount = byteCount;
    }

    public boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(boolean active) {
        isActive = active;
    }
}
