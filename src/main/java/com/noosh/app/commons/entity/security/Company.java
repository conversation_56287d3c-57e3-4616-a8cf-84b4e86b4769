package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/24/20
 */
@Entity
@Table(name = "AC_COMPANY")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Company extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 8573257496237426008L;

    @Id
    @Column(name = "AC_COMPANY_ID")
    private Long id;

    @Column(name = "NAME")
    private String name;

    @Column(name = "PHONE_NUMBER")
    private String phoneNumber;

    @Column(name = "FAX_NUMBER")
    private String faxNumber;

    @Column(name = "EMAIL_ADDRESS")
    private String emailAddress;

    @Column(name = "PARTNER_AC_WORKGROUP_ID")
    private Long partnerWorkgroupId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getFaxNumber() {
        return faxNumber;
    }

    public void setFaxNumber(String faxNumber) {
        this.faxNumber = faxNumber;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public Long getPartnerWorkgroupId() {
        return partnerWorkgroupId;
    }

    public void setPartnerWorkgroupId(Long partnerWorkgroupId) {
        this.partnerWorkgroupId = partnerWorkgroupId;
    }
}
