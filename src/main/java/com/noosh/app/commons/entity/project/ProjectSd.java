package com.noosh.app.commons.entity.project;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "PM_PROJECT_SD")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProjectSd extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pm_project_SD_id_generated")
    @SequenceGenerator(name = "pm_project_SD_id_generated", sequenceName = "PM_PROJECT_SD_SEQ", allocationSize = 1)
    @Column(name = "PM_PROJECT_SD_ID")
    private Long id;

    @Column(name = "PM_PROJECT_ID")
    private Long pmProjectId;

    @Column(name = "AUTHOR_USER_ID")
    private Long authorUserId;

    @Column(name= "DESCRIPTION")
    private String description;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "PM_DEACTIVATION_REASON_ID")
    private Long pmDeactivationReasonId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "PROJECT_DELETED_DATE")
    private LocalDateTime projectDeletedDate;

    @OneToOne
    @JoinColumn(name = "PM_PROJECT_ID", insertable = false, updatable = false)
    private Project project;

    @OneToOne
    @JoinColumn(name = "PM_DEACTIVATION_REASON_ID", insertable = false, updatable = false)
    private DeactivationReason deactivationReason;

    public DeactivationReason getDeactivationReason() {
        return deactivationReason;
    }

    public void setDeactivationReason(DeactivationReason deactivationReason) {
        this.deactivationReason = deactivationReason;
    }

    public Project getProject() {
        return project;
    }

    public void setProject(Project project) {
        this.project = project;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPmProjectId() {
        return pmProjectId;
    }

    public void setPmProjectId(Long pmProjectId) {
        this.pmProjectId = pmProjectId;
    }

    public Long getAuthorUserId() {
        return authorUserId;
    }

    public void setAuthorUserId(Long authorUserId) {
        this.authorUserId = authorUserId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getPmDeactivationReasonId() {
        return pmDeactivationReasonId;
    }

    public void setPmDeactivationReasonId(Long pmDeactivationReasonId) {
        this.pmDeactivationReasonId = pmDeactivationReasonId;
    }

    public LocalDateTime getProjectDeletedDate() {
        return projectDeletedDate;
    }

    public void setProjectDeletedDate(LocalDateTime projectDeletedDate) {
        this.projectDeletedDate = projectDeletedDate;
    }
}
