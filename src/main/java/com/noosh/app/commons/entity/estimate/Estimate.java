package com.noosh.app.commons.entity.estimate;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.rfe.Rfe;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: <PERSON>
 * @Date: 05/30/2016
 */

@Entity
@Table(name = "EM_ESTIMATE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Estimate extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "EM_ESTIMATE_ID")
    private Long id;

    @Column(name = "EM_RFE_ID")
    private Long rfeId;

    @Column(name = "REFERENCE")
    private String reference;

    @Column(name = "OWNER_REFERENCE")
    private String ownerReference;

    @Column(name = "TITLE")
    private String title;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "COMMENTS")
    private String comments;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "EXPIRATION_DATE")
    private LocalDateTime expirationDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "SUBMIT_DATE")
    private LocalDateTime submitDate;

    @Column(name = "SUBMIT_USER_ID")
    private Long submitUserId;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "OWNER_AC_WORKGROUP_ID")
    private Long ownerWorkgroupId;

    @Column(name = "OWNER_USER_ID")
    private Long ownerUserId;

    @Column(name = "AC_TERMS_ID")
    private Long termsId;

    @Column(name = "OC_OBJECT_STATE_ID")
    private Long stateId;

    @Column(name = "IS_ALL_OR_NONE")
    private Boolean isAllOrNone;

    @Column(name = "AUTO_GENERATED")
    private Boolean autoGenerated;

    @Column(name = "BEHALF_USER_ID")
    private Long behalfUserId;

    @Column(name = "STATE_CHANGE_COMMENTS")
    private String stateChangeComments;

    @Column(name = "OBJECT_ATTR")
    private String objectAttr;

    @Column(name = "ITEMIZED_TNS")
    private Boolean itemizedTaxAndShipping;

    @Column(name = "AC_SOURCE_TYPE_ID")
    private Long sourceTypeId;

    @Column(name = "RATE")
    private BigDecimal rate;

    @Column(name = "EX_CURRENCYID")
    private Long exCurrencyId;

    @ManyToOne
    @JoinColumn(name = "EM_RFE_ID", insertable = false, updatable = false)
    private Rfe rfe;

    public Rfe getRfe() {
        return rfe;
    }

    public Rfe setRfe(Rfe rfe) {
        return this.rfe = rfe;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRfeId() {
        return rfeId;
    }

    public void setRfeId(Long rfeId) {
        this.rfeId = rfeId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public Long getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(Long submitUserId) {
        this.submitUserId = submitUserId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getOwnerUserId() {
        return ownerUserId;
    }

    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    public Long getTermsId() {
        return termsId;
    }

    public void setTermsId(Long termsId) {
        this.termsId = termsId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public Boolean getIsAllOrNone() {
        return isAllOrNone;
    }

    public void setIsAllOrNone(Boolean isAllOrNone) {
        this.isAllOrNone = isAllOrNone;
    }

    public Boolean getAutoGenerated() {
        return autoGenerated;
    }

    public void setAutoGenerated(Boolean autoGenerated) {
        this.autoGenerated = autoGenerated;
    }

    public Long getBehalfUserId() {
        return behalfUserId;
    }

    public void setBehalfUserId(Long behalfUserId) {
        this.behalfUserId = behalfUserId;
    }

    public String getStateChangeComments() {
        return stateChangeComments;
    }

    public void setStateChangeComments(String stateChangeComments) {
        this.stateChangeComments = stateChangeComments;
    }

    public String getObjectAttr() {
        return objectAttr;
    }

    public void setObjectAttr(String objectAttr) {
        this.objectAttr = objectAttr;
    }

    public Boolean getItemizedTaxAndShipping() {
        return itemizedTaxAndShipping;
    }

    public void setItemizedTaxAndShipping(Boolean itemizedTaxAndShipping) {
        this.itemizedTaxAndShipping = itemizedTaxAndShipping;
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public boolean isDraft() {
        return getStateId() == ObjectStateID.ESTIMATE_DRAFT;
    }

    public boolean isSent() {
        return getStateId() == ObjectStateID.ESTIMATE_SENT;
    }

    public boolean isInvalidated() {
        return getStateId() == ObjectStateID.ESTIMATE_INVALIDATED;
    }

    public boolean isRetracted() {
        return getStateId() == ObjectStateID.ESTIMATE_RETRACTED;
    }

    public boolean isRejected() {
        return getStateId() == ObjectStateID.ESTIMATE_REJECTED;
    }

    public boolean isStateSent() {
        return getStateId() == ObjectStateID.ESTIMATE_SENT;
    }

    public boolean isInactive() {
        boolean bRet = (this.getAutoGenerated() && this.getBehalfUserId() != null && this.getBehalfUserId() > 0);
        return bRet;
    }

}