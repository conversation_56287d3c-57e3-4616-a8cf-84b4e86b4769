package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 10/19/15
 */

@Entity
@Table(name = "TM_TEAM_OBJECT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class TeamObject extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "tm_team_object_id")
    private Long id;

    @Column(name = "tm_team_id")
    private Long teamId;

    /**
     * Three type of classes
     * 1000000: project
     * 1000100: workgroup
     * 2500000: admin
     */
    @Column(name = "oc_object_class_id")
    private Long objectClassId;

    @Column(name = "object_id")
    private Long objectId;

    @Column(name = "item_oc_object_state_id")
    private Long itemObjectStateId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getItemObjectStateId() {
        return itemObjectStateId;
    }

    public void setItemObjectStateId(Long itemObjectStateId) {
        this.itemObjectStateId = itemObjectStateId;
    }
}
