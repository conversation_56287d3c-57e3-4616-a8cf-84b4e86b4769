package com.noosh.app.commons.entity.property;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;

import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "pr_property_attribute")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONE)
public class PropertyAttribute extends NooshAuditingEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pr_property_attribute_ID_generated")
    @GenericGenerator(
            name = "pr_property_attribute_ID_generated",
            strategy = "io.hypersistence.utils.hibernate.id.BatchSequenceGenerator",
            parameters = {
                    @org.hibernate.annotations.Parameter(name = "sequence", value = "PR_PROPERTY_ATTRIBUTE_SEQ"),
                    @org.hibernate.annotations.Parameter(name = "fetch_size", value = "10")
    })
    @Column(name = "PR_PROPERTY_ATTRIBUTE_ID")
    private Long id;

    @Column(name = "PR_PROPERTY_ID")
    private Long prPropertyId;

    @Column(name = "PR_PROPERTY_PARAM_ID")
    private Long prPropertyParamId;

    private BigDecimal numberValue;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "date_value")
    private LocalDateTime dateValue;

    private String stringValue;

    private Long prPropertyUnitId;

    @ManyToOne
    @JoinColumn(name = "PR_PROPERTY_ID", insertable = false , updatable = false)
    private Property property;

    @OneToOne
    @JoinColumn(name = "PR_PROPERTY_PARAM_ID", insertable = false, updatable = false)
    private PropertyParam propertyParam;

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public PropertyParam getPropertyParam() {
        return propertyParam;
    }

    public void setPropertyParam(PropertyParam propertyParam) {
        this.propertyParam = propertyParam;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPrPropertyId() {
        return prPropertyId;
    }

    public void setPrPropertyId(Long prPropertyId) {
        this.prPropertyId = prPropertyId;
    }

    public Long getPrPropertyParamId() {
        return prPropertyParamId;
    }

    public void setPrPropertyParamId(Long prPropertyParamId) {
        this.prPropertyParamId = prPropertyParamId;
    }

    public BigDecimal getNumberValue() {
        return numberValue;
    }

    public void setNumberValue(BigDecimal numberValue) {
        this.numberValue = numberValue;
    }

    public LocalDateTime getDateValue() {
        return dateValue;
    }

    public void setDateValue(LocalDateTime dateValue) {
        this.dateValue = dateValue;
    }

    public String getStringValue() {
        return stringValue;
    }

    public void setStringValue(String stringValue) {
        this.stringValue = stringValue == null ? null : stringValue.trim();
    }

    public Long getPrPropertyUnitId() {
        return prPropertyUnitId;
    }

    public void setPrPropertyUnitId(Long prPropertyUnitId) {
        this.prPropertyUnitId = prPropertyUnitId;
    }


}