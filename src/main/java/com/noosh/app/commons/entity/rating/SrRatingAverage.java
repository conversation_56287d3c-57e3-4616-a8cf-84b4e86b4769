package com.noosh.app.commons.entity.rating;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * User: leilaz
 * Date: 8/18/16
 */
@Entity
@Table(name = "SR_RATING_AVERAGE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SrRatingAverage extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SrRatingAverage_id_generated")
    @SequenceGenerator(name = "SrRatingAverage_id_generated", sequenceName = "SR_RATING_AVERAGE_SEQ", allocationSize = 1)
    @Column(name = "SR_RATING_AVERAGE_ID")
    private Long id;

    @Column(name = "RATED_BY_AC_WORKGROUP_ID")
    private Long ratedByWorkGroupId;

    @Column(name = "RATED_FOR_AC_WORKGROUP_ID")
    private Long rateForWorkgroupId;

    @Column(name = "oc_object_class_id")
    private Long objectClassId;

    @Column(name = "AVERAGE_GRADE")
    private Long averageGrade;

    @Column(name = "COUNT")
    private Long count;

    @Column(name = "COMPUTED_DATE")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime computedDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRatedByWorkGroupId() {
        return ratedByWorkGroupId;
    }

    public void setRatedByWorkGroupId(Long ratedByWorkGroupId) {
        this.ratedByWorkGroupId = ratedByWorkGroupId;
    }

    public Long getRateForWorkgroupId() {
        return rateForWorkgroupId;
    }

    public void setRateForWorkgroupId(Long rateForWorkgroupId) {
        this.rateForWorkgroupId = rateForWorkgroupId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getAverageGrade() {
        return averageGrade;
    }

    public void setAverageGrade(Long averageGrade) {
        this.averageGrade = averageGrade;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public LocalDateTime getComputedDate() {
        return computedDate;
    }

    public void setComputedDate(LocalDateTime computedDate) {
        this.computedDate = computedDate;
    }
}
