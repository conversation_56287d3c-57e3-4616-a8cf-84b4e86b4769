package com.noosh.app.commons.entity.proposal;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.logo.WorkgroupLogo;
import jakarta.persistence.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.io.Serializable;

@Entity
@Table(name = "PC_PROPOSAL_BRANDING")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProposalBranding extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_PROPOSAL_BRANDING_ID_GENERATED")
    @SequenceGenerator(name = "PC_PROPOSAL_BRANDING_ID_GENERATED", sequenceName = "PC_PROPOSAL_BRANDING_SEQ", allocationSize = 1)
    @Column(name = "PC_PROPOSAL_BRANDING_ID")
    private Long id;

    @Column(name = "PC_PROPOSAL_ID")
    private Long proposalId;

    // 1-Top Right, 2-Top Middle, 3-Top Left
    @Column(name = "LOGO_POSITION")
    private Long logoPosition;

    // 1-Default, 2-Customized, 3-Workgroup Color
    @Column(name = "BRANDING_COLORS_TYPE")
    private Long brandingColorsType;

    @Column(name = "CUSTOMIZED_COLORS")
    private String customizedColors;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProposalId() {
        return proposalId;
    }

    public void setProposalId(Long proposalId) {
        this.proposalId = proposalId;
    }

    public Long getLogoPosition() {
        return logoPosition;
    }

    public void setLogoPosition(Long logoPosition) {
        this.logoPosition = logoPosition;
    }

    public Long getBrandingColorsType() {
        return brandingColorsType;
    }

    public void setBrandingColorsType(Long brandingColorsType) {
        this.brandingColorsType = brandingColorsType;
    }

    public String getCustomizedColors() {
        return customizedColors;
    }

    public void setCustomizedColors(String customizedColors) {
        this.customizedColors = customizedColors;
    }
}
