package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 8/4/16
 */
@Entity
@Table(name = "OC_OBJECT_STATE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ObjectState extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "OC_OBJECT_STATE_ID_GENERATED")
    @SequenceGenerator(name = "OC_OBJECT_STATE_ID_GENERATED", sequenceName = "OC_OBJECT_STATE_SEQ", allocationSize = 1)
    @Column(name = "OC_OBJECT_STATE_ID")
    private Long id;

    @Column(name = "CONSTANT_TOKEN")
    private String token;

    @Column(name = "DESCRIPTION_STR_ID")
    private Long descriptionStrId;

    @Column(name = "OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "ICON")
    private String icon;

    @Column(name = "LOGICAL_SORT_ORDER")
    private Long sortOrder;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Long getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Long sortOrder) {
        this.sortOrder = sortOrder;
    }
}
