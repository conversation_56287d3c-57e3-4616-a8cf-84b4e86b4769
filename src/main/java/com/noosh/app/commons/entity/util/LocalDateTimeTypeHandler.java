package com.noosh.app.commons.entity.util;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import java.sql.*;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.TimeZone;

/**
 * For Mybatis to handle LocalDateTime
 */

@MappedTypes(LocalDateTime.class)
public class LocalDateTimeTypeHandler implements TypeHandler<LocalDateTime> {

    public void setParameter(PreparedStatement ps, int i, LocalDateTime parameter, JdbcType jdbcType) throws SQLException {
        LocalDateTime date = (LocalDateTime) parameter;
        if (date == null)
            ps.setNull(i, Types.TIMESTAMP);
        else {
            Timestamp timestamp = new Timestamp(date.atZone(ZoneId.of("UTC")).toInstant().toEpochMilli());
            Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
            ps.setTimestamp(i, timestamp, calendar);
        }
//        if (parameter != null) {
//            ps.setTimestamp(i, new Timestamp( parameter.atZone(ZoneId.of("UTC")).toInstant().toEpochMilli()));
//        } else {
//            ps.setTimestamp(i, null);
//        }
    }

    public LocalDateTime getResult(ResultSet rs, String columnName) throws SQLException {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        Timestamp ts = rs.getTimestamp(columnName, calendar);
        if (ts != null) {
            return Instant.ofEpochMilli(ts.getTime()).atZone(ZoneId.of("UTC")).toLocalDateTime();
        } else {
            return null;
        }
    }

    public LocalDateTime getResult(CallableStatement cs, int columnIndex) throws SQLException {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
        Timestamp ts = cs.getTimestamp(columnIndex, calendar);
        if (ts != null) {
            return Instant.ofEpochMilli(ts.getTime()).atZone(ZoneId.of("UTC")).toLocalDateTime();
        } else {
            return null;
        }
    }

    public LocalDateTime getResult(ResultSet rs, int columnIndex) throws SQLException {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT"));
        Timestamp ts = rs.getTimestamp(columnIndex, calendar);
        if (ts != null) {
            return Instant.ofEpochMilli(ts.getTime()).atZone(ZoneId.of("UTC")).toLocalDateTime();
        } else {
            return null;
        }
    }

}