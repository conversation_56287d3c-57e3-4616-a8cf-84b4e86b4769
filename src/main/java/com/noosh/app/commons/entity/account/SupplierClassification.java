package com.noosh.app.commons.entity.account;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 9/29/20
 */
@Entity
@Table(name="BU_SUP_CLASSIF")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SupplierClassification extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 2088078680424803213L;

    @Id
    @Column(name = "BU_SUP_CLASSIF_ID")
    private Long id;

    @Column(name = "OWNER_AC_WORKGROUP_ID")
    private Long ownerWorkgroupId;

    @Column(name = "SUPPLIER_AC_WORKGROUP_ID")
    private Long supplierWorkgroupId;

    @Column(name = "IS_CURRENT")
    private Boolean isCurrent;

    @Column(name = "VERSION_NUMBER")
    private Long versionNumber;

    @Column(name = "IS_DELETED")
    private Boolean isDeleted;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "IS_DIVERSIFIED")
    private Boolean isDiversified;

    @Column(name = "DIVERSITY_DESCRIPTION")
    private String diversityDesc;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public Boolean getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Boolean current) {
        isCurrent = current;
    }

    public Long getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean deleted) {
        isDeleted = deleted;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Boolean getIsDiversified() {
        return isDiversified;
    }

    public void setIsDiversified(Boolean diversified) {
        isDiversified = diversified;
    }

    public String getDiversityDesc() {
        return diversityDesc;
    }

    public void setDiversityDesc(String diversityDesc) {
        this.diversityDesc = diversityDesc;
    }
}
