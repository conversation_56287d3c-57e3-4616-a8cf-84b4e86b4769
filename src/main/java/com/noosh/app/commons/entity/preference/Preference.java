package com.noosh.app.commons.entity.preference;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Optional;

@Entity
@Table(name = "pf_preference")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Preference extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pf_preference_id_generated")
    @SequenceGenerator(name = "pf_preference_id_generated", sequenceName = "PF_PREFERENCE_SEQ", allocationSize = 1)
    @Column(name = "PF_PREFERENCE_ID")
    private Long pfPreferenceId;

    @Column(name = "PR_PROPERTY_ID")
    private Long prPropertyId;

    @Column(name = "OWNER_OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "OWNER_OBJECT_ID")
    private Long objectId;

    @JsonIgnore
    @OneToOne
    @JoinColumn(name = "PR_PROPERTY_ID", insertable = false, updatable = false)
    private Property property;

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public Long getPfPreferenceId() {
        return pfPreferenceId;
    }

    public void setPfPreferenceId(Long pfPreferenceId) {
        this.pfPreferenceId = pfPreferenceId;
    }

    public Long getPrPropertyId() {
        return prPropertyId;
    }

    public void setPrPropertyId(Long prPropertyId) {
        this.prPropertyId = prPropertyId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    /**
     * DO NOT USE THIS FOR Workgroup Preference, use PreferenceService
     */
    @Deprecated
    public Boolean check(String preferenceName) {
        return this.getProperty().getPropertyAttributeSet()
                .stream().filter(attribute ->
                        attribute.getPropertyParam().getParamName().equalsIgnoreCase(preferenceName)
                                && attribute.getStringValue().equalsIgnoreCase("1")).findFirst().isPresent();
    }

    public String getValue(String preferenceName) {
        Optional<PropertyAttribute> optional = this.getProperty().getPropertyAttributeSet()
                .stream().filter(attribute ->
                        attribute.getPropertyParam().getParamName().equalsIgnoreCase(preferenceName)).findFirst();
        if (optional.isPresent()) {
            return optional.get().getStringValue();
        }
        return "-1";
    }

    public String getValue(String preferenceName, String defaultValue) {
        Optional<PropertyAttribute> optional = this.getProperty().getPropertyAttributeSet()
                .stream().filter(attribute ->
                        attribute.getPropertyParam().getParamName().equalsIgnoreCase(preferenceName)).findFirst();
        if (optional.isPresent()) {
            return optional.get().getStringValue();
        }
        return defaultValue;
    }
}