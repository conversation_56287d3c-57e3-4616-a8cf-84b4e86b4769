package com.noosh.app.commons.entity.quote;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.entity.order.OrderItem;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: neals
 * @Date: 12/09/2015
 */
@Entity
@Table(name = "PC_QUOTE_PRICE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class QuotePrice extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
    @Id
    @Column(name = "PC_QUOTE_PRICE_ID")
    private Long id;

    @Column(name = "PC_QUOTE_ITEM_ID")
    private Long quoteItemId;

    @Column(name = "PC_RFQ_QUANTITY_ID")
    private Long rfqQuantityId;

    @Column(name = "QUANTITY")
    private Long quantity;

    @Column(name = "EM_ESTIMATE_ITEM_PRICE_ID")
    private Long estimateItemPriceId;

    @Column(name = "SUPPLIER_NAME")
    private String supplierName;

    @Column(name = "IS_VISIBLE_TO_BUYER")
    private Boolean isVisibleToBuyer;

    @Column(name = "IS_CHOSEN")
    private Boolean isChosen;

    @Column(name = "PRE_MARKUP")
    private BigDecimal preMarkup;

    @Column(name = "PRE_MARKUP_AC_CURRENCY_ID")
    private Long preMarkupCurrencyId;

    @Column(name = "MARKUP_PERCENT")
    private Double markupPercent;

    @Column(name = "MARKUP_FIXED")
    private BigDecimal markupFixed;

    @Column(name = "MARKUP_FIXED_AC_CURRENCY_ID")
    private Long markupFixedCurrencyId;

    @Column(name = "PRICE")
    private BigDecimal price;

    @Column(name = "PRICE_AC_CURRENCY_ID")
    private Long priceCurrencyId;

    @Column(name = "ADD_PRICE")
    private BigDecimal addPrice;

    @Column(name = "ADD_PRICE_AC_CURRENCY_ID")
    private Long addPriceCurrencyId;

    @Column(name = "OR_ORDER_ITEM_ID")
    private Long orderItemId;

    @Column(name = "SOURCE_AC_WORKGROUP_ID")
    private Long sourceWorkgroupId;

    @Column(name = "SOURCE_SP_SPEC_ID")
    private Long sourceSpecId;

    @Column(name = "IS_SOURCE_SPEC_CHANGED")
    private Boolean isSourceSpecChanged;

    @Column(name = "SOURCE_SP_SPEC_NODE_ID")
    private Long sourceSpecNodeId;

    @Column(name = "SUPPLIER_CODE")
    private String supplierCode;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "TAX")
    private BigDecimal tax;

    @Column(name = "TAX_AC_CURRENCY_ID")
    private Long taxCurrencyId;

    @Column(name = "SHIPPING")
    private BigDecimal shipping;

    @Column(name = "SHIPPING_AC_CURRENCY_ID")
    private Long shippingCurrencyId;

    @Column(name = "PARENT_PC_QUOTE_PRICE_ID")
    private Long parentQuotePriceId;

    @Column(name = "PRICE_INDEX")
    private Long priceIndex;

    @Column(name = "ROOT_PC_QUOTE_PRICE_ID")
    private Long rootQuotePriceId;

    @Column(name = "IS_FROM_RATECARD")
    private Boolean isFromRatecard;

    @Column(name = "IS_FROM_QUOTE")
    private Boolean isFromQuote;

    @Column(name = "PC_CONTRACT_ID")
    private Long contractId;

    @Column(name = "ORI_PC_QUOTE_ITEM_ID")
    private Long oriQuoteItemId;

    @Column(name = "SUPPLIER_USER_ID")
    private Long supplierUserId;

    @Column(name = "MARGIN_PERCENT")
    private BigDecimal marginPercent;
    
    @Column(name = "QUANTITY_INDEX")
    private Long quantityIndex;

    @Column(name = "EXPRE_MARKUP")
    private BigDecimal exPreMarkup;

    @Column(name = "EXMARKUP_FIX")
    private BigDecimal exMarkupFixed;

    @Column(name = "EXPRICE")
    private BigDecimal exPrice;

    @Column(name = "EXTAX")
    private BigDecimal exTax;

    @Column(name = "EXSHIPPING")
    private BigDecimal exShipping;


    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "EM_ESTIMATE_ITEM_PRICE_ID", referencedColumnName = "EM_ESTIMATE_ITEM_PRICE_ID", insertable = false, updatable = false)
    private EstimateItemPrice estimateItemPrice;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "OR_ORDER_ITEM_ID", referencedColumnName = "OR_ORDER_ITEM_ID", insertable = false, updatable = false)
    private OrderItem orderItem;

    public BigDecimal getExShipping() {
        return exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public BigDecimal getExTax() {
        return exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public BigDecimal getExPrice() {
        return exPrice;
    }

    public void setExPrice(BigDecimal exPrice) {
        this.exPrice = exPrice;
    }

    public BigDecimal getExMarkupFixed() {
        return exMarkupFixed;
    }

    public void setExMarkupFixed(BigDecimal exMarkupFixed) {
        this.exMarkupFixed = exMarkupFixed;
    }

    public BigDecimal getExPreMarkup() {
        return exPreMarkup;
    }

    public void setExPreMarkup(BigDecimal exPreMarkup) {
        this.exPreMarkup = exPreMarkup;
    }

    public Long getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Long orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuoteItemId() {
        return quoteItemId;
    }

    public void setQuoteItemId(Long quoteItemId) {
        this.quoteItemId = quoteItemId;
    }

    public Long getRfqQuantityId() {
        return rfqQuantityId;
    }

    public void setRfqQuantityId(Long rfqQuantityId) {
        this.rfqQuantityId = rfqQuantityId;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Long getEstimateItemPriceId() {
        return estimateItemPriceId;
    }

    public void setEstimateItemPriceId(Long estimateItemPriceId) {
        this.estimateItemPriceId = estimateItemPriceId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Boolean isVisibleToBuyer() {
        return isVisibleToBuyer;
    }

    public void setVisibleToBuyer(Boolean visibleToBuyer) {
        isVisibleToBuyer = visibleToBuyer;
    }

    public Boolean isChosen() {
        return isChosen;
    }

    public void setChosen(Boolean chosen) {
        isChosen = chosen;
    }

    public BigDecimal getPreMarkup() {
        return preMarkup;
    }

    public void setPreMarkup(BigDecimal preMarkup) {
        this.preMarkup = preMarkup;
    }

    public Long getPreMarkupCurrencyId() {
        return preMarkupCurrencyId;
    }

    public void setPreMarkupCurrencyId(Long preMarkupCurrencyId) {
        this.preMarkupCurrencyId = preMarkupCurrencyId;
    }

    public Double getMarkupPercent() {
        return markupPercent;
    }

    public void setMarkupPercent(Double markupPercent) {
        this.markupPercent = markupPercent;
    }

    public BigDecimal getMarkupFixed() {
        return markupFixed;
    }

    public void setMarkupFixed(BigDecimal markupFixed) {
        this.markupFixed = markupFixed;
    }

    public Long getMarkupFixedCurrencyId() {
        return markupFixedCurrencyId;
    }

    public void setMarkupFixedCurrencyId(Long markupFixedCurrencyId) {
        this.markupFixedCurrencyId = markupFixedCurrencyId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getPriceCurrencyId() {
        return priceCurrencyId;
    }

    public void setPriceCurrencyId(Long priceCurrencyId) {
        this.priceCurrencyId = priceCurrencyId;
    }

    public BigDecimal getAddPrice() {
        return addPrice;
    }

    public void setAddPrice(BigDecimal addPrice) {
        this.addPrice = addPrice;
    }

    public Long getAddPriceCurrencyId() {
        return addPriceCurrencyId;
    }

    public void setAddPriceCurrencyId(Long addPriceCurrencyId) {
        this.addPriceCurrencyId = addPriceCurrencyId;
    }

    public Long getSourceWorkgroupId() {
        return sourceWorkgroupId;
    }

    public void setSourceWorkgroupId(Long sourceWorkgroupId) {
        this.sourceWorkgroupId = sourceWorkgroupId;
    }

    public Long getSourceSpecId() {
        return sourceSpecId;
    }

    public void setSourceSpecId(Long sourceSpecId) {
        this.sourceSpecId = sourceSpecId;
    }

    public Boolean isSourceSpecChanged() {
        return isSourceSpecChanged;
    }

    public void setSourceSpecChanged(Boolean sourceSpecChanged) {
        isSourceSpecChanged = sourceSpecChanged;
    }

    public Long getSourceSpecNodeId() {
        return sourceSpecNodeId;
    }

    public void setSourceSpecNodeId(Long sourceSpecNodeId) {
        this.sourceSpecNodeId = sourceSpecNodeId;
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public Long getParentQuotePriceId() {
        return parentQuotePriceId;
    }

    public void setParentQuotePriceId(Long parentQuotePriceId) {
        this.parentQuotePriceId = parentQuotePriceId;
    }

    public Long getPriceIndex() {
        return priceIndex;
    }

    public void setPriceIndex(Long priceIndex) {
        this.priceIndex = priceIndex;
    }

    public Long getRootQuotePriceId() {
        return rootQuotePriceId;
    }

    public void setRootQuotePriceId(Long rootQuotePriceId) {
        this.rootQuotePriceId = rootQuotePriceId;
    }

    public Boolean isFromRatecard() {
        return isFromRatecard;
    }

    public void setFromRatecard(Boolean fromRatecard) {
        isFromRatecard = fromRatecard;
    }

    public Boolean isFromQuote() {
        return isFromQuote;
    }

    public void setFromQuote(Boolean fromQuote) {
        isFromQuote = fromQuote;
    }

    public Long getContractId() {
        return contractId;
    }

    public void setContractId(Long contractId) {
        this.contractId = contractId;
    }

    public Long getOriQuoteItemId() {
        return oriQuoteItemId;
    }

    public void setOriQuoteItemId(Long oriQuoteItemId) {
        this.oriQuoteItemId = oriQuoteItemId;
    }

    public Long getSupplierUserId() {
        return supplierUserId;
    }

    public void setSupplierUserId(Long supplierUserId) {
        this.supplierUserId = supplierUserId;
    }

    public BigDecimal getMarginPercent() {
        return marginPercent;
    }

    public void setMarginPercent(BigDecimal marginPercent) {
        this.marginPercent = marginPercent;
    }

    public Long getQuantityIndex() {
        return quantityIndex;
    }

    public void setQuantityIndex(Long quantityIndex) {
        this.quantityIndex = quantityIndex;
    }

    public EstimateItemPrice getEstimateItemPrice() {
        return estimateItemPrice;
    }

    public void setEstimateItemPrice(EstimateItemPrice estimateItemPrice) {
        this.estimateItemPrice = estimateItemPrice;
    }

    public OrderItem getOrderItem() {
        return orderItem;
    }

    public void setOrderItem(OrderItem orderItem) {
        this.orderItem = orderItem;
    }
}
