package com.noosh.app.commons.entity.rating;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * User: leilaz
 * Date: 8/18/16
 */
@Entity
@Table(name = "SR_RATING")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SrRating extends NooshAuditingEntity implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "SrRating_id_generated")
    @SequenceGenerator(name = "SrRating_id_generated", sequenceName = "SR_RATING_SEQ", allocationSize = 1)
    @Column(name = "SR_RATING_ID")
    private Long id;

    @Column(name = "SR_QUESTIONNAIRE_ID")
    private Long questionnaireId;

    @Column(name = "RATED_BY_AC_WORKGROUP_ID")
    private Long ratedByWorkGroupId;

    @Column(name = "RATED_FOR_AC_WORKGROUP_ID")
    private Long rateForWorkGroupId;

    @Column(name = "OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "OC_OBJECT_ID")
    private Long objectId;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "AVERAGE_GRADE")
    private Long averageGrade;

    @Column(name = "GRANULARITY")
    private Short granularity;

    @Column(name = "NA_ALLOWED")
    private Short NaAllowed;

    @Column(name = "COMPLETE_DATE")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime completeDate;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @JsonIgnore
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "srRating")
    private List<SrRatingItem> srRatingItemList;

    public List<SrRatingItem> getSrRatingItemList() {
        return srRatingItemList;
    }

    public void setSrRatingItemList(List<SrRatingItem> srRatingItemList) {
        this.srRatingItemList = srRatingItemList;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(Long questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public Long getRatedByWorkGroupId() {
        return ratedByWorkGroupId;
    }

    public void setRatedByWorkGroupId(Long ratedByWorkGroupId) {
        this.ratedByWorkGroupId = ratedByWorkGroupId;
    }

    public Long getRateForWorkGroupId() {
        return rateForWorkGroupId;
    }

    public void setRateForWorkGroupId(Long rateForWorkGroupId) {
        this.rateForWorkGroupId = rateForWorkGroupId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getAverageGrade() {
        return averageGrade;
    }

    public void setAverageGrade(Long averageGrade) {
        this.averageGrade = averageGrade;
    }

    public Short getGranularity() {
        return granularity;
    }

    public void setGranularity(Short granularity) {
        this.granularity = granularity;
    }

    public Short getNaAllowed() {
        return NaAllowed;
    }

    public void setNaAllowed(Short naAllowed) {
        NaAllowed = naAllowed;
    }

    public LocalDateTime getCompleteDate() {
        return completeDate;
    }

    public void setCompleteDate(LocalDateTime completeDate) {
        this.completeDate = completeDate;
    }
}
