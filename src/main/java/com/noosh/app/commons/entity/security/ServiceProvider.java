package com.noosh.app.commons.entity.security;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: neals
 * @Date: 12/31/2015
 */
@Entity
@Table(name = "NL_SERVICE_PROVIDER")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ServiceProvider implements Serializable{

	private static final long serialVersionUID = 1L;
	
    @Id
    @Column(name = "NL_SERVICE_PROVIDER_ID")
    private Long id;

    @Column(name = "NAME")
    private String name;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "SUB_DOMAIN")
    private String subDomain;

    @Column(name = "LOGO_IMG")
    private String logoImg;

    @Column(name = "BACKGROUND_IMG")
    private String backgroundImg;

    @Column(name = "IS_TRIAL")
    private Boolean isTrial;

    @Column(name = "NL_SERVICE_PROVIDER_TYPE_ID")
    private Long serviceProviderTypeId;

    @Column(name = "IS_ABLE_TO_CHANGE_TYPE")
    private Boolean isAbleToChangeType;

    @Column(name = "SIGN_UP_SUBDOMAIN")
    private String signUpSubdomain;

    @Column(name = "NGE_ROOT_DOMAIN")
    private String ngeRootDomain;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public String getSubDomain() {
        return subDomain;
    }

    public void setSubDomain(String subDomain) {
        this.subDomain = subDomain;
    }

    public String getLogoImg() {
        return logoImg;
    }

    public void setLogoImg(String logoImg) {
        this.logoImg = logoImg;
    }

    public String getBackgroundImg() {
        return backgroundImg;
    }

    public void setBackgroundImg(String backgroundImg) {
        this.backgroundImg = backgroundImg;
    }

    public Boolean getIsTrial() {
        return isTrial;
    }

    public void setIsTrial(Boolean isTrial) {
        this.isTrial = isTrial;
    }

    public Long getServiceProviderTypeId() {
        return serviceProviderTypeId;
    }

    public void setServiceProviderTypeId(Long serviceProviderTypeId) {
        this.serviceProviderTypeId = serviceProviderTypeId;
    }

    public Boolean getIsAbleToChangeType() {
        return isAbleToChangeType;
    }

    public void setIsAbleToChangeType(Boolean isAbleToChangeType) {
        this.isAbleToChangeType = isAbleToChangeType;
    }

    public String getSignUpSubdomain() {
        return signUpSubdomain;
    }

    public void setSignUpSubdomain(String signUpSubdomain) {
        this.signUpSubdomain = signUpSubdomain;
    }

    public String getNgeRootDomain() {
        return ngeRootDomain;
    }

    public void setNgeRootDomain(String ngeRootDomain) {
        this.ngeRootDomain = ngeRootDomain;
    }
}
