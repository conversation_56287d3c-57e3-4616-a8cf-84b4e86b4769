package com.noosh.app.commons.entity.account;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * @Author: <PERSON>
 * @Date: 06/02/2017
 */

@Entity
@Table(name="AC_COUNTRY")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONE)
public class Country extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 5346553892472207708L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ac_country_id_generated")
    @SequenceGenerator(name = "ac_country_id_generated", sequenceName = "AC_COUNTRY_SEQ", allocationSize = 1)
    @Column(name="AC_COUNTRY_ID")
    public Long id;

    @Column(name="CONSTANT_TOKEN")
    public String constantToken;

    @Column(name="NAME_STR_ID")
    public Long nameStrId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }
}