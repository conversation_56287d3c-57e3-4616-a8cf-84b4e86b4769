package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 6/6/19
 */
@Entity
@Table(name = "AC_OBJECT_COUNTER")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ObjectCounter extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "AC_OBJECT_COUNTER_ID_GENERATED")
    @SequenceGenerator(name = "AC_OBJECT_COUNTER_ID_GENERATED", sequenceName = "AC_OBJECT_COUNTER_SEQ", allocationSize = 1)
    @Column(name = "AC_OBJECT_COUNTER_ID")
    private Long id;

    @Column(name = "AC_WORKGROUP_ID")
    public Long workgroupId;

    @Column(name = "AC_OBJECT_COUNTER_TYPE_ID")
    public Long counterTypeId;

    @Column(name = "IS_ACTIVE")
    public Boolean isActive;

    @Column(name = "PREFIX")
    public String prefix;

    @Column(name = "NEXT_VALUE")
    public Long nextValue;

    @Column(name = "NUMBER_OF_DIGITS")
    public Long numberOfDigits;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getCounterTypeId() {
        return counterTypeId;
    }

    public void setCounterTypeId(Long counterTypeId) {
        this.counterTypeId = counterTypeId;
    }

    public boolean getIsActive() {
        return isActive == null ? false : isActive;
    }

    public void setIsActive(boolean IS_ACTIVE) {
        this.isActive = IS_ACTIVE;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public Long getNextValue() {
        return nextValue;
    }

    public void setNextValue(Long nextValue) {
        this.nextValue = nextValue;
    }

    public Long getNumberOfDigits() {
        return numberOfDigits;
    }

    public void setNumberOfDigits(Long numberOfDigits) {
        this.numberOfDigits = numberOfDigits;
    }
}
