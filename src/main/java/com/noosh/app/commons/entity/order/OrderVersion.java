package com.noosh.app.commons.entity.order;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.benchmark.BenchmarkItem;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import com.noosh.app.commons.entity.security.Workgroup;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: <PERSON>-<PERSON><PERSON>
 * @Date: 10/14/15
 */

@Entity
@Table(name = "OR_ORDER_VERSION")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class OrderVersion implements Serializable {

	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "order_version_id_generator")
    @SequenceGenerator(name = "order_version_id_generator", sequenceName = "OR_ORDER_VERSION_SEQ", allocationSize = 1)
    @Column(name = "or_order_version_id")
    private Long id;

    @Column(name = "OR_ORDER_ID")
    private Long orderId;

    @Column(name = "version_number")
    private Long version;

    private String reference;

    @Column(name = "buyer_ac_workgroup_id")
    private Long buyerWorkgroupId;

    @Column(name = "supplier_ac_workgroup_id")
    private Long supplierWorkgroupId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "BU_PAYMENT_METHOD_ID")
    private Long paymentMethodId;

    @Column(name = "payment_reference")
    private String paymentReference;

    @Column(name = "co_service_id")
    private Long coServiceId;

    @Column(name = "is_current")
    private Boolean isCurrent;

    private BigDecimal tax;

    private BigDecimal shipping;

    @Column(name = "tax_ac_currency_id")
    private Long taxCurrencyId;

    @Column(name = "shipping_ac_currency_id")
    private Long shippingCurrencyId;

    private String comments;

    @Column(name = "misc_cost")
    private BigDecimal miscCost;

    @Column(name = "D_OR_S")
    private BigDecimal dors;

    @Column(name = "misc_cost_ac_currency_id")
    private Long miscCostCurrencyId;

    @Column(name = "D_OR_S_AC_CURRENCY_ID")
    private Long dorsCurrencyId;

    //dual currency
    private BigDecimal rate;

    @Column(name = "EX_CURRENCYID")
    private Long exCurrencyId;

    @Column(name = "EXTAX")
    private BigDecimal exTax;

    @Column(name = "EXSHIPPING")
    private BigDecimal exShipping;

    @Column(name = "EXTAX_AC_CURRENCY_ID")
    private Long exTaxCurrencyId;

    @Column(name = "EXSHIPPING_AC_CURRENCY_ID")
    private Long exShippingCurrencyId;

    @Column(name = "EXMISC_COST")
    private BigDecimal exMiscCost;

    @Column(name = "EX_D_OR_S")
    private BigDecimal exDors;

    @Column(name = "EXMISC_COST_AC_CURRENCY_ID")
    private Long exMiscCostCurrencyId;

    @Column(name = "EX_D_OR_S_AC_CURRENCY_ID")
    private Long exDorsCurrencyId;

    @Column(name = "buyer_user_id")
    private Long buyerUserId;

    @Column(name = "supplier_user_id")
    private Long supplierUserId;

    private String title;

    @Column(name = "or_order_type_id")
    private Long orderTypeId;

    @Column(name = "init_by_ac_workgroup_id")
    private Long initByWorkgroupId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "accept_date")
    private LocalDateTime acceptDate;

    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "ORDER_CREATE_DATE")
    private LocalDateTime orderCreateDate;

    @Column(name = "buyer_ac_terms_id")
    private Long buyerTermsId;

    @Column(name = "supplier_ac_terms_id")
    private Long supplierTermsId;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "supplier_reference")
    private String supplierReference;

    @Column(name = "pc_quote_id")
    private Long quoteId;

    @Column(name = "budget_ac_custom_field_id")
    private Long bugetTypeFieldId;

    @Column(name = "is_generated")
    private Boolean isGenerated;

    @Column(name = "OVERS_PERCENT")
    private BigDecimal oversPercent;

    @Column(name = "UNDERS_PERCENT")
    private BigDecimal undersPercent;

    @Column(name = "PC_REASON_ID")
    private Long reasonId;

    @Column(name = "reason_other")
    private String reasonOther;

    @Column(name = "itemized_tns")
    private Boolean itemized;

    @Column(name = "VAT_CODE")
    private String vatCode;

    @Column(name = "VAT_RATE")
    private BigDecimal vatRate;

    @Column(name = "IS_SENSITIVE")
    private Boolean isSensitive;

    @Column(name = "bu_client_workgroup_id")
    private Long buClientWorkgroupId;

    @Column(name = "or_order_classification_id")
    private Long orderClassificationId;

    @Column(name = "RE_ACCEPTANCE_STATUS_ID")
    private Long reAcceptanceStatusId;

    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "approved_date")
    private LocalDateTime approvedDate;

    @Column(name = "create_user_id", nullable = false)
    private Long createUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "create_date", nullable = false)
    private LocalDateTime createDate = LocalDateTime.now();

    @Column(name = "mod_user_id")
    private Long modUserId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "mod_date")
    private LocalDateTime modDate = LocalDateTime.now();

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "buyer_ac_workgroup_id", referencedColumnName = "AC_WORKGROUP_ID", insertable = false, updatable = false)
    private Workgroup buyerWorkgroup;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supplier_ac_workgroup_id", referencedColumnName = "AC_WORKGROUP_ID", insertable = false, updatable = false)
    private Workgroup supplierWorkgroup;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "OR_ORDER_ID", referencedColumnName = "or_order_id", insertable = false, updatable = false)
    private Order order;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "buyer_user_id", referencedColumnName = "USER_ID", insertable = false, updatable = false)
    private AccountUser buyer;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "supplier_user_id", referencedColumnName = "USER_ID", insertable = false, updatable = false)
    private AccountUser supplier;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "BU_PAYMENT_METHOD_ID", referencedColumnName = "BU_PAYMENT_METHOD_ID", insertable = false, updatable = false)
    private PaymentMethod paymentMethod;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_REASON_ID", referencedColumnName = "PC_REASON_ID", insertable = false, updatable = false)
    private PcReason reason;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CUSTOM_PR_PROPERTY_ID", referencedColumnName = "PR_PROPERTY_ID", insertable = false, updatable = false)
    private Property property;
	
//	@OneToMany(fetch=FetchType.LAZY)
//    @JoinColumn(name = "OR_ORDER_ID", referencedColumnName = "OR_ORDER_ID")
//    private List<BenchmarkItem> benchmarkItems;

    public String getVatCode() {
        return vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public BigDecimal getVatRate() {
        return vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public Boolean getIsSensitive() {
        return isSensitive;
    }

    public void setIsSensitive(Boolean sensitive) {
        isSensitive = sensitive;
	}

//    public List<BenchmarkItem> getBenchmarkItems() {
//        return benchmarkItems;
//    }
//
//    public void setBenchmarkItems(List<BenchmarkItem> benchmarkItems) {
//        this.benchmarkItems = benchmarkItems;
//    }

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public PcReason getReason() {
        return reason;
    }

    public void setReason(PcReason reason) {
        this.reason = reason;
    }

    public LocalDateTime getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(LocalDateTime orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public AccountUser getBuyer() {
        return buyer;
    }

    public void setBuyer(AccountUser buyer) {
        this.buyer = buyer;
    }

    public AccountUser getSupplier() {
        return supplier;
    }

    public void setSupplier(AccountUser supplier) {
        this.supplier = supplier;
    }

    public BigDecimal getDors() {
        return dors;
    }

    public void setDors(BigDecimal dors) {
        this.dors = dors;
    }

    public Long getDorsCurrencyId() {
        return dorsCurrencyId;
    }

    public void setDorsCurrencyId(Long dorsCurrencyId) {
        this.dorsCurrencyId = dorsCurrencyId;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public BigDecimal getOversPercent() {
        return oversPercent;
    }

    public void setOversPercent(BigDecimal oversPercent) {
        this.oversPercent = oversPercent;
    }

    public BigDecimal getUndersPercent() {
        return undersPercent;
    }

    public void setUndersPercent(BigDecimal undersPercent) {
        this.undersPercent = undersPercent;
    }

    public Long getBugetTypeFieldId() {
        return bugetTypeFieldId;
    }

    public void setBugetTypeFieldId(Long bugetTypeFieldId) {
        this.bugetTypeFieldId = bugetTypeFieldId;
    }

    public Workgroup getBuyerWorkgroup() {
        return buyerWorkgroup;
    }

    public void setBuyerWorkgroup(Workgroup buyerWorkgroup) {
        this.buyerWorkgroup = buyerWorkgroup;
    }

    public Workgroup getSupplierWorkgroup() {
        return supplierWorkgroup;
    }

    public void setSupplierWorkgroup(Workgroup supplierWorkgroup) {
        this.supplierWorkgroup = supplierWorkgroup;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public Long getModUserId() {
        return modUserId;
    }

    public void setModUserId(Long modUserId) {
        this.modUserId = modUserId;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public Long getBuyerWorkgroupId() {
        return buyerWorkgroupId;
    }

    public void setBuyerWorkgroupId(Long buyerWorkgroupId) {
        this.buyerWorkgroupId = buyerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId == null ? (long)-1 : supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getPaymentMethodId() {
        return paymentMethodId;
    }

    public void setPaymentMethodId(Long paymentMethodId) {
        this.paymentMethodId = paymentMethodId;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public Long getCoServiceId() {
        return coServiceId;
    }

    public void setCoServiceId(Long coServiceId) {
        this.coServiceId = coServiceId;
    }

    public Boolean getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Boolean isCurrent) {
        this.isCurrent = isCurrent;
    }

    public BigDecimal getRate() {
        return this.rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return this.exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public Long getShippingCurrencyId() {
        return shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public BigDecimal getMiscCost() {
        return miscCost == null ? BigDecimal.ZERO : miscCost;
    }

    public void setMiscCost(BigDecimal miscCost) {
        this.miscCost = miscCost;
    }

    public Long getMiscCostCurrencyId() {
        return miscCostCurrencyId;
    }

    public void setMiscCostCurrencyId(Long miscCostCurrencyId) {
        this.miscCostCurrencyId = miscCostCurrencyId;
    }

    public BigDecimal getExTax() {
        return this.exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public BigDecimal getExShipping() {
        return this.exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public Long getExTaxCurrencyId() {
        return this.exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public Long getExShippingCurrencyId() {
        return this.exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public BigDecimal getExMiscCost() {
        return exMiscCost == null ? BigDecimal.ZERO : exMiscCost;
    }

    public void setExMiscCost(BigDecimal exMiscCost) {
        this.exMiscCost = exMiscCost;
    }

    public BigDecimal getExDors() {
        return this.exDors;
    }

    public void setExDors(BigDecimal exDors) {
        this.exDors = exDors;
    }

    public Long getExMiscCostCurrencyId() {
        return this.exMiscCostCurrencyId;
    }

    public void setExMiscCostCurrencyId(Long exMiscCostCurrencyId) {
        this.exMiscCostCurrencyId = exMiscCostCurrencyId;
    }

    public Long getExDorsCurrencyId() {
        return this.exDorsCurrencyId;
    }

    public void setExDorsCurrencyId(Long exDorsCurrencyId) {
        this.exDorsCurrencyId = exDorsCurrencyId;
    }

    public Long getBuyerUserId() {
        return buyerUserId;
    }

    public void setBuyerUserId(Long buyerUserId) {
        this.buyerUserId = buyerUserId;
    }

    public Long getSupplierUserId() {
        return supplierUserId == null ? (long) -1 : supplierUserId;
    }

    public void setSupplierUserId(Long supplierUserId) {
        this.supplierUserId = supplierUserId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public Long getInitByWorkgroupId() {
        return initByWorkgroupId;
    }

    public void setInitByWorkgroupId(Long initByWorkgroupId) {
        this.initByWorkgroupId = initByWorkgroupId;
    }

    public LocalDateTime getAcceptDate() {
        return acceptDate;
    }

    public void setAcceptDate(LocalDateTime acceptDate) {
        this.acceptDate = acceptDate;
    }

    public Long getBuyerTermsId() {
        return buyerTermsId;
    }

    public void setBuyerTermsId(Long buyerTermsId) {
        this.buyerTermsId = buyerTermsId;
    }

    public Long getSupplierTermsId() {
        return supplierTermsId;
    }

    public void setSupplierTermsId(Long supplierTermsId) {
        this.supplierTermsId = supplierTermsId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public String getSupplierReference() {
        return supplierReference;
    }

    public void setSupplierReference(String supplierReference) {
        this.supplierReference = supplierReference;
    }

    public Long getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    public Boolean getIsGenerated() {
        return isGenerated;
    }

    public void setIsGenerated(Boolean isGenerated) {
        this.isGenerated = isGenerated;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public String getReasonOther() {
        return reasonOther;
    }

    public void setReasonOther(String reasonOther) {
        this.reasonOther = reasonOther;
    }

    public Boolean getItemized() {
        return itemized == null ? false : itemized;
    }

    public void setItemized(Boolean itemized) {
        this.itemized = itemized;
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }

    public Long getOrderClassificationId() {
        return orderClassificationId;
    }

    public void setOrderClassificationId(Long orderClassificationId) {
        this.orderClassificationId = orderClassificationId;
    }

    public Long getReAcceptanceStatusId() {
        return reAcceptanceStatusId;
    }

    public void setReAcceptanceStatusId(Long reAcceptanceStatusId) {
        this.reAcceptanceStatusId = reAcceptanceStatusId;
    }

    public LocalDateTime getApprovedDate() {
        return approvedDate;
    }

    public void setApprovedDate(LocalDateTime approvedDate) {
        this.approvedDate = approvedDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        OrderVersion ov = (OrderVersion) o;

        return id.equals(ov.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    public String getOrderTitle() {
        if (title == null || title.length() == 0) {
            return reference;
        }
        return title;
    }
}
