package com.noosh.app.commons.entity.security;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.address.Address;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 10/18/20
 */
@Entity
@Table(name = "AC_WORKGROUP_ADDRESS")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class WorkgroupAddress extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = -1450332932347957085L;

    @Id
    @Column(name = "AC_WORKGROUP_ADDRESS_ID")
    private Long id;

    @Column(name = "AC_WORKGROUP_ID")
    private Long workgroupId;

    @Column(name = "AC_ADDRESS_ID")
    private Long addressId;

    @Column(name = "AC_ADDRESS_TYPE_ID")
    private Long addressTypeId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_ADDRESS_ID", referencedColumnName = "AC_ADDRESS_ID", insertable = false, updatable = false)
    private Address address;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_WORKGROUP_ID", referencedColumnName = "AC_WORKGROUP_ID", insertable = false, updatable = false)
    private Workgroup workgroup;

    public Workgroup getWorkgroup() {
        return workgroup;
    }

    public void setWorkgroup(Workgroup workgroup) {
        this.workgroup = workgroup;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public Long getAddressTypeId() {
        return addressTypeId;
    }

    public void setAddressTypeId(Long addressTypeId) {
        this.addressTypeId = addressTypeId;
    }
}
