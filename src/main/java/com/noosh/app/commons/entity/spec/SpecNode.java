package com.noosh.app.commons.entity.spec;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 5/24/16
 */
@Entity
@Table(name = "SP_SPEC_NODE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SpecNode extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "specNode_id_generated")
    @SequenceGenerator(name = "specNode_id_generated", sequenceName = "SP_SPEC_NODE_SEQ", allocationSize = 1)
    @Column(name="SP_SPEC_NODE_ID")
    private Long id;

    @Column(name = "SP_SPEC_ID")
    private Long spSpecId;

    private Long parentSpSpecNodeId;

    private Long rootSpSpecNodeId;

    private Long spSpecTreeId;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "SP_SPEC_ID", insertable = false, updatable = false)
    private Spec spec;

    public Spec getSpec() {
        return spec;
    }

    public void setSpec(Spec spec) {
        this.spec = spec;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSpSpecId() {
        return spSpecId;
    }

    public void setSpSpecId(Long spSpecId) {
        this.spSpecId = spSpecId;
    }

    public Long getParentSpSpecNodeId() {
        return parentSpSpecNodeId;
    }

    public void setParentSpSpecNodeId(Long parentSpSpecNodeId) {
        this.parentSpSpecNodeId = parentSpSpecNodeId;
    }

    public Long getRootSpSpecNodeId() {
        return rootSpSpecNodeId;
    }

    public void setRootSpSpecNodeId(Long rootSpSpecNodeId) {
        this.rootSpSpecNodeId = rootSpSpecNodeId;
    }

    public Long getSpSpecTreeId() {
        return spSpecTreeId;
    }

    public void setSpSpecTreeId(Long spSpecTreeId) {
        this.spSpecTreeId = spSpecTreeId;
    }
}
