package com.noosh.app.commons.entity.job;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 5/19/16
 */
@Entity
@Table(name = "PC_JOB_STATUS")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class PcJobStatus extends NooshAuditingEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pcJobStatus_id_generated")
    @SequenceGenerator(name = "pcJobStatus_id_generated", sequenceName = "PC_JOB_STATUS_SEQ", allocationSize = 1)
    @Column(name="PC_JOB_STATUS_ID")
    private Long pcJobStatusId;

    private Long buyerAcWorkgroupId;

    private Long ownerAcWorkgroupId;

    private Long ocObjectStateId;

    private Integer rfeCount;

    private Integer estimateCount;

    private Integer rfqCount;

    private Integer quoteCount;

    private Integer orderPendingCount;

    private Integer orderAcceptedCount;

    private Long orderCompletedCount;

    @Column(name = "PC_JOB_ID")
    private Long pcJobId;

    private Long pmProjectId;

    private Integer prCount;

    private Long buClientWorkgroupId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_JOB_ID", insertable = false, updatable = false)
    private PcJob pcJob;

    private static final long serialVersionUID = 1L;

    public PcJob getPcJob() {
        return pcJob;
    }

    public void setPcJob(PcJob pcJob) {
        this.pcJob = pcJob;
    }

    public Long getPcJobStatusId() {
        return pcJobStatusId;
    }

    public void setPcJobStatusId(Long pcJobStatusId) {
        this.pcJobStatusId = pcJobStatusId;
    }

    public Long getBuyerAcWorkgroupId() {
        return buyerAcWorkgroupId;
    }

    public void setBuyerAcWorkgroupId(Long buyerAcWorkgroupId) {
        this.buyerAcWorkgroupId = buyerAcWorkgroupId;
    }

    public Long getOwnerAcWorkgroupId() {
        return ownerAcWorkgroupId;
    }

    public void setOwnerAcWorkgroupId(Long ownerAcWorkgroupId) {
        this.ownerAcWorkgroupId = ownerAcWorkgroupId;
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }

    public Integer getRfeCount() {
        return rfeCount;
    }

    public void setRfeCount(Integer rfeCount) {
        this.rfeCount = rfeCount;
    }

    public Integer getEstimateCount() {
        return estimateCount;
    }

    public void setEstimateCount(Integer estimateCount) {
        this.estimateCount = estimateCount;
    }

    public Integer getRfqCount() {
        return rfqCount;
    }

    public void setRfqCount(Integer rfqCount) {
        this.rfqCount = rfqCount;
    }

    public Integer getQuoteCount() {
        return quoteCount;
    }

    public void setQuoteCount(Integer quoteCount) {
        this.quoteCount = quoteCount;
    }

    public Integer getOrderPendingCount() {
        return orderPendingCount;
    }

    public void setOrderPendingCount(Integer orderPendingCount) {
        this.orderPendingCount = orderPendingCount;
    }

    public Integer getOrderAcceptedCount() {
        return orderAcceptedCount;
    }

    public void setOrderAcceptedCount(Integer orderAcceptedCount) {
        this.orderAcceptedCount = orderAcceptedCount;
    }

    public Long getOrderCompletedCount() {
        return orderCompletedCount;
    }

    public void setOrderCompletedCount(Long orderCompletedCount) {
        this.orderCompletedCount = orderCompletedCount;
    }

    public Long getPcJobId() {
        return pcJobId;
    }

    public void setPcJobId(Long pcJobId) {
        this.pcJobId = pcJobId;
    }

    public Long getPmProjectId() {
        return pmProjectId;
    }

    public void setPmProjectId(Long pmProjectId) {
        this.pmProjectId = pmProjectId;
    }

    public Integer getPrCount() {
        return prCount;
    }

    public void setPrCount(Integer prCount) {
        this.prCount = prCount;
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }
}
