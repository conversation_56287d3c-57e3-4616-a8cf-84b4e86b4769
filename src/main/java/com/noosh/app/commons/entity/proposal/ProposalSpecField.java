package com.noosh.app.commons.entity.proposal;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import jakarta.persistence.*;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.io.Serializable;

@Entity
@Table(name = "PC_PROPOSAL_SPEC_FIELD")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ProposalSpecField extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_PROPOSAL_SPEC_FIELD_ID_GENERATED")
    @SequenceGenerator(name = "PC_PROPOSAL_SPEC_FIELD_ID_GENERATED", sequenceName = "PC_PROPOSAL_SPEC_FIELD_SEQ", allocationSize = 1)
    @Column(name = "PC_PROPOSAL_SPEC_FIELD_ID")
    private Long id;

    @Column(name = "PC_PROPOSAL_SPEC_SUM_ITEM_ID")
    private Long proposalSpecSummaryItemId;

    @Column(name = "PR_PROPERTY_PARAM_ID")
    private Long propertyParamId;

    @Column(name = "NAME")
    private String name;

    @Column(name = "ROW_NUMBER")
    private Long rowNumber;

    @Column(name = "COLUMN_NUMBER")
    private Long columnNumber;

    @Column(name = "IS_TEXT")
    private Boolean isText;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProposalSpecSummaryItemId() {
        return proposalSpecSummaryItemId;
    }

    public void setProposalSpecSummaryItemId(Long proposalSpecSummaryItemId) {
        this.proposalSpecSummaryItemId = proposalSpecSummaryItemId;
    }

    public Long getPropertyParamId() {
        return propertyParamId;
    }

    public void setPropertyParamId(Long propertyParamId) {
        this.propertyParamId = propertyParamId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getRowNumber() {
        return rowNumber;
    }

    public void setRowNumber(Long rowNumber) {
        this.rowNumber = rowNumber;
    }

    public Long getColumnNumber() {
        return columnNumber;
    }

    public void setColumnNumber(Long columnNumber) {
        this.columnNumber = columnNumber;
    }

    public Boolean getIsText() {
        return isText != null && isText;
    }

    public void setIsText(Boolean text) {
        isText = text;
    }
}
