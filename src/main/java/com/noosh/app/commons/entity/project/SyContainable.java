package com.noosh.app.commons.entity.project;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 10/20/15
 * Time: 6:24 PM
 */
@Entity
@Table(name = "SY_CONTAINABLE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class SyContainable extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "syContainable_id_generated")
    @SequenceGenerator(name = "syContainable_id_generated", sequenceName = "SY_CONTAINABLE_SEQ", allocationSize = 1)
    @Column(name="SY_CONTAINABLE_ID")
    private Long syContainableId;

    @Column(name = "PARENT_SY_CONTAINABLE_ID")
    private Long parentSyContainableId;

    @Column(name = "PARENT_OBJECT_ID")
    private Long parentObjectId;

    @Column(name = "PARENT_OBJECT_CLASS_ID")
    private Long parentObjectClassId;

    @Column(name = "OBJECT_ID")
    private Long objectId;

    @Column(name = "OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "PARENT_OBJECT_ATTR")
    private String parentObjectAttr;

    private Short isPublic;

    private Long itemOcObjectStateId;

    @Column(name = "X_PARENT_TITLE")
    private String xParentTitle;

    private Short isLinking;

    public Long getSyContainableId() {
        return syContainableId;
    }

    public void setSyContainableId(Long syContainableId) {
        this.syContainableId = syContainableId;
    }

    public Long getParentSyContainableId() {
        return parentSyContainableId;
    }

    public void setParentSyContainableId(Long parentSyContainableId) {
        this.parentSyContainableId = parentSyContainableId;
    }

    public Long getParentObjectId() {
        return parentObjectId;
    }

    public void setParentObjectId(Long parentObjectId) {
        this.parentObjectId = parentObjectId;
    }

    public Long getParentObjectClassId() {
        return parentObjectClassId;
    }

    public void setParentObjectClassId(Long parentObjectClassId) {
        this.parentObjectClassId = parentObjectClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public String getParentObjectAttr() {
        return parentObjectAttr;
    }

    public void setParentObjectAttr(String parentObjectAttr) {
        this.parentObjectAttr = parentObjectAttr == null ? null : parentObjectAttr.trim();
    }

    public Short getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Short isPublic) {
        this.isPublic = isPublic;
    }

    public Long getItemOcObjectStateId() {
        return itemOcObjectStateId;
    }

    public void setItemOcObjectStateId(Long itemOcObjectStateId) {
        this.itemOcObjectStateId = itemOcObjectStateId;
    }

    public String getxParentTitle() {
        return xParentTitle;
    }

    public void setxParentTitle(String xParentTitle) {
        this.xParentTitle = xParentTitle == null ? null : xParentTitle.trim();
    }

    public Short getIsLinking() {
        return isLinking;
    }

    public void setIsLinking(Short isLinking) {
        this.isLinking = isLinking;
    }
}
