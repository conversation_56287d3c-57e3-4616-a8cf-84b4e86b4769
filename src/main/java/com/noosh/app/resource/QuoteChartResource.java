package com.noosh.app.resource;

import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.chart.ChartFilter;
import com.noosh.app.commons.vo.chart.ChartFilterVO;
import com.noosh.app.commons.vo.chart.QuoteChartItemVO;
import com.noosh.app.commons.vo.chart.QuoteConversionChartItemVO;
import com.noosh.app.repository.mybatis.quote.QuoteChartMyBatisMapper;
import com.noosh.app.service.chart.ChartService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * quote chart for mydesk
 *
 * <AUTHOR>
 * @date 8/12/2021
 */
@RestController
@RequestMapping("/quote/chart")
public class QuoteChartResource {

    private static final int OPTION_BY_QUOTE = 1;
    private static final int OPTION_BY_SELL_ORDER = 2;

    @Autowired
    private QuoteChartMyBatisMapper quoteChartMyBatisMapper;
    @Autowired
    private ChartService chartService;

    @Operation(summary = "Time to Quote", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/timeToQuote")
    @Timed
    public ServerResponse<List<QuoteChartItemVO>> timeToQuote(
            @Parameter(description = "display chart filter", required = true, example = "30")
            @RequestParam(value = "filter", required = true, defaultValue = "30") Integer option) {

//        if (OPTION_BY_QUOTE != option && OPTION_BY_SELL_ORDER != option) {
//            throw new NotFoundException("request param option is not correct, it must be 1 or 2");
//        }

        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        List<QuoteChartItemVO> chartData;
//        if (OPTION_BY_QUOTE == option) {
//            chartData = quoteChartMyBatisMapper.findTimeToQuote(workgroupId, option);
//        } else {
//            chartData = quoteChartMyBatisMapper.findTimeToSellOrder(workgroupId);
//        }
        chartData = quoteChartMyBatisMapper.findTimeToQuote(workgroupId, option);
        ChartFilter chartFilter = new ChartFilter();
        chartFilter.setShowOption(option == null ? "30" : String.valueOf(option));
//        chartService.updateQuoteTimeFilter(workgroupId, userId, chartFilter);

        return ServerResponse.success(chartData);
    }

    @Operation(summary = "Quote conversion", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/quoteConversion")
    @Timed
    public ServerResponse<List<QuoteConversionChartItemVO>> quoteConversion(@Parameter(description = "filter", required = true, example = "30")
                                                                            @RequestParam(value = "filter", required = true, defaultValue = "30") Integer filter) {

        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();

        List<QuoteConversionChartItemVO> chartData = quoteChartMyBatisMapper.findQuoteConversion(workgroupId, filter);
        ChartFilter chartFilter = new ChartFilter();
        chartFilter.setShowOption(filter == null ? "30" : String.valueOf(filter));
//        chartService.updateQuoteConversionFilter(workgroupId, userId, chartFilter);
        return ServerResponse.success(chartData);
    }

    @Operation(summary = "Retrieves Time to Quote or Quote conversion filter by workgroup id and user id", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getFilter")
    public ServerResponse<ChartFilterVO> findFilter(@Parameter(description = "Is Time to Quote", required = true, example = "false")
                                                    @RequestParam(value = "isTimeToQuote", required = true, defaultValue = "false") boolean isTimeToQuote) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        ChartFilterVO vo = new ChartFilterVO();
        if(isTimeToQuote) {
            vo = chartService.getQuoteTimeFilter(workgroupId, userId);
        } else {
            vo = chartService.getQuoteConversionFilter(workgroupId, userId);
        }
        return ServerResponse.success(vo);
    }
}