package com.noosh.app.resource;

import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.quote.RfqAndQuoteListVO;
import com.noosh.app.exception.NoPreferenceException;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.quote.QuoteService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@RestController
@RequestMapping("/quote")
public class QuoteResource {

    @Autowired
    private ProjectService projectService;
    @Autowired
    private QuoteService quoteService;
    @Autowired
    private PreferenceService preferenceService;

    @Operation(summary = "Retrieves RFQ (request for Quote), Quote, Proposal list of project",
            security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getRfqAndQuoteList")
    @Timed
    public ServerResponse<RfqAndQuoteListVO> getRfqAndQuoteList(
            @Parameter(description = "project Id", required = true, example = "5206923")
            @RequestParam(value = "projectId") Long projectId
    ) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();

        // check if projectId is exist
        projectService.findProjectById(projectId);

        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        if(!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, groupPrefs)) {
            throw new NoPreferenceException("Your workgroup is not turned on Procurement");
        }

        RfqAndQuoteListVO vo = quoteService.getRfqAndQuoteListVO(projectId, workgroupId, userId);

        return ServerResponse.success(vo);
    }


}
