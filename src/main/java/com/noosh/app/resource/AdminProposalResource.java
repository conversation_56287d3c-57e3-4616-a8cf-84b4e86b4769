package com.noosh.app.resource;

import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.category.CategoryBodyVO;
import com.noosh.app.commons.vo.category.CategoryListVO;
import com.noosh.app.commons.vo.category.CategoryMoveVO;
import com.noosh.app.commons.vo.logo.LogoDetailVO;
import com.noosh.app.commons.vo.logo.LogoVO;
import com.noosh.app.commons.vo.logo.WorkgroupLogoListVO;
import com.noosh.app.commons.vo.proposal.*;
import com.noosh.app.commons.vo.proposal.ProposalTemplateListVO;
import com.noosh.app.commons.vo.proposal.ProposalTemplateBodyVO;
import com.noosh.app.commons.vo.proposal.ProposalTemplateDetailVO;
import com.noosh.app.commons.vo.spec.SpecTypeVO;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.service.proposal.AdminProposalService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 10/31/2021
 */
@RestController
@RequestMapping("/admin/proposal")
public class AdminProposalResource {

    @Autowired
    private AdminProposalService adminProposalService;

    @Operation(summary = "Proposal categories list", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/categories")
    @Timed
    public ServerResponse<CategoryListVO> categoriesList(
            @Parameter(description = "num", required = true, example = "1")
            @RequestParam(value = "num") int num,
            @Parameter(description = "size", required = true, example = "10")
            @RequestParam(value = "size") int size) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(num, size);
        CategoryListVO categoryListVO = adminProposalService.categoriesList(userId, workgroupId, page);
        return ServerResponse.success(categoryListVO, page);
    }

    @Operation(summary = "Move up or move down category", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/moveCategory")
    @Timed
    public ServerResponse moveCategory(@RequestBody CategoryMoveVO categoryMoveVO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        adminProposalService.moveCategory(userId, workgroupId, categoryMoveVO);
        return ServerResponse.success();
    }

    @Operation(summary = "Create or edit proposal category", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/editCategory")
    @Timed
    public ServerResponse editCategory(@RequestBody CategoryBodyVO categoryBodyVO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        adminProposalService.editCategory(userId, workgroupId, categoryBodyVO);
        return ServerResponse.success();
    }

    @Operation(summary = "Delete proposal category", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @DeleteMapping("/deleteCategory")
    @Timed
    public ServerResponse deleteCategory(
            @Parameter(description = "Category Id", required = true, example = "5016358")
            @RequestParam("categoryId") Long categoryId) {
        adminProposalService.deleteCategory(categoryId);
        return ServerResponse.success();
    }

    @Operation(summary = "Proposal logos list", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/logos")
    @Timed
    public ServerResponse<WorkgroupLogoListVO> logosList(
            @Parameter(description = "num", required = true, example = "1")
            @RequestParam(value = "num") int num,
            @Parameter(description = "size", required = true, example = "10")
            @RequestParam(value = "size") int size) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(num, size);
        WorkgroupLogoListVO logoListVO = adminProposalService.logosList(userId, workgroupId, page);
        return ServerResponse.success(logoListVO, page);
    }

    @Operation(summary = "View workgroup logo", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/viewLogo")
    @Timed
    public ServerResponse viewLogo(@Parameter(description = "Logo Id", required = true, example = "5016358")
                                   @RequestParam("logoId") Long logoId) {
        LogoDetailVO logoDetailVO = adminProposalService.viewLogo(logoId);
        return ServerResponse.success(logoDetailVO);
    }

    @Operation(summary = "View workgroup logo image", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/viewLogoImage")
    @Timed
    public void viewLogoImage(@Parameter(description = "Logo Id", required = true, example = "5016358")
                                   @RequestParam("logoId") Long logoId,
                                   HttpServletResponse response) {
        byte[] context = adminProposalService.viewLogoImage(logoId);
        response.setContentType("image/jpeg");
        try(OutputStream out = response.getOutputStream()) {
            out.write(context);
            out.flush();
        } catch (IOException e) {
            throw new UnexpectedException("Failed to get logo image");
        }
    }

    @Operation(summary = "Create or edit workgroup logo", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/editLogo")
    @Timed
    public ServerResponse editLogo(MultipartFile logoFile,
                                   @Parameter(description = "Logo Id", example = "5016358")
                                   @RequestParam(value = "logoId", required = false) Long logoId,
                                   @Parameter(description = "Logo Name", required = true, example = "test")
                                   @RequestParam("logoName") String logoName) throws IOException {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        LogoVO logoVO = adminProposalService.editLogo(userId, workgroupId, logoId, logoName, logoFile);
        return ServerResponse.success(logoVO);
    }

    @Operation(summary = "Delete workgroup logo", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @DeleteMapping("/deleteLogo")
    @Timed
    public ServerResponse deleteLogo(@Parameter(description = "Logo Id", required = true, example = "5016358")
                                     @RequestParam("logoId") Long logoId) {
        adminProposalService.deleteLogo(logoId);
        return ServerResponse.success();
    }

    @Operation(summary = "Proposal templates list", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/templates")
    @Timed
    public ServerResponse<ProposalTemplateListVO> templatesList(
            @Parameter(description = "num", required = true, example = "1")
            @RequestParam(value = "num") int num,
            @Parameter(description = "size", required = true, example = "10")
            @RequestParam(value = "size") int size) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(num, size);
        ProposalTemplateListVO templateListVO = adminProposalService.templatesList(userId, workgroupId, page);
        return ServerResponse.success(templateListVO, page);
    }

    @Operation(summary = "View Template", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/viewTemplate")
    @Timed
    public ServerResponse viewTemplate(@Parameter(description = "Template Id", required = true, example = "5016358")
                                       @RequestParam("templateId") Long templateId,
                                       @Parameter(description = "Locale", required = true, example = "en_US")
                                       @RequestParam("locale") String locale) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        ProposalTemplateDetailVO templateDetailVO = adminProposalService.viewTemplate(templateId, locale, workgroupId);
        return ServerResponse.success(templateDetailVO);
    }

    @Operation(summary = "Get logo options and country options", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/templateOptions")
    @Timed
    public ServerResponse templateOptions() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        ProposalTemplateOptionVO vo = adminProposalService.templateOptions(workgroupId);
        return ServerResponse.success(vo);
    }

    @Operation(summary = "Create or edit proposal template", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/editTemplate")
    @Timed
    public ServerResponse editTemplate(@RequestBody ProposalTemplateBodyVO templateBodyVO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        adminProposalService.editTemplate(userId, workgroupId, templateBodyVO);
        return ServerResponse.success();
    }

    @Operation(summary = "Delete proposal template", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @DeleteMapping("/deleteTemplate")
    @Timed
    public ServerResponse deleteTemplate(@Parameter(description = "Template Id", required = true, example = "5016358")
                                         @RequestParam("templateId") Long templateId) {
        adminProposalService.deleteTemplate(templateId);
        return ServerResponse.success();
    }

    @Operation(summary = "Spec summary list", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/specSummarys")
    @Timed
    public ServerResponse<SpecSummaryListVO> specSummary(
            @Parameter(description = "num", required = true, example = "1")
            @RequestParam(value = "num") int num,
            @Parameter(description = "size", required = true, example = "10")
            @RequestParam(value = "size") int size) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(num, size);
        SpecSummaryListVO specSummaryList = adminProposalService.specSummaryList(userId, workgroupId, page);
        return ServerResponse.success(specSummaryList, page);
    }

    @Operation(summary = "Get spec type list", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/specTypes")
    @Timed
    public ServerResponse specTypes(@Parameter(description = "Locale", required = true, example = "en_US")
                                    @RequestParam(value = "locale") String locale) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        List<SpecTypeVO> specTypeList = adminProposalService.specTypes(workgroupId, locale);
        return ServerResponse.success(specTypeList);
    }

    @Operation(summary = "Create or edit proposal template", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/editSpecSummary")
    @Timed
    public ServerResponse editSpecSummary(@RequestBody SpecSummaryBodyVO bodyVO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        adminProposalService.editSpecSummary(userId, workgroupId, bodyVO);
        return ServerResponse.success();
    }

    @Operation(summary = "Delete spec summary", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @DeleteMapping("/deleteSpecSummary")
    @Timed
    public ServerResponse deleteSpecSummary(@Parameter(description = "Spec summary Id", required = true, example = "5016358")
                                            @RequestParam("specSummaryId") Long specSummaryId) {
        adminProposalService.deleteSpecSummary(specSummaryId);
        return ServerResponse.success();
    }

}
