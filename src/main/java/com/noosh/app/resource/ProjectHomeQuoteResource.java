package com.noosh.app.resource;

import com.noosh.app.commons.vo.quote.RfqAndQuoteDeskoidVO;
import com.noosh.app.service.quote.ProjectHomeQuoteService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/project")
public class ProjectHomeQuoteResource {

    @Autowired
    private ProjectHomeQuoteService projectHomeQuoteService;

    @GetMapping("/rfqAndQuoteDeskoid")
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity<RfqAndQuoteDeskoidVO> getRfqAndQuoteList(
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "rfq Status Id, separated by comma", required = false, example = "-1,2000004")
            @RequestParam(value = "rfqStatusIdFilter", required = false) List<Long> rfqStatusIdFilter,
            @Parameter(description = "quote Status Id, separated by comma", required = false, example = "-1,2000018")
            @RequestParam(value = "quoteStatusIdFilter", required = false) List<Long> quoteStatusIdFilter) {

        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        RfqAndQuoteDeskoidVO result = projectHomeQuoteService.getRfqAndQuoteList(projectId, workgroupId, userId, rfqStatusIdFilter, quoteStatusIdFilter);
        return ResponseEntity.ok(result);
    }
}
