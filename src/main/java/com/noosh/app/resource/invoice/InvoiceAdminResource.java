package com.noosh.app.resource.invoice;

import com.github.pagehelper.Page;
import com.noosh.app.commons.dto.contacts.ContactDTO;
import com.noosh.app.commons.dto.invoice.InvoicePreferencesDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.contacts.ContactVO;
import com.noosh.app.commons.vo.invoice.InvoicePreferencesVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.service.invoice.InvoiceAdminService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Invoice Administration REST Controller
 * Handles invoice billing recipients and preferences management
 * 
 * <AUTHOR> Generated
 */
@RestController
@RequestMapping("/api/invoiceAdmin")
public class InvoiceAdminResource {
    
    @Autowired
    private InvoiceAdminService invoiceAdminService;


    /**
     * Get current invoice preferences
     */
    @Operation(summary = "Get invoice admin preferences", security = @SecurityRequirement(name = "JWT"),
               description = "Retrieve current invoice admin preferences for the workgroup")
    @GetMapping(value = "/preferences", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<InvoicePreferencesVO> getInvoicePreferences() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        InvoicePreferencesVO preferencesVO = invoiceAdminService.getInvoicePreferences(workgroupId);
        return ServerResponse.success(preferencesVO);
    }

    /**
     * Update invoice preferences
     */
    @Operation(summary = "Update invoice preferences", security = @SecurityRequirement(name = "JWT"),
               description = "Update invoice billing preferences for the workgroup")
    @PutMapping(value = "/preferences", produces = MediaType.APPLICATION_JSON_VALUE, 
                consumes = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<String> updateInvoicePreferences(
            @Parameter(description = "Invoice preferences to update", required = true)
            @RequestBody InvoicePreferencesDTO preferencesDTO) {
        
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        
        // Ensure workgroup ID matches current user's workgroup
        preferencesDTO.setWorkgroupId(workgroupId);
        
        // Update preferences through service
        invoiceAdminService.updateInvoicePreferences(preferencesDTO, userId);
        
        return ServerResponse.success("Preferences updated successfully");
    }

    /**
     * Get list of billing contacts with pagination
     */
    @Operation(summary = "Get billing contacts", security = @SecurityRequirement(name = "JWT"),
               description = "Retrieve list of invoice billing recipients for the workgroup with pagination support")
    @GetMapping(value = "/billing-contacts", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<Page<ContactVO>> getBillingContacts(
            @Parameter(description = "Page number (starting from 1)", example = "1")
            @RequestParam(value = "num", defaultValue = "1") int pageNum,
            @Parameter(description = "Page size", example = "10")
            @RequestParam(value = "size", defaultValue = "10") int pageSize,
            @Parameter(description = "Sort field", example = "lastName")
            @RequestParam(value = "sort", required = false) String sort,
            @Parameter(description = "Sort order (ASC/DESC)", example = "ASC")
            @RequestParam(value = "order", required = false) String order) {

        Long workgroupId = JwtUtil.getWorkgroupId();

        // Create PageVO for pagination
        PageVO pageVO = new PageVO(pageNum, pageSize, sort, order);

        // Get contacts from service with pagination
        Page<ContactDTO> contactDTOs = invoiceAdminService.getBillingContactsWithPagination(workgroupId, pageVO);

        // Convert to VOs while preserving pagination info
        Page<ContactVO> contacts = new Page<>();
        contacts.setPageNum(contactDTOs.getPageNum());
        contacts.setPageSize(contactDTOs.getPageSize());
        contacts.setTotal(contactDTOs.getTotal());
        contacts.setPages(contactDTOs.getPages());

        // Convert DTOs to VOs
        List<ContactVO> contactVOs = contactDTOs.stream()
            .map(ContactDTO::toVO)
            .collect(Collectors.toList());
        contacts.addAll(contactVOs);

        return ServerResponse.success(contacts);
    }

    /**
     * Get billing contact details by ID
     */
    @Operation(summary = "Get billing contact details", security = @SecurityRequirement(name = "JWT"),
               description = "Retrieve details of a specific billing contact")
    @GetMapping(value = "/billing-contacts/{contactId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<ContactDTO> getBillingContact(
            @Parameter(description = "Contact ID", required = true)
            @PathVariable Long contactId) {
        ContactDTO contactDTO = invoiceAdminService.findContactWithAddress(contactId);
        if (contactDTO == null) {
            throw new NotFoundException("Contact not found");
        }
        return ServerResponse.success(contactDTO);
    }
}