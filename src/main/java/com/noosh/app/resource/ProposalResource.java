package com.noosh.app.resource;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.proposal.ProposalDTO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.service.proposal.ProposalService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 12/14/2021
 */
@RestController
@RequestMapping("/proposal")
public class ProposalResource {

    @Autowired
    private PermissionService permissionService;
    @Autowired
    private ProposalService proposalService;

    @Operation(summary = "Export proposal to PDF", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/exportProposal")
    @Timed
    public void exportProposal(
            @Parameter(description = "Proposal Id", required = true, example = "1")
            @RequestParam(value = "proposalId") Long proposalId,
            @Parameter(description = "Project Id", required = true, example = "10")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "template", required = true, example = "proposal")
            @RequestParam(value = "template") String template,
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale") String locale,
            HttpServletResponse response) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();

        if (!permissionService.checkAll(PermissionID.VIEW_PROPOSAL, workgroupId, userId, projectId)) {
            throw new NoPermissionException("You have no permission to export proposal");
        }

        try (OutputStream out = response.getOutputStream();
             ByteArrayOutputStream baos = proposalService.exportProposal(userId, workgroupId, projectId, proposalId, template, locale)) {
            response.setContentType("application/x-msdownload");
            String fileName = URLEncoder.encode("Proposal.pdf", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            baos.writeTo(out);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Operation(summary = "Get proposal data to PDF", description = "authored by Neal", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getProposalDataForPdf")
    @Timed
    public ProposalDTO getProposalDataForPdf(@Parameter(description = "Proposal Id", required = true, example = "1")
                                             @RequestParam(value = "proposalId") Long proposalId,
                                             @Parameter(description = "Project Id", required = true, example = "10")
                                             @RequestParam(value = "projectId") Long projectId) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return proposalService.findProposalDTO(userId, workgroupId, projectId, proposalId);
    }

}
