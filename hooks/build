#!/bin/bash
#This build script is intended for usage for git repositories, which only contain one service
#This also assumes that the $DOCKER_TAG value is the branch name, with no timestamp already appended
#This also assumes that dockerhub.com is configured to automatically build based on commits to the branch
#Raymond Kwong 2020-02-27

BUILD_TIMESTAMP=$(date +%Y_%m_%d_%H_%M_%S_%Z)
DOCKER_TAG_BUILD_TIMESTAMP=$DOCKER_TAG"-"$BUILD_TIMESTAMP
DOCKER_TAG_LATEST=$DOCKER_TAG"-latest"

#Remove various chars
COMMIT_MSG="$(echo -e "${COMMIT_MSG}" | xargs | tr -cd '[:space:][:alnum:]._-')"

docker build . -t $IMAGE_NAME -t $DOCKER_REPO:$DOCKER_TAG_BUILD_TIMESTAMP -t $DOCKER_REPO:$DOCKER_TAG_LATEST --build-arg DOCKER_REPO="$DOCKER_REPO" --build-arg DOCKER_TAG="$DOCKER_TAG_BUILD_TIMESTAMP" --build-arg SOURCE_BRANCH="$SOURCE_BRANCH" --build-arg SOURCE_COMMIT="$SOURCE_COMMIT" --build-arg COMMIT_MSG="$COMMIT_MSG"
docker push $DOCKER_REPO:$DOCKER_TAG_BUILD_TIMESTAMP
docker push $DOCKER_REPO:$DOCKER_TAG_LATEST