#!/bin/bash
#This build script is intended for usage for git repositories, which only contain one service
#This also assumes that the $DOCKER_TAG value is the branch name, with no timestamp already appended
#This also assumes that dockerhub.com is configured to automatically build based on commits to the branch
#Raymond Kwong 2024-09-19

BUILD_TIMESTAMP=$(date +%Y_%m_%d_%H_%M_%S_%Z)
DOCKER_TAG_BUILD_TIMESTAMP=$DOCKER_TAG"-"$BUILD_TIMESTAMP
DOCKER_TAG_LATEST=$DOCKER_TAG"-latest"

#Remove various chars
COMMIT_MSG="$(echo -e "${COMMIT_MSG}" | xargs | tr -cd '[:space:][:alnum:]._-')"

docker buildx ls
#2024-09-17T20:59:15Z NAME/NODE DRIVER/ENDPOINT STATUS  BUILDKIT PLATFORMS
#2024-09-17T20:59:15Z default * docker
#2024-09-17T20:59:15Z   default default         running 20.10.15 linux/amd64, linux/386

docker buildx version
#2024-09-17T20:59:15Z github.com/docker/buildx v0.10.5 86bdced

#Assertations are only supported in BuildKit >=0.11
#Therefore use 'docker-container' build driver
docker buildx create --use --name=buildkit-container --driver=docker-container

#Build the image
docker buildx build . -t $DOCKER_REPO:$DOCKER_TAG -t $DOCKER_REPO:$DOCKER_TAG_LATEST -t $DOCKER_REPO:$DOCKER_TAG_BUILD_TIMESTAMP --build-arg DOCKER_REPO="$DOCKER_REPO" --build-arg DOCKER_TAG="$DOCKER_TAG_BUILD_TIMESTAMP" --build-arg SOURCE_BRANCH="$SOURCE_BRANCH" --build-arg SOURCE_COMMIT="$SOURCE_COMMIT" --build-arg COMMIT_MSG="$COMMIT_MSG" --platform linux/amd64 --push --sbom=true --provenance=true